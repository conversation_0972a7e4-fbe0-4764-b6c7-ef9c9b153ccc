"use client";

import React from 'react';
import { useSite } from '@/context/site-context';
import { useAuth } from '@/context/auth-context';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Building2, Globe } from 'lucide-react';

interface HosSiteSelectorProps {
  className?: string;
  variant?: 'select' | 'badge';
  showDescription?: boolean;
}

export function HosSiteSelector({ 
  className = '',
  variant = 'select',
  showDescription = false 
}: HosSiteSelectorProps) {
  const { currentSystemType } = useAuth();
  const { 
    selectedHosSites, 
    hosSiteOptions, 
    setSelectedHosSites,
    isLoading 
  } = useSite();

  // 只在HOS系统中显示
  if (currentSystemType !== 'HOS') {
    return null;
  }

  // 获取当前选择的显示文本
  const getSelectedDisplayText = () => {
    if (selectedHosSites === 'all') {
      return 'All Sites';
    }
    
    if (Array.isArray(selectedHosSites)) {
      if (selectedHosSites.length === 0) {
        return 'No Sites Selected';
      }
      
      if (selectedHosSites.length === 1) {
        const site = hosSiteOptions.find(option => option.id === selectedHosSites[0]);
        return site?.name || 'Unknown Site';
      }
      
      return `${selectedHosSites.length} Sites Selected`;
    }
    
    return 'All Sites';
  };

  // 处理选择变化
  const handleSelectionChange = (value: string) => {
    if (value === 'all') {
      setSelectedHosSites('all');
    } else {
      // 单选模式：选择特定站点
      const siteId = parseInt(value);
      setSelectedHosSites([siteId]);
    }
  };

  // Badge变体
  if (variant === 'badge') {
    return (
      <Badge variant="outline" className={className}>
        <Building2 className="mr-1 h-3 w-3" />
        {getSelectedDisplayText()}
        {showDescription && selectedHosSites === 'all' && (
          <span className="ml-1 text-xs opacity-70">- Global Access</span>
        )}
      </Badge>
    );
  }

  // Select变体（默认）
  return (
    <div className={className}>
      <Select
        value={selectedHosSites === 'all' ? 'all' : selectedHosSites[0]?.toString() || 'all'}
        onValueChange={handleSelectionChange}
        disabled={isLoading || hosSiteOptions.length === 0}
      >
        <SelectTrigger className="w-full">
          <div className="flex items-center">
            {selectedHosSites === 'all' ? (
              <Globe className="mr-2 h-4 w-4 text-blue-500" />
            ) : (
              <Building2 className="mr-2 h-4 w-4 text-green-500" />
            )}
            <SelectValue placeholder="Select sites..." />
          </div>
        </SelectTrigger>
        <SelectContent>
          {hosSiteOptions.map((option) => (
            <SelectItem key={option.id} value={option.id.toString()}>
              <div className="flex items-center">
                {option.isAllSites ? (
                  <Globe className="mr-2 h-4 w-4 text-blue-500" />
                ) : (
                  <Building2 className="mr-2 h-4 w-4 text-green-500" />
                )}
                <div className="flex flex-col">
                  <span className="font-medium">{option.name}</span>
                  {showDescription && option.code && !option.isAllSites && (
                    <span className="text-xs text-muted-foreground">
                      Code: {option.code}
                    </span>
                  )}
                  {showDescription && option.isAllSites && (
                    <span className="text-xs text-muted-foreground">
                      Access all sites within your permission scope
                    </span>
                  )}
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {/* 显示当前选择的站点数量 */}
      {selectedHosSites !== 'all' && Array.isArray(selectedHosSites) && selectedHosSites.length > 0 && (
        <div className="mt-1 text-xs text-muted-foreground">
          {selectedHosSites.length} site{selectedHosSites.length > 1 ? 's' : ''} selected
        </div>
      )}
    </div>
  );
}

export default HosSiteSelector;
