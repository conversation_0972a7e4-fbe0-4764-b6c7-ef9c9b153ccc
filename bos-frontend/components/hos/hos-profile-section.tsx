"use client";

import React from 'react';
import { useAuth } from '@/context/auth-context';
import { useSite } from '@/context/site-context';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Building2,
  Globe,
  Shield,
  Users,
  MapPin,
  Crown
} from "lucide-react";

export function HosProfileSection() {
  const { user, systemAccess } = useAuth();
  const { hosSiteOptions } = useSite();

  if (!user || !systemAccess) {
    return null;
  }

  // 计算管理权限范围
  const getManagementScope = () => {
    if (systemAccess.scopeType === 'global') {
      return {
        type: 'Global',
        description: 'Full access to all sites and operations',
        icon: <Globe className="h-4 w-4 text-blue-500" />,
        color: 'bg-blue-50 text-blue-700 border-blue-200'
      };
    } else if (systemAccess.scopeType === 'station') {
      const siteCount = systemAccess.scopeIds?.length || 0;
      return {
        type: 'Station Level',
        description: `Access to ${siteCount} specific site${siteCount > 1 ? 's' : ''}`,
        icon: <Building2 className="h-4 w-4 text-green-500" />,
        color: 'bg-green-50 text-green-700 border-green-200'
      };
    }
    return {
      type: 'Limited',
      description: 'Restricted access',
      icon: <Shield className="h-4 w-4 text-orange-500" />,
      color: 'bg-orange-50 text-orange-700 border-orange-200'
    };
  };

  const managementScope = getManagementScope();

  // HOS系统优化：固定显示全部站点，不需要动态选择信息

  return (
    <div className="space-y-4">
      {/* 集团/总部信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Crown className="mr-2 h-5 w-5 text-yellow-500" />
            Headquarters Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Organization</label>
              <p className="text-sm font-medium">BP AKR Fuels Retail</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Department</label>
              <p className="text-sm font-medium">Headquarters Operations</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">User Level</label>
              <Badge variant="outline" className="text-xs">
                {systemAccess.accessLevel?.toUpperCase() || 'MANAGER'}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">System Access</label>
              <Badge variant="outline" className="text-xs">
                HOS - Headquarters Office System
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* 管理权限范围 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Shield className="mr-2 h-5 w-5 text-green-500" />
            Management Authority
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className={`p-3 rounded-lg border ${managementScope.color}`}>
            <div className="flex items-center mb-2">
              {managementScope.icon}
              <span className="ml-2 font-medium">{managementScope.type}</span>
            </div>
            <p className="text-sm">{managementScope.description}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {systemAccess.scopeType === 'global' ? '∞' : (systemAccess.scopeIds?.length || 0)}
              </div>
              <div className="text-xs text-muted-foreground">Accessible Sites</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {systemAccess.permissions?.length || 0}
              </div>
              <div className="text-xs text-muted-foreground">Permissions</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {user.roles?.length || 0}
              </div>
              <div className="text-xs text-muted-foreground">Roles</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* 数据查看范围说明 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <MapPin className="mr-2 h-5 w-5 text-orange-500" />
            Data Scope
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <div className="font-medium">All Sites</div>
              <div className="text-sm text-muted-foreground">Viewing aggregated data from all accessible sites within your permission scope</div>
            </div>
            <Badge variant="secondary">
              {hosSiteOptions.length - 1} Sites
            </Badge>
          </div>

          <div className="text-xs text-muted-foreground">
            <p>• Dashboard and reports show consolidated data across all sites you have access to</p>
            <p>• Data aggregation provides comprehensive insights for headquarters-level decision making</p>
            <p>• Individual site details can be accessed through specific reports and analytics</p>
          </div>
        </CardContent>
      </Card>

      {/* 权限列表 */}
      {systemAccess.permissions && systemAccess.permissions.length > 0 && (
        <>
          <Separator />
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Users className="mr-2 h-5 w-5 text-indigo-500" />
                System Permissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {systemAccess.permissions.slice(0, 10).map((permission, index) => (
                  <Badge key={index} variant="outline" className="text-xs justify-start">
                    {permission}
                  </Badge>
                ))}
                {systemAccess.permissions.length > 10 && (
                  <Badge variant="secondary" className="text-xs">
                    +{systemAccess.permissions.length - 10} more...
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

export default HosProfileSection;
