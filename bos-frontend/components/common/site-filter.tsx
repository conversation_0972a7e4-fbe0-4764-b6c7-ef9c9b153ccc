"use client";

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/auth-context';
import { useSite } from '@/context/site-context';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Building2, Globe, Filter } from 'lucide-react';

interface SiteFilterProps {
  className?: string;
  placeholder?: string;
  showLabel?: boolean;
  onSiteChange?: (siteIds: number[] | null) => void; // null表示所有站点
  variant?: 'select' | 'badge';
}

export function SiteFilter({ 
  className = '',
  placeholder = 'Select sites...',
  showLabel = true,
  onSiteChange,
  variant = 'select'
}: SiteFilterProps) {
  const { currentSystemType } = useAuth();
  const { 
    selectedHosSites, 
    hosSiteOptions, 
    setSelectedHosSites,
    getFilteredSiteIds,
    currentSite,
    availableSites
  } = useSite();

  const [localSelection, setLocalSelection] = useState<string>('');

  // 根据系统类型获取当前选择的站点ID列表
  const getCurrentSiteIds = (): number[] | null => {
    if (currentSystemType === 'HOS') {
      return getFilteredSiteIds();
    } else {
      // BOS/EDC系统：返回当前站点ID
      return currentSite ? [currentSite.id] : null;
    }
  };

  // 获取显示文本
  const getDisplayText = () => {
    if (currentSystemType === 'HOS') {
      if (selectedHosSites === 'all') {
        return 'All Sites';
      }
      if (Array.isArray(selectedHosSites)) {
        if (selectedHosSites.length === 0) {
          return 'No Sites';
        }
        if (selectedHosSites.length === 1) {
          const site = hosSiteOptions.find(option => option.id === selectedHosSites[0]);
          return site?.name || 'Unknown Site';
        }
        return `${selectedHosSites.length} Sites`;
      }
    } else {
      // BOS/EDC系统
      return currentSite?.name || 'No Site';
    }
    return 'All Sites';
  };

  // 处理HOS系统的站点选择变化
  const handleHosSelectionChange = (value: string) => {
    if (value === 'all') {
      setSelectedHosSites('all');
      onSiteChange?.(null); // null表示所有站点
    } else {
      const siteId = parseInt(value);
      setSelectedHosSites([siteId]);
      onSiteChange?.([siteId]);
    }
  };

  // 处理BOS/EDC系统的站点选择变化
  const handleBosSelectionChange = (value: string) => {
    const siteId = parseInt(value);
    const selectedSite = availableSites.find(site => site.id === siteId);
    if (selectedSite) {
      // 这里可以触发站点切换逻辑
      onSiteChange?.([siteId]);
    }
  };

  // 监听站点选择变化，通知父组件
  useEffect(() => {
    const siteIds = getCurrentSiteIds();
    onSiteChange?.(siteIds);
  }, [selectedHosSites, currentSite?.id, currentSystemType]);

  // Badge变体
  if (variant === 'badge') {
    return (
      <Badge variant="outline" className={className}>
        <Filter className="mr-1 h-3 w-3" />
        {getDisplayText()}
      </Badge>
    );
  }

  // HOS系统的选择器
  if (currentSystemType === 'HOS') {
    return (
      <div className={className}>
        {showLabel && (
          <label className="text-sm font-medium text-muted-foreground mb-2 block">
            Site Filter
          </label>
        )}
        <Select
          value={selectedHosSites === 'all' ? 'all' : selectedHosSites[0]?.toString() || 'all'}
          onValueChange={handleHosSelectionChange}
        >
          <SelectTrigger>
            <div className="flex items-center">
              {selectedHosSites === 'all' ? (
                <Globe className="mr-2 h-4 w-4 text-blue-500" />
              ) : (
                <Building2 className="mr-2 h-4 w-4 text-green-500" />
              )}
              <SelectValue placeholder={placeholder} />
            </div>
          </SelectTrigger>
          <SelectContent>
            {hosSiteOptions.map((option) => (
              <SelectItem key={option.id} value={option.id.toString()}>
                <div className="flex items-center">
                  {option.isAllSites ? (
                    <Globe className="mr-2 h-4 w-4 text-blue-500" />
                  ) : (
                    <Building2 className="mr-2 h-4 w-4 text-green-500" />
                  )}
                  <div className="flex flex-col">
                    <span className="font-medium">{option.name}</span>
                    {option.code && !option.isAllSites && (
                      <span className="text-xs text-muted-foreground">
                        {option.code}
                      </span>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  // BOS/EDC系统的选择器（如果有多个站点可选）
  if (availableSites.length > 1) {
    return (
      <div className={className}>
        {showLabel && (
          <label className="text-sm font-medium text-muted-foreground mb-2 block">
            Current Site
          </label>
        )}
        <Select
          value={currentSite?.id.toString() || ''}
          onValueChange={handleBosSelectionChange}
        >
          <SelectTrigger>
            <div className="flex items-center">
              <Building2 className="mr-2 h-4 w-4 text-green-500" />
              <SelectValue placeholder={placeholder} />
            </div>
          </SelectTrigger>
          <SelectContent>
            {availableSites.map((site) => (
              <SelectItem key={site.id} value={site.id.toString()}>
                <div className="flex items-center">
                  <Building2 className="mr-2 h-4 w-4 text-green-500" />
                  <div className="flex flex-col">
                    <span className="font-medium">{site.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {site.code}
                    </span>
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  // 单站点或无站点的情况，显示当前站点信息
  return (
    <div className={className}>
      {showLabel && (
        <label className="text-sm font-medium text-muted-foreground mb-2 block">
          Current Site
        </label>
      )}
      <div className="flex items-center px-3 py-2 border rounded-md bg-muted/50">
        <Building2 className="mr-2 h-4 w-4 text-muted-foreground" />
        <span className="text-sm">{getDisplayText()}</span>
      </div>
    </div>
  );
}

export default SiteFilter;
