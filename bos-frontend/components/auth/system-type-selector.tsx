"use client";

import React from 'react';
import { useAuth } from '@/context/auth-context';
import { SystemType, SYSTEM_TYPES } from '@/lib/config';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

interface SystemTypeSelectorProps {
  variant?: 'select' | 'buttons' | 'badge';
  className?: string;
  showDescription?: boolean;
}

export function SystemTypeSelector({ 
  variant = 'select', 
  className = '',
  showDescription = false 
}: SystemTypeSelectorProps) {
  const { currentSystemType, setSystemType, isAuthenticated } = useAuth();

  const handleSystemTypeChange = (systemType: SystemType) => {
    setSystemType(systemType);
  };

  if (variant === 'badge') {
    return (
      <Badge variant="outline" className={className}>
        {SYSTEM_TYPES[currentSystemType].name}
        {showDescription && ` - ${SYSTEM_TYPES[currentSystemType].description}`}
      </Badge>
    );
  }

  if (variant === 'buttons') {
    return (
      <div className={`flex gap-2 ${className}`}>
        {Object.entries(SYSTEM_TYPES).map(([key, value]) => (
          <Button
            key={key}
            variant={currentSystemType === key ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSystemTypeChange(key as SystemType)}
            disabled={isAuthenticated} // 登录后禁用切换
          >
            {value.name}
          </Button>
        ))}
      </div>
    );
  }

  // 默认为 select 变体
  return (
    <div className={className}>
      <Select
        value={currentSystemType}
        onValueChange={(value) => handleSystemTypeChange(value as SystemType)}
        disabled={isAuthenticated} // 登录后禁用切换
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="选择系统类型" />
        </SelectTrigger>
        <SelectContent>
          {Object.entries(SYSTEM_TYPES).map(([key, value]) => (
            <SelectItem key={key} value={key}>
              <div className="flex flex-col">
                <span className="font-medium">{value.name}</span>
                {showDescription && (
                  <span className="text-xs text-muted-foreground">
                    {value.description}
                  </span>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

export default SystemTypeSelector;
