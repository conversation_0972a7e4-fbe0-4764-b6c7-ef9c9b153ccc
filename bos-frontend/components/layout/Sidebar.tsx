"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { useState } from "react";
import {
  BarChart2, ShoppingCart, DollarSign,
  Fuel, Home, UserCog, Building2, Calendar,
  Settings, ChevronDown, ChevronRight, Tag, Globe
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getStationDisplayName } from "@/utils/station-data-parser";
import { useAuth } from "@/context/auth-context";
import { HosSiteSelector } from "@/components/hos/hos-site-selector";

// 图标映射
const iconMap: Record<string, React.ReactNode> = {
  "dashboard": <Home className="h-5 w-5" />,
  "reports": <BarChart2 className="h-5 w-5" />,
  "oil": <Fuel className="h-5 w-5" />,
  "order": <ShoppingCart className="h-5 w-5" />,
  "employee": <UserCog className="h-5 w-5" />,
  "calendar": <Calendar className="h-5 w-5" />,
  "payment": <DollarSign className="h-5 w-5" />,
  "promotion": <Tag className="h-5 w-5" />
};

interface SidebarItemProps {
  title: string;
  href: string;
  icon?: string;
  items?: SidebarItemProps[];
}

interface SidebarProps {
  items: SidebarItemProps[];
  site: any;
  activeRole: string | null;
}

export default function Sidebar({
  items,
  site,
  activeRole
}: SidebarProps) {
  const pathname = usePathname() || "/";
  const { currentSystemType } = useAuth();
  
  // 管理每个菜单项的展开状态
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(() => {
    // 初始化时展开包含当前路径的菜单项
    const initialExpanded: Record<string, boolean> = {};
    items.forEach((item, index) => {
      if (item.items) {
        const hasActiveChild = item.items.some(child => {
          // 精确匹配或者是动态路由的子路径
          return pathname === child.href || 
                 (child.href !== '/' && pathname.startsWith(child.href + '/'));
        });
        initialExpanded[index.toString()] = hasActiveChild;
      }
    });
    return initialExpanded;
  });

  // 切换菜单项展开状态
  const toggleExpanded = (index: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // 检查子菜单项是否激活（精确匹配逻辑）
  const isChildMenuActive = (childHref: string, currentPath: string) => {
    // 精确匹配
    if (currentPath === childHref) return true;
    
    // 对于动态路由，如 /order-management/detail/[id]
    // 检查是否是其子路径，但要排除同级别的其他菜单项
    if (childHref !== '/' && currentPath.startsWith(childHref + '/')) {
      // 确保不是同一级别的其他菜单项
      // 例如，当前路径是 /oil-management/basic-info 时
      // 不应该激活 /oil-management (Overview) 菜单项
      const pathSegments = currentPath.split('/');
      const hrefSegments = childHref.split('/');
      
      // 如果href是基础路径（如 /oil-management），而当前路径有更多段
      // 那么只有当前路径确实是该href的子路径时才激活
      if (hrefSegments.length === 2 && pathSegments.length > 2) {
        // 这种情况下，基础路径菜单项不应该被激活
        return false;
      }
      
      return true;
    }
    
    return false;
  };

  return (
    <aside className="w-64 bg-green-600 text-white h-full flex flex-col">
        <div className="pb-4"/>
        
        <nav className="flex-1 px-3 py-4 overflow-y-auto scrollbar-hide">
          <ul className="space-y-2.5">
            {items.map((item, index) => {
              const itemKey = index.toString();
              const isExpanded = expandedItems[itemKey] || false;
              const hasSubItems = item.items && item.items.length > 0;
              
              // 检查是否有子项激活
              const hasActiveChild = hasSubItems && item.items!.some(child => 
                isChildMenuActive(child.href, pathname)
              );
              
              // 对于没有子项的顶级菜单项（如Dashboard），检查自身是否激活
              const isSelfActive = !hasSubItems && pathname === item.href;
              
              return (
                <li key={index} className="space-y-1">
                  {/* 一级菜单项 */}
                  {hasSubItems ? (
                    // 有子项的菜单 - 点击切换展开状态
                    <button
                      onClick={() => toggleExpanded(itemKey)}
                      className={cn(
                        "w-full flex items-center justify-between py-3 px-4 rounded-md text-sm font-medium transition-colors text-left",
                        hasActiveChild
                          ? "bg-white/10 text-white"
                          : "text-white/80 hover:bg-white/10 hover:text-white"
                      )}
                    >
                      <div className="flex items-center">
                        {item.icon && iconMap[item.icon] ? (
                          <span className="mr-3">{iconMap[item.icon]}</span>
                        ) : (
                          <span className="mr-3">
                            <Settings className="h-5 w-5" />
                          </span>
                        )}
                        <span>{item.title}</span>
                      </div>
                      <span className="text-white/70">
                        {isExpanded ? (
                          <ChevronDown className="h-5 w-5" />
                        ) : (
                          <ChevronRight className="h-5 w-5" />
                        )}
                      </span>
                    </button>
                  ) : (
                    // 没有子项的菜单 - 直接跳转（如Dashboard）
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center justify-between py-3 px-4 rounded-md text-sm font-medium transition-colors",
                        isSelfActive
                          ? "bg-white/10 text-white"
                          : "text-white/80 hover:bg-white/10 hover:text-white"
                      )}
                    >
                      <div className="flex items-center">
                        {item.icon && iconMap[item.icon] ? (
                          <span className="mr-3">{iconMap[item.icon]}</span>
                        ) : (
                          <span className="mr-3">
                            <Settings className="h-5 w-5" />
                          </span>
                        )}
                        <span>{item.title}</span>
                      </div>
                    </Link>
                  )}
                  
                  {/* 二级菜单项 */}
                  {isExpanded && hasSubItems && (
                    <ul className="pl-10 pr-2 space-y-1.5 mt-1.5">
                      {item.items!.map((child, childIndex) => {
                        const isChildActive = isChildMenuActive(child.href, pathname);
                        
                        return (
                          <li key={childIndex}>
                            <Link
                              href={child.href}
                              className={cn(
                                "block py-2 px-4 rounded-md text-sm transition-colors",
                                isChildActive
                                  ? "bg-white/10 text-white font-medium"
                                  : "text-white/70 hover:bg-white/10 hover:text-white"
                              )}
                            >
                              {child.title}
                            </Link>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </li>
              );
            })}
          </ul>
        </nav>
        
        {/* 底部信息部分 */}
        <div className="border-t border-white/10 p-4 space-y-4">
          {/* 当前站点信息 - 根据系统类型显示不同内容 */}
          <div className="rounded-md bg-white/5 p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-white/80">
                {currentSystemType === 'HOS' ? 'Site Management' : 'Current Site'}
              </span>
              {activeRole && (
                <Badge variant="outline" className="text-xs py-0 h-5 bg-white/10 text-white border-white/20">
                  {activeRole}
                </Badge>
              )}
            </div>

            {currentSystemType === 'HOS' ? (
              /* HOS系统：静态显示全部站点（优化后：不可交互） */
              <div className="w-full flex items-center px-3 py-2 rounded-md bg-white/5 text-white text-sm">
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-white/70" />
                  <span className="truncate">All Sites</span>
                </div>
              </div>
            ) : (
              /* BOS/EDC系统：显示当前站点信息（不可切换） */
              <div className="w-full flex items-center px-3 py-2 rounded-md bg-white/5 text-white text-sm">
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2 text-white/70" />
                  <span className="truncate">{getStationDisplayName(site)}</span>
                </div>
              </div>
            )}
          </div>
          
          {/* 版本信息 */}
          <div className="flex items-center justify-between text-xs text-white/60 px-2">
            <span>BP AKR Fuels Retail</span>
            <span>v1.0.0</span>
          </div>
        </div>
    </aside>
  );
} 