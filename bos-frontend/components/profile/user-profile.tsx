"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/auth-context';
import { useSite } from '@/context/site-context';
import { HosProfileSection } from '@/components/hos/hos-profile-section';
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Avatar, 
  AvatarFallback, 
  AvatarImage 
} from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  MapPin, 
  Mail, 
  Building2,
  Lock,
  LogOut,
  Camera,
  Save,
  X
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { 
  parseStationData, 
  getStationDisplayName, 
  getStationCode,
  formatAddress,
  formatShort<PERSON><PERSON><PERSON>,
  parseAddress
} from "@/utils/station-data-parser";
import { changePassword } from "@/services/auth-service";
import { ChangePasswordRequest } from "@/types/auth";

interface UserProfileProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function UserProfile({ open, onOpenChange }: UserProfileProps) {
  const { user, logout, activeStation, currentSystemType } = useAuth();
  const { currentSite } = useSite();
  const { toast } = useToast();

  // 优先使用activeStation中的站点信息，如果没有则使用currentSite
  const rawSite = activeStation?.station || currentSite;

  // 解析站点数据（仅在非HOS系统中使用）
  const displaySite = currentSystemType !== 'HOS' ? parseStationData(rawSite) : null;
  
  // 编辑状态
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // 获取用户头像首字母
  const getInitials = () => {
    if (!user) return "BP";
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };
  

  
  // 处理密码修改
  const handleChangePassword = async () => {
    // 验证密码
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "New password and confirm password do not match.",
        variant: "destructive"
      });
      return;
    }
    
    if (passwordData.newPassword.length < 6) {
      toast({
        title: "Password Too Short",
        description: "Password must be at least 6 characters long.",
        variant: "destructive"
      });
      return;
    }

    try {
      // 调用真实的API修改密码
      const changePasswordData: ChangePasswordRequest = {
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
        confirmPassword: passwordData.confirmPassword
      };
      
      await changePassword(changePasswordData);
      
      toast({
        title: "Password Changed",
        description: "Your password has been changed successfully.",
      });
      
      setIsChangingPassword(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      console.error('Password change error:', error);
      toast({
        title: "Password Change Failed",
        description: error instanceof Error ? error.message : "Failed to change password. Please check your current password.",
        variant: "destructive"
      });
    }
  };
  
  // 处理头像上传
  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 这里应该上传头像文件
      console.log('Uploading avatar:', file.name);
      toast({
        title: "Avatar Updated",
        description: "Profile picture has been updated successfully.",
      });
    }
  };

  // 处理退出登录
  const handleLogout = async () => {
    try {
      await logout();
      onOpenChange(false); // 关闭弹窗
      toast({
        title: "Logged Out",
        description: "You have been logged out successfully.",
      });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!user) {
    return null;
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Profile</DialogTitle>
        </DialogHeader>
      
        <div className="space-y-6 py-4">
          {/* 个人信息区域 */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-muted-foreground" />
              <h3 className="text-lg font-semibold">Personal Information</h3>
            </div>
          
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex flex-col items-center text-center space-y-4">
                {/* 头像 */}
                <div className="relative">
                  <Avatar className="h-20 w-20 border-4 border-white shadow-lg">
                  <AvatarImage src={user.avatar} alt={user.fullName} />
                    <AvatarFallback className="text-lg font-bold bg-green-100 text-green-700">
                      {getInitials()}
                    </AvatarFallback>
                </Avatar>
                
                  {/* 头像上传按钮 */}
                  <label className="absolute bottom-0 right-0 bg-green-600 hover:bg-green-700 text-white p-1.5 rounded-full cursor-pointer transition-colors">
                    <Camera className="h-3 w-3" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarChange}
                      className="hidden"
                    />
                  </label>
              </div>
              
                {/* 基本信息 */}
                <div className="space-y-2">
                  <h4 className="text-xl font-bold">{user.fullName}</h4>
                  <p className="text-muted-foreground">{user.roles?.[0] || 'Site Manager'}</p>
                  
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div className="flex items-center justify-center">
                      <Mail className="mr-2 h-4 w-4" />
                      {user.email}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <Separator />

          {/* 根据系统类型显示不同的信息区域 */}
          {currentSystemType === 'HOS' ? (
            /* HOS系统：显示总部/集团信息 */
            <HosProfileSection />
          ) : (
            /* BOS/EDC系统：显示站点信息 */
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5 text-muted-foreground" />
                <h3 className="text-lg font-semibold">Current Site</h3>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div>
                  <h4 className="text-lg font-semibold">
                    {displaySite?.displayName || 'BP Site'}
                  </h4>
                  <p className="text-muted-foreground">
                    {displaySite?.formattedShortAddress || 'Location not available'}
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-3 text-sm">
                  <div className="flex items-center">
                    <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Address:</span>
                    <span className="ml-2">
                      {displaySite?.formattedAddress || 'Address not available'}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <Building2 className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Site Code:</span>
                    <span className="ml-2 font-mono">
                      {displaySite?.displayCode || 'N/A'}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <User className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Manager since:</span>
                    <span className="ml-2">{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <Separator />

          {/* 安全设置区域 */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Lock className="h-5 w-5 text-muted-foreground" />
              <h3 className="text-lg font-semibold">Security</h3>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              {!isChangingPassword ? (
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Password</h4>
                <p className="text-sm text-muted-foreground">
                      Last changed: {user.updatedAt ? new Date(user.updatedAt).toLocaleDateString() : 'N/A'}
                </p>
              </div>
              
                  <Button onClick={() => setIsChangingPassword(true)}>
                    Change Password
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                      placeholder="Enter current password"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                      placeholder="Enter new password"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                      placeholder="Confirm new password"
                    />
                </div>
                
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={() => setIsChangingPassword(false)} className="flex-1">
                      <X className="mr-2 h-4 w-4" />
                      Cancel
                    </Button>
                    <Button onClick={handleChangePassword} className="flex-1">
                      <Save className="mr-2 h-4 w-4" />
                      Change
                    </Button>
                  </div>
                </div>
              )}
              
              <div className="pt-4 border-t">
                <Button 
                  variant="destructive" 
                  onClick={handleLogout}
                  className="w-full"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export { UserProfile };
export default UserProfile;