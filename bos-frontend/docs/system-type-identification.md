# 系统类型识别功能文档

## 概述

本文档描述了bos-frontend项目中实现的系统类型识别功能，该功能允许用户在HOS（总部管理系统）和BOS（站点后台系统）之间进行选择和切换。

## 功能特性

### 1. 全局系统类型管理
- 支持HOS、BOS、EDC三种系统类型
- 全局状态管理，整个应用中可访问
- 持久化存储到localStorage
- 支持环境变量配置默认系统类型

### 2. 动态登录接口调用
- 登录时根据选择的系统类型动态设置`system`参数
- 符合统一认证API文档规范
- 自动验证系统访问权限

### 3. 用户界面组件
- 系统类型选择器组件，支持多种显示模式
- 登录页面集成系统类型选择
- 实时显示当前系统类型

## 技术实现

### 配置文件 (`lib/config.ts`)

```typescript
// 系统类型定义
export type SystemType = 'HOS' | 'BOS' | 'EDC';

// 系统类型配置
export const SYSTEM_TYPES: Record<SystemType, { name: string; description: string }> = {
  HOS: { name: 'HOS', description: 'Headquarters Office System' },
  BOS: { name: 'BOS', description: 'Back Office System' },
  EDC: { name: 'EDC', description: 'Electronic Data Capture' }
};

// 默认系统类型（从环境变量读取，默认为BOS）
export const DEFAULT_SYSTEM_TYPE: SystemType = (process.env.NEXT_PUBLIC_SYSTEM_TYPE as SystemType) || 'BOS';
```

### 认证上下文扩展 (`context/auth-context.tsx`)

新增的属性和方法：
- `currentSystemType`: 当前选择的系统类型
- `systemAccess`: 系统访问权限信息
- `setSystemType(systemType: SystemType)`: 设置系统类型的方法

### 系统类型选择器组件 (`components/auth/system-type-selector.tsx`)

支持三种显示模式：
- `select`: 下拉选择框（默认）
- `buttons`: 按钮组
- `badge`: 徽章显示

## 使用方法

### 1. 环境变量配置

在`.env.local`文件中配置默认系统类型：

```bash
# 系统类型配置
NEXT_PUBLIC_SYSTEM_TYPE=BOS
```

### 2. 在组件中使用

```typescript
import { useAuth } from '@/context/auth-context';

function MyComponent() {
  const { currentSystemType, setSystemType, systemAccess } = useAuth();
  
  // 获取当前系统类型
  console.log('当前系统类型:', currentSystemType);
  
  // 切换系统类型
  const handleSwitchSystem = () => {
    setSystemType('HOS');
  };
  
  return (
    <div>
      <p>当前系统: {currentSystemType}</p>
      <button onClick={handleSwitchSystem}>切换到HOS</button>
    </div>
  );
}
```

### 3. 使用系统类型选择器

```typescript
import { SystemTypeSelector } from '@/components/auth/system-type-selector';

function LoginForm() {
  return (
    <form>
      {/* 其他表单字段 */}
      
      <SystemTypeSelector 
        variant="select" 
        showDescription={true}
        className="w-full"
      />
      
      {/* 登录按钮 */}
    </form>
  );
}
```

## API集成

### 登录接口调用

系统会自动根据选择的系统类型调用登录接口：

```typescript
// 自动使用当前选择的系统类型
const response = await apiLogin({ 
  username, 
  password, 
  system: currentSystemType  // 动态设置
});
```

### 响应处理

登录成功后，系统会验证返回的`systemAccess`信息是否与选择的系统类型匹配。

## 注意事项

1. **系统切换限制**: 用户登录后无法切换系统类型，需要重新登录
2. **权限验证**: 系统会根据用户权限验证是否可以访问选择的系统类型
3. **向后兼容**: 默认系统类型为BOS，保持与现有功能的兼容性
4. **持久化存储**: 系统类型选择会保存到localStorage，页面刷新后保持状态

## 错误处理

- 如果用户没有访问选择系统的权限，会显示相应的错误信息
- 系统类型切换时会清理相关状态，确保数据一致性
- 网络错误或API错误会有相应的用户提示

## 扩展性

该实现支持未来扩展：
- 添加新的系统类型
- 支持运行时系统切换
- 集成更多的系统特定功能
- 支持多系统同时登录（如果需要）
