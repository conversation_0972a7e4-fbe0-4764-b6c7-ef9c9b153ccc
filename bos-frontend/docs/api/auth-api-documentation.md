# BOS 认证API接口文档

## 概述

本文档详细描述了BOS系统的认证API接口，包括用户登录和获取当前用户信息等核心功能。

### 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: <PERSON><PERSON> (JWT)
- **字符编码**: UTF-8

### 通用响应格式

所有API响应都遵循统一的格式：

```json
{
  "code": 0,
  "message": "Success",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

**响应字段说明**：
- `code`: 业务状态码 (number, 0表示成功，正数表示业务错误)
- `message`: 响应消息 (string)
- `data`: 响应数据 (object/array/null)
- `timestamp`: 响应时间戳 (ISO 8601)
- `requestId`: 请求唯一标识 (string)

### 错误响应格式

```json
{
  "code": 1001,
  "message": "错误描述",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

## API接口详情

### 1. 用户登录

用户通过用户名/邮箱和密码登录指定系统。

#### 请求

- **路径**: `/auth/login`
- **方法**: `POST`
- **认证**: 无需认证

#### 请求参数

```json
{
  "username": "admin",           // 用户名或邮箱地址 (必填)
  "password": "Admin123",        // 登录密码 (必填)
  "system": "BOS",              // 目标系统: BOS|EDC (必填)
  "authType": "local",          // 认证类型: local|ldap|ad (可选，默认local)
  "rememberMe": false,          // 是否记住登录状态 (可选，默认false)
  "captcha": "",                // 验证码 (可选)
  "captchaId": ""               // 验证码ID (可选)
}
```

**参数说明**：
- `username`: 支持用户名或邮箱地址登录，长度3-100字符
- `password`: 密码，长度8-128字符
- `system`: 目标系统，必须是 `BOS` 或 `EDC`
- `authType`: 认证类型，支持 `local`(本地)、`ldap`、`ad`
- `rememberMe`: 是否记住登录状态，影响token过期时间
- `captcha`: 验证码，在需要时提供
- `captchaId`: 验证码ID，与验证码配套使用

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "admin",
      "email": "<EMAIL>",
      "fullName": "系统管理员",
      "phone": "+62123456789",
      "status": "active",
      "roles": ["admin", "manager"],
      "permissions": ["user:read", "user:write", "station:manage"],
      "lastLoginAt": "2024-01-15T10:30:00Z",
      "language": "en",
      "sites": [
        {
          "siteId": "1",
          "siteName": "Jakarta Central Station",
          "role": "manager",
          "isDefault": true
        }
      ],
      "stations": [
        {
          "stationId": "1",
          "roleType": "manager",
          "isDefault": true,
          "hasManagePermission": true,
          "station": {
            "id": 1,
            "site_code": "JKT001",
            "site_name": "Jakarta Central Station",
            "type": "fuel_station",
            "business_status": "active",
            "address": "Jl. Sudirman No. 123",
            "city": "Jakarta",
            "province": "DKI Jakarta",
            "country": "Indonesia",
            "latitude": -6.2088,
            "longitude": 106.8456,
            "contact_info": {
              "phone": "+***********",
              "email": "<EMAIL>"
            },
            "manager_name": "John Doe",
            "company_id": 1,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-15T10:30:00Z"
          }
        }
      ]
    },
    "systemAccess": {
      "system": "BOS",
      "accessLevel": "manager",
      "stationIds": [1, 2, 3],
      "stationCount": 3
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

#### 错误响应

**400 Bad Request - 参数错误**
```json
{
  "code": 1001,
  "message": "Invalid request parameters",
  "errors": [
    {
      "field": "username",
      "message": "Username is required"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

**401 Unauthorized - 认证失败**
```json
{
  "code": 1001,
  "message": "Invalid username or password",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

**403 Forbidden - 系统访问权限不足**
```json
{
  "code": 4001,
  "message": "No station association found for user in BOS system",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 2. 获取当前用户信息

获取当前登录用户的详细信息，包括角色、权限、站点等。

#### 请求

- **路径**: `/auth/me`
- **方法**: `GET`
- **认证**: 需要Bearer Token

#### 请求头

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "User information retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "admin",
    "email": "<EMAIL>",
    "fullName": "系统管理员",
    "phone": "+62123456789",
    "status": "active",
    "roles": ["admin", "manager"],
    "permissions": ["user:read", "user:write", "station:manage"],
    "lastLoginAt": "2024-01-15T10:30:00Z",
    "language": "en",
    "sites": [
      {
        "siteId": "1",
        "siteName": "Jakarta Central Station",
        "role": "manager",
        "isDefault": true
      }
    ],
    "stations": [
      {
        "stationId": "1",
        "roleType": "manager",
        "isDefault": true,
        "hasManagePermission": true,
        "station": {
          "id": 1,
          "site_code": "JKT001",
          "site_name": "Jakarta Central Station",
          "type": "fuel_station",
          "business_status": "active",
          "address": "Jl. Sudirman No. 123",
          "city": "Jakarta",
          "province": "DKI Jakarta",
          "country": "Indonesia",
          "latitude": -6.2088,
          "longitude": 106.8456,
          "contact_info": {
            "phone": "+***********",
            "email": "<EMAIL>"
          },
          "manager_name": "John Doe",
          "company_id": 1,
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-15T10:30:00Z"
        }
      }
    ],
    "systemAccess": {
      "system": "BOS",
      "accessLevel": "manager",
      "stationIds": [1, 2, 3],
      "stationCount": 3
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

#### 错误响应

**401 Unauthorized - Token无效或过期**
```json
{
  "code": 1005,
  "message": "Token expired",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

**404 Not Found - 用户不存在**
```json
{
  "code": 2001,
  "message": "User not found",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 3. 刷新访问令牌

使用刷新令牌获取新的访问令牌。

#### 请求

- **路径**: `/auth/refresh`
- **方法**: `POST`
- **认证**: 无需认证

#### 请求参数

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

#### 错误响应

**401 Unauthorized - 刷新令牌无效**
```json
{
  "code": 1006,
  "message": "Invalid refresh token",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 4. 用户登出

使当前访问令牌失效，清除用户会话。

#### 请求

- **路径**: `/auth/logout`
- **方法**: `POST`
- **认证**: 需要Bearer Token

#### 请求头

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "Logout successful",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 5. 验证令牌

验证访问令牌的有效性。

#### 请求

- **路径**: `/auth/verify`
- **方法**: `POST`
- **认证**: 无需认证

#### 请求参数

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "Token is valid",
  "data": {
    "valid": true,
    "userId": "550e8400-e29b-41d4-a716-************",
    "username": "admin",
    "email": "<EMAIL>",
    "fullName": "系统管理员",
    "status": "active"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 6. 修改密码

修改当前用户的登录密码。

#### 请求

- **路径**: `/auth/password`
- **方法**: `PUT`
- **认证**: 需要Bearer Token

#### 请求参数

```json
{
  "currentPassword": "OldPassword123",
  "newPassword": "NewPassword456",
  "confirmPassword": "NewPassword456"
}
```

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "Password changed successfully",
  "data": null,
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

#### 错误响应

**400 Bad Request - 当前密码错误**
```json
{
  "code": 1001,
  "message": "Current password is incorrect",
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

### 7. 权限验证

验证用户是否具有特定资源的操作权限。

#### 请求

- **路径**: `/auth/verify-permission`
- **方法**: `POST`
- **认证**: 需要Bearer Token

#### 请求参数

```json
{
  "resource": "user",
  "action": "write"
}
```

#### 成功响应 (200 OK)

```json
{
  "code": 0,
  "message": "Permission check completed",
  "data": {
    "granted": true,
    "resource": "user",
    "action": "write",
    "permission": "user:write"
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456789"
}
```

## JWT令牌结构

### 访问令牌 (Access Token)

访问令牌包含以下声明 (Claims)：

```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "username": "admin",
  "email": "<EMAIL>",
  "roles": ["admin", "manager"],
  "session_id": "sess_123456789",
  "token_type": "access",
  "system": "BOS",
  "access_level": "manager",
  "station_ids": [1, 2, 3],
  "exp": 1642248000,
  "iat": 1642244400,
  "iss": "bos-core"
}
```

**字段说明**：
- `user_id`: 用户UUID
- `username`: 用户名
- `email`: 邮箱地址
- `roles`: 用户角色列表
- `session_id`: 会话ID
- `token_type`: 令牌类型 (access/refresh)
- `system`: 当前登录的系统 (BOS/EDC)
- `access_level`: 用户在该系统的最高权限级别
- `station_ids`: 用户可访问的站点ID列表
- `exp`: 过期时间 (Unix时间戳)
- `iat`: 签发时间 (Unix时间戳)
- `iss`: 签发者

### 刷新令牌 (Refresh Token)

刷新令牌包含以下声明：

```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "session_id": "sess_123456789",
  "token_type": "refresh",
  "exp": 1642852800,
  "iat": 1642244400,
  "iss": "bos-core"
}
```

## 数据模型

### User (用户)
```typescript
interface User {
  id: string;                    // 用户UUID
  username: string;              // 用户名
  email: string;                 // 邮箱地址
  fullName: string;              // 全名
  phone?: string;                // 电话号码
  status: string;                // 用户状态: active|inactive|locked
  roles: string[];               // 角色列表
  permissions: string[];         // 权限列表
  lastLoginAt: string;           // 最后登录时间 (ISO 8601)
  language: string;              // 语言设置
  sites: UserSiteRole[];         // 用户站点角色
  stations: UserStationInfo[];   // 用户站点详细信息
}
```

### UserSiteRole (用户站点角色)
```typescript
interface UserSiteRole {
  siteId: string;                // 站点ID
  siteName: string;              // 站点名称
  role: string;                  // 在该站点的角色
  isDefault: boolean;            // 是否为默认站点
}
```

### UserStationInfo (用户站点详细信息)
```typescript
interface UserStationInfo {
  stationId: string;             // 站点ID
  roleType: string;              // 角色类型
  isDefault: boolean;            // 是否为默认站点
  hasManagePermission: boolean;  // 是否有管理权限
  station: StationDetail;        // 站点详细信息
}
```

### SystemAccess (系统访问信息)
```typescript
interface SystemAccess {
  system: string;                // 当前系统: BOS|EDC
  accessLevel: string;           // 用户的最高权限级别
  stationIds: number[];          // 可访问的站点ID列表
  stationCount: number;          // 可访问站点数量
}
```

## 错误码参考

### 认证相关错误 (1000-1999)

| 错误码 | 描述 | HTTP状态码 | 说明 |
|--------|------|------------|------|
| 0 | 成功 | 200 | 操作成功 |
| 1001 | 用户名或密码错误 | 401 | 认证失败 |
| 1002 | 账户已锁定 | 403 | 账户被锁定 |
| 1003 | 账户已禁用 | 403 | 账户被禁用 |
| 1004 | 密码已过期 | 403 | 需要修改密码 |
| 1005 | Token已过期 | 401 | 访问令牌过期 |
| 1006 | Token无效 | 401 | 访问令牌格式错误或被篡改 |
| 1007 | 需要验证码 | 400 | 登录需要提供验证码 |
| 1008 | 验证码错误 | 400 | 验证码不正确 |
| 1009 | 登录尝试过多 | 429 | 短时间内登录失败次数过多 |
| 1010 | 未登录 | 401 | 需要先登录 |
| 1011 | 会话已过期 | 401 | 用户会话过期 |
| 1012 | 外部认证失败 | 401 | LDAP/AD认证失败 |

### 用户相关错误 (2000-2999)

| 错误码 | 描述 | HTTP状态码 | 说明 |
|--------|------|------------|------|
| 2001 | 用户不存在 | 404 | 指定用户不存在 |
| 2002 | 用户已存在 | 409 | 用户名或邮箱已被使用 |
| 2003 | 用户信息无效 | 400 | 用户信息格式错误 |

### 权限相关错误 (3000-3999)

| 错误码 | 描述 | HTTP状态码 | 说明 |
|--------|------|------------|------|
| 3001 | 权限不足 | 403 | 用户权限不足 |
| 3002 | 角色不存在 | 404 | 指定角色不存在 |
| 3003 | 权限不存在 | 404 | 指定权限不存在 |

### 系统访问错误 (4000-4999)

| 错误码 | 描述 | HTTP状态码 | 说明 |
|--------|------|------------|------|
| 4001 | 无站点关联 | 403 | 用户在指定系统中无站点关联 |
| 4002 | 权限不足 | 403 | 用户在指定系统中权限不足 |
| 4003 | 未知系统 | 400 | 不支持的系统类型 |
| 4004 | 系统访问被拒绝 | 403 | 系统访问被拒绝 |

### 系统错误 (5000+)

| 错误码 | 描述 | HTTP状态码 | 说明 |
|--------|------|------------|------|
| -1 | 系统繁忙 | 503 | 系统暂时不可用 |
| 5001 | 内部服务器错误 | 500 | 服务器内部错误 |
| 5002 | 数据库连接失败 | 500 | 数据库不可用 |
| 5003 | 外部服务不可用 | 503 | 依赖的外部服务不可用 |

## 使用示例

### 登录示例

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123",
    "system": "BOS"
  }'
```

### 获取用户信息示例

```bash
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 刷新令牌示例

```bash
curl -X POST http://localhost:8080/api/v1/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

### 登出示例

```bash
curl -X POST http://localhost:8080/api/v1/auth/logout \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 修改密码示例

```bash
curl -X PUT http://localhost:8080/api/v1/auth/password \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "OldPassword123",
    "newPassword": "NewPassword456",
    "confirmPassword": "NewPassword456"
  }'
```

### 权限验证示例

```bash
curl -X POST http://localhost:8080/api/v1/auth/verify-permission \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "resource": "user",
    "action": "write"
  }'
```

## JavaScript SDK示例

### 基础认证类

```javascript
class AuthService {
  constructor(baseURL = 'http://localhost:8080/api/v1') {
    this.baseURL = baseURL;
    this.accessToken = null;
    this.refreshToken = null;
  }

  async login(username, password, system = 'BOS') {
    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username,
        password,
        system,
        authType: 'local'
      }),
    });

    const result = await response.json();

    if (result.code === 0) {
      this.accessToken = result.data.accessToken;
      this.refreshToken = result.data.refreshToken;

      // 设置自动刷新
      this.scheduleTokenRefresh(result.data.expiresIn);

      return result.data;
    } else {
      throw new Error(result.message);
    }
  }

  async getCurrentUser() {
    const response = await this.authenticatedRequest('/auth/me');
    return response.data;
  }

  async logout() {
    try {
      await this.authenticatedRequest('/auth/logout', 'POST');
    } finally {
      this.clearTokens();
    }
  }

  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch(`${this.baseURL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refreshToken: this.refreshToken
      }),
    });

    const result = await response.json();

    if (result.code === 0) {
      this.accessToken = result.data.accessToken;
      this.refreshToken = result.data.refreshToken;
      this.scheduleTokenRefresh(result.data.expiresIn);
      return result.data;
    } else {
      this.clearTokens();
      throw new Error(result.message);
    }
  }

  async authenticatedRequest(endpoint, method = 'GET', body = null) {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : null,
    });

    const result = await response.json();

    // 自动处理token过期
    if (result.code === 1005) { // Token expired
      try {
        await this.refreshAccessToken();
        // 重试原请求
        return this.authenticatedRequest(endpoint, method, body);
      } catch (error) {
        // 刷新失败，需要重新登录
        this.clearTokens();
        throw new Error('Session expired, please login again');
      }
    }

    if (result.code !== 0) {
      throw new Error(result.message);
    }

    return result;
  }

  scheduleTokenRefresh(expiresIn) {
    // 在过期前5分钟刷新token
    const refreshTime = (expiresIn - 300) * 1000;
    setTimeout(() => {
      this.refreshAccessToken().catch(console.error);
    }, refreshTime);
  }

  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
  }

  isAuthenticated() {
    return !!this.accessToken;
  }
}

// 使用示例
const auth = new AuthService();

// 登录
try {
  const loginResult = await auth.login('admin', 'Admin123', 'BOS');
  console.log('Login successful:', loginResult.user);
} catch (error) {
  console.error('Login failed:', error.message);
}

// 获取用户信息
try {
  const user = await auth.getCurrentUser();
  console.log('Current user:', user);
} catch (error) {
  console.error('Failed to get user info:', error.message);
}
```

## 测试用例

### 1. 正常登录流程测试

```javascript
describe('Authentication API', () => {
  test('should login successfully with valid credentials', async () => {
    const response = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'Admin123',
        system: 'BOS'
      })
    });

    const result = await response.json();

    expect(response.status).toBe(200);
    expect(result.code).toBe(0);
    expect(result.data.accessToken).toBeDefined();
    expect(result.data.user.username).toBe('admin');
  });

  test('should fail with invalid credentials', async () => {
    const response = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'wrongpassword',
        system: 'BOS'
      })
    });

    const result = await response.json();

    expect(response.status).toBe(401);
    expect(result.code).toBe(1001);
  });
});
```

### 2. Token刷新测试

```javascript
test('should refresh token successfully', async () => {
  // 先登录获取refresh token
  const loginResponse = await login('admin', 'Admin123');
  const refreshToken = loginResponse.data.refreshToken;

  // 刷新token
  const response = await fetch('/api/v1/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken })
  });

  const result = await response.json();

  expect(response.status).toBe(200);
  expect(result.code).toBe(0);
  expect(result.data.accessToken).toBeDefined();
});
```

## 故障排除

### 常见问题

1. **登录失败 - 401错误**
   - 检查用户名和密码是否正确
   - 确认用户账户状态是否为active
   - 验证系统参数是否正确 (BOS/EDC)

2. **Token过期 - 1005错误**
   - 实现自动token刷新机制
   - 检查系统时间是否同步
   - 确认token存储和传输正确

3. **权限不足 - 403错误**
   - 检查用户角色和权限配置
   - 确认用户在目标系统中有相应权限
   - 验证站点关联是否正确

4. **系统访问被拒绝 - 4001错误**
   - 检查用户是否关联了目标系统的站点
   - 确认用户角色在目标系统中有效
   - 联系管理员分配相应权限

### 调试技巧

1. **启用详细日志**
   ```bash
   # 设置环境变量启用调试模式
   export LOG_LEVEL=debug
   export AUTH_DEBUG=true
   ```

2. **检查JWT令牌内容**
   ```javascript
   // 解码JWT令牌查看内容（仅用于调试）
   function decodeJWT(token) {
     const base64Url = token.split('.')[1];
     const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
     const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
       return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
     }).join(''));
     return JSON.parse(jsonPayload);
   }
   ```

## 安全考虑

### 1. 令牌安全

- **访问令牌**: 有效期为1小时，存储在内存中，不应持久化到本地存储
- **刷新令牌**: 有效期为7天，应安全存储，定期轮换
- **令牌传输**: 仅通过HTTPS传输，避免在URL中传递
- **令牌撤销**: 登出时立即撤销所有相关令牌

### 2. 密码策略

- **最小长度**: 8个字符
- **复杂度要求**: 包含大小写字母、数字和特殊字符
- **历史检查**: 不能重复使用最近5次的密码
- **过期策略**: 密码90天过期（可配置）

### 3. 账户安全

- **登录失败限制**: 连续5次失败后需要验证码
- **会话管理**: 支持单点登录和多设备并发
- **异常检测**: 监控异常登录行为
- **审计日志**: 记录所有认证相关操作

### 4. 网络安全

- **HTTPS强制**: 生产环境必须使用HTTPS
- **CORS配置**: 严格配置跨域访问策略
- **Rate Limiting**: API调用频率限制
- **IP白名单**: 支持IP访问控制（可选）

## 最佳实践

### 客户端实现

1. **令牌存储**
   ```javascript
   // ✅ 推荐：存储在内存中
   let accessToken = null;

   // ✅ 可以：存储在sessionStorage
   sessionStorage.setItem('accessToken', token);

   // ❌ 不推荐：存储在localStorage
   // localStorage.setItem('accessToken', token);
   ```

2. **自动刷新令牌**
   ```javascript
   // 在令牌过期前5分钟自动刷新
   const refreshBeforeExpiry = 5 * 60 * 1000; // 5分钟
   setTimeout(refreshToken, expiresIn * 1000 - refreshBeforeExpiry);
   ```

3. **错误处理**
   ```javascript
   // 统一处理认证错误
   if (response.status === 401) {
     // 清除本地令牌
     clearTokens();
     // 重定向到登录页
     window.location.href = '/login';
   }
   ```

### 服务端实现

1. **令牌验证**: 每个请求都验证令牌的有效性和权限
2. **会话管理**: 维护活跃会话列表，支持强制登出
3. **日志记录**: 记录所有认证和授权操作
4. **监控告警**: 监控异常登录和权限访问

## 注意事项

1. **Token管理**: 访问令牌有效期为1小时，刷新令牌有效期为7天
2. **系统区分**: 不同系统(BOS/EDC)的用户权限和可访问站点可能不同
3. **站点权限**: 用户只能访问其被授权的站点数据
4. **密码安全**: 密码必须符合安全策略要求
5. **并发登录**: 系统支持同一用户在多个设备上登录
6. **日志记录**: 所有登录和认证操作都会被记录到审计日志
7. **时区处理**: 所有时间戳使用UTC时间，客户端负责本地化显示
8. **版本兼容**: API向后兼容，新版本不会破坏现有客户端

## 更新历史

- **v1.0.0** (2024-01-15): 初始版本，支持基本登录和用户信息获取
- **v1.1.0** (2024-01-20): 添加站点详细信息和系统访问控制
- **v1.2.0** (2024-01-25): 优化响应数据结构，添加更多用户属性
- **v2.0.0** (2024-07-19): 完整重构API文档
  - 添加完整的接口规范和数据模型
  - 新增JWT令牌结构说明
  - 添加详细的错误码参考
  - 包含安全考虑和最佳实践
  - 提供JavaScript SDK示例
  - 添加测试用例和故障排除指南
  - 支持刷新令牌、登出、密码修改等完整功能
