# BOS 认证API快速参考

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **Content-Type**: `application/json`

## 接口列表

### 1. 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "Admin123",
  "system": "BOS"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJ...",
    "refreshToken": "eyJ...",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "user": { ... },
    "systemAccess": { ... }
  }
}
```

### 2. 获取当前用户信息
```http
GET /auth/me
Authorization: Bearer {token}
```

**响应**:
```json
{
  "code": 0,
  "message": "User information retrieved successfully",
  "data": {
    "id": "uuid",
    "username": "admin",
    "email": "<EMAIL>",
    "fullName": "系统管理员",
    "roles": ["admin"],
    "permissions": ["user:read", "user:write"],
    "stations": [...],
    "systemAccess": {...}
  }
}
```

### 3. 刷新访问令牌
```http
POST /auth/refresh
```

**请求体**:
```json
{
  "refreshToken": "eyJ..."
}
```

**响应**:
```json
{
  "code": 0,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "eyJ...",
    "refreshToken": "eyJ...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  }
}
```

### 4. 用户登出
```http
POST /auth/logout
Authorization: Bearer {token}
```

**响应**:
```json
{
  "code": 0,
  "message": "Logout successful",
  "data": null
}
```

### 5. 修改密码
```http
PUT /auth/password
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "currentPassword": "OldPassword123",
  "newPassword": "NewPassword456",
  "confirmPassword": "NewPassword456"
}
```

### 6. 权限验证
```http
POST /auth/verify-permission
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "resource": "user",
  "action": "write"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "Permission check completed",
  "data": {
    "granted": true,
    "resource": "user",
    "action": "write",
    "permission": "user:write"
  }
}
```

## 常用错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| 1001 | 用户名或密码错误 | 检查登录凭据 |
| 1005 | Token已过期 | 刷新token或重新登录 |
| 1006 | Token无效 | 重新登录 |
| 1010 | 未登录 | 先进行登录 |
| 2001 | 用户不存在 | 检查用户账户 |
| 4001 | 无站点关联 | 联系管理员分配站点权限 |
| 4002 | 权限不足 | 联系管理员分配相应权限 |

## 快速开始

### 1. 登录获取Token
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123",
    "system": "BOS"
  }'
```

### 2. 使用Token访问API
```bash
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. JavaScript示例
```javascript
// 登录
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: 'Admin123',
    system: 'BOS'
  })
});

const { data } = await loginResponse.json();
const accessToken = data.accessToken;

// 获取用户信息
const userResponse = await fetch('/api/v1/auth/me', {
  headers: { 'Authorization': `Bearer ${accessToken}` }
});

const userData = await userResponse.json();
console.log('Current user:', userData.data);
```

## 注意事项

1. **Token安全**: 访问令牌应存储在内存中，避免持久化到本地存储
2. **HTTPS**: 生产环境必须使用HTTPS传输
3. **Token过期**: 访问令牌1小时过期，刷新令牌7天过期
4. **错误处理**: 始终检查响应中的`code`字段判断操作是否成功
5. **系统区分**: BOS和EDC系统的用户权限可能不同
6. **自动刷新**: 建议实现token自动刷新机制

## 相关文档

- [完整API文档](./auth-api-documentation.md)
- [前端集成指南](../frontend-integration-guide.md)
- [安全最佳实践](../security-best-practices.md)

---

**版本**: v2.0.0  
**更新时间**: 2024-07-19  
**维护者**: BOS开发团队
