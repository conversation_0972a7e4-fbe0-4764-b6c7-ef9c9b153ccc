# BOS API 文档

本目录包含BOS系统的API接口文档，为开发者提供完整的接口规范和使用指南。

## 📁 文档结构

### 认证API文档

- **[auth-api-documentation.md](./auth-api-documentation.md)** - 完整的认证API文档
  - 详细的接口规范和参数说明
  - 完整的响应格式和错误码
  - JWT令牌结构说明
  - 安全考虑和最佳实践
  - JavaScript SDK示例
  - 测试用例和故障排除

- **[auth-api-quick-reference.md](./auth-api-quick-reference.md)** - 认证API快速参考
  - 简化的接口列表
  - 常用错误码
  - 快速开始指南
  - 基础使用示例

- **[BOS-Auth-API.postman_collection.json](./BOS-Auth-API.postman_collection.json)** - Postman集合
  - 可导入的API测试集合
  - 包含所有认证接口
  - 自动化测试脚本
  - 环境变量配置

## 🚀 快速开始

### 1. 查看API文档

如果你是第一次使用BOS API，建议按以下顺序阅读：

1. 先阅读 [快速参考](./auth-api-quick-reference.md) 了解基本概念
2. 查看 [完整文档](./auth-api-documentation.md) 了解详细规范
3. 使用 [Postman集合](./BOS-Auth-API.postman_collection.json) 进行接口测试

### 2. 导入Postman集合

1. 打开Postman应用
2. 点击 "Import" 按钮
3. 选择 `BOS-Auth-API.postman_collection.json` 文件
4. 导入后即可开始测试API接口

### 3. 基础使用示例

```javascript
// 1. 登录获取token
const loginResponse = await fetch('http://localhost:8080/api/v1/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: 'Admin123',
    system: 'BOS'
  })
});

const { data } = await loginResponse.json();
const accessToken = data.accessToken;

// 2. 使用token访问API
const userResponse = await fetch('http://localhost:8080/api/v1/auth/me', {
  headers: { 'Authorization': `Bearer ${accessToken}` }
});

const userData = await userResponse.json();
console.log('Current user:', userData.data);
```

## 📋 API概览

### 认证相关接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 用户登录 | POST | `/auth/login` | 用户登录获取访问令牌 |
| 获取用户信息 | GET | `/auth/me` | 获取当前用户详细信息 |
| 刷新令牌 | POST | `/auth/refresh` | 刷新访问令牌 |
| 用户登出 | POST | `/auth/logout` | 用户登出，撤销令牌 |
| 修改密码 | PUT | `/auth/password` | 修改用户密码 |
| 权限验证 | POST | `/auth/verify-permission` | 验证用户权限 |
| 验证令牌 | POST | `/auth/verify` | 验证令牌有效性 |

### 系统支持

- **BOS系统**: 加油站管理系统
- **EDC系统**: 手持设备系统

## 🔧 开发环境

### 本地开发

- **API Base URL**: `http://localhost:8080/api/v1`
- **前端开发服务器**: `http://localhost:3001`
- **测试账户**: `admin` / `Admin123`

### 环境变量

```bash
# API服务地址
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1

# 认证相关配置
NEXT_PUBLIC_AUTH_TIMEOUT=3600000
NEXT_PUBLIC_REFRESH_BEFORE_EXPIRY=300000
```

## 🛡️ 安全注意事项

1. **HTTPS**: 生产环境必须使用HTTPS
2. **Token存储**: 访问令牌存储在内存中，避免持久化
3. **Token过期**: 访问令牌1小时过期，刷新令牌7天过期
4. **权限控制**: 严格验证用户权限和站点访问权限
5. **错误处理**: 正确处理认证错误和权限错误

## 📝 更新日志

### v2.0.0 (2024-07-19)
- 完整重构API文档结构
- 添加详细的接口规范和示例
- 新增Postman测试集合
- 完善安全考虑和最佳实践
- 添加故障排除指南

### v1.2.0 (2024-01-25)
- 优化响应数据结构
- 添加更多用户属性

### v1.1.0 (2024-01-20)
- 添加站点详细信息
- 支持系统访问控制

### v1.0.0 (2024-01-15)
- 初始版本发布
- 基本登录和用户信息获取功能

## 🤝 贡献指南

如果你发现文档中的错误或需要补充内容，请：

1. 创建Issue描述问题
2. 提交Pull Request修复问题
3. 确保文档格式正确
4. 添加必要的示例和说明

## 📞 技术支持

如有技术问题，请联系：

- **开发团队**: BOS开发团队
- **邮箱**: <EMAIL>
- **文档维护**: 前端开发组

---

**最后更新**: 2024-07-19  
**文档版本**: v2.0.0  
**API版本**: v1
