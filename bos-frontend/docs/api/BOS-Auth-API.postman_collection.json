{"info": {"name": "BOS Authentication API", "description": "BOS系统认证API接口集合，包含登录、用户信息获取、token刷新等功能", "version": "2.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api/v1", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}], "item": [{"name": "1. 用户登录", "event": [{"listen": "test", "script": {"exec": ["// 检查响应状态", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "// 解析响应数据", "const responseJson = pm.response.json();", "", "// 检查业务状态码", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});", "", "// 检查必要字段", "pm.test('Response has required fields', function () {", "    pm.expect(responseJson.data).to.have.property('accessToken');", "    pm.expect(responseJson.data).to.have.property('refreshToken');", "    pm.expect(responseJson.data).to.have.property('user');", "});", "", "// 保存token到环境变量", "if (responseJson.code === 0) {", "    pm.collectionVariables.set('accessToken', responseJson.data.accessToken);", "    pm.collectionVariables.set('refreshToken', responseJson.data.refreshToken);", "    console.log('Tokens saved to collection variables');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"Admin123\",\n  \"system\": \"BOS\",\n  \"authType\": \"local\",\n  \"rememberMe\": false\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "用户登录接口，支持用户名或邮箱登录"}}, {"name": "2. 获取当前用户信息", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "const responseJson = pm.response.json();", "", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});", "", "pm.test('User data is present', function () {", "    pm.expect(responseJson.data).to.have.property('id');", "    pm.expect(responseJson.data).to.have.property('username');", "    pm.expect(responseJson.data).to.have.property('roles');", "    pm.expect(responseJson.data).to.have.property('permissions');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}, "description": "获取当前登录用户的详细信息"}}, {"name": "3. 刷新访问令牌", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "const responseJson = pm.response.json();", "", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});", "", "// 更新token", "if (responseJson.code === 0) {", "    pm.collectionVariables.set('accessToken', responseJson.data.accessToken);", "    pm.collectionVariables.set('refreshToken', responseJson.data.refreshToken);", "    console.log('Tokens refreshed and updated');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}, "description": "使用刷新令牌获取新的访问令牌"}}, {"name": "4. 权限验证", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "const responseJson = pm.response.json();", "", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});", "", "pm.test('Permission result is present', function () {", "    pm.expect(responseJson.data).to.have.property('granted');", "    pm.expect(responseJson.data).to.have.property('resource');", "    pm.expect(responseJson.data).to.have.property('action');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resource\": \"user\",\n  \"action\": \"read\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/verify-permission", "host": ["{{baseUrl}}"], "path": ["auth", "verify-permission"]}, "description": "验证用户是否具有特定资源的操作权限"}}, {"name": "5. 修改密码", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "const responseJson = pm.response.json();", "", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"Admin123\",\n  \"newPassword\": \"NewPassword456\",\n  \"confirmPassword\": \"NewPassword456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/password", "host": ["{{baseUrl}}"], "path": ["auth", "password"]}, "description": "修改当前用户的登录密码"}}, {"name": "6. 用户登出", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "const responseJson = pm.response.json();", "", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});", "", "// 清除token", "pm.collectionVariables.set('accessToken', '');", "pm.collectionVariables.set('refreshToken', '');", "console.log('Tokens cleared from collection variables');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}, "description": "用户登出，使当前访问令牌失效"}}, {"name": "7. 验证令牌", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "const responseJson = pm.response.json();", "", "pm.test('Business code is 0', function () {", "    pm.expect(responseJson.code).to.eql(0);", "});", "", "pm.test('Token validation result is present', function () {", "    pm.expect(responseJson.data).to.have.property('valid');", "    pm.expect(responseJson.data).to.have.property('userId');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"{{accessToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/verify", "host": ["{{baseUrl}}"], "path": ["auth", "verify"]}, "description": "验证访问令牌的有效性"}}]}