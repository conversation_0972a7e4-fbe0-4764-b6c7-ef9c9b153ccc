# BOS Frontend JWT 认证机制 - 最终版本

## 概述

BOS Frontend项目现在完全使用JWT认证机制，已移除所有Demo模式和模拟数据，全部使用真实的API接口。

## JWT认证流程

### 1. 登录流程
```
用户输入凭据 → 调用 /auth/login API → 获取JWT tokens → 保存到localStorage和cookie → 设置认证状态 → 重定向到dashboard
```

### 2. Token管理
- **Access Token**: 用于API请求认证，存储在localStorage和cookie中
- **Refresh Token**: 用于刷新access token，存储在localStorage中
- **自动刷新**: 当token即将过期时（30分钟内）自动刷新

### 3. 认证检查
- 页面加载时检查localStorage中的token
- 使用jwtDecode验证token有效性
- 自动处理token过期和刷新

## 核心组件

### AuthContext (`context/auth-context.tsx`)

**主要功能**:
- JWT token管理
- 用户认证状态管理
- 自动token刷新
- 路由重定向控制

**关键方法**:
```typescript
// 登录
const login = async (username: string, password: string) => {
  const response = await apiLogin({ username, password, system: 'BOS' });
  // 保存tokens到localStorage和cookie
  // 设置用户状态
  // 重定向到dashboard
}

// 初始化认证
const initializeAuth = async () => {
  const accessToken = localStorage.getItem('accessToken');
  // 检查token有效性
  // 自动刷新过期token
  // 加载用户信息
}

// 刷新认证
const refreshAuth = async (): Promise<boolean> => {
  const refreshToken = localStorage.getItem('refreshToken');
  const tokenResponse = await apiRefreshToken(refreshToken);
  // 保存新的tokens
  // 更新认证状态
}
```

### JWT服务 (`services/auth-service.ts`)

**API接口**:
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出
- `POST /auth/refresh` - 刷新token
- `GET /auth/me` - 获取当前用户信息

### 中间件 (`middleware.ts`)

**功能**:
- 检查cookie中的access token
- 保护需要认证的路由
- 自动重定向未认证用户到登录页

## Token存储策略

### localStorage
```typescript
// 存储的数据
{
  accessToken: string,
  refreshToken: string,
  tokenType: string,
  expiresIn: number,
  tokenExpiresAt: string,
  systemAccess: object
}
```

### Cookie
```typescript
// 仅存储access token供middleware使用
document.cookie = `accessToken=${token}; max-age=${expiresIn}; SameSite=Strict`;
```

## 安全特性

### 1. Token验证
- 使用jwtDecode验证token格式和过期时间
- 服务端验证token签名和有效性
- 自动处理过期token

### 2. 安全存储
- localStorage用于客户端状态管理
- HttpOnly cookie用于服务端路由保护
- SameSite=Strict防止CSRF攻击

### 3. 自动刷新
- 检测token即将过期（30分钟内）
- 使用refresh token获取新的access token
- 刷新失败时自动登出

## 错误处理

### 1. 认证错误
```typescript
// 401 Unauthorized
if (error.status === 401) {
  // 清除认证状态
  // 重定向到登录页
}
```

### 2. 网络错误
```typescript
// 网络连接失败
if (error instanceof TypeError && error.message.includes('fetch')) {
  // 显示网络错误提示
  // 提供重试选项
}
```

### 3. Token刷新失败
```typescript
// Refresh token无效
if (refreshError.status === 401) {
  // 清除所有认证信息
  // 重定向到登录页
}
```

## 路由保护

### 1. 中间件保护
```typescript
// 检查需要认证的路径
const protectedPaths = ['/dashboard', '/users', '/settings'];
if (protectedPaths.some(path => pathname.startsWith(path)) && !token) {
  return NextResponse.redirect('/login');
}
```

### 2. 组件级保护
```typescript
// 在组件中检查认证状态
const { isAuthenticated, isLoading } = useAuth();

if (isLoading) return <Loading />;
if (!isAuthenticated) return <LoginRequired />;
```

## API请求认证

### 1. 自动添加Authorization头
```typescript
const createHeaders = (): HeadersInit => {
  const token = localStorage.getItem('accessToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};
```

### 2. 错误处理
```typescript
// API请求失败时的处理
if (response.status === 401) {
  // Token无效，尝试刷新或登出
}
```

## 用户体验优化

### 1. 重定向逻辑
- 登录成功后重定向到原始请求页面
- 使用setTimeout避免重定向冲突
- 保持用户的导航状态

### 2. 加载状态
- 认证初始化时显示加载状态
- API请求时显示加载指示器
- 优雅的错误提示

### 3. 自动重试
- 网络错误时提供重试选项
- Token刷新失败时自动重试
- 用户友好的错误信息

## 配置选项

### 环境变量
```bash
# API基础URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1

# JWT配置（后端）
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d
```

### Token过期时间
- Access Token: 1小时
- Refresh Token: 7天
- 自动刷新阈值: 30分钟

## 最佳实践

### 1. 安全性
- 定期轮换JWT密钥
- 使用HTTPS传输
- 实施适当的CORS策略
- 监控异常登录活动

### 2. 性能
- 缓存用户信息
- 批量API请求
- 优化token刷新频率

### 3. 用户体验
- 提供清晰的错误信息
- 实现无缝的认证流程
- 支持记住登录状态

## 故障排除

### 常见问题

1. **Token过期**
   - 检查系统时间是否正确
   - 确认token过期时间配置
   - 验证refresh token是否有效

2. **认证失败**
   - 检查API端点是否可用
   - 验证用户凭据
   - 确认JWT密钥配置

3. **重定向循环**
   - 检查middleware配置
   - 验证路由保护逻辑
   - 确认认证状态更新

### 调试工具

1. **浏览器开发者工具**
   - Application > Local Storage 查看token
   - Network 查看API请求
   - Console 查看错误日志

2. **JWT调试**
   - 使用jwt.io解码token
   - 检查token声明和过期时间
   - 验证签名有效性

## 相关文件

- `context/auth-context.tsx` - 认证上下文
- `services/auth-service.ts` - 认证服务
- `middleware.ts` - 路由中间件
- `api/dashboard.ts` - API客户端
- `types/auth.ts` - 认证类型定义

---

**总结**: BOS Frontend现在使用完整的JWT认证机制，提供安全、可靠的用户认证和授权功能。所有模拟数据已移除，系统完全依赖真实的API接口。
