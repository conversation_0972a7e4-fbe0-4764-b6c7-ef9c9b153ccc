# HOS系统多站点管理功能文档

## 概述

本文档描述了为HOS（总部管理系统）实现的多站点管理功能，该功能允许总部用户管理多个站点并根据权限范围进行数据筛选。

## 功能特性

### 1. 权限控制
- **全局权限**：`scopeType = "global"` - 可以管理所有站点
- **站点权限**：`scopeType = "station"` - 只能管理`scopeIds`中指定的站点
- **权限验证**：基于登录响应中的`systemAccess`信息

### 2. 站点选择器
- **位置**：左侧边栏下方的"Site Management"区域
- **选项**：
  - "All Sites" - 查看所有有权限的站点数据
  - 特定站点 - 查看单个站点的数据
- **状态保持**：选择状态保存到localStorage，页面刷新后保持

### 3. 个人资料页面
- **HOS系统**：显示集团/总部相关信息
  - 组织信息
  - 管理权限范围
  - 当前站点选择状态
  - 系统权限列表
- **BOS/EDC系统**：保持原有的单站点信息显示

### 4. 全局站点筛选
- **自动同步**：所有页面的站点筛选器与左侧边栏选择同步
- **API集成**：自动在API调用中添加站点筛选参数
- **数据过滤**：根据选择的站点过滤显示数据

## 技术实现

### 1. 站点上下文扩展

```typescript
// 新增的HOS站点管理状态
interface SiteContextType {
  // 原有属性...
  selectedHosSites: number[] | 'all';
  hosSiteOptions: HosSiteOption[];
  setSelectedHosSites: (siteIds: number[] | 'all') => void;
  getFilteredSiteIds: () => number[] | null;
}
```

### 2. HOS站点选择器组件

```typescript
import { HosSiteSelector } from '@/components/hos/hos-site-selector';

// 使用示例
<HosSiteSelector 
  variant="select"
  showDescription={true}
/>
```

### 3. 通用站点筛选器

```typescript
import { SiteFilter } from '@/components/common/site-filter';

// 使用示例
<SiteFilter 
  onSiteChange={(siteIds) => {
    // 处理站点选择变化
    console.log('Selected site IDs:', siteIds);
  }}
/>
```

### 4. API调用辅助工具

```typescript
import { 
  buildApiParams, 
  fetchWithSiteFilter,
  createSiteFilteredApiConfig 
} from '@/utils/api-helpers';

// 构建带站点筛选的API参数
const params = buildApiParams(
  { date_from: '2025-01-01', date_to: '2025-01-31' },
  'HOS',
  [1, 2, 3] // 选择的站点ID
);

// 执行带站点筛选的API请求
const response = await fetchWithSiteFilter(
  '/api/v1/reports/sales',
  'GET',
  'HOS',
  [1, 2, 3],
  { date_from: '2025-01-01' }
);
```

## 使用指南

### 1. 在页面中使用站点筛选

```typescript
"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/auth-context';
import { useSite } from '@/context/site-context';
import { SiteFilter } from '@/components/common/site-filter';

export default function MyPage() {
  const { currentSystemType } = useAuth();
  const { getFilteredSiteIds } = useSite();
  const [data, setData] = useState([]);

  // 监听站点选择变化
  const handleSiteChange = (siteIds: number[] | null) => {
    // 重新加载数据
    loadData(siteIds);
  };

  const loadData = async (siteIds: number[] | null) => {
    const params = buildApiParams(
      { /* 其他参数 */ },
      currentSystemType,
      siteIds
    );
    
    // 调用API加载数据
    const response = await fetch(`/api/v1/data${buildQueryString(params)}`);
    const result = await response.json();
    setData(result.data);
  };

  return (
    <div>
      <SiteFilter onSiteChange={handleSiteChange} />
      {/* 显示数据 */}
    </div>
  );
}
```

### 2. 在API服务中使用

```typescript
// services/my-service.ts
import { fetchWithSiteFilter } from '@/utils/api-helpers';

export async function getReportData(
  systemType: SystemType,
  selectedSiteIds: number[] | null,
  dateRange: { from: string; to: string }
) {
  const response = await fetchWithSiteFilter(
    '/api/v1/reports/data',
    'GET',
    systemType,
    selectedSiteIds,
    {
      date_from: dateRange.from,
      date_to: dateRange.to
    }
  );

  if (!response.ok) {
    throw new Error('Failed to fetch report data');
  }

  return response.json();
}
```

### 3. 检查当前站点选择

```typescript
import { useSite } from '@/context/site-context';

function MyComponent() {
  const { selectedHosSites, getFilteredSiteIds } = useSite();

  // 获取当前选择的站点ID列表
  const currentSiteIds = getFilteredSiteIds();
  
  if (currentSiteIds === null) {
    console.log('选择了所有站点');
  } else {
    console.log('选择的站点:', currentSiteIds);
  }
}
```

## API参数格式

### HOS系统多站点参数
```
GET /api/v1/reports/sales?site_ids=1,2,3&date_from=2025-01-01&date_to=2025-01-31
```

### BOS/EDC系统单站点参数
```
GET /api/v1/reports/sales?station_id=1&date_from=2025-01-01&date_to=2025-01-31
```

## 状态管理

### localStorage存储
- `hos_selected_sites`: HOS系统的站点选择状态
- 格式：`"all"` 或 `[1,2,3]`（JSON字符串）

### 状态同步
- 左侧边栏站点选择器
- 页面中的站点筛选器
- API调用参数
- 个人资料页面显示

## 注意事项

1. **系统兼容性**：功能只在HOS系统中启用，BOS和EDC保持原有行为
2. **权限验证**：确保用户只能选择有权限的站点
3. **数据一致性**：站点切换时自动更新相关数据
4. **性能考虑**：避免频繁的API调用，使用防抖机制
5. **错误处理**：处理站点权限不足或网络错误的情况

## 扩展性

该实现支持未来扩展：
- 多选站点功能
- 站点分组管理
- 更复杂的权限控制
- 实时数据同步
- 站点性能监控
