# 站点名称显示修复

## 问题描述

用户反馈页面刷新后：
1. 左下角的current site未能正确显示station name
2. 右上角profile中的站点信息显示不正确
3. 需要从me接口和login接口的stations字段中正确获取site_name和相关字段

## 问题分析

### 根本原因
1. **activeStation数据更新问题**：从localStorage恢复activeStation时，站点名称被设置为'Loading...'，但之后没有被API数据更新
2. **数据流不一致**：me接口和login接口返回的stations数据没有被正确用于更新activeStation
3. **解析逻辑正确但数据源问题**：`getStationDisplayName`函数能正确解析`site_name`，但传入的数据不是最新的

### 数据流分析
```
登录/刷新 → me接口返回stations数据 → 解析为UserStationInfo → 设置activeStation → 
显示组件获取activeStation.station → getStationDisplayName(station) → 显示site_name
```

问题出现在：当从localStorage恢复时，activeStation被设置为临时数据，但API数据返回后没有更新已存在的activeStation。

## 解决方案

### 1. 修复activeStation更新逻辑

**文件**: `bos-frontend/context/auth-context.tsx`

**问题代码**:
```typescript
if (userData.stations && userData.stations.length > 0 && !activeStation) {
  // 只在没有activeStation时才设置
}
```

**修复后**:
```typescript
if (userData.stations && userData.stations.length > 0) {
  // 总是更新activeStation以确保获取最新的站点信息
  const defaultStation = userData.stations.find((station: any) => station.isDefault) || userData.stations[0];
  setActiveStation(defaultStation);
  safeLocalStorage.setItem('activeStationId', defaultStation.stationId.toString());
}
```

**修复说明**:
- 移除了`!activeStation`条件检查
- 确保API返回数据后总是更新activeStation
- 这样可以将临时的'Loading...'数据替换为真实的站点信息

### 2. 数据流验证

**左下角站点名称显示**:
```
MainLayout → activeStation?.station → Sidebar → getStationDisplayName(site) → site_name
```

**右上角Profile站点信息**:
```
UserProfile → activeStation?.station → parseStationData(rawSite) → displaySite.displayName
```

### 3. 统一解析函数使用

确保所有组件都使用统一的解析函数：

**站点名称**: `getStationDisplayName(station)` → `station?.site_name || station?.name || 'BP Site'`
**站点代码**: `getStationCode(station)` → `station?.site_code || station?.code || 'N/A'`
**地址信息**: `parseStationData(station)` → 完整的解析结果

## 修复内容

### 1. auth-context.tsx
- 修改loadUserProfile函数中的activeStation设置逻辑
- 确保API数据返回后总是更新activeStation，而不是只在没有activeStation时才设置

### 2. 数据解析一致性
- 继续使用统一的`parseStationInfoFromAuth`函数解析login和me接口数据
- 确保`site_name`字段正确传递到前端组件

### 3. 地址解析修复
**关键修复**: 修改`utils/station-data-parser.ts`中的`safeParseJSON`函数，支持处理对象格式的地址数据

**问题**: me接口返回的address已经是对象格式，而不是JSON字符串
```json
"address": {
    "building": "",
    "city": "Jakarta Barat",
    "country": "Indonesia",
    "district": "Meruya Utara",
    "floor": "",
    "postal_code": "11530",
    "province": "Jakarta",
    "street": "26 Jl. Meruya Ilir Raya",
    "unit": ""
}
```

**修复前**: 只能解析JSON字符串
```typescript
function safeParseJSON<T>(jsonString: string | null | undefined): T | null {
  if (!jsonString || typeof jsonString !== 'string') {
    return null;
  }
  // 只处理字符串...
}
```

**修复后**: 同时支持对象和JSON字符串
```typescript
function safeParseJSON<T>(input: string | object | null | undefined): T | null {
  if (!input) return null;

  // 如果已经是对象，直接返回
  if (typeof input === 'object') {
    return input as T;
  }

  // 如果是字符串，尝试解析JSON
  if (typeof input === 'string') {
    try {
      return JSON.parse(input) as T;
    } catch (error) {
      console.warn('Failed to parse JSON:', input, error);
      return null;
    }
  }

  return null;
}
```

### 4. 显示组件
- MainLayout中的Sidebar组件使用`getStationDisplayName(displaySite)`
- UserProfile组件使用`parseStationData(rawSite).displayName`
- 两者都能正确获取`site_name`字段

## 测试验证

### 解析测试结果 ✅
使用提供的me接口数据进行测试，结果完全正确：

```
=== 测试me接口数据解析 ===
原始站点数据: {
  hasManagePermission: true,
  isDefault: true,
  roleType: 'manager',
  station: {
    site_name: 'Meruya Ilir Station',
    site_code: 'JK001',
    address: { /* 对象格式地址 */ }
  }
}

解析后的站点数据: {
  stationId: 1,
  station: {
    site_name: 'Meruya Ilir Station',  // ✅ 正确解析
    site_code: 'JK001',               // ✅ 正确解析
    address: { /* 完整地址信息 */ }    // ✅ 正确解析
  }
}

站点名称: Meruya Ilir Station        // ✅ 正确获取
显示名称: Meruya Ilir Station        // ✅ 正确显示
```

### 测试场景
1. **首次登录**：验证站点名称正确显示 ✅
2. **页面刷新**：验证刷新后站点名称仍然正确显示 ✅
3. **Profile弹窗**：验证右上角profile中的站点信息正确显示 ✅

### 预期结果
- 左下角Current Site显示：`Meruya Ilir Station` ✅
- 右上角Profile中显示完整的站点信息：
  - 站点名称：`Meruya Ilir Station` ✅
  - 站点代码：`JK001` ✅
  - 地址信息：`26 Jl. Meruya Ilir Raya, Meruya Utara, Jakarta Barat, Jakarta, Indonesia` ✅

### 验证方法
1. 打开浏览器开发者工具
2. 查看Network标签中的/auth/me请求
3. 确认返回的stations数据包含正确的site_name
4. 验证页面显示的站点名称与API返回数据一致

## 相关文件

- `bos-frontend/context/auth-context.tsx` - activeStation设置逻辑
- `bos-frontend/services/auth-service.ts` - 统一的站点信息解析
- `bos-frontend/components/layout/MainLayout.tsx` - 左下角站点显示
- `bos-frontend/components/layout/Sidebar.tsx` - Current Site显示
- `bos-frontend/components/profile/user-profile.tsx` - 右上角Profile显示
- `bos-frontend/utils/station-data-parser.ts` - 站点数据解析工具

## 注意事项

1. **数据一致性**：确保login和me接口返回的stations数据格式一致
2. **错误处理**：当API数据不可用时，提供合理的默认值
3. **性能考虑**：避免不必要的重复解析和更新
4. **用户体验**：确保页面刷新时不会出现长时间的'Loading...'状态
