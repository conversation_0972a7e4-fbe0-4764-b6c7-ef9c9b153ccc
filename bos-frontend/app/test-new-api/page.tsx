"use client";

import { useState } from 'react';
import { loginService, getCurrentUserService } from '@/services/auth-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function TestNewApiPage() {
  const [credentials, setCredentials] = useState({
    username: 'admin',
    password: 'Admin123',
    system: 'BOS'
  });
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string>('');
  const [token, setToken] = useState<string>('');

  const testLogin = async () => {
    setLoading('login');
    try {
      const result = await loginService(credentials);
      setResults(prev => ({ ...prev, login: result }));
      setToken(result.data.accessToken);
      console.log('登录结果:', result);
    } catch (error) {
      console.error('登录错误:', error);
      setResults(prev => ({ ...prev, login: { error: error.message } }));
    } finally {
      setLoading('');
    }
  };

  const testGetCurrentUser = async () => {
    if (!token) {
      alert('请先登录获取token');
      return;
    }
    
    setLoading('getCurrentUser');
    try {
      const result = await getCurrentUserService(token);
      setResults(prev => ({ ...prev, getCurrentUser: result }));
      console.log('获取用户信息结果:', result);
    } catch (error) {
      console.error('获取用户信息错误:', error);
      setResults(prev => ({ ...prev, getCurrentUser: { error: error.message } }));
    } finally {
      setLoading('');
    }
  };

  const clearResults = () => {
    setResults({});
    setToken('');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">新API对接测试</h1>
      
      {/* 登录表单 */}
      <Card>
        <CardHeader>
          <CardTitle>登录测试</CardTitle>
          <CardDescription>测试新的登录API对接</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                value={credentials.username}
                onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="system">系统</Label>
              <Input
                id="system"
                value={credentials.system}
                onChange={(e) => setCredentials(prev => ({ ...prev, system: e.target.value }))}
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button 
              onClick={testLogin} 
              disabled={loading === 'login'}
            >
              {loading === 'login' ? '登录中...' : '测试登录'}
            </Button>
            <Button 
              onClick={testGetCurrentUser} 
              disabled={loading === 'getCurrentUser' || !token}
              variant="outline"
            >
              {loading === 'getCurrentUser' ? '获取中...' : '测试获取用户信息'}
            </Button>
            <Button onClick={clearResults} variant="outline">
              清除结果
            </Button>
          </div>
          {token && (
            <div className="mt-4 p-2 bg-green-100 rounded">
              <div className="text-sm font-medium">Token已获取:</div>
              <div className="text-xs text-gray-600 break-all">{token.substring(0, 50)}...</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 结果显示 */}
      <div className="space-y-4">
        {Object.entries(results).map(([key, value]) => (
          <Card key={key}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {key}
                {key === 'login' && value?.data?.user?.stations && (
                  <span className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                    {value.data.user.stations.length} 个站点
                  </span>
                )}
                {key === 'getCurrentUser' && value?.data?.user?.stations && (
                  <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {value.data.user.stations.length} 个站点
                  </span>
                )}
              </CardTitle>
              <CardDescription>API调用结果</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 特殊显示站点信息 */}
              {(key === 'login' || key === 'getCurrentUser') && value?.data?.user?.stations && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">站点信息摘要:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {value.data.user.stations.map((station: any, index: number) => (
                      <div key={index} className="p-2 bg-gray-50 rounded text-sm">
                        <div className="font-medium">{station.station.site_name}</div>
                        <div className="text-gray-600">代码: {station.station.site_code}</div>
                        <div className="text-gray-600">ID: {station.stationId}</div>
                        <div className="text-gray-600">
                          地址: {typeof station.station.address === 'object' 
                            ? `${station.station.address.city || ''}, ${station.station.address.street || ''}` 
                            : station.station.address || 'N/A'}
                        </div>
                        {station.isDefault && (
                          <span className="text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded">默认</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 完整JSON数据 */}
              <details className="cursor-pointer">
                <summary className="font-medium mb-2">完整响应数据 (点击展开)</summary>
                <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-xs max-h-96">
                  {JSON.stringify(value, null, 2)}
                </pre>
              </details>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button onClick={() => window.location.href = '/dashboard'}>
              访问Dashboard
            </Button>
            <Button onClick={() => window.location.href = '/debug-auth'} variant="outline">
              认证调试页面
            </Button>
            <Button onClick={() => window.location.href = '/test-api'} variant="outline">
              旧API测试
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
