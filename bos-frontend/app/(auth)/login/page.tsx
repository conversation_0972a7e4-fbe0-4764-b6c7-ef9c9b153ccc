"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/components/ui/use-toast";
import { SystemTypeSelector } from "@/components/auth/system-type-selector";

export default function LoginPage() {
  const [employeeNoOrEmail, setEmployeeNoOrEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const { toast } = useToast();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 防止重复提交
    if (isLoading) {
      return;
    }
    
    if (!employeeNoOrEmail || !password) {
      return toast({
        title: "Error",
        description: "Please enter both employee No./email and password",
        variant: "destructive",
      });
    }
    
    try {
      setIsLoading(true);
      await login(employeeNoOrEmail, password);
      // Redirect will be handled in the auth context
    } catch (error) {
      let errorMessage = "Invalid employee No./email or password";

      if (error instanceof Error) {
        // 根据 BOS Core API v2 文档中的错误信息，提供更友好的错误提示
        const message = error.message.toLowerCase();

        if (message.includes("invalid username or password") || message.includes("invalid credentials")) {
          errorMessage = "Invalid username/email or password. Please check your credentials.";
        } else if (message.includes("insufficient permissions for bos system")) {
          errorMessage = "Access denied: You don't have permission to access BOS system. Only managers, assistant managers, and supervisors can access BOS. Please contact administrator or use EDC system.";
        } else if (message.includes("user has no station associations")) {
          errorMessage = "Access denied: Your account is not associated with any stations. Please contact administrator.";
        } else if (message.includes("system parameter is required")) {
          errorMessage = "System configuration error. Please contact administrator.";
        } else if (message.includes("account is locked")) {
          errorMessage = "Your account has been locked. Please contact administrator.";
        } else if (message.includes("user account is disabled")) {
          errorMessage = "Your account has been disabled. Please contact administrator.";
        } else if (message.includes("captcha")) {
          errorMessage = "Captcha verification required. Please refresh and try again.";
        } else if (message.includes("network") || message.includes("fetch")) {
          errorMessage = "Network error. Please check your connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-green-600 via-green-500 to-green-700">
      {/* 左侧品牌区域 */}
      <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-20 lg:py-24">
        <div className="mx-auto max-w-xl">
          <h1 className="text-4xl font-bold text-white mb-6">
            Welcome to BP-AKR BOS
          </h1>
          <p className="text-xl text-green-100 leading-relaxed">
            Your comprehensive fuel retail management system. Access all your business operations from one powerful platform.
          </p>
          <div className="mt-8 grid grid-cols-2 gap-4 text-green-100">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-200 rounded-full"></div>
              <span className="text-sm">Real-time Analytics</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-200 rounded-full"></div>
              <span className="text-sm">Inventory Management</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-200 rounded-full"></div>
              <span className="text-sm">Employee Management</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-200 rounded-full"></div>
              <span className="text-sm">Sales Reporting</span>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧登录区域 */}
      <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:px-20 xl:px-24 bg-white lg:rounded-l-3xl lg:shadow-2xl">
        <div className="mx-auto w-full max-w-sm lg:max-w-md">
          {/* Logo */}
          <div className="flex justify-center mb-10">
            <Image
              src="/images/bp_akr_logo.png"
              alt="BP AKR Fuels Retail"
              width={240}
              height={80}
              className="h-20 w-auto object-contain"
              priority
            />
          </div>

          <div className="text-center lg:text-left">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">
              Employee Login
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Enter your credentials to access the system
            </p>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-5">
              <div>
                <Label htmlFor="employee-no-email" className="block text-sm font-medium text-gray-700">
                  Employee No. / Email Address
                </Label>
                <div className="mt-1">
                  <Input
                    id="employee-no-email"
                    placeholder="Enter your employee No. or email address"
                    value={employeeNoOrEmail}
                    onChange={(e) => setEmployeeNoOrEmail(e.target.value)}
                    className="block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isLoading}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </Label>
                <div className="mt-1">
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isLoading}
                    required
                  />
                </div>
              </div>

              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">
                  System Type
                </Label>
                <SystemTypeSelector
                  variant="select"
                  showDescription={true}
                  className="w-full"
                />
              </div>
            </div>

            <div>
              <Button 
                type="submit" 
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-green-600 transition-colors duration-200" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Logging in...
                  </>
                ) : (
                  "Sign in to your account"
                )}
              </Button>
            </div>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              © 2025 BP AKR Fuels Retail. All rights reserved.
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Powered by WeCar Technology
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 