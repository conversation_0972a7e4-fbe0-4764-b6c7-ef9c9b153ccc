'use client';

import { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { useToast } from "@/components/ui/use-toast";
import { CreditCard, TrendingUp, Fuel, RefreshCw, Calendar } from "lucide-react";
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart, Pie, Cell } from 'recharts';
import { useAuth } from "@/context/auth-context";
import { useSite } from "@/context/site-context";
import { buildApiParams } from "@/utils/api-helpers";
import { useTranslation } from "@/context/i18n-context";
import { 
  getDashboardSummary, 
  getSalesTrend, 
  handleApiError,
  validateDashboardData,
  validateSalesTrendData
} from "@/api/dashboard";
import { 
  DashboardSummary, 
  SalesTrendData, 
  FuelSalesMix,
  FuelSalesMixDetail 
} from "@/types/dashboard-api";

// BP品牌颜色配置
const BP_COLORS = {
  primary: '#00A651',      // BP绿色
  secondary: '#FFE500',    // BP黄色
  accent: '#005A2B',       // 深绿色
  chart: ['#00A651', '#FFE500', '#005A2B', '#7ED321', '#F5A623', '#D0021B']
};

// 格式化工具函数
const formatters = {
  currency: (value: number) => `Rp ${value.toLocaleString('id-ID')}`,
  currencyShort: (value: number) => {
    if (value >= 1000000000) return `Rp ${(value / 1000000000).toFixed(1)}B`;
    if (value >= 1000000) return `Rp ${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `Rp ${(value / 1000).toFixed(1)}K`;
    return `Rp ${value.toLocaleString('id-ID')}`;
  },
  number: (value: number) => value.toLocaleString('id-ID'),
  percentage: (value: number) => `${value.toFixed(1)}%`,
  volume: (value: number) => `${value.toFixed(3)} L`
};

export default function DashboardPage() {
  const { user, activeStation, isAuthenticated, isLoading: authLoading, currentSystemType } = useAuth();
  const { currentSite, isLoading: siteLoading, getFilteredSiteIds } = useSite();
  const { t } = useTranslation();
  const { toast } = useToast();

  console.log('Dashboard页面状态:', {
    isAuthenticated,
    authLoading,
    siteLoading,
    currentSite: currentSite?.id,
    activeStation: activeStation?.stationId
  });
  
  // 状态管理 - 初始化为undefined，让后端自动选择最近有数据的日期
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 数据状态
  const [dashboardData, setDashboardData] = useState<DashboardSummary | null>(null);
  const [salesTrendData, setSalesTrendData] = useState<SalesTrendData[]>([]);
  const [fuelSalesMixData, setFuelSalesMixData] = useState<FuelSalesMix[]>([]);
  
  // 销售趋势图表视图切换状态
  const [chartView, setChartView] = useState<'revenue' | 'transactions' | 'volume'>('revenue');

  // 图表视图配置
  const chartConfigs = {
    revenue: {
      title: 'Revenue Trend (Last 7 Days)',
      description: 'Daily fuel sales revenue',
      dataKey: 'revenue',
      color: BP_COLORS.primary,
      formatValue: (value: number) => formatters.currency(value),
      formatAxisValue: (value: number) => formatters.currencyShort(value),
      unit: ''
    },
    transactions: {
      title: 'Transactions Trend (Last 7 Days)', 
      description: 'Daily transaction count',
      dataKey: 'transactions',
      color: BP_COLORS.secondary,
      formatValue: (value: number) => formatters.number(value),
      formatAxisValue: (value: number) => formatters.number(value),
      unit: 'transactions'
    },
    volume: {
      title: 'Volume Trend (Last 7 Days)',
      description: 'Daily fuel dispensed volume',
      dataKey: 'volume', 
      color: BP_COLORS.accent,
      formatValue: (value: number) => formatters.volume(value),
      formatAxisValue: (value: number) => value.toFixed(1) + 'L',
      unit: ''
    }
  };

  const currentConfig = chartConfigs[chartView];

  /**
   * 获取Dashboard数据
   */
  const fetchDashboardData = useCallback(async (date?: Date) => {
    // 优先使用activeStation，如果没有则使用currentSite
    const stationId = activeStation?.stationId || currentSite?.id;

    // HOS系统优化：不需要stationId检查
    if (currentSystemType !== 'HOS' && !stationId) {
      console.log('BOS/EDC系统：缺少stationId，跳过数据加载');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      // 日期参数处理逻辑 - 始终传递用户选择的日期
      let dateStr: string | undefined = undefined;
      if (date) {
        // 将用户选择的日期转换为YYYY-MM-DD格式（使用本地时间，避免时区问题）
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        dateStr = `${year}-${month}-${day}`;
      }
      
      // 构建API参数 - HOS系统优化：不传递站点限制参数
      const baseParams = {
        ...(dateStr && { date: dateStr })
      };

      // HOS系统：不传递站点参数，获取全集团数据
      // BOS/EDC系统：传递当前站点ID
      const summaryParams = currentSystemType === 'HOS'
        ? baseParams
        : { ...baseParams, station_id: stationId };

      const trendParams = currentSystemType === 'HOS'
        ? { ...baseParams, days: 7 }
        : { ...baseParams, station_id: stationId, days: 7 };

      console.log('Dashboard API调用参数:', {
        systemType: currentSystemType,
        summaryParams,
        trendParams
      });

      // 并行获取所有数据
      const [summaryResponse, trendResponse] = await Promise.all([
        getDashboardSummary(summaryParams),
        getSalesTrend(trendParams)
      ]);
      
      // 验证和设置数据
      if (validateDashboardData(summaryResponse.data)) {
        setDashboardData(summaryResponse.data);
        // 直接从dashboard summary数据中获取fuel_sales_mix
        setFuelSalesMixData(summaryResponse.data.fuel_sales_mix || []);
      } else {
        throw new Error('Invalid dashboard summary data format');
      }
      
      if (validateSalesTrendData(trendResponse.data)) {
        setSalesTrendData(trendResponse.data.trend_data);
      } else {
        throw new Error('Invalid sales trend data format');
      }
      
      // 数据更新成功时不显示提醒，避免干扰用户体验
      
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      
      toast({
        title: "Failed to Load Data",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentSite?.id, currentSystemType, activeStation?.stationId, toast]);

  /**
   * 处理日期变更
   */
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      const today = new Date();
      today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
      
      // 验证日期不能大于今天
      if (date > today) {
        toast({
          title: "Invalid Date",
          description: "Selected date cannot be greater than today",
          variant: "destructive",
        });
        return;
      }
    }
    
    setSelectedDate(date);
    // 无论是否有选择日期，都传递给fetchDashboardData
    // 如果date为undefined，后端会自动选择最近有数据的日期
    fetchDashboardData(date);
  };

  /**
   * 刷新数据
   */
  const handleRefresh = () => {
    fetchDashboardData(selectedDate);
  };

  // 初始化数据加载 - 优化防止重复调用
  useEffect(() => {
    console.log('Dashboard useEffect 触发，状态:', {
      isAuthenticated,
      authLoading,
      siteLoading,
      currentSiteId: currentSite?.id,
      activeStationId: activeStation?.stationId
    });

    // 确保认证完成，并根据系统类型判断是否可以加载数据
    const stationId = activeStation?.stationId || currentSite?.id;

    // HOS系统：只需要认证完成即可，不需要站点ID和站点加载状态
    // BOS/EDC系统：需要认证完成且有站点ID
    const canLoadData = isAuthenticated && !authLoading &&
      (currentSystemType === 'HOS' || (!siteLoading && stationId));

    if (canLoadData) {
      console.log('所有条件满足，开始加载dashboard数据:', {
        systemType: currentSystemType,
        stationId: currentSystemType === 'HOS' ? 'N/A (全集团数据)' : stationId,
        isAuthenticated,
        authLoading,
        siteLoading,
        selectedDate: selectedDate ? selectedDate.toISOString() : 'undefined'
      });

      // 添加防抖机制，避免快速连续调用
      const timeoutId = setTimeout(() => {
        // 如果selectedDate为undefined，使用当前日期
        const dateToUse = selectedDate || new Date();
        fetchDashboardData(dateToUse);
      }, 200);

      return () => clearTimeout(timeoutId);
    } else {
      console.log('条件不满足，无法加载dashboard数据:', {
        isAuthenticated,
        authLoading,
        siteLoading,
        currentSystemType,
        currentSiteId: currentSite?.id,
        activeStationId: activeStation?.stationId,
        finalStationId: stationId,
        canLoadData
      });
    }
  }, [isAuthenticated, authLoading, siteLoading, activeStation?.stationId, currentSystemType, fetchDashboardData, selectedDate]);

  // 准备图表数据 - 转换API数据格式为图表格式
  const prepareChartData = (data: SalesTrendData[]) => {
    return data.map(item => ({
      day: item.date,
      date: item.date,
      revenue: item.revenue,
      transactions: item.transactions,
      volume: item.volume
    }));
  };

  // 准备饼图数据 - 转换API数据格式为饼图格式
  const preparePieData = (data: FuelSalesMix[]) => {
    return data.map(item => ({
      name: item.fuel_grade,
      type: item.fuel_grade,
      value: item.volume,
      percentage: item.percentage,
      revenue: item.revenue
    }));
  };

  const chartData = prepareChartData(salesTrendData);
  const pieData = preparePieData(fuelSalesMixData);

  // 如果正在加载认证或站点数据，显示加载状态
  if (authLoading || siteLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">
            Loading dashboard...
            {authLoading && ' (认证中)'}
            {siteLoading && ' (加载站点)'}
          </p>
          <div className="mt-4 text-xs text-gray-500 space-y-1">
            <div>认证状态: {isAuthenticated ? '已认证' : '未认证'}</div>
            <div>当前站点ID: {currentSite?.id || 'null'}</div>
            <div>活动站点ID: {activeStation?.stationId || 'null'}</div>
            <div>认证加载: {authLoading ? '是' : '否'}</div>
            <div>站点加载: {siteLoading ? '是' : '否'}</div>
          </div>
        </div>
      </div>
    );
  }

  // 如果认证完成但没有站点数据，显示提示（HOS系统优化：不需要站点数据）
  if (isAuthenticated && !authLoading && !siteLoading && !currentSite && !activeStation && currentSystemType !== 'HOS') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-yellow-600 mb-4">⚠️</div>
          <p className="text-muted-foreground mb-4">
            No station data available. Please check your permissions.
          </p>
          <div className="text-xs text-gray-500 space-y-1">
            <div>认证状态: {isAuthenticated ? '已认证' : '未认证'}</div>
            <div>当前站点ID: {currentSite?.id || 'null'}</div>
            <div>活动站点ID: {activeStation?.stationId || 'null'}</div>
            <div>系统类型: {currentSystemType}</div>
          </div>
          <Button
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和日期选择器 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            {siteLoading ? 'Loading...' : (currentSite?.name || 'BP Site')}
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* 日期选择器 */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <DatePicker
              date={selectedDate}
              setDate={handleDateChange}
              placeholder="Select date"
              className="w-40"
              maxDate={new Date()}
            />
          </div>
          
          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <span className="text-sm font-medium">Error:</span>
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* 核心指标卡片 */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Revenue (IDR)</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData ? formatters.currency(dashboardData.today_revenue) : 'Loading...'}
            </div>
            <p className="text-xs text-muted-foreground">
              {dashboardData ? (
                `${dashboardData.revenue_change > 0 ? '+' : ''}${formatters.percentage(dashboardData.revenue_change)} vs yesterday`
              ) : (
                'Loading...'
              )}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Transactions</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData ? formatters.number(dashboardData.today_transactions) : 'Loading...'}
            </div>
            <p className="text-xs text-muted-foreground">
              {dashboardData ? (
                `${dashboardData.transaction_change > 0 ? '+' : ''}${formatters.percentage(dashboardData.transaction_change)} vs yesterday`
              ) : (
                'Loading...'
              )}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Sales Volume (L)</CardTitle>
            <Fuel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData ? formatters.number(dashboardData.today_volume) : 'Loading...'}
            </div>
            <p className="text-xs text-muted-foreground">
              {dashboardData ? (
                `${dashboardData.volume_change > 0 ? '+' : ''}${formatters.percentage(dashboardData.volume_change)} vs yesterday`
              ) : (
                'Loading...'
              )}
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* 主要内容区域 - 销售趋势图和燃油销售组合 */}
      <div className="grid gap-6 grid-cols-1 xl:grid-cols-2">
        {/* 销售趋势图 - 可切换视图 */}
        <Card>
          <CardHeader>
            <div className="flex flex-col space-y-4">
              <div>
                <CardTitle>{currentConfig.title}</CardTitle>
                <CardDescription>{currentConfig.description}</CardDescription>
              </div>
              
              {/* 视图切换按钮 */}
              <div className="flex space-x-2">
                <Button
                  variant={chartView === 'revenue' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setChartView('revenue')}
                  className={chartView === 'revenue' ? '' : 'text-muted-foreground'}
                >
                  Revenue
                </Button>
                <Button
                  variant={chartView === 'transactions' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setChartView('transactions')}
                  className={chartView === 'transactions' ? '' : 'text-muted-foreground'}
                >
                  Transactions
                </Button>
                <Button
                  variant={chartView === 'volume' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setChartView('volume')}
                  className={chartView === 'volume' ? '' : 'text-muted-foreground'}
                >
                  Volume
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={350}>
                <LineChart
                  data={chartData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="day" 
                    tickFormatter={(value: string) => {
                      const date = new Date(value);
                      return `${date.getDate()}/${date.getMonth() + 1}`;
                    }}
                  />
                  <YAxis 
                    tickFormatter={currentConfig.formatAxisValue}
                  />
                  <Tooltip 
                    formatter={(value: number) => [
                      currentConfig.formatValue(value),
                      currentConfig.dataKey === 'revenue' ? 'Revenue' :
                      currentConfig.dataKey === 'transactions' ? 'Transactions' : 'Volume'
                    ]}
                    labelFormatter={(label: string) => {
                      const date = new Date(label);
                      return date.toLocaleDateString('id-ID');
                    }}
                    contentStyle={{
                      backgroundColor: '#f8f9fa',
                      border: `1px solid ${currentConfig.color}`,
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey={currentConfig.dataKey}
                    stroke={currentConfig.color}
                    strokeWidth={3}
                    activeDot={{ r: 8, fill: currentConfig.color }}
                    name={currentConfig.dataKey === 'revenue' ? 'Revenue' :
                          currentConfig.dataKey === 'transactions' ? 'Transactions' : 'Volume'}
                    dot={{ r: 4, fill: currentConfig.color }}
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[350px] text-muted-foreground">
                {isLoading ? 'Loading chart data...' : 'No data available'}
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* 燃油销售组合 */}
        <Card>
          <CardHeader>
            <CardTitle>Fuel Sales Mix</CardTitle>
            <CardDescription>Sales proportion by fuel type</CardDescription>
          </CardHeader>
          <CardContent>
            {pieData.length > 0 ? (
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="percentage"
                    nameKey="name"
                    label={({ name, percentage }: { name: string; percentage: number }) => 
                      `${name}: ${percentage}%`
                    }
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={BP_COLORS.chart[index % BP_COLORS.chart.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: number) => `${value}%`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[350px] text-muted-foreground">
                {isLoading ? 'Loading fuel mix data...' : 'No data available'}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
