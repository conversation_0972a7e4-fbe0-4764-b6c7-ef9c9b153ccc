"use client";

import { useState } from 'react';
import { getSites, getSiteDetail } from '@/api/site';
import { getDashboardSummary } from '@/api/dashboard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function TestApiPage() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string>('');

  const testGetSites = async () => {
    setLoading('getSites');
    try {
      const result = await getSites();
      setResults(prev => ({ ...prev, getSites: result }));
      console.log('getSites结果:', result);
    } catch (error) {
      console.error('getSites错误:', error);
      setResults(prev => ({ ...prev, getSites: { error: error.message } }));
    } finally {
      setLoading('');
    }
  };

  const testGetSiteDetail = async () => {
    setLoading('getSiteDetail');
    try {
      const result = await getSiteDetail(1);
      setResults(prev => ({ ...prev, getSiteDetail: result }));
      console.log('getSiteDetail结果:', result);
    } catch (error) {
      console.error('getSiteDetail错误:', error);
      setResults(prev => ({ ...prev, getSiteDetail: { error: error.message } }));
    } finally {
      setLoading('');
    }
  };

  const testGetDashboard = async () => {
    setLoading('getDashboard');
    try {
      const result = await getDashboardSummary({ station_id: 1 });
      setResults(prev => ({ ...prev, getDashboard: result }));
      console.log('getDashboard结果:', result);
    } catch (error) {
      console.error('getDashboard错误:', error);
      setResults(prev => ({ ...prev, getDashboard: { error: error.message } }));
    } finally {
      setLoading('');
    }
  };

  const checkToken = () => {
    const token = localStorage.getItem('accessToken');
    console.log('当前token:', token);
    setResults(prev => ({ ...prev, token: token ? 'exists' : 'missing' }));
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">API 测试页面</h1>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Button onClick={checkToken} variant="outline">
          检查Token
        </Button>
        <Button 
          onClick={testGetSites} 
          disabled={loading === 'getSites'}
        >
          {loading === 'getSites' ? '加载中...' : '测试 getSites'}
        </Button>
        <Button 
          onClick={testGetSiteDetail} 
          disabled={loading === 'getSiteDetail'}
        >
          {loading === 'getSiteDetail' ? '加载中...' : '测试 getSiteDetail'}
        </Button>
        <Button 
          onClick={testGetDashboard} 
          disabled={loading === 'getDashboard'}
        >
          {loading === 'getDashboard' ? '加载中...' : '测试 Dashboard'}
        </Button>
      </div>

      <div className="space-y-4">
        {Object.entries(results).map(([key, value]) => (
          <Card key={key}>
            <CardHeader>
              <CardTitle>{key}</CardTitle>
              <CardDescription>API调用结果</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-xs">
                {JSON.stringify(value, null, 2)}
              </pre>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
