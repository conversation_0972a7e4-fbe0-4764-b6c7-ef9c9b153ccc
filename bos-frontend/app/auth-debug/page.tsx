"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle, CheckCircle, XCircle, RefreshCw, Info } from "lucide-react";

export default function AuthDebugPage() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);
  const [tokenInfo, setTokenInfo] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[AUTH-DEBUG] ${message}`);
  };

  useEffect(() => {
    addLog('Auth Debug页面加载');
    checkTokenInfo();
  }, []);

  useEffect(() => {
    addLog(`认证状态变化: isAuthenticated=${isAuthenticated}, isLoading=${isLoading}`);
  }, [isAuthenticated, isLoading]);

  useEffect(() => {
    if (user) {
      addLog(`用户信息更新: ${user.username} (${user.email})`);
    } else {
      addLog('用户信息清空');
    }
  }, [user]);

  const checkTokenInfo = () => {
    try {
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const tokenExpiresAt = localStorage.getItem('tokenExpiresAt');
      
      if (accessToken) {
        // 解析JWT token
        try {
          const payload = JSON.parse(atob(accessToken.split('.')[1]));
          const now = Date.now() / 1000;
          const timeUntilExpiry = payload.exp - now;
          
          setTokenInfo({
            hasAccessToken: true,
            hasRefreshToken: !!refreshToken,
            tokenExpiresAt: tokenExpiresAt,
            jwtExpiry: new Date(payload.exp * 1000).toLocaleString(),
            timeUntilExpiry: Math.round(timeUntilExpiry / 60),
            isExpired: payload.exp <= now,
            payload: payload
          });
          
          addLog(`Token信息: 过期时间=${new Date(payload.exp * 1000).toLocaleString()}, 剩余${Math.round(timeUntilExpiry / 60)}分钟`);
        } catch (e) {
          addLog('Token解析失败: ' + e);
          setTokenInfo({
            hasAccessToken: true,
            hasRefreshToken: !!refreshToken,
            tokenExpiresAt: tokenExpiresAt,
            parseError: e
          });
        }
      } else {
        setTokenInfo({
          hasAccessToken: false,
          hasRefreshToken: !!refreshToken,
          tokenExpiresAt: tokenExpiresAt
        });
        addLog('没有访问token');
      }
    } catch (error) {
      addLog('检查token信息失败: ' + error);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const refreshPage = () => {
    window.location.reload();
  };

  const clearTokens = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('tokenExpiresAt');
    localStorage.removeItem('tokenType');
    localStorage.removeItem('expiresIn');
    localStorage.removeItem('systemAccess');
    addLog('已清除所有token');
    checkTokenInfo();
  };

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === undefined) return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
    return status ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">认证状态调试</h1>
        <div className="flex items-center gap-2">
          <Badge variant={isAuthenticated ? "default" : "destructive"}>
            {isAuthenticated ? "已认证" : "未认证"}
          </Badge>
          {isLoading && <Badge variant="secondary">加载中</Badge>}
        </div>
      </div>

      {/* 认证状态概览 */}
      <Card>
        <CardHeader>
          <CardTitle>认证状态概览</CardTitle>
          <CardDescription>当前的认证状态和用户信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium flex items-center gap-2">
                {getStatusIcon(isAuthenticated)}
                认证状态
              </div>
              <div className="text-sm text-gray-600">
                {isAuthenticated ? "已认证" : "未认证"}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium flex items-center gap-2">
                {getStatusIcon(!isLoading)}
                加载状态
              </div>
              <div className="text-sm text-gray-600">
                {isLoading ? "加载中" : "已完成"}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium">用户名</div>
              <div className="text-sm text-gray-600">{user?.username || 'N/A'}</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium">邮箱</div>
              <div className="text-sm text-gray-600">{user?.email || 'N/A'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Token信息 */}
      <Card>
        <CardHeader>
          <CardTitle>Token信息</CardTitle>
          <CardDescription>JWT token的详细信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 mb-4">
            <Button onClick={checkTokenInfo} size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新Token信息
            </Button>
            <Button onClick={clearTokens} variant="outline" size="sm">
              清除Token
            </Button>
          </div>
          
          {tokenInfo && (
            <div className="space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium flex items-center gap-2">
                    {getStatusIcon(tokenInfo.hasAccessToken)}
                    Access Token
                  </div>
                  <div className="text-xs text-gray-600">
                    {tokenInfo.hasAccessToken ? "存在" : "不存在"}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium flex items-center gap-2">
                    {getStatusIcon(tokenInfo.hasRefreshToken)}
                    Refresh Token
                  </div>
                  <div className="text-xs text-gray-600">
                    {tokenInfo.hasRefreshToken ? "存在" : "不存在"}
                  </div>
                </div>
              </div>
              
              {tokenInfo.jwtExpiry && (
                <div className="space-y-1">
                  <div className="text-sm font-medium">Token过期时间</div>
                  <div className="text-sm text-gray-600">{tokenInfo.jwtExpiry}</div>
                  <div className="text-xs text-gray-500">
                    剩余时间: {tokenInfo.timeUntilExpiry}分钟
                    {tokenInfo.isExpired && <span className="text-red-500 ml-2">(已过期)</span>}
                  </div>
                </div>
              )}
              
              {tokenInfo.parseError && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Token解析错误</AlertTitle>
                  <AlertDescription>
                    {tokenInfo.parseError.toString()}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>调试操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button onClick={refreshPage}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新页面
            </Button>
            <Button onClick={() => window.location.href = '/dashboard'} variant="outline">
              访问Dashboard
            </Button>
            <Button onClick={() => window.location.href = '/login'} variant="outline">
              访问登录页
            </Button>
            <Button onClick={clearLogs} variant="outline">
              清除日志
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 调试日志 */}
      <Card>
        <CardHeader>
          <CardTitle>调试日志</CardTitle>
          <CardDescription>实时的认证状态变化日志</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500">暂无日志</div>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-xs font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 提示信息 */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>使用说明</AlertTitle>
        <AlertDescription>
          这个页面用于调试认证状态。刷新页面可以测试认证状态的恢复。
          如果刷新后仍然保持认证状态，说明修复成功。
        </AlertDescription>
      </Alert>
    </div>
  );
}
