"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/auth-context';
import { useSite } from '@/context/site-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function DebugAuthPage() {
  const { 
    user, 
    isAuthenticated, 
    isLoading: authLoading, 
    activeSite, 
    activeStation,
    activeRole 
  } = useAuth();
  
  const { currentSite, availableSites, isLoading: siteLoading } = useSite();
  
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[DEBUG-AUTH] ${message}`);
  };

  useEffect(() => {
    addLog('DebugAuth页面加载');
  }, []);

  useEffect(() => {
    addLog(`认证状态变化: isAuthenticated=${isAuthenticated}, authLoading=${authLoading}`);
  }, [isAuthenticated, authLoading]);

  useEffect(() => {
    if (user) {
      addLog(`用户信息更新: ${user.username} (${user.email})`);
    } else {
      addLog('用户信息为空');
    }
  }, [user]);

  useEffect(() => {
    if (activeSite) {
      addLog(`activeSite更新: ${JSON.stringify(activeSite)}`);
    } else {
      addLog('activeSite为空');
    }
  }, [activeSite]);

  useEffect(() => {
    if (activeStation) {
      addLog(`activeStation更新: ${JSON.stringify(activeStation)}`);
    } else {
      addLog('activeStation为空');
    }
  }, [activeStation]);

  useEffect(() => {
    if (currentSite) {
      addLog(`currentSite更新: ${JSON.stringify(currentSite)}`);
    } else {
      addLog('currentSite为空');
    }
  }, [currentSite]);

  const checkLocalStorage = () => {
    const accessToken = localStorage.getItem('accessToken');
    const activeSiteId = localStorage.getItem('activeSiteId');
    const activeStationId = localStorage.getItem('activeStationId');
    const activeRoleValue = localStorage.getItem('activeRole');
    
    addLog(`localStorage检查:`);
    addLog(`- accessToken: ${accessToken ? 'exists' : 'missing'}`);
    addLog(`- activeSiteId: ${activeSiteId || 'null'}`);
    addLog(`- activeStationId: ${activeStationId || 'null'}`);
    addLog(`- activeRole: ${activeRoleValue || 'null'}`);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const forceRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">认证状态调试</h1>
        <div className="flex gap-2">
          <Button onClick={checkLocalStorage} variant="outline" size="sm">
            检查LocalStorage
          </Button>
          <Button onClick={clearLogs} variant="outline" size="sm">
            清除日志
          </Button>
          <Button onClick={forceRefresh} variant="outline" size="sm">
            强制刷新
          </Button>
        </div>
      </div>

      {/* 当前状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">认证状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>已认证: {isAuthenticated ? '✅' : '❌'}</div>
              <div>加载中: {authLoading ? '⏳' : '✅'}</div>
              <div>用户: {user?.username || 'null'}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">活动站点状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>activeSite: {activeSite?.siteId || 'null'}</div>
              <div>activeStation: {activeStation?.stationId || 'null'}</div>
              <div>activeRole: {activeRole || 'null'}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">站点数据状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>站点加载: {siteLoading ? '⏳' : '✅'}</div>
              <div>currentSite: {currentSite?.id || 'null'}</div>
              <div>可用站点: {availableSites.length}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-xs">
              {JSON.stringify(user, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>活动站点信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="font-medium text-sm mb-2">Active Site:</div>
                <pre className="bg-gray-100 p-2 rounded text-xs">
                  {JSON.stringify(activeSite, null, 2)}
                </pre>
              </div>
              <div>
                <div className="font-medium text-sm mb-2">Active Station:</div>
                <pre className="bg-gray-100 p-2 rounded text-xs">
                  {JSON.stringify(activeStation, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 调试日志 */}
      <Card>
        <CardHeader>
          <CardTitle>实时日志</CardTitle>
          <CardDescription>认证状态变化的详细日志</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500">暂无日志</div>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-xs font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button onClick={() => window.location.href = '/dashboard'}>
              访问Dashboard
            </Button>
            <Button onClick={() => window.location.href = '/login'} variant="outline">
              去登录页
            </Button>
            <Button onClick={() => window.location.href = '/test-api'} variant="outline">
              API测试
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
