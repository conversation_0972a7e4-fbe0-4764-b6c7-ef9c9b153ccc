"use client";

import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { jwtDecode } from 'jwt-decode';
import { User, UserSiteRole, UserStationInfo, UserPreferences } from '@/types/user';
import { AuthState, SystemAccess } from '@/types/auth';
import { SystemType, DEFAULT_SYSTEM_TYPE, SYSTEM_TYPE_STORAGE_KEY } from '@/lib/config';
import { matchPathToPermission } from '@/utils/permission-utils';
import {
  apiLogin,
  apiLogout,
  apiRefreshToken,
  fetchUserProfile,
  updateUserProfile
} from '@/services/auth-service';



// 认证上下文类型
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  activeRole: string | null;
  activeSite: UserSiteRole | null;
  activeStation: UserStationInfo | null;
  currentSystemType: SystemType;
  systemAccess: SystemAccess | null;
  login: (username: string, password: string) => Promise<void>;
  loginWithToken: (token: string) => Promise<User | null>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<boolean>;
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
  checkUrlAccess: (url: string) => boolean;
  setActiveRole: (role: string) => void;
  setActiveSite: (site: UserSiteRole) => void;
  setActiveStation: (station: UserStationInfo) => void;
  setSystemType: (systemType: SystemType) => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  updatePreferences: (prefs: Partial<UserPreferences>) => Promise<void>;
  checkAuthAndRedirect: () => void;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 默认用户偏好设置
const defaultPreferences: UserPreferences = {
  theme: 'system',
  language: 'zh-CN',
  timeZone: 'Asia/Shanghai',
  notifications: {
    email: true,
    push: true,
    sms: false
  },
  sidebarCollapsed: false
};

// 辅助函数：安全地访问localStorage
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  },
  removeItem: (key: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove from localStorage:', error);
    }
  }
};

// 辅助函数：安全地设置cookie
const safeCookie = {
  set: (name: string, value: string, options: string = ''): void => {
    if (typeof document === 'undefined') return;
    try {
      document.cookie = `${name}=${value}; path=/; ${options}`;
    } catch (error) {
      console.error('Failed to set cookie:', error);
    }
  },
  remove: (name: string): void => {
    if (typeof document === 'undefined') return;
    try {
      document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
    } catch (error) {
      console.error('Failed to remove cookie:', error);
    }
  }
};

// 认证提供者组件
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 认证状态
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
    accessToken: null,
    refreshToken: null,
    systemAccess: null
  });
  
  // 活动角色和站点
  const [activeRole, setActiveRole] = useState<string | null>(null);
  const [activeSite, setActiveSite] = useState<UserSiteRole | null>(null);
  const [activeStation, setActiveStation] = useState<UserStationInfo | null>(null);

  // 系统类型状态
  const [currentSystemType, setCurrentSystemType] = useState<SystemType>(() => {
    // 从localStorage读取系统类型，如果没有则使用默认值
    if (typeof window !== 'undefined') {
      const savedSystemType = localStorage.getItem(SYSTEM_TYPE_STORAGE_KEY) as SystemType;
      return savedSystemType || DEFAULT_SYSTEM_TYPE;
    }
    return DEFAULT_SYSTEM_TYPE;
  });

  const router = useRouter();

  // 🟢 添加初始化状态标记，防止重复调用
  const [isInitializing, setIsInitializing] = useState(false);

  // 🟢 添加全局标记防止React严格模式的重复调用
  const initializationRef = useRef(false);

  // 初始化认证状态 - 防止React严格模式的重复调用
  useEffect(() => {
    console.log('🔄 AuthProvider useEffect 触发 - 初始化认证');

    // 🟢 防止React严格模式的重复调用
    if (initializationRef.current) {
      console.log('⚠️ 初始化已执行过，跳过重复调用（React严格模式）');
      return;
    }

    initializationRef.current = true;

    // 确保只在客户端执行
    if (typeof window !== 'undefined') {
      initializeAuth();
    } else {
      // 服务器端直接设置为未加载状态
      console.log('服务器端环境，设置为未加载状态');
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  }, []); // 🟢 修复：移除isInitializing依赖，避免无限循环

  // 初始化认证
  const initializeAuth = async () => {
    if (isInitializing) {
      console.log('⚠️ 初始化已在进行中，跳过重复调用');
      return;
    }

    setIsInitializing(true);
    try {
      console.log('🚀 开始初始化认证状态...');
      const accessToken = safeLocalStorage.getItem('accessToken');

      if (!accessToken) {
        console.log('❌ 没有访问token，设置为未认证状态');
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      console.log('✅ 找到访问token，检查有效性...');

      // 检查token是否过期
      try {
        const decoded = jwtDecode<{ exp: number }>(accessToken);
        const currentTime = Date.now() / 1000;
        const timeUntilExpiry = decoded.exp - currentTime;

        console.log(`Token将在${Math.round(timeUntilExpiry / 60)}分钟后过期`);

        // 如果token已经过期
        if (decoded.exp <= currentTime) {
          console.log('Token已过期，尝试刷新...');
          const success = await refreshAuth();
          if (!success) {
            console.log('Token刷新失败，清除认证状态');
            await handleLogout();
            return;
          }
          console.log('Token刷新成功');
        }
        // 如果token即将过期（30分钟内），尝试刷新
        else if (timeUntilExpiry < 1800) {
          console.log('Token即将过期，尝试刷新...');
          const success = await refreshAuth();
          if (!success) {
            console.log('Token刷新失败，但token仍然有效，继续使用');
            // 刷新失败但token仍有效，继续加载用户信息
            await loadUserProfile(accessToken, 0);
          } else {
            console.log('Token刷新成功');
            // 使用新的token加载用户信息
            const newAccessToken = safeLocalStorage.getItem('accessToken');
            if (newAccessToken) {
              await loadUserProfile(newAccessToken, 0);
            }
          }
        } else {
          // token有效且不需要刷新，直接加载用户信息
          console.log('Token有效，加载用户信息...');
          await loadUserProfile(accessToken, 0);
        }
      } catch (e) {
        console.error('Token解析失败:', e);
        // token格式无效，清除认证状态
        await handleLogout();
      }
    } catch (error) {
      console.error('❌ 初始化认证过程中发生错误:', error);
      // 不要因为初始化错误就登出用户，除非是明确的认证错误
      setAuthState(prev => ({
        ...prev,
        error: `Authentication initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }));
    } finally {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      setIsInitializing(false);
      console.log('✅ 认证初始化完成');
    }
  };

  // 重塑的用户信息加载函数 - 完全参考login流程
  const loadUserProfile = async (token: string, retryCount: number = 0): Promise<any> => {
    const maxRetries = 2;

    try {
      console.log(`🔄 === 加载用户信息 (第${retryCount + 1}次/${maxRetries + 1}) ===`);
      console.log('📡 调用 /auth/me 接口...');
      const userData = await fetchUserProfile(token);
      console.log('✅ /auth/me 接口响应成功，用户数据:', userData);

      // 🟢 参考login流程：直接设置完整的认证状态
      setAuthState(prev => ({
        ...prev,
        user: userData,
        isAuthenticated: true,
        accessToken: token,
        refreshToken: safeLocalStorage.getItem('refreshToken'),
        error: null
      }));

      // 🟢 参考login流程：立即设置活动站点信息（与login()函数完全一致）
      console.log('=== 刷新流程：设置活动站点信息 ===');

      // 设置activeSite（参考login流程）
      if (userData.sites && userData.sites.length > 0) {
        const defaultSite = userData.sites.find((site: any) => site.isDefault) || userData.sites[0];
        console.log('设置activeSite:', defaultSite);
        setActiveSite(defaultSite);
        safeLocalStorage.setItem('activeSiteId', defaultSite.siteId);
      }

      // 设置activeStation（参考login流程）
      if (userData.stations && userData.stations.length > 0) {
        const defaultStation = userData.stations.find((station: any) => station.isDefault) || userData.stations[0];
        console.log('设置activeStation:', defaultStation);
        console.log('站点名称:', defaultStation?.station?.site_name);
        console.log('站点ID:', defaultStation?.stationId);

        setActiveStation(defaultStation);
        safeLocalStorage.setItem('activeStationId', defaultStation.stationId.toString());
        console.log('✅ activeStation设置完成');
      } else {
        console.log('❌ 没有可用的stations数据');
        console.log('userData.stations:', userData.stations);
      }

      // 设置activeRole（参考login流程）
      if (userData.roles && userData.roles.length > 0) {
        const savedRole = safeLocalStorage.getItem('activeRole');
        const roleToSet = (savedRole && userData.roles.includes(savedRole)) ? savedRole : userData.roles[0];
        console.log('设置activeRole:', roleToSet);
        setActiveRole(roleToSet);
        safeLocalStorage.setItem('activeRole', roleToSet);
      }

      // 设置默认活动角色
      if (userData.roles && userData.roles.length > 0 && !activeRole) {
        const savedRole = safeLocalStorage.getItem('activeRole');
        if (savedRole && userData.roles.includes(savedRole)) {
          setActiveRole(savedRole);
        } else {
          setActiveRole(userData.roles[0]);
          safeLocalStorage.setItem('activeRole', userData.roles[0]);
        }
      }

      // 🟢 移除localStorage恢复逻辑 - 现在完全依赖API数据
      // 这样确保数据的一致性和准确性，避免显示过时的"Loading..."信息

      console.log('用户信息加载成功');
      return userData;
    } catch (error: any) {
      console.error(`加载用户资料失败 (第${retryCount + 1}次):`, error);

      // 检查错误类型
      const isAuthError = error?.status === 401 || error?.status === 403;
      const isNetworkError = error?.message?.includes('fetch') ||
                            error?.message?.includes('network') ||
                            error?.message?.includes('Failed to fetch') ||
                            error?.status >= 500;

      // 如果是认证错误，直接登出
      if (isAuthError) {
        console.log('认证错误，清除认证状态');
        await handleLogout();
        return null;
      }

      // 如果是网络错误且还有重试次数，则重试
      if (isNetworkError && retryCount < maxRetries) {
        console.log(`网络错误，${1000 * (retryCount + 1)}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return await loadUserProfile(token, retryCount + 1);
      }

      // 如果重试次数用完或其他错误，设置错误状态但不登出
      console.log('加载用户信息失败，但保持认证状态');
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: true, // 保持认证状态
        accessToken: token,
        refreshToken: safeLocalStorage.getItem('refreshToken'),
        error: `Failed to load user profile: ${error?.message || 'Unknown error'}`
      }));

      return null;
    }
  };

  // 登录
  const login = async (username: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log(`使用系统类型 ${currentSystemType} 进行登录`);
      const response = await apiLogin({ username, password, system: currentSystemType });
      
      // 保存令牌到localStorage
      safeLocalStorage.setItem('accessToken', response.accessToken);
      safeLocalStorage.setItem('refreshToken', response.refreshToken);
      
      // 保存额外的会话信息
      safeLocalStorage.setItem('tokenType', response.tokenType);
      safeLocalStorage.setItem('expiresIn', response.expiresIn.toString());
      safeLocalStorage.setItem('systemAccess', JSON.stringify(response.systemAccess));
      
      // 保存令牌过期时间（当前时间 + expiresIn秒）
      const expiresAt = new Date(Date.now() + response.expiresIn * 1000);
      safeLocalStorage.setItem('tokenExpiresAt', expiresAt.toISOString());
      
      // 同时保存令牌到cookie，以便middleware可以读取
      safeCookie.set('accessToken', response.accessToken, `max-age=${response.expiresIn}; SameSite=Strict`);
      
      // 直接设置用户状态，无需再次调用API
      setAuthState(prev => ({
        ...prev,
        user: response.user,
        isAuthenticated: true,
        accessToken: response.accessToken,
        refreshToken: response.refreshToken,
        error: null
      }));
      
      // 设置默认活动站点
      if (response.user.sites && response.user.sites.length > 0) {
        const defaultSite = response.user.sites.find((site: UserSiteRole) => site.isDefault) || response.user.sites[0];
        setActiveSite(defaultSite);
        safeLocalStorage.setItem('activeSiteId', defaultSite.siteId);
      }
      
      // 设置默认活动站点详细信息
      if (response.user.stations && response.user.stations.length > 0) {
        const defaultStation = response.user.stations.find(station => station.isDefault) || response.user.stations[0];
        setActiveStation(defaultStation);
        safeLocalStorage.setItem('activeStationId', defaultStation.stationId.toString());
      }
      
      // 设置默认活动角色
      if (response.user.roles && response.user.roles.length > 0) {
        const defaultRole = response.user.roles[0];
        setActiveRole(defaultRole);
        safeLocalStorage.setItem('activeRole', defaultRole);
      }

      // 获取重定向URL（如果有）
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const redirectPath = urlParams.get('redirect');

        // 登录成功后重定向到目标页面或仪表盘
        // 使用setTimeout避免与其他重定向逻辑冲突
        setTimeout(() => {
          if (redirectPath && redirectPath !== '/login') {
            router.push(redirectPath);
          } else {
            router.push('/dashboard');
          }
        }, 100);
      }
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        error: error.message || '登录失败，请检查用户名和密码'
      }));
      throw error;
    } finally {
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  };
  
  // 使用令牌登录（如SSO）
  const loginWithToken = async (token: string) => {
    safeLocalStorage.setItem('accessToken', token);
    // 同时设置cookie
    safeCookie.set('accessToken', token, 'max-age=86400; SameSite=Strict');

    const userData = await loadUserProfile(token, 0);
    if (userData) {
      setAuthState(prev => ({ ...prev, isAuthenticated: true }));
      return userData;
    }
    return null;
  };

  // 刷新认证
  const refreshAuth = async (): Promise<boolean> => {
    try {
      const currentRefreshToken = safeLocalStorage.getItem('refreshToken');
      
      if (!currentRefreshToken) {
        return false;
      }
      
      const tokenResponse = await apiRefreshToken(currentRefreshToken);
      if (tokenResponse) {
        // 保存新的令牌信息
        safeLocalStorage.setItem('accessToken', tokenResponse.accessToken);
        safeLocalStorage.setItem('refreshToken', tokenResponse.refreshToken);
        
        // 保存令牌过期时间
        const expiresAt = new Date(Date.now() + tokenResponse.expiresIn * 1000);
        safeLocalStorage.setItem('tokenExpiresAt', expiresAt.toISOString());
        safeLocalStorage.setItem('expiresIn', tokenResponse.expiresIn.toString());
        
        // 同时更新cookie
        safeCookie.set('accessToken', tokenResponse.accessToken, `max-age=${tokenResponse.expiresIn}; SameSite=Strict`);
        
        // 更新认证状态中的令牌
        setAuthState(prev => ({
          ...prev,
          accessToken: tokenResponse.accessToken,
          refreshToken: tokenResponse.refreshToken
        }));
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('刷新认证失败:', error);
      return false;
    }
  };

  // 登出
  const handleLogout = async () => {
    try {
      if (authState.accessToken) {
        await apiLogout();
      }
    } catch (error) {
      console.error('登出API调用失败:', error);
    } finally {
      // 从localStorage移除所有会话相关信息
      safeLocalStorage.removeItem('accessToken');
      safeLocalStorage.removeItem('refreshToken');
      safeLocalStorage.removeItem('tokenType');
      safeLocalStorage.removeItem('expiresIn');
      safeLocalStorage.removeItem('stationIds');
      safeLocalStorage.removeItem('tokenExpiresAt');
      safeLocalStorage.removeItem('activeRole');
      safeLocalStorage.removeItem('activeSiteId');
      safeLocalStorage.removeItem('activeStationId');
      
      // 从cookie中移除令牌
      safeCookie.remove('accessToken');
      
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        accessToken: null,
        refreshToken: null,
        systemAccess: null
      });
      
      setActiveRole(null);
      setActiveSite(null);
      setActiveStation(null);
      
      // 登出后自动跳转到登录页面
      if (typeof window !== 'undefined') {
        router.push('/login');
      }
    }
  };

  // 更新用户资料
  const updateProfile = async (userData: Partial<User>) => {
    if (!authState.user) return;
    
    try {
      const updatedUser = await updateUserProfile(userData);
      setAuthState(prev => ({
        ...prev,
        user: { ...prev.user!, ...updatedUser }
      }));
    } catch (error) {
      console.error('更新用户资料失败:', error);
      throw error;
    }
  };
  
  // 更新用户偏好设置
  const updatePreferences = async (prefs: Partial<UserPreferences>) => {
    if (!authState.user) return;
    
    try {
      const updatedPrefs = { ...authState.user.preferences, ...prefs };
      await updateProfile({ preferences: updatedPrefs });
    } catch (error) {
      console.error('更新用户偏好失败:', error);
      throw error;
    }
  };

  // 设置活动角色
  const handleSetActiveRole = (role: string) => {
    if (authState.user && authState.user.roles.includes(role)) {
      setActiveRole(role);
      
      // 保存到本地存储以便下次加载
      safeLocalStorage.setItem('activeRole', role);
    }
  };
  
  // 设置活动站点
  const handleSetActiveSite = (site: UserSiteRole) => {
    if (authState.user && authState.user.sites.some(s => s.siteId === site.siteId)) {
      setActiveSite(site);
      
      // 保存到本地存储以便下次加载
      safeLocalStorage.setItem('activeSiteId', site.siteId);
    }
  };

  // 设置活动站点详细信息
  const handleSetActiveStation = (station: UserStationInfo) => {
    if (authState.user && authState.user.stations?.some(s => s.stationId === station.stationId)) {
      setActiveStation(station);
      
      // 保存到本地存储以便下次加载
      safeLocalStorage.setItem('activeStationId', station.stationId.toString());
      
      // 同时更新兼容的 activeSite
      const compatibleSite: UserSiteRole = {
        siteId: station.stationId.toString(),
        siteName: station.station.site_name,
        role: station.roleType,
        isDefault: station.isDefault
      };
      setActiveSite(compatibleSite);
      safeLocalStorage.setItem('activeSiteId', compatibleSite.siteId);
    }
  };

  // 设置系统类型
  const handleSetSystemType = (systemType: SystemType) => {
    setCurrentSystemType(systemType);

    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem(SYSTEM_TYPE_STORAGE_KEY, systemType);
    }

    // 如果用户已登录且切换了系统类型，需要清除当前会话并重新登录
    if (authState.isAuthenticated && authState.systemAccess?.system !== systemType) {
      console.log(`系统类型从 ${authState.systemAccess?.system} 切换到 ${systemType}，需要重新登录`);
      // 可以选择自动登出或提示用户重新登录
      // 这里暂时不自动登出，让用户手动重新登录
    }
  };

  // 检查角色
  const hasRole = (role: string): boolean => {
    if (!authState.user) return false;
    return authState.user.roles.includes(role);
  };

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!authState.user) return false;
    return authState.user.permissions.includes(permission);
  };

  // 检查URL访问权限
  const checkUrlAccess = (url: string): boolean => {
    if (!authState.user) return false;
    
    // 总是允许访问仪表板和个人资料页面
    if (url === '/dashboard' || url === '/profile') {
      return true;
    }
    
    // 允许访问公共页面
    const publicPages = ['/login', '/register', '/forgot-password', '/reset-password'];
    if (publicPages.includes(url)) {
      return true;
    }
    
    // 基于用户权限检查URL访问权限
    return matchPathToPermission(url, authState.user.permissions);
  };

  // 添加一个专门的方法来检查是否需要重定向到登录页
  const checkAuthAndRedirect = () => {
    // 如果正在加载或不在客户端，不做任何操作
    if (authState.isLoading || typeof window === 'undefined') return;

    const currentPath = window.location.pathname;
    const publicPages = ['/login', '/register', '/forgot-password', '/reset-password'];

    // 如果未登录且不在公共页面，重定向到登录
    if (!authState.isAuthenticated && !publicPages.includes(currentPath)) {
      console.log('未登录，重定向到登录页:', currentPath);
      // 延迟重定向，确保认证状态已经稳定
      setTimeout(() => {
        if (!authState.isAuthenticated && !authState.isLoading) {
          router.push(`/login?redirect=${encodeURIComponent(currentPath)}`);
        }
      }, 100);
      return;
    }

    // 如果已登录但在登录页，重定向到仪表板
    // 但要避免在登录过程中立即触发重定向
    if (authState.isAuthenticated && currentPath === '/login') {
      console.log('已登录但在登录页，重定向到dashboard');
      // 延迟重定向，避免与登录函数中的重定向冲突
      setTimeout(() => {
        if (window.location.pathname === '/login' && authState.isAuthenticated) {
          router.push('/dashboard');
        }
      }, 300);
    }
  };
  
  // 监听认证状态变化，自动处理重定向（带防抖）
  useEffect(() => {
    if (!authState.isLoading && typeof window !== 'undefined') {
      // 防抖处理，避免频繁的重定向检查
      const timeoutId = setTimeout(() => {
        checkAuthAndRedirect();
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [authState.isAuthenticated, authState.isLoading]);

  // 上下文值
  const contextValue: AuthContextType = {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    activeRole,
    activeSite,
    activeStation,
    currentSystemType,
    systemAccess: authState.systemAccess,
    login,
    loginWithToken,
    logout: handleLogout,
    refreshAuth,
    hasRole,
    hasPermission,
    checkUrlAccess,
    setActiveRole: handleSetActiveRole,
    setActiveSite: handleSetActiveSite,
    setActiveStation: handleSetActiveStation,
    setSystemType: handleSetSystemType,
    updateProfile,
    updatePreferences,
    checkAuthAndRedirect
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

// 使用认证上下文的钩子
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
}; 