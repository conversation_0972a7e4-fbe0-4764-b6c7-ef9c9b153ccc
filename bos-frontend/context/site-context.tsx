"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { Site, SiteStyling } from "@/types/site";
import { getSiteDetail } from "@/api/site";
import { useAuth } from "./auth-context";
import { SystemType } from "@/lib/config";

// HOS系统的站点选择选项
export interface HosSiteOption {
  id: number | 'all';
  name: string;
  code?: string;
  isAllSites?: boolean;
}

// 站点上下文类型
interface SiteContextType {
  currentSite: Site | null;
  availableSites: Site[];
  isLoading: boolean;
  siteTheme: SiteStyling | null;
  // HOS系统多站点管理
  selectedHosSites: number[] | 'all'; // 'all' 表示选择所有站点
  hosSiteOptions: HosSiteOption[];
  switchSite: (siteId: number) => Promise<void>;
  fetchSites: () => Promise<void>;
  updateSiteStyling: (styling: Partial<SiteStyling>) => Promise<void>;
  // HOS系统专用方法
  setSelectedHosSites: (siteIds: number[] | 'all') => void;
  getFilteredSiteIds: () => number[] | null; // null表示不限制站点
}

// 创建站点上下文
const SiteContext = createContext<SiteContextType | undefined>(undefined);

// 默认站点样式
const defaultSiteStyling: SiteStyling = {
  primaryColor: '#0066cc',
  secondaryColor: '#e5e5e5',
  accentColor: '#ff9900',
  textColor: '#333333',
  backgroundColor: '#ffffff',
  fontFamily: 'system-ui, sans-serif'
};

// 站点提供者组件
export function SiteProvider({ children }: { children: React.ReactNode }) {
  const [currentSite, setCurrentSite] = useState<Site | null>(null);
  const [availableSites, setAvailableSites] = useState<Site[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [siteTheme, setSiteTheme] = useState<SiteStyling | null>(null);

  // HOS系统多站点管理状态
  const [selectedHosSites, setSelectedHosSites] = useState<number[] | 'all'>('all');
  const [hosSiteOptions, setHosSiteOptions] = useState<HosSiteOption[]>([]);

  const { user, isAuthenticated, activeSite, currentSystemType, systemAccess } = useAuth();
  
  // 监听活动站点变化
  useEffect(() => {
    if (activeSite && currentSite?.id.toString() !== activeSite.siteId) {
      loadSiteById(parseInt(activeSite.siteId));
    }
  }, [activeSite]);
  
  // 加载特定站点
  const loadSiteById = async (siteId: number) => {
    try {
      console.log('开始加载站点详情，站点ID:', siteId);
      const siteData = await getSiteDetail(siteId);
      console.log('成功加载站点详情:', siteData);
      setCurrentSite(siteData);

      // 设置站点样式
      setSiteTheme(siteData.styling || defaultSiteStyling);
      console.log('站点数据设置完成，currentSite已更新');
    } catch (error) {
      console.error('加载站点详情失败:', error);
    }
  };
  
  // 根据HOS系统权限获取可管理的站点列表
  const getHosAccessibleSites = () => {
    if (currentSystemType !== 'HOS') {
      return [];
    }

    // HOS系统优化：即使没有systemAccess或站点数据，也返回默认站点信息
    if (!systemAccess) {
      console.log('HOS系统：systemAccess为空，使用默认站点配置');
      // 返回一个默认的站点配置，确保HOS系统能正常工作
      return [{ id: 0, name: 'All Sites', code: 'ALL' }];
    }

    // 如果是全局权限，返回所有站点
    if (systemAccess.scopeType === 'global') {
      console.log('HOS用户具有全局权限，可管理所有站点');
      // 如果没有具体站点数据，返回默认配置
      if (availableSites.length === 0) {
        return [{ id: 0, name: 'All Sites', code: 'ALL' }];
      }
      return availableSites;
    }

    // 如果是站点权限，根据scopeIds过滤站点
    if (systemAccess.scopeType === 'station' && systemAccess.scopeIds) {
      console.log('HOS用户具有站点权限，可管理站点:', systemAccess.scopeIds);
      const filteredSites = availableSites.filter(site => systemAccess.scopeIds.includes(site.id));
      // 如果过滤后没有站点，返回默认配置
      if (filteredSites.length === 0) {
        return [{ id: 0, name: 'All Sites', code: 'ALL' }];
      }
      return filteredSites;
    }

    // 默认情况：返回默认站点配置
    console.log('HOS系统：使用默认站点配置');
    return [{ id: 0, name: 'All Sites', code: 'ALL' }];
  };

  // 构建HOS系统的站点选择选项
  const buildHosSiteOptions = (sites: Site[]): HosSiteOption[] => {
    const options: HosSiteOption[] = [
      {
        id: 'all',
        name: 'All Sites',
        isAllSites: true
      }
    ];

    sites.forEach(site => {
      options.push({
        id: site.id,
        name: site.name,
        code: site.code,
        isAllSites: false
      });
    });

    return options;
  };

  // 从用户数据中构建站点列表（避免重复调用/stations接口）
  const buildSitesFromUserData = (userData: any) => {
    console.log('从用户数据构建站点列表:', userData);

    if (!userData?.stations || !Array.isArray(userData.stations)) {
      console.log('用户数据中没有stations信息');
      return [];
    }

    // 将用户的stations数据转换为Site格式
    const sites = userData.stations.map((stationInfo: any) => ({
      id: parseInt(stationInfo.stationId),
      code: stationInfo.station?.site_code || `SITE_${stationInfo.stationId}`,
      name: stationInfo.station?.site_name || `Station ${stationInfo.stationId}`,
      type: stationInfo.station?.type || 'fuel_station',
      status: stationInfo.station?.business_status || 'active',
      address: stationInfo.station?.address || '',
      city: stationInfo.station?.city || '',
      province: stationInfo.station?.province || '',
      postal_code: stationInfo.station?.postal_code || '',
      country: stationInfo.station?.country || 'Indonesia',
      latitude: stationInfo.station?.latitude || 0,
      longitude: stationInfo.station?.longitude || 0,
      phone: stationInfo.station?.contact_info?.phone || '',
      email: stationInfo.station?.contact_info?.email || '',
      manager_name: stationInfo.station?.manager_name || '',
      company_id: stationInfo.station?.company_id || 1,
      styling: stationInfo.station?.styling || null,
      created_at: stationInfo.station?.created_at || new Date().toISOString(),
      updated_at: stationInfo.station?.updated_at || new Date().toISOString(),
    }));

    console.log('构建的站点列表:', sites);
    return sites;
  };

  // 优化后的站点数据获取函数
  const fetchSites = async () => {
    console.log('fetchSites 被调用，认证状态:', { isAuthenticated, user: !!user, activeSite, currentSystemType });

    if (!isAuthenticated || !user) {
      console.log('用户未认证，清空站点数据');
      setAvailableSites([]);
      setCurrentSite(null);
      setHosSiteOptions([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      console.log('开始处理站点数据...');

      // 根据系统类型处理站点数据
      if (currentSystemType === 'HOS') {
        console.log('HOS系统：处理多站点管理');

        // 对于HOS系统，首先获取用户可访问的站点
        const userSites = buildSitesFromUserData(user);
        setAvailableSites(userSites);

        // 根据权限获取可管理的站点
        const accessibleSites = getHosAccessibleSites();
        const hosOptions = buildHosSiteOptions(accessibleSites);
        setHosSiteOptions(hosOptions);

        // HOS系统不设置单个currentSite，而是使用selectedHosSites
        setCurrentSite(null);

        console.log('HOS站点选项:', hosOptions);
        console.log('HOS系统数据加载完成，可访问站点数量:', accessibleSites.length);
      } else {
        // BOS/EDC系统：使用原有的单站点逻辑
        console.log('BOS/EDC系统：使用单站点模式');
        const sites = buildSitesFromUserData(user);
        setAvailableSites(sites);

        // 清空HOS相关状态
        setHosSiteOptions([]);
        setSelectedHosSites('all');

        // 如果有activeSite，加载对应站点详情
        if (activeSite) {
          console.log('使用activeSite加载站点详情:', activeSite.siteId);
          const siteId = parseInt(activeSite.siteId);
          await loadSiteById(siteId);
        }
        // 否则加载第一个站点
        else if (sites.length > 0) {
          console.log('没有activeSite，加载第一个站点:', sites[0].id);
          await loadSiteById(sites[0].id);
        } else {
          console.log('没有可用的站点');
        }
      }
    } catch (error) {
      console.error("处理站点数据失败:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // 切换当前站点（BOS/EDC系统使用）
  const switchSite = async (siteId: number) => {
    const site = availableSites.find(site => site.id === siteId);
    if (site) {
      await loadSiteById(siteId);
    }
  };

  // HOS系统：设置选择的站点（优化后：HOS系统固定为'all'）
  const handleSetSelectedHosSites = (siteIds: number[] | 'all') => {
    // HOS系统优化：始终使用'all'模式，不允许手动切换
    if (currentSystemType === 'HOS') {
      console.log('HOS系统：固定使用全部站点模式，忽略手动切换');
      setSelectedHosSites('all');

      // 保存到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('hos_selected_sites', JSON.stringify('all'));
      }
      return;
    }

    // 非HOS系统：保持原有逻辑
    console.log('非HOS系统：设置选择的站点', siteIds);
    setSelectedHosSites(siteIds);

    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('hos_selected_sites', JSON.stringify(siteIds));
    }
  };

  // HOS系统：获取过滤后的站点ID列表
  const getFilteredSiteIds = (): number[] | null => {
    if (currentSystemType !== 'HOS') {
      return null; // 非HOS系统不使用站点过滤
    }

    if (selectedHosSites === 'all') {
      return null; // 'all'表示不限制站点
    }

    return selectedHosSites;
  };
  
  // 更新站点样式
  const updateSiteStyling = async (styling: Partial<SiteStyling>) => {
    if (!currentSite || !siteTheme) return;
    
    try {
      // 在实际项目中，这里应该调用API来更新站点样式
      // 这里仅在本地更新
      const updatedStyling: SiteStyling = {
        primaryColor: styling.primaryColor || siteTheme.primaryColor,
        secondaryColor: styling.secondaryColor || siteTheme.secondaryColor,
        accentColor: styling.accentColor || siteTheme.accentColor,
        textColor: styling.textColor || siteTheme.textColor,
        backgroundColor: styling.backgroundColor || siteTheme.backgroundColor,
        logoUrl: styling.logoUrl || siteTheme.logoUrl,
        faviconUrl: styling.faviconUrl || siteTheme.faviconUrl,
        bannerUrl: styling.bannerUrl || siteTheme.bannerUrl,
        customCss: styling.customCss || siteTheme.customCss,
        fontFamily: styling.fontFamily || siteTheme.fontFamily
      };
      
      setSiteTheme(updatedStyling);
      
      // 更新当前站点对象
      setCurrentSite({
        ...currentSite,
        styling: updatedStyling
      });
    } catch (error) {
      console.error('更新站点样式失败:', error);
      throw error;
    }
  };
  
  // 初始化时设置HOS站点选择（优化后：HOS系统固定为'all'）
  useEffect(() => {
    if (currentSystemType === 'HOS') {
      console.log('HOS系统：强制设置为全部站点模式');
      setSelectedHosSites('all');

      // 保存到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('hos_selected_sites', JSON.stringify('all'));
      }
    } else if (typeof window !== 'undefined') {
      // 非HOS系统：从localStorage恢复选择
      const savedSelection = localStorage.getItem('hos_selected_sites');
      if (savedSelection) {
        try {
          const parsed = JSON.parse(savedSelection);
          setSelectedHosSites(parsed);
        } catch (error) {
          console.error('解析保存的站点选择失败:', error);
        }
      }
    }
  }, [currentSystemType]);

  // 🟢 优化：合并useEffect逻辑，避免重复调用
  useEffect(() => {
    console.log('SiteProvider useEffect 触发，状态:', {
      isAuthenticated,
      user: !!user,
      activeSite: activeSite?.siteId,
      currentSite: currentSite?.id,
      currentSystemType
    });

    // 确保认证完成且有用户数据
    if (!isAuthenticated || !user) {
      console.log('用户未认证，清空站点数据');
      setAvailableSites([]);
      setCurrentSite(null);
      setIsLoading(false);
      return;
    }

    // 检查是否需要重新获取站点数据
    // HOS系统的逻辑与BOS/EDC不同
    const needsRefetch = currentSystemType === 'HOS'
      ? (availableSites.length === 0 || hosSiteOptions.length === 0)
      : (!currentSite ||
         (activeSite && currentSite.id.toString() !== activeSite.siteId) ||
         (!activeSite && availableSites.length === 0));

    if (needsRefetch) {
      console.log('需要获取站点数据，原因:', {
        systemType: currentSystemType,
        noCurrentSite: !currentSite,
        activeSiteMismatch: activeSite && currentSite && currentSite.id.toString() !== activeSite.siteId,
        noAvailableSites: availableSites.length === 0,
        noHosOptions: hosSiteOptions.length === 0
      });

      // 添加防抖机制，避免快速连续调用
      const timer = setTimeout(() => {
        fetchSites();
      }, 100);

      return () => clearTimeout(timer);
    } else {
      console.log('无需重新获取站点数据');
      setIsLoading(false);
    }
  }, [isAuthenticated, user, activeSite?.siteId, currentSite?.id, availableSites.length, currentSystemType, hosSiteOptions.length]);
  
  // 上下文值
  const contextValue: SiteContextType = {
    currentSite,
    availableSites,
    isLoading,
    siteTheme,
    selectedHosSites,
    hosSiteOptions,
    switchSite,
    fetchSites,
    updateSiteStyling,
    setSelectedHosSites: handleSetSelectedHosSites,
    getFilteredSiteIds
  };
  
  return (
    <SiteContext.Provider value={contextValue}>
      {children}
    </SiteContext.Provider>
  );
}

// 使用站点上下文的钩子
export function useSite() {
  const context = useContext(SiteContext);
  if (context === undefined) {
    throw new Error("useSite必须在SiteProvider内部使用");
  }
  return context;
} 