# Price (IDR) 显示修复总结

## 🎯 问题分析

### 原始问题
End of Day Report 页面中 Price (IDR) 部分的班次小计和日汇总显示错误，使用了硬编码的固定价格而不是基于实际数据计算的平均单价。

### 具体问题
1. **员工数据显示正确** ✅
   - 正确显示 API 返回的 `unit_price` 字段
   - 例如：BP 92 显示 12,600 IDR/升

2. **班次小计使用硬编码** ❌
   - BP 92: 固定显示 15,000 IDR
   - Ultimate: 固定显示 16,000 IDR  
   - Diesel: 固定显示 15,500 IDR

3. **日汇总使用硬编码** ❌
   - BP 92: 固定显示 15,000 IDR
   - Ultimate: 固定显示 16,000 IDR
   - Diesel: 固定显示 15,500 IDR

## 🛠️ 修复方案

### 修复逻辑
Price (IDR) 应该显示**平均单价**，计算公式：
```
平均单价 = 总金额 ÷ 总销量
```

使用 `gross_amount` 和 `sales_volume` 字段进行计算。

### 修复内容

#### 1. BP 92 班次小计修复
**修复前**:
```typescript
{formatIDR(15000)}  // 硬编码
```

**修复后**:
```typescript
{(() => {
  // 计算班次内BP 92的平均单价
  const bp92Sales = shift.attendants.filter((att: any) => {
    const bp92 = findFuelData(att.fuel_sales, '92');
    return bp92 && bp92.sales_volume > 0;
  });
  if (bp92Sales.length === 0) return '-';
  
  const totalAmount = bp92Sales.reduce((sum: number, att: any) => {
    const bp92 = findFuelData(att.fuel_sales, '92');
    return sum + (bp92?.gross_amount || 0);
  }, 0);
  const totalVolume = bp92Sales.reduce((sum: number, att: any) => {
    const bp92 = findFuelData(att.fuel_sales, '92');
    return sum + (bp92?.sales_volume || 0);
  }, 0);
  
  return totalVolume > 0 ? formatIDR(Math.round(totalAmount / totalVolume)) : '-';
})()}
```

#### 2. BP 92 日汇总修复
**修复前**:
```typescript
{formatIDR(15000)}  // 硬编码
```

**修复后**:
```typescript
{(() => {
  // 计算全天BP 92的平均单价
  const allBp92Sales = reportData.shifts.flatMap((shift: any) => 
    shift.attendants.filter((att: any) => {
      const bp92 = findFuelData(att.fuel_sales, '92');
      return bp92 && bp92.sales_volume > 0;
    })
  );
  if (allBp92Sales.length === 0) return '-';
  
  const totalAmount = allBp92Sales.reduce((sum: number, att: any) => {
    const bp92 = findFuelData(att.fuel_sales, '92');
    return sum + (bp92?.gross_amount || 0);
  }, 0);
  const totalVolume = allBp92Sales.reduce((sum: number, att: any) => {
    const bp92 = findFuelData(att.fuel_sales, '92');
    return sum + (bp92?.sales_volume || 0);
  }, 0);
  
  return totalVolume > 0 ? formatIDR(Math.round(totalAmount / totalVolume)) : '-';
})()}
```

#### 3. Ultimate 和 Diesel 修复
同样的逻辑应用到 Ultimate (95) 和 Diesel 油品类型。

## 📊 修复效果

### 修复前 (硬编码)
- BP 92 班次小计: 15,000 IDR (固定)
- BP 92 日汇总: 15,000 IDR (固定)
- Ultimate 班次小计: 16,000 IDR (固定)
- Ultimate 日汇总: 16,000 IDR (固定)
- Diesel 班次小计: 15,500 IDR (固定)
- Diesel 日汇总: 15,500 IDR (固定)

### 修复后 (动态计算)
基于实际数据计算的平均单价：

**示例计算** (2025-07-18 数据):
- BP 92 员工数据: 12,600 IDR/升
- 班次小计: (38,800 ÷ 3) = 12,933 IDR/升
- 日汇总: 所有班次的加权平均

## 🧪 测试验证

### 测试数据
使用 2025-07-18 的实际数据：
```json
{
  "fuel_grade": "BP 92",
  "unit_price": 12600,
  "sales_volume": 3,
  "gross_amount": 38800,
  "net_amount": 37800
}
```

### 验证计算
- **员工单价**: 12,600 IDR/升 (API 数据)
- **实际平均单价**: 38,800 ÷ 3 = 12,933 IDR/升
- **预期显示**: 12,933 IDR/升 (四舍五入)

### 测试步骤
1. 访问 `/shift-management/end-of-day-report`
2. 选择日期: 2025-07-18
3. 选择站点: Jakarta Main Station
4. 验证 Price (IDR) 部分：
   - [ ] 员工数据显示 12,600 IDR
   - [ ] 班次小计显示计算后的平均价格
   - [ ] 日汇总显示计算后的平均价格
   - [ ] 不再显示硬编码的 15,000/16,000/15,500

## 🔧 技术改进

### 1. 数据准确性
- ❌ 移除硬编码的固定价格
- ✅ 使用真实数据计算平均单价
- ✅ 支持动态价格变化

### 2. 计算逻辑
- ✅ 基于 `gross_amount` 和 `sales_volume` 计算
- ✅ 处理零销量情况 (显示 '-')
- ✅ 四舍五入到整数 IDR

### 3. 代码质量
- ✅ 统一的计算逻辑
- ✅ 错误处理 (零除法保护)
- ✅ 可读性良好的内联函数

## 📝 修复文件

**修改文件**: `bos-frontend/app/shift-management/end-of-day-report/page.tsx`

**修改行数**:
- BP 92 班次小计: 第2206-2226行
- BP 92 日汇总: 第2229-2251行
- Ultimate 班次小计: 第2266-2286行
- Ultimate 日汇总: 第2289-2311行
- Diesel 班次小计: 第2326-2346行
- Diesel 日汇总: 第2349-2371行

## 🎯 总结

✅ **问题根源**: 前端使用硬编码价格而不是计算平均单价  
✅ **解决方案**: 基于实际销售数据动态计算平均单价  
✅ **修复范围**: 所有油品类型的班次小计和日汇总  
✅ **数据准确性**: 显示真实的平均单价而不是固定值  

现在 Price (IDR) 部分将显示基于实际销售数据计算的平均单价，提供更准确的价格信息。
