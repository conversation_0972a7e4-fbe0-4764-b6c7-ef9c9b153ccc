// API基础URL
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api/v1';

// 应用名称和版本
export const APP_NAME = 'BP BOS';
export const APP_VERSION = '1.0.0';

// 系统类型定义
export type SystemType = 'HOS' | 'BOS' | 'EDC';

// 系统类型配置
export const SYSTEM_TYPES: Record<SystemType, { name: string; description: string }> = {
  HOS: { name: 'HOS', description: 'Headquarters Office System' },
  BOS: { name: 'BOS', description: 'Back Office System' },
  EDC: { name: 'EDC', description: 'Electronic Data Capture' }
};

// 默认系统类型（从环境变量读取，默认为BOS）
export const DEFAULT_SYSTEM_TYPE: SystemType = (process.env.NEXT_PUBLIC_SYSTEM_TYPE as SystemType) || 'BOS';

// 系统类型存储键
export const SYSTEM_TYPE_STORAGE_KEY = 'current_system_type';

// 认证相关配置
export const AUTH_TOKEN_KEY = 'auth_token';
export const AUTH_USER_KEY = 'auth_user';
export const AUTH_TOKEN_EXPIRY = 60 * 60 * 24 * 7; // 7天（秒）

// 分页默认值
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE_SIZES = [10, 20, 50, 100];

// 日期格式
export const DATE_FORMAT = 'yyyy-MM-dd';
export const DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';

// 货币格式设置
export const CURRENCY = 'IDR';
export const CURRENCY_LOCALE = 'id-ID';

// 请求超时时间（毫秒）
export const REQUEST_TIMEOUT = 30000; // 30秒

// 时区配置类型定义
export type SupportedTimezone = 'Asia/Jakarta' | 'Asia/Singapore' | 'Asia/Kuala_Lumpur' | 'Asia/Bangkok' | 'Asia/Manila' | 'UTC';

export interface TimezoneInfo {
  offset: string;
  name: string;
}

// 时区配置
export const TIMEZONE_CONFIG = {
  // 默认时区
  DEFAULT: 'Asia/Jakarta' as SupportedTimezone,
  
  // 支持的时区列表
  SUPPORTED_TIMEZONES: {
    'Asia/Jakarta': { offset: '+07:00', name: 'Jakarta' },
    'Asia/Singapore': { offset: '+08:00', name: 'Singapore' },
    'Asia/Kuala_Lumpur': { offset: '+08:00', name: 'Kuala Lumpur' },
    'Asia/Bangkok': { offset: '+07:00', name: 'Bangkok' },
    'Asia/Manila': { offset: '+08:00', name: 'Manila' },
    'UTC': { offset: '+00:00', name: 'UTC' },
  } as Record<SupportedTimezone, TimezoneInfo>,
  
  // 从localStorage、环境变量或默认配置获取当前时区
  getCurrentTimezone: (): SupportedTimezone => {
    // 优先级：localStorage > 环境变量 > 默认值
    if (typeof window !== 'undefined') {
      const savedTimezone = localStorage.getItem('preferred_timezone') as SupportedTimezone;
      if (savedTimezone && savedTimezone in TIMEZONE_CONFIG.SUPPORTED_TIMEZONES) {
        return savedTimezone;
      }
    }
    
    const envTimezone = process.env.NEXT_PUBLIC_TIMEZONE as SupportedTimezone;
    if (envTimezone && envTimezone in TIMEZONE_CONFIG.SUPPORTED_TIMEZONES) {
      return envTimezone;
    }
    
    return TIMEZONE_CONFIG.DEFAULT;
  },
  
  // 获取时区偏移量
  getTimezoneOffset: (timezone?: SupportedTimezone): string => {
    const tz = timezone || TIMEZONE_CONFIG.getCurrentTimezone();
    return TIMEZONE_CONFIG.SUPPORTED_TIMEZONES[tz]?.offset || '+07:00';
  },
  
  // 获取时区名称
  getTimezoneName: (timezone?: SupportedTimezone): string => {
    const tz = timezone || TIMEZONE_CONFIG.getCurrentTimezone();
    return TIMEZONE_CONFIG.SUPPORTED_TIMEZONES[tz]?.name || 'Jakarta';
  }
};

// 向后兼容的站点时区配置
export const SITE_DEFAULT_TIMEZONE = TIMEZONE_CONFIG.DEFAULT; 