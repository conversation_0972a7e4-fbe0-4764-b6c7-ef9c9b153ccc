// 认证和授权相关类型定义
import { User } from './user';
import { SystemType } from '@/lib/config';

// 登录请求参数
export interface LoginRequest {
  username: string;
  password: string;
  system: SystemType; // 目标系统：HOS|BOS|EDC
  authType?: string; // 认证类型：local、ldap、ad
  rememberMe?: boolean; // 是否记住登录状态
  captcha?: string; // 验证码
  captchaId?: string; // 验证码ID
}

// BOS Core API 通用响应格式
export interface BosApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
  requestId: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

// 系统访问信息
export interface SystemAccess {
  system: SystemType; // 当前系统 (HOS|BOS|EDC)
  accessLevel: string; // 用户的最高角色级别 (admin|manager|supervisor|operator)
  scopeType: string; // 权限范围类型 (global|station|operation)
  scopeIds: number[]; // 权限范围ID列表
  stationIds: number[]; // 可访问的站点ID列表 (向后兼容)
  stationCount: number; // 可访问站点数量
  permissions: string[]; // 用户权限列表
}

// 登录响应数据
export interface LoginResponseData {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: {
    id: string;
    username: string;
    email: string;
    fullName: string;
    phone?: string;
    status?: string;
    roles: string[];
    permissions: string[];
    lastLoginAt: string;
    language?: string;
    // 新增：站点相关字段
    sites?: Array<{
      siteId: string;
      siteName: string;
      role: string;
      isDefault: boolean;
    }>;
    stations?: Array<{
      stationId: number;
      roleType: string;
      isDefault: boolean;
      hasManagePermission: boolean;
      station: {
        id: number;
        site_name: string;
        site_code: string;
        business_status: string;
        address?: any;
        latitude?: number;
        longitude?: number;
        [key: string]: any;
      };
    }>;
  };
  systemAccess: SystemAccess;
}

// 登录响应
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: User;
  systemAccess: SystemAccess;
}

// 刷新令牌请求
export interface RefreshTokenRequest {
  refreshToken: string;
}

// 刷新令牌响应数据
export interface RefreshTokenResponseData {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
}

// 角色定义
export interface Role {
  id: string;
  code: string;
  name: string;
  description?: string;
  isSystem: boolean;
  createdAt: string;
  updatedAt?: string;
}

// 权限定义
export interface Permission {
  id: string;
  code: string;
  name: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'execute';
  type?: 'function' | 'url' | 'field';
  description?: string;
  createdAt: string;
}

// 角色权限关系
export interface RolePermission {
  roleId: string;
  permissionId: string;
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  accessToken: string | null;
  refreshToken: string | null;
  user: User | null;
  systemAccess: SystemAccess | null;
}

// 修改密码请求
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 重置密码请求
export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// API错误代码常量
export const AUTH_ERROR_CODES = {
  INVALID_CREDENTIALS: 1001,
  NO_STATION_ASSOCIATION: 4001,
  INSUFFICIENT_PERMISSIONS: 4002,
  INVALID_SYSTEM: 4003,
} as const;

// 注意：SystemType 现在从 @/lib/config 导入