<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Calls Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-call {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .api-call.me { border-left-color: #28a745; }
        .api-call.stations { border-left-color: #ffc107; }
        .api-call.dashboard { border-left-color: #dc3545; }
        .counter {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
        .counter.me { background: #28a745; }
        .counter.stations { background: #ffc107; color: black; }
        .counter.dashboard { background: #dc3545; }
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .clear-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Calls Monitor</h1>
        <p>监控dashboard页面刷新时的API调用情况</p>
        
        <button class="clear-btn" onclick="clearLogs()">清除日志</button>
        
        <div class="summary">
            <h3>调用统计</h3>
            <div>
                /auth/me 接口: <span class="counter me" id="me-count">0</span>
            </div>
            <div>
                /stations 接口: <span class="counter stations" id="stations-count">0</span>
            </div>
            <div>
                /dashboard 接口: <span class="counter dashboard" id="dashboard-count">0</span>
            </div>
        </div>
        
        <div id="api-logs">
            <!-- API调用日志将显示在这里 -->
        </div>
    </div>

    <script>
        let apiCalls = {
            me: 0,
            stations: 0,
            dashboard: 0
        };

        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            const timestamp = new Date().toLocaleTimeString();
            
            // 检查URL类型
            let apiType = 'other';
            if (url.includes('/auth/me')) {
                apiType = 'me';
                apiCalls.me++;
            } else if (url.includes('/stations')) {
                apiType = 'stations';
                apiCalls.stations++;
            } else if (url.includes('/dashboard')) {
                apiType = 'dashboard';
                apiCalls.dashboard++;
            }
            
            // 记录API调用
            if (apiType !== 'other') {
                logApiCall(apiType, url, timestamp);
                updateCounters();
            }
            
            return originalFetch.apply(this, args);
        };

        function logApiCall(type, url, timestamp) {
            const logsContainer = document.getElementById('api-logs');
            const logEntry = document.createElement('div');
            logEntry.className = `api-call ${type}`;
            logEntry.innerHTML = `
                <strong>[${timestamp}]</strong> 
                <span style="color: #666;">${type.toUpperCase()}</span> 
                ${url}
            `;
            logsContainer.appendChild(logEntry);
            
            // 自动滚动到底部
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function updateCounters() {
            document.getElementById('me-count').textContent = apiCalls.me;
            document.getElementById('stations-count').textContent = apiCalls.stations;
            document.getElementById('dashboard-count').textContent = apiCalls.dashboard;
        }

        function clearLogs() {
            document.getElementById('api-logs').innerHTML = '';
            apiCalls = { me: 0, stations: 0, dashboard: 0 };
            updateCounters();
        }

        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            const logsContainer = document.getElementById('api-logs');
            const welcomeMsg = document.createElement('div');
            welcomeMsg.style.cssText = 'text-align: center; color: #666; padding: 20px; font-style: italic;';
            welcomeMsg.textContent = '等待API调用...请在另一个标签页打开 http://localhost:3001/dashboard';
            logsContainer.appendChild(welcomeMsg);
        });
    </script>
</body>
</html>
