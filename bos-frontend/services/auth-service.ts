import {
  LoginRequest,
  LoginResponse,
  LoginResponseData,
  RefreshTokenResponseData,
  ChangePasswordRequest,
  BosApiResponse
} from '@/types/auth';
import { User, UserStationInfo } from '@/types/user';
import {
  parseAddress,
  parseContactInfo,
  parseBusinessHours,
  parseAdministrativeDivision,
  formatAddress,
  formatShortAddress
} from '@/utils/station-data-parser';

// 统一的油站信息解析函数
function parseStationInfoFromAuth(stationInfo: any): UserStationInfo {
  try {
    const stationData = stationInfo.station;

    // 使用统一的解析工具解析地址信息
    const parsedAddress = parseAddress(stationData.address);
    const addressInfo = {
      country: parsedAddress?.country || 'Indonesia',
      province: parsedAddress?.province || 'Jakarta',
      city: parsedAddress?.city || 'Jakarta',
      district: parsedAddress?.district || 'Unknown',
      street: parsedAddress?.street || (typeof stationData.address === 'string' ? stationData.address : 'Unknown'),
      postal_code: parsedAddress?.postal_code || '00000',
      building: parsedAddress?.building,
      floor: parsedAddress?.floor,
      unit: parsedAddress?.unit,
    };

    // 解析其他信息
    const parsedContactInfo = parseContactInfo(stationData.contact_info);
    const contactInfo = {
      phone: parsedContactInfo?.phone || '',
      email: parsedContactInfo?.email || '',
      fax: parsedContactInfo?.fax,
      emergency_contact: parsedContactInfo?.emergency_contact,
    };

    const parsedBusinessHours = parseBusinessHours(stationData.business_hours);
    const defaultDayHours = { open: '08:00', close: '22:00', is_open: true };
    const businessHours = {
      monday: parsedBusinessHours?.monday || defaultDayHours,
      tuesday: parsedBusinessHours?.tuesday || defaultDayHours,
      wednesday: parsedBusinessHours?.wednesday || defaultDayHours,
      thursday: parsedBusinessHours?.thursday || defaultDayHours,
      friday: parsedBusinessHours?.friday || defaultDayHours,
      saturday: parsedBusinessHours?.saturday || defaultDayHours,
      sunday: parsedBusinessHours?.sunday || defaultDayHours,
      holidays: parsedBusinessHours?.holidays || defaultDayHours,
    };

    const parsedAdminDivision = parseAdministrativeDivision(stationData.administrative_division);
    const administrativeDivision = {
      province_code: parsedAdminDivision?.province_code || '',
      city_code: parsedAdminDivision?.city_code || '',
      district_code: parsedAdminDivision?.district_code || '',
      village_code: parsedAdminDivision?.village_code || '',
    };

    // 构建符合Station接口的station对象
    const station = {
      id: stationData.id,
      site_code: stationData.site_code,
      site_name: stationData.site_name,
      address: addressInfo,
      latitude: stationData.latitude || 0,
      longitude: stationData.longitude || 0,
      administrative_division: administrativeDivision,
      business_hours: businessHours,
      business_status: stationData.business_status || 'ACTIVE',
      contact_info: contactInfo,
      manager_id: stationData.manager_id || 0,
      management_level: stationData.management_level || 'LEVEL_1',
      parent_organization: stationData.parent_organization || '',
      created_at: stationData.created_at || new Date().toISOString(),
      updated_at: stationData.updated_at || new Date().toISOString(),
      created_by: stationData.created_by || 0,
      updated_by: stationData.updated_by || 0,
      version: stationData.version || 1,
    };

    return {
      stationId: stationData.id,
      station: station,
      roleType: stationInfo.roleType as any,
      isDefault: stationInfo.isDefault || false,
      hasManagePermission: stationInfo.hasManagePermission || false
    };
  } catch (error) {
    console.warn('解析油站信息失败:', error);
    // 返回默认值
    return {
      stationId: stationInfo.stationId || 0,
      station: {
        id: stationInfo.stationId || 0,
        site_code: 'UNKNOWN',
        site_name: 'Unknown Station',
        address: {
          country: 'Indonesia',
          province: 'Jakarta',
          city: 'Jakarta',
          district: 'Unknown',
          street: 'Unknown',
          postal_code: '00000',
        },
        latitude: 0,
        longitude: 0,
        administrative_division: {
          province_code: '',
          city_code: '',
          district_code: '',
          village_code: '',
        },
        business_hours: {
          monday: { open: '08:00', close: '22:00', is_open: true },
          tuesday: { open: '08:00', close: '22:00', is_open: true },
          wednesday: { open: '08:00', close: '22:00', is_open: true },
          thursday: { open: '08:00', close: '22:00', is_open: true },
          friday: { open: '08:00', close: '22:00', is_open: true },
          saturday: { open: '08:00', close: '22:00', is_open: true },
          sunday: { open: '08:00', close: '22:00', is_open: true },
          holidays: { open: '08:00', close: '22:00', is_open: true },
        },
        business_status: 'ACTIVE',
        contact_info: {
          phone: '',
          email: '',
        },
        manager_id: 0,
        management_level: 'LEVEL_1',
        parent_organization: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: 0,
        updated_by: 0,
        version: 1,
      },
      roleType: stationInfo.roleType as any,
      isDefault: stationInfo.isDefault || false,
      hasManagePermission: stationInfo.hasManagePermission || false
    };
  }
}

// API 基础 URL - 统一使用8080端口
const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api/v1';

// 通用 API 请求方法
async function apiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<BosApiResponse<T>> {
  const url = `${API_BASE}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
  };

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    // 检查 BOS API 响应格式
    if (data.code !== 0) {
      // API 返回业务错误
      throw new Error(data.message || 'API request failed');
    }

    return data;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Network error or invalid response');
  }
}

// 带认证的 API 请求方法
async function authenticatedApiRequest<T>(
  endpoint: string, 
  options: RequestInit = {},
  token?: string
): Promise<BosApiResponse<T>> {
  // 获取 token（优先使用参数传入的，否则从 localStorage 获取）
  const accessToken = token || (typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null);
  
  const authHeaders: Record<string, string> = accessToken ? {
    'Authorization': `Bearer ${accessToken}`
  } : {};

  return apiRequest<T>(endpoint, {
    ...options,
    headers: {
      ...options.headers,
      ...authHeaders,
    },
  });
}

// 登录服务
export async function loginService(credentials: LoginRequest): Promise<{ data: LoginResponse }> {
  try {
    const response = await apiRequest<LoginResponseData>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        username: credentials.username,
        password: credentials.password,
        system: credentials.system,
        authType: credentials.authType || 'local',
        rememberMe: credentials.rememberMe || false,
        ...(credentials.captcha && { captcha: credentials.captcha }),
        ...(credentials.captchaId && { captchaId: credentials.captchaId }),
      }),
    });

    console.log('Login API 响应:', response);

    // 新的API响应格式：user对象中直接包含sites和stations
    const { systemAccess, user } = response.data;

    // 解析新API响应中的站点数据
    const stations: UserStationInfo[] = [];

    console.log('用户站点数据:', { sites: user.sites, stations: user.stations });

    // 新的API响应格式：user.stations 包含完整的站点信息
    if (user.stations && Array.isArray(user.stations)) {
      user.stations.forEach((stationInfo: any, index: number) => {
        try {
          // 使用统一的解析函数
          const stationData = parseStationInfoFromAuth({
            ...stationInfo,
            roleType: stationInfo.roleType || systemAccess.accessLevel,
            isDefault: stationInfo.isDefault || index === 0,
            hasManagePermission: stationInfo.hasManagePermission || ['manager', 'assistant_manager', 'supervisor'].includes(systemAccess.accessLevel)
          });

          stations.push(stationData);
          console.log(`解析站点 ${stationInfo.stationId}:`, stationData);
        } catch (error) {
          console.error(`解析站点 ${stationInfo.stationId} 数据失败:`, error);
        }
      });
    }

    // 如果没有站点数据，但有systemAccess.stationIds，创建基本的站点信息
    if (stations.length === 0 && systemAccess.stationIds && systemAccess.stationIds.length > 0) {
      console.log('没有详细站点数据，使用systemAccess.stationIds创建基本信息');
      systemAccess.stationIds.forEach((stationId: number, index: number) => {
        stations.push({
          stationId,
          station: {
            id: stationId,
            site_name: `Station ${stationId}`,
            site_code: `ST${stationId.toString().padStart(3, '0')}`,
            address: {
              country: 'Indonesia',
              province: 'Jakarta',
              city: 'Jakarta',
              district: 'Unknown',
              street: 'Unknown',
              postal_code: '00000',
            },
            latitude: 0,
            longitude: 0,
            administrative_division: {},
            business_hours: {
              monday: { open: '08:00', close: '22:00', is_open: true },
              tuesday: { open: '08:00', close: '22:00', is_open: true },
              wednesday: { open: '08:00', close: '22:00', is_open: true },
              thursday: { open: '08:00', close: '22:00', is_open: true },
              friday: { open: '08:00', close: '22:00', is_open: true },
              saturday: { open: '08:00', close: '22:00', is_open: true },
              sunday: { open: '08:00', close: '22:00', is_open: true },
              holidays: { open: '08:00', close: '22:00', is_open: true },
            },
            business_status: 'ACTIVE',
            contact_info: { phone: '', email: '' },
            manager_id: 0,
            management_level: 'LEVEL_1',
            parent_organization: '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            created_by: 0,
            updated_by: 0,
            version: 1,
          },
          roleType: systemAccess.accessLevel,
          isDefault: index === 0,
          hasManagePermission: ['manager', 'assistant_manager', 'supervisor'].includes(systemAccess.accessLevel)
        });
      });
    }

    console.log('解析后的站点数据:', stations);

    // 转换 API 响应为前端需要的格式
    const loginResponse: LoginResponse = {
      accessToken: response.data.accessToken,
      refreshToken: response.data.refreshToken,
      tokenType: response.data.tokenType,
      expiresIn: response.data.expiresIn,
      systemAccess: response.data.systemAccess,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        employeeNo: user.username, // 假设用户名即员工编号
        firstName: user.fullName?.split(' ')[0] || '',
        lastName: user.fullName?.split(' ').slice(1).join(' ') || '',
        status: 'active' as const,
        createdAt: new Date(),
        updatedAt: new Date(user.lastLoginAt || new Date().toISOString()),
        roles: user.roles || [],
        permissions: user.permissions || [],
        // 兼容旧版本的 sites 字段 - 使用API返回的sites数据或从stations构造
        sites: user.sites || stations.map(station => ({
          siteId: station.stationId.toString(),
          siteName: station.station.site_name,
          role: station.roleType,
          isDefault: station.isDefault,
        })),
        // 新的站点详细信息字段
        stations: stations,
        // 设置默认活动站点
        activeStation: stations.length > 0 ? stations[0] : undefined,
        preferences: {
          theme: 'system' as const,
          language: user.language || 'en',
          timeZone: 'Asia/Jakarta',
          notifications: {
            email: true,
            push: true,
            sms: false
          },
          sidebarCollapsed: false
        }
      }
    };

    console.log('最终登录响应:', loginResponse);

    return { data: loginResponse };
  } catch (error) {
    console.error('Login API error:', error);
    throw new Error(error instanceof Error ? error.message : 'Login failed');
  }
}

// 登出服务
export async function logoutService(): Promise<void> {
  try {
    await authenticatedApiRequest('/auth/logout', {
      method: 'POST',
    });
  } catch (error) {
    // 即使登出 API 失败，也继续清除本地存储
    console.error('Logout API error:', error);
  }
}

// 刷新令牌服务
export async function refreshTokenService(refreshToken: string): Promise<RefreshTokenResponseData> {
  try {
    const response = await apiRequest<RefreshTokenResponseData>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({
        refreshToken
      }),
    });

    return response.data;
  } catch (error) {
    console.error('Refresh token API error:', error);
    throw new Error(error instanceof Error ? error.message : 'Token refresh failed');
  }
}

// 获取当前用户信息服务
export async function getCurrentUserService(token: string): Promise<{ data: { user: User, roles: string[], permissions: string[] } }> {
  try {
    // 调用新的 /auth/me 接口，该接口现在返回完整的用户信息包括站点数据
    const response = await authenticatedApiRequest<{
      id: string;
      username: string;
      email: string;
      fullName: string;
      phone?: string;
      status: string;
      roles: string[];
      permissions: string[];
      lastLoginAt: string;
      language?: string;
      sites?: any[];
      stations?: any[];
      systemAccess?: any;
    }>('/auth/me', {
      method: 'GET',
    }, token);

    console.log('/auth/me API 响应:', response);

    // 解析站点数据
    const stations: UserStationInfo[] = [];

    if (response.data.stations && Array.isArray(response.data.stations)) {
      response.data.stations.forEach((stationInfo: any, index: number) => {
        try {
          // 使用统一的解析函数
          const stationData = parseStationInfoFromAuth({
            ...stationInfo,
            roleType: stationInfo.roleType || 'manager',
            isDefault: stationInfo.isDefault || index === 0,
            hasManagePermission: stationInfo.hasManagePermission || true
          });

          stations.push(stationData);
          console.log(`解析站点 ${stationInfo.stationId} 成功，站点名称: ${stationData.station.site_name}`);
        } catch (error) {
          console.error(`解析站点 ${stationInfo.stationId} 数据失败:`, error);
        }
      });
    }

    // 转换为前端格式
    const user: User = {
      id: response.data.id,
      username: response.data.username,
      email: response.data.email,
      fullName: response.data.fullName,
      employeeNo: response.data.username,
      firstName: response.data.fullName?.split(' ')[0] || '',
      lastName: response.data.fullName?.split(' ').slice(1).join(' ') || '',
      status: 'active' as const,
      createdAt: new Date(),
      updatedAt: new Date(response.data.lastLoginAt || new Date().toISOString()),
      roles: response.data.roles || [],
      permissions: response.data.permissions || [],
      // 使用API返回的sites数据或从stations构造
      sites: response.data.sites || stations.map(station => ({
        siteId: station.stationId.toString(),
        siteName: station.station.site_name,
        role: station.roleType,
        isDefault: station.isDefault,
      })),
      // 站点详细信息
      stations: stations,
      // 设置默认活动站点
      activeStation: stations.length > 0 ? stations[0] : undefined,
      preferences: {
        theme: 'system' as const,
        language: response.data.language || 'en',
        timeZone: 'Asia/Jakarta',
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        sidebarCollapsed: false
      }
    };

    console.log('getCurrentUser 解析后的用户数据:', user);

    return {
      data: {
        user,
        roles: response.data.roles || [],
        permissions: response.data.permissions || []
      }
    };
  } catch (error) {
    console.error('Get user profile API error:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to get user profile');
  }
}

// 修改密码服务
export async function changePasswordService(passwordData: ChangePasswordRequest): Promise<void> {
  try {
    await authenticatedApiRequest('/auth/password', {
      method: 'PUT',
      body: JSON.stringify(passwordData),
    });
  } catch (error) {
    console.error('Change password API error:', error);
    throw new Error(error instanceof Error ? error.message : 'Password change failed');
  }
}

// API 登录函数
export async function apiLogin(credentials: LoginRequest): Promise<LoginResponse> {
  const response = await loginService(credentials);
  return response.data;
}

// API 登出函数
export async function apiLogout(): Promise<void> {
  await logoutService();
}

// API 刷新令牌函数
export async function apiRefreshToken(refreshToken: string): Promise<RefreshTokenResponseData | null> {
  try {
    const response = await refreshTokenService(refreshToken);
    return response;
  } catch (error) {
    console.error('Refresh token error:', error);
    return null;
  }
}

// 获取用户资料函数
export async function fetchUserProfile(token: string): Promise<User> {
  const response = await getCurrentUserService(token);
  console.log('fetchUserProfile 返回的用户数据:', response.data.user);
  return response.data.user;
}

// 更新用户资料函数
export async function updateUserProfile(userData: Partial<User>): Promise<User> {
  try {
    // 注意：需要根据实际的用户更新 API 接口调整
    const response = await authenticatedApiRequest<User>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });

    return response.data;
  } catch (error) {
    console.error('Update user profile API error:', error);
    throw new Error(error instanceof Error ? error.message : 'Profile update failed');
  }
}

// 修改密码函数
export async function changePassword(passwordData: ChangePasswordRequest): Promise<void> {
  await changePasswordService(passwordData);
}