/**
 * 油品颜色配置
 * 定义不同油品类型的颜色标识
 */

export interface FuelColorConfig {
  primary: string;      // 主色
  secondary: string;    // 次色（浅色背景）
  text: string;         // 文字颜色
  border: string;       // 边框颜色
}

/**
 * 油品颜色映射
 * 92号汽油：绿色
 * Ultimate：橙色
 * Diesel：黑色
 */
export const FUEL_COLORS: Record<string, FuelColorConfig> = {
  // 92号汽油 - 绿色系
  '92': {
    primary: '#22C55E',      // 绿色
    secondary: '#F0FDF4',    // 浅绿色背景
    text: '#15803D',         // 深绿色文字
    border: '#22C55E'        // 绿色边框
  },
  'BP92': {
    primary: '#22C55E',
    secondary: '#F0FDF4',
    text: '#15803D',
    border: '#22C55E'
  },
  'RON92': {
    primary: '#22C55E',
    secondary: '#F0FDF4',
    text: '#15803D',
    border: '#22C55E'
  },

  // Ultimate - 橙色系
  'Ultimate': {
    primary: '#F97316',      // 橙色
    secondary: '#FFF7ED',    // 浅橙色背景
    text: '#C2410C',         // 深橙色文字
    border: '#F97316'        // 橙色边框
  },
  'BP95': {
    primary: '#F97316',
    secondary: '#FFF7ED',
    text: '#C2410C',
    border: '#F97316'
  },
  'RON95': {
    primary: '#F97316',
    secondary: '#FFF7ED',
    text: '#C2410C',
    border: '#F97316'
  },
  '95': {
    primary: '#F97316',
    secondary: '#FFF7ED',
    text: '#C2410C',
    border: '#F97316'
  },

  // Diesel - 黑色系
  'Diesel': {
    primary: '#374151',      // 深灰色（接近黑色）
    secondary: '#F9FAFB',    // 浅灰色背景
    text: '#111827',         // 黑色文字
    border: '#374151'        // 深灰色边框
  },
  'diesel': {
    primary: '#374151',
    secondary: '#F9FAFB',
    text: '#111827',
    border: '#374151'
  },
  'BP_Diesel': {
    primary: '#374151',
    secondary: '#F9FAFB',
    text: '#111827',
    border: '#374151'
  }
};

/**
 * 根据油品名称获取颜色配置
 * @param fuelGrade 油品等级/名称
 * @returns 颜色配置对象
 */
export const getFuelColor = (fuelGrade: string): FuelColorConfig => {
  // 标准化油品名称
  const normalizedGrade = normalizeFuelGrade(fuelGrade);
  
  // 查找匹配的颜色配置
  for (const [key, config] of Object.entries(FUEL_COLORS)) {
    if (normalizedGrade.includes(key.toLowerCase()) || 
        key.toLowerCase().includes(normalizedGrade)) {
      return config;
    }
  }
  
  // 默认颜色（灰色）
  return {
    primary: '#6B7280',
    secondary: '#F9FAFB',
    text: '#374151',
    border: '#6B7280'
  };
};

/**
 * 标准化油品名称
 * @param fuelGrade 原始油品名称
 * @returns 标准化后的名称
 */
export const normalizeFuelGrade = (fuelGrade: string): string => {
  if (!fuelGrade) return '';
  
  const normalized = fuelGrade.toLowerCase().trim();
  
  // 处理常见的油品名称变体
  if (normalized.includes('92') || normalized.includes('ron92')) {
    return '92';
  }
  if (normalized.includes('95') || normalized.includes('ron95') || 
      normalized.includes('ultimate')) {
    return 'ultimate';
  }
  if (normalized.includes('diesel') || normalized.includes('solar')) {
    return 'diesel';
  }
  
  return normalized;
};

/**
 * 获取油品显示名称
 * @param fuelGrade 油品等级
 * @returns 标准化的显示名称
 */
export const getFuelDisplayName = (fuelGrade: string): string => {
  const normalized = normalizeFuelGrade(fuelGrade);
  
  switch (normalized) {
    case '92':
      return 'BP 92';
    case 'ultimate':
      return 'BP Ultimate';
    case 'diesel':
      return 'BP Diesel';
    default:
      return fuelGrade;
  }
};

/**
 * 默认的BP品牌颜色
 */
export const BP_BRAND_COLORS = {
  primary: '#00A651',      // BP绿色
  secondary: '#FFE500',    // BP黄色
  accent: '#005A2B',       // 深绿色
  text: '#000000',         // 黑色文字
  background: '#FFFFFF'    // 白色背景
};
