/**
 * API辅助工具函数
 * 用于处理HOS系统的多站点管理和API调用
 */

import { SystemType } from '@/lib/config';

// API查询参数接口
export interface ApiQueryParams {
  [key: string]: string | number | boolean | undefined;
}

// 站点筛选参数接口
export interface SiteFilterParams {
  site_ids?: string; // 逗号分隔的站点ID列表
  station_id?: number; // 单个站点ID（向后兼容）
  station_ids?: string; // 逗号分隔的站点ID列表（向后兼容）
}

/**
 * 根据系统类型和站点选择构建API查询参数
 * @param baseParams 基础查询参数
 * @param systemType 当前系统类型
 * @param selectedSiteIds 选择的站点ID列表，null表示所有站点
 * @returns 包含站点筛选的完整查询参数
 */
export function buildApiParams(
  baseParams: ApiQueryParams = {},
  systemType: SystemType,
  selectedSiteIds: number[] | null = null
): ApiQueryParams & SiteFilterParams {
  const params: ApiQueryParams & SiteFilterParams = { ...baseParams };

  // HOS系统：添加多站点筛选参数
  if (systemType === 'HOS' && selectedSiteIds && selectedSiteIds.length > 0) {
    // 使用site_ids参数传递多个站点ID
    params.site_ids = selectedSiteIds.join(',');
    
    // 向后兼容：如果只有一个站点，也设置station_id
    if (selectedSiteIds.length === 1) {
      params.station_id = selectedSiteIds[0];
    }
  }
  
  // BOS/EDC系统：使用单站点参数（如果提供）
  else if (systemType !== 'HOS' && selectedSiteIds && selectedSiteIds.length > 0) {
    params.station_id = selectedSiteIds[0]; // BOS/EDC只使用第一个站点
  }

  return params;
}

/**
 * 构建查询字符串
 * @param params 查询参数对象
 * @returns URL查询字符串
 */
export function buildQueryString(params: ApiQueryParams): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
}

/**
 * 构建完整的API URL
 * @param baseUrl API基础URL
 * @param endpoint API端点
 * @param params 查询参数
 * @returns 完整的API URL
 */
export function buildApiUrl(
  baseUrl: string,
  endpoint: string,
  params: ApiQueryParams = {}
): string {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const queryString = buildQueryString(params);
  return `${baseUrl}${cleanEndpoint}${queryString}`;
}

/**
 * 创建带有站点筛选的API请求配置
 * @param systemType 当前系统类型
 * @param selectedSiteIds 选择的站点ID列表
 * @param additionalParams 额外的查询参数
 * @returns API请求配置对象
 */
export function createSiteFilteredApiConfig(
  systemType: SystemType,
  selectedSiteIds: number[] | null,
  additionalParams: ApiQueryParams = {}
) {
  const params = buildApiParams(additionalParams, systemType, selectedSiteIds);
  
  return {
    params,
    queryString: buildQueryString(params),
    headers: {
      'Content-Type': 'application/json',
      // 可以在这里添加其他通用头部
    }
  };
}

/**
 * 获取认证头部
 * @returns 包含认证信息的头部对象
 */
export function getAuthHeaders(): Record<string, string> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('accessToken');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  return headers;
}

/**
 * 创建完整的API请求配置
 * @param method HTTP方法
 * @param systemType 当前系统类型
 * @param selectedSiteIds 选择的站点ID列表
 * @param additionalParams 额外的查询参数
 * @param body 请求体（可选）
 * @returns 完整的fetch请求配置
 */
export function createApiRequestConfig(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  systemType: SystemType,
  selectedSiteIds: number[] | null,
  additionalParams: ApiQueryParams = {},
  body?: any
): RequestInit {
  const config = createSiteFilteredApiConfig(systemType, selectedSiteIds, additionalParams);
  
  const requestConfig: RequestInit = {
    method,
    headers: {
      ...getAuthHeaders(),
      ...config.headers,
    },
  };

  if (body && method !== 'GET') {
    requestConfig.body = JSON.stringify(body);
  }

  return requestConfig;
}

/**
 * 执行带有站点筛选的API请求
 * @param url API URL
 * @param method HTTP方法
 * @param systemType 当前系统类型
 * @param selectedSiteIds 选择的站点ID列表
 * @param additionalParams 额外的查询参数
 * @param body 请求体（可选）
 * @returns Promise<Response>
 */
export async function fetchWithSiteFilter(
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  systemType: SystemType,
  selectedSiteIds: number[] | null,
  additionalParams: ApiQueryParams = {},
  body?: any
): Promise<Response> {
  const config = createSiteFilteredApiConfig(systemType, selectedSiteIds, additionalParams);
  const fullUrl = `${url}${config.queryString}`;
  
  const requestConfig = createApiRequestConfig(method, systemType, selectedSiteIds, {}, body);
  
  return fetch(fullUrl, requestConfig);
}

/**
 * 解析站点ID列表字符串
 * @param siteIdsString 逗号分隔的站点ID字符串
 * @returns 站点ID数组
 */
export function parseSiteIds(siteIdsString?: string): number[] {
  if (!siteIdsString) return [];
  
  return siteIdsString
    .split(',')
    .map(id => parseInt(id.trim()))
    .filter(id => !isNaN(id));
}

/**
 * 格式化站点ID列表为字符串
 * @param siteIds 站点ID数组
 * @returns 逗号分隔的站点ID字符串
 */
export function formatSiteIds(siteIds: number[]): string {
  return siteIds.join(',');
}
