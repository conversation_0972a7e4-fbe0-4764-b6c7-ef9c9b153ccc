// 站点数据解析工具函数

import { Station } from '@/types/station';

// 地址信息接口
export interface ParsedAddress {
  country?: string;
  province?: string;
  city?: string;
  district?: string;
  street?: string;
  postal_code?: string;
  building?: string;
  floor?: string;
  unit?: string;
}

// 联系信息接口
export interface ParsedContactInfo {
  phone?: string;
  email?: string;
  fax?: string;
  emergency_contact?: string;
}

// 营业时间接口
export interface ParsedBusinessHours {
  monday?: DayHours;
  tuesday?: DayHours;
  wednesday?: DayHours;
  thursday?: DayHours;
  friday?: DayHours;
  saturday?: DayHours;
  sunday?: DayHours;
  holidays?: DayHours;
}

export interface DayHours {
  open: string;
  close: string;
  is_open: boolean;
}

// 行政区划信息接口
export interface ParsedAdministrativeDivision {
  province_code?: string;
  city_code?: string;
  district_code?: string;
  village_code?: string;
}

// 安全解析JSON字符串或对象
function safeParseJSON<T>(input: string | object | null | undefined): T | null {
  if (!input) {
    return null;
  }

  // 如果已经是对象，直接返回
  if (typeof input === 'object') {
    return input as T;
  }

  // 如果是字符串，尝试解析JSON
  if (typeof input === 'string') {
    try {
      return JSON.parse(input) as T;
    } catch (error) {
      console.warn('Failed to parse JSON:', input, error);
      return null;
    }
  }

  return null;
}

// 解析地址信息
export function parseAddress(addressInput: string | object | null | undefined): ParsedAddress | null {
  return safeParseJSON<ParsedAddress>(addressInput);
}

// 解析联系信息
export function parseContactInfo(contactInput: string | object | null | undefined): ParsedContactInfo | null {
  return safeParseJSON<ParsedContactInfo>(contactInput);
}

// 解析营业时间
export function parseBusinessHours(businessHoursInput: string | object | null | undefined): ParsedBusinessHours | null {
  return safeParseJSON<ParsedBusinessHours>(businessHoursInput);
}

// 解析行政区划信息
export function parseAdministrativeDivision(divisionInput: string | object | null | undefined): ParsedAdministrativeDivision | null {
  return safeParseJSON<ParsedAdministrativeDivision>(divisionInput);
}

// 格式化地址为可读字符串
export function formatAddress(address: ParsedAddress | null): string {
  if (!address) {
    return 'Address not available';
  }
  
  const addressParts = [
    address.building,
    address.floor && address.unit ? `${address.floor}, ${address.unit}` : address.floor || address.unit,
    address.street,
    address.district,
    address.city,
    address.province,
    address.country
  ].filter(Boolean);
  
  return addressParts.length > 0 ? addressParts.join(', ') : 'Address not available';
}

// 格式化简短地址（只包含城市和省份）
export function formatShortAddress(address: ParsedAddress | null): string {
  if (!address) {
    return 'Location not available';
  }
  
  const locationParts = [address.city, address.province].filter(Boolean);
  return locationParts.length > 0 ? locationParts.join(', ') : 'Location not available';
}

// 获取站点显示名称
export function getStationDisplayName(station: any): string {
  return station?.site_name || station?.name || 'BP Site';
}

// 获取站点编码
export function getStationCode(station: any): string {
  return station?.site_code || station?.code || station?.id?.toString() || 'N/A';
}

// 解析完整的站点数据
export function parseStationData(station: any) {
  if (!station) {
    return null;
  }
  
  const address = parseAddress(station.address);
  const contactInfo = parseContactInfo(station.contact_info);
  const businessHours = parseBusinessHours(station.business_hours);
  const administrativeDivision = parseAdministrativeDivision(station.administrative_division);
  
  return {
    ...station,
    parsedAddress: address,
    parsedContactInfo: contactInfo,
    parsedBusinessHours: businessHours,
    parsedAdministrativeDivision: administrativeDivision,
    displayName: getStationDisplayName(station),
    displayCode: getStationCode(station),
    formattedAddress: formatAddress(address),
    formattedShortAddress: formatShortAddress(address)
  };
}

// 获取营业状态文本
export function getBusinessStatusText(status: string): string {
  switch (status?.toUpperCase()) {
    case 'ACTIVE':
      return 'Active';
    case 'SUSPENDED':
      return 'Suspended';
    case 'CLOSED':
      return 'Closed';
    default:
      return 'Unknown';
  }
}

// 获取管理级别文本
export function getManagementLevelText(level: string): string {
  switch (level?.toUpperCase()) {
    case 'LEVEL_1':
      return 'Level 1';
    case 'LEVEL_2':
      return 'Level 2';
    case 'LEVEL_3':
      return 'Level 3';
    default:
      return 'Unknown';
  }
} 