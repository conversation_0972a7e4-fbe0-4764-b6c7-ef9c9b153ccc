-- 验证End of Day Report中gross_amount和net_amount计算逻辑的SQL查询
-- 这个查询用于检查汇总表和实时计算的差异

-- 1. 查找一个有折扣交易的已结束班次
WITH sample_shift AS (
    SELECT s.id as shift_id, s.shift_number, s.station_id, s.start_time, s.end_time
    FROM order_schema.shifts s
    WHERE s.end_time IS NOT NULL  -- 已结束的班次
      AND s.start_time >= CURRENT_DATE - INTERVAL '7 days'  -- 最近7天
    ORDER BY s.start_time DESC
    LIMIT 1
),

-- 2. 获取该班次的汇总表数据
summary_data AS (
    SELECT 
        sfd.shift_id,
        sfd.fuel_grade,
        sfd.total_volume,
        sfd.gross_amount as summary_gross_amount,
        sfd.total_discount_amount as summary_discount_amount,
        sfd.net_amount as summary_net_amount,
        sfd.transaction_count as summary_transaction_count
    FROM order_schema.shift_fuel_details sfd
    JOIN sample_shift ss ON sfd.shift_id = ss.shift_id
    WHERE sfd.employee_id IS NOT NULL  -- 只看有员工ID的记录
),

-- 3. 获取该班次的实时计算数据（模拟实时逻辑）
realtime_data AS (
    SELECT 
        ss.shift_id,
        ft.fuel_grade,
        SUM(ft.volume) as total_volume,
        -- 实时逻辑：gross_amount = ft.amount + discount_amount
        SUM(ft.amount + COALESCE(oi.discount_amount, 0)) as realtime_gross_amount,
        SUM(COALESCE(oi.discount_amount, 0)) as realtime_discount_amount,
        -- 实时逻辑：net_amount = ft.amount
        SUM(ft.amount) as realtime_net_amount,
        COUNT(*) as realtime_transaction_count
    FROM order_schema.fuel_transactions ft
    JOIN sample_shift ss ON ft.station_id = ss.station_id 
        AND ft.created_at BETWEEN ss.start_time AND ss.end_time
    LEFT JOIN order_schema.fuel_transaction_order_links ftol ON ft.id = ftol.fuel_transaction_id
    LEFT JOIN order_schema.orders o ON ftol.order_id = o.id
    LEFT JOIN order_schema.order_items oi ON o.id = oi.order_id AND oi.product_type = 'fuel'
    WHERE ft.status = 'processed'
      AND ft.employee_id IS NOT NULL
    GROUP BY ss.shift_id, ft.fuel_grade
),

-- 4. 获取当前汇总表SQL的计算结果（问题逻辑）
current_summary_logic AS (
    SELECT 
        ss.shift_id,
        ft.fuel_grade,
        SUM(ft.volume) as total_volume,
        -- 当前汇总表逻辑：直接使用ft.amount作为gross_amount（错误）
        SUM(ft.amount) as current_gross_amount,
        SUM(COALESCE(oi.discount_amount, 0)) as current_discount_amount,
        -- 当前汇总表逻辑：net_amount = gross_amount - discount（错误）
        SUM(ft.amount) - SUM(COALESCE(oi.discount_amount, 0)) as current_net_amount,
        COUNT(*) as current_transaction_count
    FROM order_schema.fuel_transactions ft
    JOIN sample_shift ss ON ft.station_id = ss.station_id 
        AND ft.created_at BETWEEN ss.start_time AND ss.end_time
    LEFT JOIN order_schema.fuel_transaction_order_links ftol ON ft.id = ftol.fuel_transaction_id
    LEFT JOIN order_schema.orders o ON ftol.order_id = o.id
    LEFT JOIN order_schema.order_items oi ON o.id = oi.order_id AND oi.product_type = 'fuel'
    WHERE ft.status = 'processed'
      AND ft.employee_id IS NOT NULL
    GROUP BY ss.shift_id, ft.fuel_grade
)

-- 5. 对比结果
SELECT 
    ss.shift_id,
    ss.shift_number,
    COALESCE(sd.fuel_grade, rd.fuel_grade, csl.fuel_grade) as fuel_grade,
    
    -- 销量对比
    sd.total_volume as summary_volume,
    rd.total_volume as realtime_volume,
    csl.total_volume as current_logic_volume,
    
    -- 毛金额对比
    sd.summary_gross_amount,
    rd.realtime_gross_amount,
    csl.current_gross_amount,
    
    -- 折扣金额对比
    sd.summary_discount_amount,
    rd.realtime_discount_amount,
    csl.current_discount_amount,
    
    -- 净金额对比
    sd.summary_net_amount,
    rd.realtime_net_amount,
    csl.current_net_amount,
    
    -- 差异分析
    CASE 
        WHEN ABS(COALESCE(sd.summary_gross_amount, 0) - COALESCE(rd.realtime_gross_amount, 0)) > 0.01 
        THEN '❌ 汇总表与实时逻辑毛金额不一致'
        ELSE '✅ 汇总表与实时逻辑毛金额一致'
    END as gross_amount_check,
    
    CASE 
        WHEN ABS(COALESCE(sd.summary_net_amount, 0) - COALESCE(rd.realtime_net_amount, 0)) > 0.01 
        THEN '❌ 汇总表与实时逻辑净金额不一致'
        ELSE '✅ 汇总表与实时逻辑净金额一致'
    END as net_amount_check,
    
    CASE 
        WHEN ABS(COALESCE(csl.current_gross_amount, 0) - COALESCE(rd.realtime_gross_amount, 0)) > 0.01 
        THEN '❌ 当前汇总逻辑与实时逻辑毛金额不一致'
        ELSE '✅ 当前汇总逻辑与实时逻辑毛金额一致'
    END as current_logic_check,
    
    -- 具体差异值
    COALESCE(sd.summary_gross_amount, 0) - COALESCE(rd.realtime_gross_amount, 0) as gross_diff_summary_vs_realtime,
    COALESCE(csl.current_gross_amount, 0) - COALESCE(rd.realtime_gross_amount, 0) as gross_diff_current_vs_realtime

FROM sample_shift ss
FULL OUTER JOIN summary_data sd ON ss.shift_id = sd.shift_id
FULL OUTER JOIN realtime_data rd ON ss.shift_id = rd.shift_id AND sd.fuel_grade = rd.fuel_grade
FULL OUTER JOIN current_summary_logic csl ON ss.shift_id = csl.shift_id AND rd.fuel_grade = csl.fuel_grade
ORDER BY fuel_grade;

-- 6. 额外查询：检查是否有折扣数据
SELECT 
    '折扣数据统计' as info,
    COUNT(*) as total_transactions,
    COUNT(CASE WHEN oi.discount_amount > 0 THEN 1 END) as transactions_with_discount,
    SUM(COALESCE(oi.discount_amount, 0)) as total_discount_amount,
    AVG(COALESCE(oi.discount_amount, 0)) as avg_discount_amount
FROM sample_shift ss
JOIN order_schema.fuel_transactions ft ON ft.station_id = ss.station_id 
    AND ft.created_at BETWEEN ss.start_time AND ss.end_time
LEFT JOIN order_schema.fuel_transaction_order_links ftol ON ft.id = ftol.fuel_transaction_id
LEFT JOIN order_schema.orders o ON ftol.order_id = o.id
LEFT JOIN order_schema.order_items oi ON o.id = oi.order_id AND oi.product_type = 'fuel'
WHERE ft.status = 'processed';

-- 7. 显示样本班次信息
SELECT 
    '样本班次信息' as info,
    shift_id,
    shift_number,
    station_id,
    start_time,
    end_time,
    EXTRACT(EPOCH FROM (end_time - start_time))/3600 as duration_hours
FROM sample_shift;
