package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/api/handlers"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/service"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/types"
	orderRepository "gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	orderService "gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// 这是一个演示如何使用重构后的End of Day Report服务的示例

func main() {
	fmt.Println("=== End of Day Report Service (Redesigned) 示例 ===")

	// 1. 初始化服务依赖（这里使用模拟实现）
	shiftRepo := &MockShiftRepository{}
	shiftAttendantService := &MockShiftAttendantService{}
	stationService := &MockStationService{}

	// 2. 创建重构后的服务
	endOfDayService := service.NewEndOfDayReportServiceRedesigned(
		shiftRepo,
		shiftAttendantService,
		stationService,
	)

	// 3. 创建处理器
	handler := handlers.NewEndOfDayReportHandler(endOfDayService)

	// 4. 设置Echo服务器
	e := echo.New()
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	// 5. 注册路由
	api := e.Group("/api/v1")
	reports := api.Group("/reports")
	reports.GET("/end-of-day", handler.GetEndOfDayReport)

	// 6. 添加健康检查
	e.GET("/health", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"version":   "2.0-redesigned",
		})
	})

	// 7. 演示API调用
	go func() {
		time.Sleep(2 * time.Second) // 等待服务器启动
		demonstrateAPIUsage()
	}()

	// 8. 启动服务器
	fmt.Println("服务器启动在 :8080")
	fmt.Println("API端点: http://localhost:8080/api/v1/reports/end-of-day")
	fmt.Println("健康检查: http://localhost:8080/health")
	log.Fatal(e.Start(":8080"))
}

// demonstrateAPIUsage 演示API使用
func demonstrateAPIUsage() {
	fmt.Println("\n=== API使用演示 ===")

	// 基本查询
	testEndpoint("基本查询", "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04")

	// 带筛选条件的查询
	testEndpoint("员工筛选", "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&attendant_name=Test")

	// 油品等级筛选
	testEndpoint("油品筛选", "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&fuel_grade=BP%2092")

	// 支付方式筛选
	testEndpoint("支付方式筛选", "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&payment_method=cash")

	// 不包含汇总
	testEndpoint("不包含汇总", "http://localhost:8080/api/v1/reports/end-of-day?station_id=1&date=2024-01-04&include_summary=false")
}

// testEndpoint 测试API端点
func testEndpoint(name, url string) {
	fmt.Printf("\n--- %s ---\n", name)
	fmt.Printf("请求URL: %s\n", url)

	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("响应状态: %s\n", resp.Status)

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		return
	}

	// 打印关键信息
	if success, ok := result["success"].(bool); ok && success {
		fmt.Printf("✅ 请求成功\n")
		
		if data, ok := result["data"].(map[string]interface{}); ok {
			if shifts, ok := data["shifts"].([]interface{}); ok {
				fmt.Printf("班次数量: %d\n", len(shifts))
			}
			
			if dailySummary, ok := data["daily_summary"].(map[string]interface{}); ok {
				if totalTransactions, ok := dailySummary["total_transactions"].(float64); ok {
					fmt.Printf("总交易数: %.0f\n", totalTransactions)
				}
				if totalFuelSales, ok := dailySummary["total_fuel_sales"].(float64); ok {
					fmt.Printf("总燃油销售额: %.2f\n", totalFuelSales)
				}
			}
		}

		if meta, ok := result["meta"].(map[string]interface{}); ok {
			if processingTime, ok := meta["processing_time_ms"].(float64); ok {
				fmt.Printf("处理时间: %.0fms\n", processingTime)
			}
			if dataSource, ok := meta["data_source"].(string); ok {
				fmt.Printf("数据源: %s\n", dataSource)
			}
			if version, ok := meta["version"].(string); ok {
				fmt.Printf("版本: %s\n", version)
			}
		}
	} else {
		fmt.Printf("❌ 请求失败\n")
		if errorInfo, ok := result["error"].(map[string]interface{}); ok {
			if message, ok := errorInfo["message"].(string); ok {
				fmt.Printf("错误信息: %s\n", message)
			}
		}
	}
}

// 以下是模拟实现，用于演示

// MockShiftRepository 模拟班次仓储
type MockShiftRepository struct{}

func (m *MockShiftRepository) ListShifts(ctx context.Context, filter *orderRepository.ShiftFilter, pagination *orderRepository.Pagination, sort *orderRepository.SortOrder) ([]*orderRepository.Shift, int, error) {
	endTime := time.Now()
	shifts := []*orderRepository.Shift{
		{
			ID:          1,
			ShiftNumber: "SHIFT-1-20240104005945",
			StationID:   1,
			StartTime:   time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC),
			EndTime:     &endTime,
		},
		{
			ID:          2,
			ShiftNumber: "SHIFT-2-20240104080000",
			StationID:   1,
			StartTime:   time.Date(2024, 1, 4, 8, 0, 0, 0, time.UTC),
			EndTime:     &endTime,
		},
	}
	return shifts, len(shifts), nil
}

// 其他必需的方法（简化实现）
func (m *MockShiftRepository) CreateShift(ctx context.Context, shift *orderRepository.Shift) (*orderRepository.Shift, error) { return shift, nil }
func (m *MockShiftRepository) UpdateShift(ctx context.Context, shift *orderRepository.Shift) (*orderRepository.Shift, error) { return shift, nil }
func (m *MockShiftRepository) UpdateShiftEndTime(ctx context.Context, shiftID orderRepository.ID, endTime time.Time) (*orderRepository.Shift, error) { return nil, nil }
func (m *MockShiftRepository) GetShiftByID(ctx context.Context, id orderRepository.ID) (*orderRepository.Shift, error) { return nil, nil }
func (m *MockShiftRepository) GetShiftByNumber(ctx context.Context, shiftNumber string) (*orderRepository.Shift, error) { return nil, nil }
func (m *MockShiftRepository) FindActiveShiftByStationID(ctx context.Context, stationID orderRepository.ID) (*orderRepository.Shift, error) { return nil, nil }
func (m *MockShiftRepository) FindActiveShiftByStationIDWithLock(ctx context.Context, stationID orderRepository.ID) (*orderRepository.Shift, error) { return nil, nil }
func (m *MockShiftRepository) FindLastCompletedShiftByStationID(ctx context.Context, stationID orderRepository.ID) (*orderRepository.Shift, error) { return nil, nil }
func (m *MockShiftRepository) GetDailyShiftCount(ctx context.Context, stationID orderRepository.ID, date time.Time) (int, error) { return 0, nil }
func (m *MockShiftRepository) SoftDeleteShift(ctx context.Context, shiftID orderRepository.ID) error { return nil }
func (m *MockShiftRepository) RestoreShift(ctx context.Context, shiftID orderRepository.ID) error { return nil }

// MockShiftAttendantService 模拟班次员工服务
type MockShiftAttendantService struct{}

func (m *MockShiftAttendantService) GetShiftAttendants(ctx context.Context, request orderRepository.ShiftAttendantRequest) (orderRepository.ShiftAttendantData, error) {
	endTime := time.Now()
	
	return orderRepository.ShiftAttendantData{
		ShiftInfo: orderRepository.ShiftAttendantShiftInfo{
			ID:          request.ShiftID,
			ShiftNumber: fmt.Sprintf("SHIFT-%d-20240104005945", request.ShiftID),
			StationID:   1,
			StationName: "Test Station",
			StartTime:   time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC),
			EndTime:     &endTime,
			Status:      "closed",
		},
		Attendants: []orderRepository.ShiftAttendantInfo{
			{
				AttendantInfo: orderRepository.AttendantBasicInfo{
					AttendantName: "Test Employee",
					StaffCardID:   1,
				},
				TransactionCount: 10,
				SalesVolumeLtr:   100.5,
				SalesAmountIDR:   1500000,
				FuelSales: orderRepository.ShiftAttendantFuelSales{
					ByGrade: []orderRepository.ShiftAttendantFuelGrade{
						{
							FuelGrade:        "BP 92",
							FuelName:         "BP 92",
							FuelType:         "oil_92",
							SalesVolume:      100.5,
							GrossAmount:      1500000,
							DiscountAmount:   50000,
							NetAmount:        1450000,
							UnitPrice:        15000,
							TransactionCount: 10,
						},
					},
					Total: orderRepository.ShiftAttendantFuelTotal{
						TotalVolume:         100.5,
						TotalGrossAmount:    1500000,
						TotalDiscountAmount: 50000,
						TotalNetAmount:      1450000,
						TotalTransactions:   10,
					},
				},
				PaymentSummary: orderRepository.ShiftAttendantPayments{
					Cash:         1000000,
					NonCashTotal: 450000,
					BCA:          450000,
					ByMethod: []orderRepository.ShiftAttendantPaymentMethod{
						{
							PaymentMethod:     "cash",
							PaymentMethodName: "现金",
							TotalAmount:       1000000,
							TransactionCount:  7,
							Percentage:        69.0,
						},
						{
							PaymentMethod:     "bca",
							PaymentMethodName: "BCA",
							TotalAmount:       450000,
							TransactionCount:  3,
							Percentage:        31.0,
						},
					},
				},
				DryIncome:  0,
				GrandTotal: 1450000,
			},
		},
		ShiftSummary: orderRepository.ShiftAttendantSummary{
			TotalAttendants:   1,
			TotalTransactions: 10,
			TotalSalesVolume:  100.5,
			TotalFuelSales:    1450000,
			TotalOtherIncome:  0,
			TotalCash:         1000000,
			TotalNonCash:      450000,
			TotalBCA:          450000,
			GrandTotal:        1450000,
			ControlPoint:      "normal",
		},
	}, nil
}

// MockStationService 模拟站点服务
type MockStationService struct{}

func (m *MockStationService) GetStationByID(ctx context.Context, id orderRepository.ID) (*orderRepository.Station, error) {
	return &orderRepository.Station{
		ID:       id,
		SiteCode: "TEST001",
		SiteName: "Test Station",
	}, nil
}
