# HOS Dashboard API兼容性文档

## 概述

本文档描述了为支持HOS系统全站点模式而对Dashboard API进行的后端修改，确保API能够兼容HOS系统的多站点管理需求。

## 修改内容

### 1. Handler层修改 (`internal/api/handlers/dashboard_handler.go`)

#### parseStationID方法优化
```go
// 支持HOS全站点模式的参数解析
func (h *DashboardHandler) parseStationID(c echo.Context) (int64, error) {
    stationIDStr := c.QueryParam("station_id")
    
    // HOS系统优化：检查site_ids参数
    if stationIDStr == "" {
        siteIDsStr := c.QueryParam("site_ids")
        if siteIDsStr != "" {
            // 返回0表示全站点查询
            return 0, nil
        }
        // 传统模式：获取默认站点ID
        return h.service.GetDefaultStationID(c.Request().Context(), 1)
    }
    
    // 正常解析station_id
    // ...
}
```

**关键变化：**
- 当没有`station_id`参数但有`site_ids`参数时，返回`0`表示全站点查询
- 保持向后兼容，传统BOS/EDC模式不受影响

### 2. Service层修改 (`internal/service/dashboard_service.go`)

#### 支持全站点查询模式
```go
// GetDashboardSummary支持全站点查询
func (s *DashboardService) GetDashboardSummary(ctx context.Context, stationID int64, dateStr string) (*DashboardSummaryResponse, error) {
    // ...
    
    if stationID == 0 {
        // 全站点查询模式
        summary, err = s.repo.GetDashboardSummaryAllStations(ctx, date)
    } else {
        // 单站点查询模式
        summary, err = s.repo.GetDashboardSummary(ctx, stationID, date)
    }
    
    // ...
}
```

**修改的方法：**
- `GetDashboardSummary` - 支持全站点汇总查询
- `GetSalesTrend` - 支持全站点销售趋势查询
- `GetFuelSalesDetail` - 支持全站点燃油销售详情查询

### 3. Repository层新增 (`internal/repository/dashboard_repository.go`)

#### 新增全站点查询方法

**GetDashboardSummaryAllStations**
```go
func (r *DashboardRepository) GetDashboardSummaryAllStations(ctx context.Context, date time.Time) (*DashboardSummary, error) {
    // 查询所有站点的汇总数据，移除station_id条件
    query := `
        SELECT 
            COALESCE(SUM(amount), 0) as today_revenue,
            COUNT(*) as today_transactions,
            COALESCE(SUM(volume), 0) as today_volume
        FROM order_schema.fuel_transactions 
        WHERE created_at >= $1::timestamp AT TIME ZONE 'Asia/Jakarta'
          AND created_at < ($1::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
          AND status = 'processed'
    `
    // ...
}
```

**GetSalesTrendAllStations**
```go
func (r *DashboardRepository) GetSalesTrendAllStations(ctx context.Context, endDate time.Time, days int) (*SalesTrendData, error) {
    // 查询所有站点的销售趋势，移除station_id条件
    query := `
        SELECT 
            DATE(created_at AT TIME ZONE 'Asia/Jakarta') as date,
            COALESCE(SUM(amount), 0) as revenue,
            COUNT(*) as transactions,
            COALESCE(SUM(volume), 0) as volume
        FROM order_schema.fuel_transactions 
        WHERE DATE(created_at AT TIME ZONE 'Asia/Jakarta') >= $1
          AND DATE(created_at AT TIME ZONE 'Asia/Jakarta') <= $2
          AND status = 'processed'
        GROUP BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
        ORDER BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
    `
    // ...
}
```

**GetFuelSalesDetailAllStations**
```go
func (r *DashboardRepository) GetFuelSalesDetailAllStations(ctx context.Context, date time.Time) (*FuelSalesDetail, error) {
    // 查询所有站点的燃油销售详情，移除station_id条件
    // ...
}
```

## API调用方式

### 1. 传统BOS/EDC模式（单站点）
```bash
# 带station_id参数
GET /api/v1/dashboard/summary?station_id=1&date=2025-01-20
GET /api/v1/dashboard/sales-trend?station_id=1&date=2025-01-20&days=7
GET /api/v1/dashboard/fuel-sales-mix?station_id=1&date=2025-01-20
```

### 2. HOS全站点模式
```bash
# 不带station_id参数，或带site_ids参数
GET /api/v1/dashboard/summary?date=2025-01-20
GET /api/v1/dashboard/summary?site_ids=1,2,3&date=2025-01-20

GET /api/v1/dashboard/sales-trend?date=2025-01-20&days=7
GET /api/v1/dashboard/sales-trend?site_ids=1,2,3&date=2025-01-20&days=7

GET /api/v1/dashboard/fuel-sales-mix?date=2025-01-20
GET /api/v1/dashboard/fuel-sales-mix?site_ids=1,2,3&date=2025-01-20
```

## 数据聚合逻辑

### 全站点数据聚合
- **收入聚合**: `SUM(amount)` - 所有站点的收入总和
- **交易数聚合**: `COUNT(*)` - 所有站点的交易总数
- **销量聚合**: `SUM(volume)` - 所有站点的销量总和
- **燃油组合**: 按`fuel_grade`分组聚合所有站点数据

### 时间范围处理
- 使用`Asia/Jakarta`时区进行时间转换
- 支持日期范围查询和趋势分析
- 自动补充缺失日期的空数据

## 兼容性保证

### 1. 向后兼容
- 所有现有的BOS/EDC API调用方式保持不变
- 传统的`station_id`参数继续有效
- 响应数据结构完全一致

### 2. 错误处理
- 保持原有的错误响应格式
- 添加了全站点查询的错误处理
- 数据库查询失败时返回空数据而不是错误

### 3. 性能考虑
- 全站点查询可能涉及更多数据，但SQL查询已优化
- 使用索引优化查询性能
- 添加了查询日志便于监控

## 测试验证

### 自动化测试
使用提供的测试脚本验证API兼容性：
```bash
chmod +x test_hos_dashboard_api.sh
./test_hos_dashboard_api.sh
```

### 测试场景
1. **传统BOS模式测试** - 验证带`station_id`的调用
2. **HOS全站点模式测试** - 验证不带`station_id`的调用
3. **HOS site_ids模式测试** - 验证带`site_ids`的调用
4. **错误情况测试** - 验证错误处理
5. **数据对比测试** - 验证数据合理性

### 预期结果
- 所有API调用都应返回200状态码
- 响应数据结构保持一致
- 全站点数据应大于等于单站点数据
- 错误情况应正确处理

## 部署注意事项

### 1. 数据库性能
- 全站点查询可能增加数据库负载
- 建议在`fuel_transactions`表上添加适当索引
- 监控查询性能并根据需要优化

### 2. 缓存策略
- 考虑为全站点查询添加缓存
- 缓存键应区分单站点和全站点查询
- 设置合适的缓存过期时间

### 3. 监控和日志
- 添加了详细的查询日志
- 监控全站点查询的响应时间
- 跟踪API调用模式的变化

## 总结

通过这些修改，Dashboard API现在完全兼容HOS系统的全站点管理需求：

✅ **支持全站点数据聚合** - HOS用户可以查看所有站点的汇总数据
✅ **保持向后兼容** - BOS/EDC系统的功能不受影响  
✅ **灵活的参数处理** - 支持多种API调用方式
✅ **完整的错误处理** - 保持一致的错误响应格式
✅ **性能优化** - SQL查询已针对全站点场景优化

这些修改确保了前端的HOS系统优化能够正常工作，用户可以在Dashboard中查看全集团范围的运营数据。
