#!/bin/bash

# 燃油交易泵码数更新接口测试脚本
# 测试新增的 PATCH /api/v1/fuel-transactions/{id}/pump-readings 接口

set -e

# 配置
BASE_URL="http://localhost:8080"
API_URL="${BASE_URL}/api/v1"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查BOS服务状态..."
    if curl -s "${BASE_URL}/health" > /dev/null 2>&1; then
        log_success "BOS服务运行正常"
    else
        log_error "BOS服务未运行，请先启动服务"
        exit 1
    fi
}

# 获取认证token (如果需要)
get_auth_token() {
    # 这里可以添加获取认证token的逻辑
    # 目前假设不需要认证或使用默认token
    AUTH_TOKEN=""
}

# 获取测试用的燃油交易ID
get_test_transaction_id() {
    log_info "获取测试用的燃油交易ID..."
    
    # 获取燃油交易列表
    response=$(curl -s "${API_URL}/fuel-transactions?limit=1" \
        -H "Content-Type: application/json")
    
    # 提取第一个交易的ID
    transaction_id=$(echo "$response" | jq -r '.data.items[0].id // empty')
    
    if [ -z "$transaction_id" ] || [ "$transaction_id" = "null" ]; then
        log_error "未找到可用的燃油交易记录"
        exit 1
    fi
    
    log_success "找到测试交易ID: $transaction_id"
    echo "$transaction_id"
}

# 测试1: 更新起始泵码数
test_update_start_totalizer() {
    local transaction_id=$1
    log_info "测试1: 更新起始泵码数"
    
    response=$(curl -s -w "%{http_code}" "${API_URL}/fuel-transactions/${transaction_id}/pump-readings" \
        -X PATCH \
        -H "Content-Type: application/json" \
        -d '{
            "start_totalizer": 12345.67
        }')
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "更新起始泵码数成功"
        echo "$body" | jq '.'
    else
        log_error "更新起始泵码数失败 (HTTP $http_code)"
        echo "$body"
    fi
}

# 测试2: 更新截止泵码数
test_update_end_totalizer() {
    local transaction_id=$1
    log_info "测试2: 更新截止泵码数"
    
    response=$(curl -s -w "%{http_code}" "${API_URL}/fuel-transactions/${transaction_id}/pump-readings" \
        -X PATCH \
        -H "Content-Type: application/json" \
        -d '{
            "end_totalizer": 12395.67
        }')
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "更新截止泵码数成功"
        echo "$body" | jq '.'
    else
        log_error "更新截止泵码数失败 (HTTP $http_code)"
        echo "$body"
    fi
}

# 测试3: 同时更新两个字段
test_update_both_totalizers() {
    local transaction_id=$1
    log_info "测试3: 同时更新起始和截止泵码数"
    
    response=$(curl -s -w "%{http_code}" "${API_URL}/fuel-transactions/${transaction_id}/pump-readings" \
        -X PATCH \
        -H "Content-Type: application/json" \
        -d '{
            "start_totalizer": 12300.00,
            "end_totalizer": 12350.00
        }')
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        log_success "同时更新两个字段成功"
        echo "$body" | jq '.'
    else
        log_error "同时更新两个字段失败 (HTTP $http_code)"
        echo "$body"
    fi
}

# 测试4: 参数验证 - 空请求体
test_empty_request() {
    local transaction_id=$1
    log_info "测试4: 参数验证 - 空请求体"
    
    response=$(curl -s -w "%{http_code}" "${API_URL}/fuel-transactions/${transaction_id}/pump-readings" \
        -X PATCH \
        -H "Content-Type: application/json" \
        -d '{}')
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "400" ]; then
        log_success "空请求体验证正确 (HTTP 400)"
        echo "$body" | jq '.'
    else
        log_warning "空请求体验证异常 (HTTP $http_code)"
        echo "$body"
    fi
}

# 测试5: 参数验证 - 截止泵码数小于起始泵码数
test_invalid_totalizer_range() {
    local transaction_id=$1
    log_info "测试5: 参数验证 - 截止泵码数小于起始泵码数"
    
    response=$(curl -s -w "%{http_code}" "${API_URL}/fuel-transactions/${transaction_id}/pump-readings" \
        -X PATCH \
        -H "Content-Type: application/json" \
        -d '{
            "start_totalizer": 12400.00,
            "end_totalizer": 12300.00
        }')
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "400" ]; then
        log_success "泵码数范围验证正确 (HTTP 400)"
        echo "$body" | jq '.'
    else
        log_warning "泵码数范围验证异常 (HTTP $http_code)"
        echo "$body"
    fi
}

# 测试6: 不存在的交易ID
test_nonexistent_transaction() {
    log_info "测试6: 不存在的交易ID"
    
    response=$(curl -s -w "%{http_code}" "${API_URL}/fuel-transactions/999999/pump-readings" \
        -X PATCH \
        -H "Content-Type: application/json" \
        -d '{
            "start_totalizer": 12345.67
        }')
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "404" ]; then
        log_success "不存在交易ID验证正确 (HTTP 404)"
        echo "$body" | jq '.'
    else
        log_warning "不存在交易ID验证异常 (HTTP $http_code)"
        echo "$body"
    fi
}

# 验证更新结果
verify_update_result() {
    local transaction_id=$1
    log_info "验证更新结果..."
    
    response=$(curl -s "${API_URL}/fuel-transactions/${transaction_id}")
    
    start_totalizer=$(echo "$response" | jq -r '.data.start_totalizer // "null"')
    end_totalizer=$(echo "$response" | jq -r '.data.end_totalizer // "null"')
    
    log_info "当前泵码数状态:"
    echo "  起始泵码数: $start_totalizer"
    echo "  截止泵码数: $end_totalizer"
}

# 主函数
main() {
    echo "=========================================="
    echo "燃油交易泵码数更新接口测试"
    echo "=========================================="
    
    # 检查依赖
    if ! command -v jq &> /dev/null; then
        log_error "需要安装 jq 工具来解析JSON响应"
        exit 1
    fi
    
    # 检查服务
    check_service
    
    # 获取认证token
    get_auth_token
    
    # 获取测试交易ID
    transaction_id=$(get_test_transaction_id)
    
    echo ""
    echo "开始测试..."
    echo ""
    
    # 执行测试
    test_update_start_totalizer "$transaction_id"
    echo ""
    
    test_update_end_totalizer "$transaction_id"
    echo ""
    
    test_update_both_totalizers "$transaction_id"
    echo ""
    
    test_empty_request "$transaction_id"
    echo ""
    
    test_invalid_totalizer_range "$transaction_id"
    echo ""
    
    test_nonexistent_transaction
    echo ""
    
    # 验证最终结果
    verify_update_result "$transaction_id"
    
    echo ""
    echo "=========================================="
    log_success "测试完成"
    echo "=========================================="
}

# 运行主函数
main "$@"
