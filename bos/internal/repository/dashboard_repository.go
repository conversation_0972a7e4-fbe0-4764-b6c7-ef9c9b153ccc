package repository

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
)

// DashboardRepository Dashboard数据访问层
type DashboardRepository struct {
	db *database.Database
}

// NewDashboardRepository 创建新的Dashboard数据访问层
func NewDashboardRepository(db *database.Database) *DashboardRepository {
	return &DashboardRepository{
		db: db,
	}
}

// DashboardSummary Dashboard汇总数据结构
type DashboardSummary struct {
	QueryDate        string             `json:"query_date"`
	TodayRevenue     float64            `json:"today_revenue"`
	TodayTransactions int               `json:"today_transactions"`
	TodayVolume      float64            `json:"today_volume"`
	FuelSalesMix     []FuelSalesMixItem `json:"fuel_sales_mix"`
}

// FuelSalesMixItem 燃油销售组合项
type FuelSalesMixItem struct {
	FuelGrade  string  `json:"fuel_grade"`
	Volume     float64 `json:"volume"`
	Revenue    float64 `json:"revenue"`
	Percentage float64 `json:"percentage"`
}

// SalesTrendData 销售趋势数据结构
type SalesTrendData struct {
	QueryDate string            `json:"query_date"`
	Days      int               `json:"days"`
	TrendData []SalesTrendItem  `json:"trend_data"`
}

// SalesTrendItem 销售趋势项
type SalesTrendItem struct {
	Date         string  `json:"date"`
	Revenue      float64 `json:"revenue"`
	Transactions int     `json:"transactions"`
	Volume       float64 `json:"volume"`
}

// FuelSalesDetail 燃油销售详情数据结构
type FuelSalesDetail struct {
	QueryDate         string                `json:"query_date"`
	TotalVolume       float64               `json:"total_volume"`
	TotalRevenue      float64               `json:"total_revenue"`
	TotalTransactions int                   `json:"total_transactions"`
	FuelTypes         []FuelSalesDetailItem `json:"fuel_types"`
}

// FuelSalesDetailItem 燃油销售详情项
type FuelSalesDetailItem struct {
	FuelGrade        string  `json:"fuel_grade"`
	Volume           float64 `json:"volume"`
	Revenue          float64 `json:"revenue"`
	Percentage       float64 `json:"percentage"`
	Transactions     int     `json:"transactions"`
	AvgPricePerLiter float64 `json:"avg_price_per_liter"`
}

// GetDashboardSummary 获取Dashboard汇总数据
func (r *DashboardRepository) GetDashboardSummary(ctx context.Context, stationID int64, date time.Time) (*DashboardSummary, error) {
	dateStr := date.Format("2006-01-02")
	
	// 查询当日汇总数据
	// 使用时间范围查询而不是DATE函数，以避免时区问题
	query := `
		SELECT 
			COALESCE(SUM(amount), 0) as today_revenue,
			COUNT(*) as today_transactions,
			COALESCE(SUM(volume), 0) as today_volume
		FROM order_schema.fuel_transactions 
		WHERE station_id = $1 
		  AND created_at >= $2::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($2::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
	`
	
	var summary DashboardSummary
	summary.QueryDate = dateStr
	
	err := r.db.GetPool().QueryRow(ctx, query, stationID, dateStr).Scan(
		&summary.TodayRevenue,
		&summary.TodayTransactions,
		&summary.TodayVolume,
	)
	if err != nil {
		return nil, fmt.Errorf("查询Dashboard汇总数据失败: %w", err)
	}
	
	// 查询燃油销售组合数据
	fuelMixQuery := `
		SELECT 
			fuel_grade,
			COALESCE(SUM(volume), 0) as volume,
			COALESCE(SUM(amount), 0) as revenue
		FROM order_schema.fuel_transactions 
		WHERE station_id = $1 
		  AND created_at >= $2::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($2::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
		GROUP BY fuel_grade
		ORDER BY revenue DESC
	`
	
	rows, err := r.db.GetPool().Query(ctx, fuelMixQuery, stationID, dateStr)
	if err != nil {
		return nil, fmt.Errorf("查询燃油销售组合数据失败: %w", err)
	}
	defer rows.Close()
	
	var fuelMix []FuelSalesMixItem
	for rows.Next() {
		var item FuelSalesMixItem
		err := rows.Scan(&item.FuelGrade, &item.Volume, &item.Revenue)
		if err != nil {
			return nil, fmt.Errorf("扫描燃油销售组合数据失败: %w", err)
		}
		fuelMix = append(fuelMix, item)
	}
	
	// 计算百分比
	if summary.TodayRevenue > 0 {
		for i := range fuelMix {
			fuelMix[i].Percentage = r.roundToTwoDecimal((fuelMix[i].Revenue / summary.TodayRevenue) * 100)
		}
	}
	
	summary.FuelSalesMix = fuelMix
	return &summary, nil
}

shu// GetDashboardSummaryAllStations 获取所有站点的Dashboard汇总数据（HOS系统专用）
func (r *DashboardRepository) GetDashboardSummaryAllStations(ctx context.Context, date time.Time) (*DashboardSummary, error) {
	dateStr := date.Format("2006-01-02")

	// 查询所有站点的当日汇总数据
	query := `
		SELECT
			COALESCE(SUM(amount), 0) as today_revenue,
			COUNT(*) as today_transactions,
			COALESCE(SUM(volume), 0) as today_volume
		FROM order_schema.fuel_transactions
		WHERE created_at >= $1::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($1::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
	`

	var summary DashboardSummary
	summary.QueryDate = dateStr

	err := r.db.GetPool().QueryRow(ctx, query, dateStr).Scan(
		&summary.TodayRevenue,
		&summary.TodayTransactions,
		&summary.TodayVolume,
	)
	if err != nil {
		return nil, fmt.Errorf("查询全站点Dashboard汇总数据失败: %w", err)
	}

	// 查询所有站点的燃油销售组合数据
	fuelMixQuery := `
		SELECT
			fuel_grade,
			COALESCE(SUM(volume), 0) as volume,
			COALESCE(SUM(amount), 0) as revenue
		FROM order_schema.fuel_transactions
		WHERE created_at >= $1::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($1::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
		GROUP BY fuel_grade
		ORDER BY revenue DESC
	`

	rows, err := r.db.GetPool().Query(ctx, fuelMixQuery, dateStr)
	if err != nil {
		return nil, fmt.Errorf("查询全站点燃油销售组合数据失败: %w", err)
	}
	defer rows.Close()

	var fuelMix []FuelSalesMixItem
	for rows.Next() {
		var item FuelSalesMixItem
		err := rows.Scan(&item.FuelGrade, &item.Volume, &item.Revenue)
		if err != nil {
			return nil, fmt.Errorf("扫描全站点燃油销售组合数据失败: %w", err)
		}
		fuelMix = append(fuelMix, item)
	}

	// 计算百分比
	if summary.TodayRevenue > 0 {
		for i := range fuelMix {
			fuelMix[i].Percentage = r.roundToTwoDecimal((fuelMix[i].Revenue / summary.TodayRevenue) * 100)
		}
	}

	summary.FuelSalesMix = fuelMix
	fmt.Printf("全站点Dashboard汇总查询完成，收入: %.2f，交易数: %d，销量: %.2f\n",
		summary.TodayRevenue, summary.TodayTransactions, summary.TodayVolume)
	return &summary, nil
}

// GetPreviousDayRevenue 获取前一天收入用于计算变化率
func (r *DashboardRepository) GetPreviousDayRevenue(ctx context.Context, stationID int64, date time.Time) (float64, error) {
	previousDate := date.AddDate(0, 0, -1).Format("2006-01-02")
	
	query := `
		SELECT COALESCE(SUM(amount), 0) as revenue
		FROM order_schema.fuel_transactions 
		WHERE station_id = $1 
		  AND created_at >= $2::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($2::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
	`
	
	var revenue float64
	err := r.db.GetPool().QueryRow(ctx, query, stationID, previousDate).Scan(&revenue)
	if err != nil {
		return 0, fmt.Errorf("查询前一天收入失败: %w", err)
	}
	
	return revenue, nil
}

// GetSalesTrend 获取销售趋势数据
func (r *DashboardRepository) GetSalesTrend(ctx context.Context, stationID int64, endDate time.Time, days int) (*SalesTrendData, error) {
	startDate := endDate.AddDate(0, 0, -days+1)
	
	query := `
		SELECT 
			DATE(created_at AT TIME ZONE 'Asia/Jakarta') as date,
			COALESCE(SUM(amount), 0) as revenue,
			COUNT(*) as transactions,
			COALESCE(SUM(volume), 0) as volume
		FROM order_schema.fuel_transactions 
		WHERE station_id = $1 
		  AND DATE(created_at AT TIME ZONE 'Asia/Jakarta') >= $2
		  AND DATE(created_at AT TIME ZONE 'Asia/Jakarta') <= $3
		  AND status = 'processed'
		GROUP BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
		ORDER BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
	`
	
	rows, err := r.db.GetPool().Query(ctx, query, stationID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	if err != nil {
		return nil, fmt.Errorf("查询销售趋势数据失败: %w", err)
	}
	defer rows.Close()
	
	var trendData []SalesTrendItem
	for rows.Next() {
		var item SalesTrendItem
		var transactions int64
		var dateValue interface{}
		err := rows.Scan(&dateValue, &item.Revenue, &transactions, &item.Volume)
		if err != nil {
			return nil, fmt.Errorf("扫描销售趋势数据失败: %w", err)
		}
		
		// 处理date字段
		if dateTime, ok := dateValue.(time.Time); ok {
			item.Date = dateTime.Format("2006-01-02")
		} else {
			item.Date = fmt.Sprintf("%v", dateValue)
		}
		
		item.Transactions = int(transactions)
		trendData = append(trendData, item)
	}
	
	// 补充缺失的日期数据(如果某天没有交易)
	trendData = r.fillMissingDates(trendData, startDate, endDate)
	
	return &SalesTrendData{
		QueryDate: endDate.Format("2006-01-02"),
		Days:      days,
		TrendData: trendData,
	}, nil
}

// GetSalesTrendAllStations 获取所有站点的销售趋势数据（HOS系统专用）
func (r *DashboardRepository) GetSalesTrendAllStations(ctx context.Context, endDate time.Time, days int) (*SalesTrendData, error) {
	startDate := endDate.AddDate(0, 0, -days+1)

	query := `
		SELECT
			DATE(created_at AT TIME ZONE 'Asia/Jakarta') as date,
			COALESCE(SUM(amount), 0) as revenue,
			COUNT(*) as transactions,
			COALESCE(SUM(volume), 0) as volume
		FROM order_schema.fuel_transactions
		WHERE DATE(created_at AT TIME ZONE 'Asia/Jakarta') >= $1
		  AND DATE(created_at AT TIME ZONE 'Asia/Jakarta') <= $2
		  AND status = 'processed'
		GROUP BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
		ORDER BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
	`

	rows, err := r.db.GetPool().Query(ctx, query, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	if err != nil {
		return nil, fmt.Errorf("查询全站点销售趋势数据失败: %w", err)
	}
	defer rows.Close()

	var trendData []SalesTrendItem
	for rows.Next() {
		var item SalesTrendItem
		var transactions int64
		var dateValue interface{}
		err := rows.Scan(&dateValue, &item.Revenue, &transactions, &item.Volume)
		if err != nil {
			return nil, fmt.Errorf("扫描全站点销售趋势数据失败: %w", err)
		}

		// 处理date字段
		if dateTime, ok := dateValue.(time.Time); ok {
			item.Date = dateTime.Format("2006-01-02")
		} else {
			item.Date = fmt.Sprintf("%v", dateValue)
		}

		item.Transactions = int(transactions)
		trendData = append(trendData, item)
	}

	// 补充缺失的日期数据(如果某天没有交易)
	trendData = r.fillMissingDates(trendData, startDate, endDate)

	fmt.Printf("全站点销售趋势查询完成，日期范围: %s 到 %s，数据点: %d\n",
		startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), len(trendData))

	return &SalesTrendData{
		QueryDate: endDate.Format("2006-01-02"),
		Days:      days,
		TrendData: trendData,
	}, nil
}

// GetFuelSalesDetail 获取燃油销售详情
func (r *DashboardRepository) GetFuelSalesDetail(ctx context.Context, stationID int64, date time.Time) (*FuelSalesDetail, error) {
	dateStr := date.Format("2006-01-02")
	
	// 查询总计数据
	totalQuery := `
		SELECT 
			COALESCE(SUM(volume), 0) as total_volume,
			COALESCE(SUM(amount), 0) as total_revenue,
			COUNT(*) as total_transactions
		FROM order_schema.fuel_transactions 
		WHERE station_id = $1 
		  AND created_at >= $2::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($2::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
	`
	
	var detail FuelSalesDetail
	detail.QueryDate = dateStr
	
	err := r.db.GetPool().QueryRow(ctx, totalQuery, stationID, dateStr).Scan(
		&detail.TotalVolume,
		&detail.TotalRevenue,
		&detail.TotalTransactions,
	)
	if err != nil {
		return nil, fmt.Errorf("查询燃油销售总计数据失败: %w", err)
	}
	
	// 查询按燃油等级分组的详细数据
	detailQuery := `
		SELECT 
			fuel_grade,
			COALESCE(SUM(volume), 0) as volume,
			COALESCE(SUM(amount), 0) as revenue,
			COUNT(*) as transactions,
			COALESCE(AVG(unit_price), 0) as avg_price_per_liter
		FROM order_schema.fuel_transactions 
		WHERE station_id = $1 
		  AND created_at >= $2::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($2::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
		GROUP BY fuel_grade
		ORDER BY revenue DESC
	`
	
	rows, err := r.db.GetPool().Query(ctx, detailQuery, stationID, dateStr)
	if err != nil {
		return nil, fmt.Errorf("查询燃油销售详情数据失败: %w", err)
	}
	defer rows.Close()
	
	var fuelTypes []FuelSalesDetailItem
	for rows.Next() {
		var item FuelSalesDetailItem
		err := rows.Scan(
			&item.FuelGrade,
			&item.Volume,
			&item.Revenue,
			&item.Transactions,
			&item.AvgPricePerLiter,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描燃油销售详情数据失败: %w", err)
		}
		fuelTypes = append(fuelTypes, item)
	}
	
	// 计算百分比
	if detail.TotalRevenue > 0 {
		for i := range fuelTypes {
			fuelTypes[i].Percentage = r.roundToTwoDecimal((fuelTypes[i].Revenue / detail.TotalRevenue) * 100)
		}
	}
	
	detail.FuelTypes = fuelTypes
	return &detail, nil
}

// GetFuelSalesDetailAllStations 获取所有站点的燃油销售详情（HOS系统专用）
func (r *DashboardRepository) GetFuelSalesDetailAllStations(ctx context.Context, date time.Time) (*FuelSalesDetail, error) {
	dateStr := date.Format("2006-01-02")

	// 查询所有站点的总计数据
	totalQuery := `
		SELECT
			COALESCE(SUM(volume), 0) as total_volume,
			COALESCE(SUM(amount), 0) as total_revenue,
			COUNT(*) as total_transactions
		FROM order_schema.fuel_transactions
		WHERE created_at >= $1::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($1::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
	`

	var detail FuelSalesDetail
	detail.QueryDate = dateStr

	err := r.db.GetPool().QueryRow(ctx, totalQuery, dateStr).Scan(
		&detail.TotalVolume,
		&detail.TotalRevenue,
		&detail.TotalTransactions,
	)
	if err != nil {
		return nil, fmt.Errorf("查询全站点燃油销售总计数据失败: %w", err)
	}

	// 查询所有站点按燃油等级分组的详细数据
	detailQuery := `
		SELECT
			fuel_grade,
			COALESCE(SUM(volume), 0) as volume,
			COALESCE(SUM(amount), 0) as revenue,
			COUNT(*) as transactions,
			COALESCE(AVG(unit_price), 0) as avg_price_per_liter
		FROM order_schema.fuel_transactions
		WHERE created_at >= $1::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND created_at < ($1::timestamp AT TIME ZONE 'Asia/Jakarta') + INTERVAL '1 day'
		  AND status = 'processed'
		GROUP BY fuel_grade
		ORDER BY revenue DESC
	`

	rows, err := r.db.GetPool().Query(ctx, detailQuery, dateStr)
	if err != nil {
		return nil, fmt.Errorf("查询全站点燃油销售详情数据失败: %w", err)
	}
	defer rows.Close()

	var fuelTypes []FuelSalesDetailItem
	for rows.Next() {
		var item FuelSalesDetailItem
		err := rows.Scan(
			&item.FuelGrade,
			&item.Volume,
			&item.Revenue,
			&item.Transactions,
			&item.AvgPricePerLiter,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描全站点燃油销售详情数据失败: %w", err)
		}
		fuelTypes = append(fuelTypes, item)
	}

	// 计算百分比
	if detail.TotalRevenue > 0 {
		for i := range fuelTypes {
			fuelTypes[i].Percentage = r.roundToTwoDecimal((fuelTypes[i].Revenue / detail.TotalRevenue) * 100)
		}
	}

	detail.FuelTypes = fuelTypes
	fmt.Printf("全站点燃油销售详情查询完成，总收入: %.2f，总交易数: %d，总销量: %.2f\n",
		detail.TotalRevenue, detail.TotalTransactions, detail.TotalVolume)
	return &detail, nil
}

// fillMissingDates 补充缺失的日期数据
func (r *DashboardRepository) fillMissingDates(data []SalesTrendItem, startDate, endDate time.Time) []SalesTrendItem {
	if len(data) == 0 {
		// 如果没有任何数据，创建空的趋势数据
		var result []SalesTrendItem
		for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
			result = append(result, SalesTrendItem{
				Date:         d.Format("2006-01-02"),
				Revenue:      0,
				Transactions: 0,
				Volume:       0,
			})
		}
		return result
	}
	
	// 创建日期到数据的映射
	dataMap := make(map[string]SalesTrendItem)
	for _, item := range data {
		dataMap[item.Date] = item
	}
	
	// 补充缺失的日期
	var result []SalesTrendItem
	for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
		dateStr := d.Format("2006-01-02")
		if item, exists := dataMap[dateStr]; exists {
			result = append(result, item)
		} else {
			result = append(result, SalesTrendItem{
				Date:         dateStr,
				Revenue:      0,
				Transactions: 0,
				Volume:       0,
			})
		}
	}
	
	return result
}

// GetLatestDataDate 获取最近有数据的日期
func (r *DashboardRepository) GetLatestDataDate(ctx context.Context, stationID int64, maxDaysBack int) (time.Time, error) {
	// 计算开始日期
	loc, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return time.Time{}, fmt.Errorf("加载时区失败: %w", err)
	}

	now := time.Now().In(loc)
	startDate := now.AddDate(0, 0, -maxDaysBack)

	// 查询最近有数据的日期，从今天开始向前查找
	query := `
		SELECT DATE(created_at AT TIME ZONE 'Asia/Jakarta') as date
		FROM order_schema.fuel_transactions
		WHERE station_id = $1
		  AND created_at >= $2::timestamp AT TIME ZONE 'Asia/Jakarta'
		  AND status = 'processed'
		GROUP BY DATE(created_at AT TIME ZONE 'Asia/Jakarta')
		HAVING COUNT(*) > 0
		ORDER BY DATE(created_at AT TIME ZONE 'Asia/Jakarta') DESC
		LIMIT 1
	`

	var dateValue interface{}
	err = r.db.GetPool().QueryRow(ctx, query, stationID, startDate.Format("2006-01-02")).Scan(&dateValue)
	if err != nil {
		// 如果没有找到数据，返回今天的日期
		loc, locErr := time.LoadLocation("Asia/Jakarta")
		if locErr != nil {
			return time.Time{}, fmt.Errorf("加载时区失败: %w", locErr)
		}
		return time.Now().In(loc), nil
	}

	// 处理date字段
	if dateTime, ok := dateValue.(time.Time); ok {
		// 转换为Asia/Jakarta时区
		return dateTime.In(loc), nil
	}

	// 如果类型转换失败，返回今天的日期
	return time.Now().In(loc), nil
}

// roundToTwoDecimal 四舍五入到两位小数
func (r *DashboardRepository) roundToTwoDecimal(value float64) float64 {
	return float64(int(value*100+0.5)) / 100
}