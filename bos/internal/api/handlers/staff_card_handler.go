package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// StaffCardHandler 处理员工卡相关HTTP请求
type StaffCardHandler struct {
	staffCardService service.StaffCardService
}

// NewStaffCardHandler 创建新的StaffCardHandler实例
func NewStaffCardHandler(staffCardService service.StaffCardService) *StaffCardHandler {
	return &StaffCardHandler{
		staffCardService: staffCardService,
	}
}

// CreateStaffCardRequest 创建员工卡请求
type CreateStaffCardRequest struct {
	CardNumber  string                 `json:"cardNumber" validate:"required"`
	UserID      string                 `json:"userId" validate:"required"`
	StationID   *int64                 `json:"stationId"`
	CardType    string                 `json:"cardType" validate:"required,oneof=employee manager admin"`
	Status      string                 `json:"status" validate:"required,oneof=active inactive suspended expired"`
	ValidFrom   *time.Time             `json:"validFrom"`
	ValidUntil  *time.Time             `json:"validUntil"`
	Permissions map[string]interface{} `json:"permissions"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// CreateStaffCard 创建员工卡
func (h *StaffCardHandler) CreateStaffCard(c echo.Context) error {
	var req CreateStaffCardRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 转换为仓储层模型
	staffCard := repository.StaffCard{
		CardNumber: req.CardNumber,
		UserID:     req.UserID,
		CardType:   repository.StaffCardType(req.CardType),
		Status:     repository.StaffCardStatus(req.Status),
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := int64ToUUID(*req.StationID)
		staffCard.StationID = &stationID
	}

	// 处理时间字段
	if req.ValidFrom != nil {
		staffCard.ValidFrom = *req.ValidFrom
	}
	staffCard.ValidUntil = req.ValidUntil

	// 处理权限和元数据
	if req.Permissions != nil {
		staffCard.Permissions = req.Permissions
	} else {
		staffCard.Permissions = make(map[string]interface{})
	}

	if req.Metadata != nil {
		staffCard.Metadata = req.Metadata
	} else {
		staffCard.Metadata = make(map[string]interface{})
	}

	// 创建员工卡
	createdCard, err := h.staffCardService.CreateStaffCard(c.Request().Context(), staffCard)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create staff card")
	}

	return c.JSON(http.StatusCreated, createdCard)
}

// GetStaffCard 获取员工卡详情
func (h *StaffCardHandler) GetStaffCard(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid staff card ID")
	}

	includeUser := c.QueryParam("includeUser") == "true"

	var staffCard repository.StaffCard
	if includeUser {
		staffCard, err = h.staffCardService.GetStaffCardWithUser(c.Request().Context(), repository.IDFromInt64(id))
	} else {
		staffCard, err = h.staffCardService.GetStaffCard(c.Request().Context(), repository.IDFromInt64(id))
	}

	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "Staff card not found")
	}

	return c.JSON(http.StatusOK, staffCard)
}

// GetStaffCardByNumber 根据卡号获取员工卡
func (h *StaffCardHandler) GetStaffCardByNumber(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	staffCard, err := h.staffCardService.GetStaffCardByNumber(c.Request().Context(), cardNumber)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "Staff card not found")
	}

	return c.JSON(http.StatusOK, staffCard)
}

// StaffCardFilterRequest 员工卡筛选请求
type StaffCardFilterRequest struct {
	UserID      *string `json:"userId" form:"userId"`
	CardNumber  *string `json:"cardNumber" form:"cardNumber"`
	StationID   *int64  `json:"stationId" form:"stationId"`
	CardType    *string `json:"cardType" form:"cardType" validate:"omitempty,oneof=employee manager admin"`
	Status      *string `json:"status" form:"status" validate:"omitempty,oneof=active inactive suspended expired"`
	ValidOnly   *bool   `json:"validOnly" form:"validOnly"`
	DateFrom    *string `json:"dateFrom" form:"dateFrom"`
	DateTo      *string `json:"dateTo" form:"dateTo"`
	Page        int     `json:"page" form:"page"`
	PageSize    int     `json:"pageSize" form:"pageSize"`
	SortBy      string  `json:"sortBy" form:"sortBy"`
	SortOrder   string  `json:"sortOrder" form:"sortOrder"`
	IncludeUser *bool   `json:"includeUser" form:"includeUser"`
}

// ListStaffCards 获取员工卡列表
func (h *StaffCardHandler) ListStaffCards(c echo.Context) error {
	var req StaffCardFilterRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	} else if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 创建过滤器
	filter := repository.StaffCardFilter{
		UserID:     req.UserID,
		CardNumber: req.CardNumber,
		ValidOnly:  req.ValidOnly,
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := int64ToUUID(*req.StationID)
		filter.StationID = &stationID
	}

	// 处理CardType
	if req.CardType != nil {
		cardType := repository.StaffCardType(*req.CardType)
		filter.CardType = &cardType
	}

	// 处理Status
	if req.Status != nil {
		status := repository.StaffCardStatus(*req.Status)
		filter.Status = &status
	}

	// 处理日期范围
	if req.DateFrom != nil {
		if dateFrom, err := time.Parse("2006-01-02", *req.DateFrom); err == nil {
			filter.DateFrom = &dateFrom
		}
	}
	if req.DateTo != nil {
		if dateTo, err := time.Parse("2006-01-02", *req.DateTo); err == nil {
			filter.DateTo = &dateTo
		}
	}

	// 创建分页
	pagination := repository.Pagination{
		Page:  req.Page,
		Limit: req.PageSize,
	}

	// 创建排序
	sort := repository.SortOrder{
		Field:     req.SortBy,
		Direction: req.SortOrder,
	}

	// 如果排序字段未指定，默认按创建时间降序
	if sort.Field == "" {
		sort.Field = "created_at"
		sort.Direction = "desc"
	}

	// 获取员工卡列表
	var staffCards []repository.StaffCard
	var total int
	var err error

	includeUser := req.IncludeUser != nil && *req.IncludeUser
	if includeUser {
		staffCards, total, err = h.staffCardService.ListStaffCardsWithUser(c.Request().Context(), filter, pagination, sort)
	} else {
		staffCards, total, err = h.staffCardService.ListStaffCards(c.Request().Context(), filter, pagination, sort)
	}

	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch staff cards")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  staffCards,
		"page":  req.Page,
		"size":  req.PageSize,
		"total": total,
	})
}

// UpdateStaffCardRequest 更新员工卡请求
type UpdateStaffCardRequest struct {
	StationID        *int64                 `json:"stationId"`
	CardType         *string                `json:"cardType" validate:"omitempty,oneof=employee manager admin"`
	Status           *string                `json:"status" validate:"omitempty,oneof=active inactive suspended expired"`
	ValidFrom        *time.Time             `json:"validFrom"`
	ValidUntil       *time.Time             `json:"validUntil"`
	ClearValidUntil  *bool                  `json:"clearValidUntil"`
	Permissions      map[string]interface{} `json:"permissions"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// UpdateStaffCard 更新员工卡
func (h *StaffCardHandler) UpdateStaffCard(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid staff card ID")
	}

	var req UpdateStaffCardRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 获取现有员工卡
	existingCard, err := h.staffCardService.GetStaffCard(c.Request().Context(), repository.IDFromInt64(id))
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "Staff card not found")
	}

	// 更新字段
	if req.StationID != nil {
		stationID := int64ToUUID(*req.StationID)
		existingCard.StationID = &stationID
	}

	if req.CardType != nil {
		existingCard.CardType = repository.StaffCardType(*req.CardType)
	}

	if req.Status != nil {
		existingCard.Status = repository.StaffCardStatus(*req.Status)
	}

	if req.ValidFrom != nil {
		existingCard.ValidFrom = *req.ValidFrom
	}

	if req.ClearValidUntil != nil && *req.ClearValidUntil {
		existingCard.ValidUntil = nil
	} else if req.ValidUntil != nil {
		existingCard.ValidUntil = req.ValidUntil
	}

	if req.Permissions != nil {
		existingCard.Permissions = req.Permissions
	}

	if req.Metadata != nil {
		existingCard.Metadata = req.Metadata
	}

	// 更新员工卡
	updatedCard, err := h.staffCardService.UpdateStaffCard(c.Request().Context(), existingCard)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update staff card")
	}

	return c.JSON(http.StatusOK, updatedCard)
}

// DeleteStaffCard 删除员工卡
func (h *StaffCardHandler) DeleteStaffCard(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid staff card ID")
	}

	err = h.staffCardService.DeleteStaffCard(c.Request().Context(), repository.IDFromInt64(id))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete staff card")
	}

	return c.NoContent(http.StatusNoContent)
}

// ActivateCard 激活员工卡
func (h *StaffCardHandler) ActivateCard(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	err := h.staffCardService.ActivateCard(c.Request().Context(), cardNumber)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to activate card")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Card activated successfully"})
}

// SuspendCardRequest 暂停员工卡请求
type SuspendCardRequest struct {
	Reason string `json:"reason" validate:"required"`
}

// SuspendCard 暂停员工卡
func (h *StaffCardHandler) SuspendCard(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	var req SuspendCardRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	err := h.staffCardService.SuspendCard(c.Request().Context(), cardNumber, req.Reason)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to suspend card")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Card suspended successfully"})
}

// ValidateCardRequest 验证员工卡请求
type ValidateCardRequest struct {
	StationID int64 `json:"stationId" validate:"required"`
}

// ValidateCard 验证员工卡
func (h *StaffCardHandler) ValidateCard(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	var req ValidateCardRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	result, err := h.staffCardService.ValidateCardForTransaction(c.Request().Context(), cardNumber, int64ToUUID(req.StationID))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to validate card")
	}

	return c.JSON(http.StatusOK, result)
}

// GetUserStaffCards 获取用户的员工卡列表
func (h *StaffCardHandler) GetUserStaffCards(c echo.Context) error {
	userID := c.Param("userId")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	staffCards, err := h.staffCardService.GetUserStaffCards(c.Request().Context(), userID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get user staff cards")
	}

	return c.JSON(http.StatusOK, staffCards)
}

// CreateStaffCardForUserRequest 为用户创建员工卡请求
type CreateStaffCardForUserRequest struct {
	CardNumber string `json:"cardNumber" validate:"required,min=3,max=50"`
	CardType   string `json:"cardType" validate:"required,oneof=employee manager admin"`
	StationID  *int64 `json:"stationId"`
}

// CreateStaffCardForUser 为用户创建员工卡
func (h *StaffCardHandler) CreateStaffCardForUser(c echo.Context) error {
	userID := c.Param("userId")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	var req CreateStaffCardForUserRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	var stationID *repository.ID
	if req.StationID != nil {
		id := int64ToUUID(*req.StationID)
		stationID = &id
	}

	staffCard, err := h.staffCardService.CreateStaffCardForUserWithCardNumber(c.Request().Context(), userID, req.CardNumber, repository.StaffCardType(req.CardType), stationID)
	if err != nil {
		c.Logger().Errorf("Failed to create staff card for user %s: %v", userID, err)
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create staff card for user")
	}

	return c.JSON(http.StatusCreated, staffCard)
}

// ExtendCardValidityRequest 延长员工卡有效期请求
type ExtendCardValidityRequest struct {
	ValidUntil *time.Time `json:"validUntil"`
}

// ExtendCardValidity 延长员工卡有效期
func (h *StaffCardHandler) ExtendCardValidity(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	var req ExtendCardValidityRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	err := h.staffCardService.ExtendCardValidity(c.Request().Context(), cardNumber, req.ValidUntil)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to extend card validity")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Card validity extended successfully"})
}

// GetActiveCardsByStation 获取指定站点的活跃员工卡
func (h *StaffCardHandler) GetActiveCardsByStation(c echo.Context) error {
	stationID, err := strconv.ParseInt(c.Param("stationId"), 10, 64)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid station ID")
	}

	staffCards, err := h.staffCardService.GetActiveCardsByStation(c.Request().Context(), int64ToUUID(stationID))
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get active cards by station")
	}

	return c.JSON(http.StatusOK, staffCards)
}

// BulkCreateStaffCardsRequest 批量创建员工卡请求
type BulkCreateStaffCardsRequest struct {
	UserIDs   []string `json:"userIds" validate:"required,min=1"`
	CardType  string   `json:"cardType" validate:"required,oneof=employee manager admin"`
	StationID *int64   `json:"stationId"`
}

// BulkCreateStaffCards 批量创建员工卡
func (h *StaffCardHandler) BulkCreateStaffCards(c echo.Context) error {
	var req BulkCreateStaffCardsRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	var stationID *repository.ID
	if req.StationID != nil {
		id := int64ToUUID(*req.StationID)
		stationID = &id
	}

	staffCards, err := h.staffCardService.BulkCreateStaffCards(c.Request().Context(), req.UserIDs, repository.StaffCardType(req.CardType), stationID)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to bulk create staff cards")
	}

	return c.JSON(http.StatusCreated, staffCards)
} 