package service

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos/internal/repository"
)

// FuelTransactionServiceOptimized 优化版燃油交易服务实现
type FuelTransactionServiceOptimized struct {
	fuelTransactionRepo repository.FuelTransactionRepository
	useOptimization     bool
	performanceLog      bool
}

// NewFuelTransactionServiceOptimized 创建优化版燃油交易服务
func NewFuelTransactionServiceOptimized(
	repo repository.FuelTransactionRepository,
	useOptimization bool,
	performanceLog bool,
) *FuelTransactionServiceOptimized {
	return &FuelTransactionServiceOptimized{
		fuelTransactionRepo: repo,
		useOptimization:     useOptimization,
		performanceLog:      performanceLog,
	}
}

// GetFuelTransactionsFullWithJoinsOptimized 获取完整燃油交易信息（使用汇总表优化）
func (s *FuelTransactionServiceOptimized) GetFuelTransactionsFullWithJoinsOptimized(
	ctx context.Context,
	filter repository.FuelTransactionFilter,
	pagination repository.Pagination,
	sort repository.SortOrder,
) ([]repository.FuelTransactionFull, int, error) {
	startTime := time.Now()

	var transactions []repository.FuelTransactionFull
	var total int
	var err error

	if s.useOptimization {
		// 使用汇总表查询
		transactions, total, err = s.getFuelTransactionsFromSummaryTable(ctx, filter, pagination, sort)
		if s.performanceLog {
			s.logPerformance(ctx, "GetFuelTransactionsFullWithJoinsOptimized", map[string]interface{}{
				"source":     "summary_table",
				"count":      len(transactions),
				"total":      total,
				"filter":     filter,
				"pagination": pagination,
			}, time.Since(startTime))
		}
	} else {
		// 使用原始连表查询
		transactions, total, err = s.fuelTransactionRepo.GetFuelTransactionsFullWithJoins(ctx, filter, pagination, sort)
		if s.performanceLog {
			s.logPerformance(ctx, "GetFuelTransactionsFullWithJoinsOptimized", map[string]interface{}{
				"source":     "original_joins",
				"count":      len(transactions),
				"total":      total,
				"filter":     filter,
				"pagination": pagination,
			}, time.Since(startTime))
		}
	}

	return transactions, total, err
}

// getFuelTransactionsFromSummaryTable 从汇总表获取燃油交易数据
func (s *FuelTransactionServiceOptimized) getFuelTransactionsFromSummaryTable(
	ctx context.Context,
	filter repository.FuelTransactionFilter,
	pagination repository.Pagination,
	sort repository.SortOrder,
) ([]repository.FuelTransactionFull, int, error) {
	// 构建使用汇总表的查询
	query := `
		SELECT 
			ft.id,
			ft.transaction_number,
			ft.station_id,
			ft.pump_id,
			ft.nozzle_id,
			ft.fuel_type,
			ft.fuel_grade,
			ft.tank,
			ft.unit_price,
			ft.volume,
			ft.amount,
			ft.total_volume,
			ft.total_amount,
			ft.status,
			ft.member_card_id,
			ft.member_id,
			ft.employee_id,
			ft.staff_card_id,
			ft.shift_id,
			ft.fcc_transaction_id,
			ft.pos_terminal_id,
			ft.start_totalizer,
			ft.end_totalizer,
			ft.nozzle_start_time,
			ft.nozzle_end_time,
			ft.metadata,
			ft.created_at,
			ft.updated_at,
			ft.processed_at,
			ft.cancelled_at,
			COALESCE(ft.totalizer_continuity_status, 'unknown') as totalizer_continuity_status,
			
			-- 从汇总表获取预计算数据
			COALESCE(fts.station_name, '') as station_name,
			COALESCE(fts.site_code, '') as site_code,
			COALESCE(fts.employee_name, 'Unknown Employee') as employee_name,
			COALESCE(fts.shift_name, '') as shift_name,
			COALESCE(fts.customer_name, '') as customer_name,
			COALESCE(fts.customer_phone, '') as customer_phone,
			COALESCE(fts.vehicle_type, '') as vehicle_type,
			COALESCE(fts.license_plate, '') as license_plate,
			COALESCE(fts.promotion_name, '') as promotion_name,
			COALESCE(fts.order_numbers, '') as order_numbers,
			COALESCE(fts.payment_methods, '') as payment_methods,
			fts.first_payment_time,
			COALESCE(fts.item_discount_amount, 0) as item_discount_amount,
			COALESCE(fts.promotion_discount_amount, 0) as promotion_discount_amount,
			COALESCE(fts.order_discount_amount, 0) as order_discount_amount,
			COALESCE(fts.total_discount_amount, 0) as total_discount_amount,
			COALESCE(fts.free_liter, 0) as free_liter,
			COALESCE(fts.free_liter_amount, 0) as free_liter_amount
		FROM order_schema.fuel_transactions ft
		LEFT JOIN order_schema.fuel_transactions_summary fts ON ft.id = fts.fuel_transaction_id
		WHERE 1=1
	`

	countQuery := `
		SELECT COUNT(*)
		FROM order_schema.fuel_transactions ft
		LEFT JOIN order_schema.fuel_transactions_summary fts ON ft.id = fts.fuel_transaction_id
		WHERE 1=1
	`

	// 构建WHERE条件
	whereClause, params := s.buildWhereClause(filter)
	query += whereClause
	countQuery += whereClause

	// 添加排序
	if sort.Field != "" {
		direction := "ASC"
		if sort.Order == "desc" {
			direction = "DESC"
		}
		query += fmt.Sprintf(" ORDER BY ft.%s %s", sort.Field, direction)
	} else {
		query += " ORDER BY ft.created_at DESC"
	}

	// 添加分页
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", pagination.PageSize, (pagination.Page-1)*pagination.PageSize)

	// 执行查询
	return s.fuelTransactionRepo.ExecuteFullTransactionQuery(ctx, query, countQuery, params)
}

// buildWhereClause 构建WHERE子句
func (s *FuelTransactionServiceOptimized) buildWhereClause(filter repository.FuelTransactionFilter) (string, []interface{}) {
	var whereClause string
	var params []interface{}
	paramIndex := 1

	if filter.StationID != nil {
		whereClause += fmt.Sprintf(" AND ft.station_id = $%d", paramIndex)
		params = append(params, *filter.StationID)
		paramIndex++
	}

	if filter.Status != nil {
		whereClause += fmt.Sprintf(" AND ft.status = $%d", paramIndex)
		params = append(params, *filter.Status)
		paramIndex++
	}

	if filter.DateFrom != nil {
		whereClause += fmt.Sprintf(" AND ft.created_at >= $%d", paramIndex)
		params = append(params, *filter.DateFrom)
		paramIndex++
	}

	if filter.DateTo != nil {
		whereClause += fmt.Sprintf(" AND ft.created_at <= $%d", paramIndex)
		params = append(params, *filter.DateTo)
		paramIndex++
	}

	if filter.TransactionNumber != nil {
		whereClause += fmt.Sprintf(" AND ft.transaction_number LIKE $%d", paramIndex)
		params = append(params, "%"+*filter.TransactionNumber+"%")
		paramIndex++
	}

	if filter.FuelType != nil {
		whereClause += fmt.Sprintf(" AND ft.fuel_type = $%d", paramIndex)
		params = append(params, *filter.FuelType)
		paramIndex++
	}

	if filter.FuelGrade != nil {
		whereClause += fmt.Sprintf(" AND ft.fuel_grade = $%d", paramIndex)
		params = append(params, *filter.FuelGrade)
		paramIndex++
	}

	if filter.PumpID != nil {
		whereClause += fmt.Sprintf(" AND ft.pump_id = $%d", paramIndex)
		params = append(params, *filter.PumpID)
		paramIndex++
	}

	if filter.EmployeeID != nil {
		whereClause += fmt.Sprintf(" AND ft.employee_id = $%d", paramIndex)
		params = append(params, *filter.EmployeeID)
		paramIndex++
	}

	if filter.StaffCardID != nil {
		whereClause += fmt.Sprintf(" AND ft.staff_card_id = $%d", paramIndex)
		params = append(params, *filter.StaffCardID)
		paramIndex++
	}

	if filter.ShiftID != nil {
		whereClause += fmt.Sprintf(" AND ft.shift_id = $%d", paramIndex)
		params = append(params, *filter.ShiftID)
		paramIndex++
	}

	if filter.Tank != nil {
		whereClause += fmt.Sprintf(" AND ft.tank = $%d", paramIndex)
		params = append(params, *filter.Tank)
		paramIndex++
	}

	if filter.TotalizerContinuityStatus != nil {
		whereClause += fmt.Sprintf(" AND ft.totalizer_continuity_status = $%d", paramIndex)
		params = append(params, *filter.TotalizerContinuityStatus)
		paramIndex++
	}

	return whereClause, params
}

// logPerformance 记录性能日志
func (s *FuelTransactionServiceOptimized) logPerformance(ctx context.Context, operation string, metadata map[string]interface{}, duration time.Duration) {
	// 实际项目中可以使用日志系统记录性能指标
	fmt.Printf("[PERF] %s took %v ms | metadata: %+v\n", operation, duration.Milliseconds(), metadata)
}

// GetFuelTransactionsFullWithJoins 保持原有接口兼容性
func (s *FuelTransactionServiceOptimized) GetFuelTransactionsFullWithJoins(
	ctx context.Context,
	filter repository.FuelTransactionFilter,
	pagination repository.Pagination,
	sort repository.SortOrder,
) ([]repository.FuelTransactionFull, int, error) {
	return s.GetFuelTransactionsFullWithJoinsOptimized(ctx, filter, pagination, sort)
}

// 其他方法保持不变，委托给原始repository
func (s *FuelTransactionServiceOptimized) CreateFuelTransaction(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.Create(ctx, transaction)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransaction(ctx context.Context, id repository.ID) (repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.Get(ctx, id)
}

func (s *FuelTransactionServiceOptimized) ListFuelTransactions(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransaction, int, error) {
	return s.fuelTransactionRepo.List(ctx, filter, pagination, sort)
}

func (s *FuelTransactionServiceOptimized) LinkFuelTransactionToOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID, allocatedAmount float64) (repository.FuelTransactionOrderLink, error) {
	return s.fuelTransactionRepo.LinkFuelTransactionToOrder(ctx, fuelTransactionID, orderID, allocatedAmount)
}

func (s *FuelTransactionServiceOptimized) UnlinkFuelTransactionFromOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	return s.fuelTransactionRepo.UnlinkFuelTransactionFromOrder(ctx, fuelTransactionID, orderID)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransactionOrders(ctx context.Context, fuelTransactionID repository.ID) ([]repository.Order, error) {
	return s.fuelTransactionRepo.GetFuelTransactionOrders(ctx, fuelTransactionID)
}

func (s *FuelTransactionServiceOptimized) UpdateLinkAllocatedAmount(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID, allocatedAmount float64) error {
	return s.fuelTransactionRepo.UpdateLinkAllocatedAmount(ctx, fuelTransactionID, orderID, allocatedAmount)
}

func (s *FuelTransactionServiceOptimized) ConfirmFuelTransactionLink(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	return s.fuelTransactionRepo.ConfirmFuelTransactionLink(ctx, fuelTransactionID, orderID)
}

func (s *FuelTransactionServiceOptimized) CancelFuelTransactionLink(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	return s.fuelTransactionRepo.CancelFuelTransactionLink(ctx, fuelTransactionID, orderID)
}

func (s *FuelTransactionServiceOptimized) CleanupFuelTransactionLinks(ctx context.Context, fuelTransactionID repository.ID) error {
	return s.fuelTransactionRepo.CleanupFuelTransactionLinks(ctx, fuelTransactionID)
}

func (s *FuelTransactionServiceOptimized) ProcessFuelTransactionStatus(ctx context.Context, fuelTransactionID repository.ID, force bool) error {
	return s.fuelTransactionRepo.ProcessFuelTransactionStatus(ctx, fuelTransactionID, force)
}

func (s *FuelTransactionServiceOptimized) GetOrderFuelTransactions(ctx context.Context, orderID repository.ID) ([]repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.GetOrderFuelTransactions(ctx, orderID)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransactionStats(ctx context.Context, filter repository.FuelTransactionFilter) (repository.FuelTransactionStats, error) {
	return s.fuelTransactionRepo.GetFuelTransactionStats(ctx, filter)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransactionsByDateRange(ctx context.Context, stationID repository.ID, startDate, endDate time.Time) ([]repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.GetFuelTransactionsByDateRange(ctx, stationID, startDate, endDate)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransactionsByShift(ctx context.Context, shiftID repository.ID) ([]repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.GetFuelTransactionsByShift(ctx, shiftID)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransactionsByEmployee(ctx context.Context, employeeID repository.ID) ([]repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.GetFuelTransactionsByEmployee(ctx, employeeID)
}

func (s *FuelTransactionServiceOptimized) GetFuelTransactionsByMember(ctx context.Context, memberID repository.ID) ([]repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.GetFuelTransactionsByMember(ctx, memberID)
}

// UpdatePumpReadings 更新燃油交易的泵码数
func (s *FuelTransactionServiceOptimized) UpdatePumpReadings(ctx context.Context, fuelTransactionID repository.ID, startTotalizer *float64, endTotalizer *float64) (repository.FuelTransaction, error) {
	// 参数验证
	if startTotalizer == nil && endTotalizer == nil {
		return repository.FuelTransaction{}, fmt.Errorf("至少需要提供一个泵码数字段")
	}

	// 验证泵码数的合理性
	if startTotalizer != nil && *startTotalizer < 0 {
		return repository.FuelTransaction{}, fmt.Errorf("起始泵码数不能为负数")
	}
	if endTotalizer != nil && *endTotalizer < 0 {
		return repository.FuelTransaction{}, fmt.Errorf("截止泵码数不能为负数")
	}
	if startTotalizer != nil && endTotalizer != nil && *endTotalizer < *startTotalizer {
		return repository.FuelTransaction{}, fmt.Errorf("截止泵码数不能小于起始泵码数")
	}

	// 获取现有的燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransaction{}, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 检查交易状态 - 只允许特定状态下修改
	if transaction.Status != repository.FuelTransactionStatusPending &&
	   transaction.Status != repository.FuelTransactionStatusProcessed {
		return repository.FuelTransaction{}, fmt.Errorf("只能修改待处理或已处理状态的交易泵码数")
	}

	// 更新泵码数字段
	if startTotalizer != nil {
		transaction.StartTotalizer = startTotalizer
	}
	if endTotalizer != nil {
		transaction.EndTotalizer = endTotalizer
	}

	// 更新时间戳
	transaction.UpdatedAt = time.Now()

	// 保存更新
	updatedTransaction, err := s.fuelTransactionRepo.Update(ctx, transaction)
	if err != nil {
		return repository.FuelTransaction{}, fmt.Errorf("更新燃油交易失败: %w", err)
	}

	return updatedTransaction, nil
}
