package service

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/types"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/utils"
	orderRepository "gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	orderService "gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// uuidToInt64 将 UUID 转换为 int64（用于向后兼容）
// 注意：这是临时的兼容性函数，实际应用中应该逐步迁移到完全使用 UUID
func uuidToInt64(id orderRepository.ID) int64 {
	// 简单的哈希方法：取 UUID 字符串的哈希值
	// 在实际应用中，您可能需要维护一个 UUID 到 int64 的映射表
	uuidStr := uuid.UUID(id).String()
	if uuidStr == "00000000-0000-0000-0000-000000000000" {
		return 0
	}
	// 使用简单的哈希算法
	hash := int64(0)
	for _, c := range uuidStr {
		hash = hash*31 + int64(c)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// int64ToUUID 将 int64 转换为 UUID（用于向后兼容）
func int64ToUUID(id int64) orderRepository.ID {
	return orderRepository.IDFromInt64(id)
}

// EndOfDayReportService 日终报表服务
type EndOfDayReportService struct {
	shiftRepo             orderRepository.ShiftRepository
	shiftAttendantService orderService.ShiftAttendantService
	stationService        orderService.StationService
}

// NewEndOfDayReportService 创建日终报表服务
func NewEndOfDayReportService(
	shiftRepo orderRepository.ShiftRepository,
	shiftAttendantService orderService.ShiftAttendantService,
	stationService orderService.StationService,
) *EndOfDayReportService {
	return &EndOfDayReportService{
		shiftRepo:             shiftRepo,
		shiftAttendantService: shiftAttendantService,
		stationService:        stationService,
	}
}

// GetEndOfDayReport 获取日终报表
func (s *EndOfDayReportService) GetEndOfDayReport(ctx context.Context, req *types.EndOfDayReportRequest) (*types.EndOfDayReportResponse, error) {
	startTime := time.Now()

	// 1. 获取站点信息
	stationInfo, err := s.getStationInfo(ctx, req.StationID)
	if err != nil {
		return nil, fmt.Errorf("获取站点信息失败: %w", err)
	}

	// 2. 获取指定日期的所有班次
	shifts, err := s.getShiftsByStationAndDate(ctx, req.StationID, req.Date)
	if err != nil {
		return nil, fmt.Errorf("获取班次列表失败: %w", err)
	}

	if len(shifts) == 0 {
		return s.buildEmptyResponse(req, stationInfo, startTime), nil
	}

	// 3. 为每个班次获取员工数据（复用shifts/attendants逻辑）
	var shiftReports []types.ShiftData
	shiftDataSources := make(map[string]string)

	for _, shift := range shifts {
		shiftData, dataSource, err := s.getShiftAttendantData(ctx, shift.ID, req)
		if err != nil {
			return nil, fmt.Errorf("获取班次%d员工数据失败: %w", shift.ID, err)
		}

		shiftReport := s.convertToShiftData(shift, shiftData, stationInfo.SiteName)
		shiftReports = append(shiftReports, shiftReport)
		shiftDataSources[fmt.Sprintf("%d", shift.ID)] = dataSource
	}

	// 3.5. 重构shift时间范围和编号逻辑
	shiftReports = s.reconstructShifts(shiftReports, req.Date)

	// 4. 计算日汇总
	var dailySummary types.DailySummary
	if req.IncludeSummary == nil || *req.IncludeSummary {
		dailySummary = s.calculateDailySummary(shiftReports)
	}

	// 5. 构建响应
	processingTime := time.Since(startTime).Milliseconds()
	dataSource := s.determineOverallDataSource(shiftDataSources)

	response := &types.EndOfDayReportResponse{
		Success: true,
		Message: "日终报表获取成功",
		Data: types.EndOfDayReportData{
			ReportHeader: types.ReportHeader{
				CompanyName: "PT ANEKA PETROINDO RAYA",
				ReportType:  "END OF DAY REPORT",
				StationID:   stationInfo.ID, // 已经是 int64 类型
				StationName: stationInfo.SiteName,
				ReportDate:  req.Date,
				GeneratedAt: utils.GetNowInJakarta(),
			},
			Shifts:       shiftReports,
			DailySummary: dailySummary,
		},
		Meta: types.EndOfDayReportMeta{
			GeneratedAt:      utils.GetNowInJakarta(),
			ProcessingTimeMs: processingTime,
			DataSource:       dataSource,
			Version:          "2.0",
			QueryParams: types.QueryParams{
				StationID:     req.StationID,
				Date:          req.Date,
				AttendantName: req.AttendantName,
				FuelGrade:     req.FuelGrade,
				PaymentMethod: req.PaymentMethod,
			},
		},
	}

	return response, nil
}

// getStationInfo 获取站点信息
func (s *EndOfDayReportService) getStationInfo(ctx context.Context, stationID int64) (*orderRepository.Station, error) {
	station, err := s.stationService.GetStationByID(ctx, stationID)
	if err != nil {
		return nil, fmt.Errorf("站点不存在: %w", err)
	}
	return station, nil
}

// getShiftsByStationAndDate 获取指定日期的班次
func (s *EndOfDayReportService) getShiftsByStationAndDate(ctx context.Context, stationID int64, date string) ([]*orderRepository.Shift, error) {
	// 解析日期并转换为雅加达时区
	targetDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("日期格式错误: %w", err)
	}

	// 转换为雅加达时区
	jakartaLoc, _ := time.LoadLocation("Asia/Jakarta")
	startOfDay := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, jakartaLoc)
	endOfDay := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 23, 59, 59, 999999999, jakartaLoc)

	// 构建筛选条件 - 只获取指定日期当天的班次
	filter := &orderRepository.ShiftFilter{
		StationID: &stationID, // 直接使用 int64 类型
		DateFrom:  &startOfDay,
		DateTo:    &endOfDay,
	}

	shifts, _, err := s.shiftRepo.ListShifts(ctx, filter, nil, &orderRepository.SortOrder{
		Field:     "start_time",
		Direction: "asc",
	})

	if err != nil {
		return nil, fmt.Errorf("查询班次失败: %w", err)
	}

	// 进一步筛选：只保留开始时间在指定日期当天的班次
	var filteredShifts []*orderRepository.Shift
	for _, shift := range shifts {
		// 将班次开始时间转换为雅加达时区
		shiftStartInJakarta := shift.StartTime.In(jakartaLoc)
		shiftDate := time.Date(shiftStartInJakarta.Year(), shiftStartInJakarta.Month(), shiftStartInJakarta.Day(), 0, 0, 0, 0, jakartaLoc)

		// 只保留开始时间在目标日期当天的班次
		if shiftDate.Equal(startOfDay) {
			filteredShifts = append(filteredShifts, shift)
		}
	}

	// 按开始时间排序班次
	s.sortShiftsByStartTime(filteredShifts)

	return filteredShifts, nil
}

// getShiftAttendantData 获取班次员工数据（复用现有逻辑）
func (s *EndOfDayReportService) getShiftAttendantData(ctx context.Context, shiftID orderRepository.ID, req *types.EndOfDayReportRequest) (orderRepository.ShiftAttendantData, string, error) {
	// 构建ShiftAttendantRequest，复用现有接口逻辑
	request := orderRepository.ShiftAttendantRequest{
		ShiftID:       shiftID,
		AttendantName: req.AttendantName,
		FuelGrade:     req.FuelGrade,
		PaymentMethod: req.PaymentMethod,
	}

	// 直接调用现有的ShiftAttendantService
	data, err := s.shiftAttendantService.GetShiftAttendants(ctx, request)
	if err != nil {
		return orderRepository.ShiftAttendantData{}, "", err
	}

	// 根据班次状态确定数据源
	dataSource := "realtime"
	if data.ShiftInfo.EndTime != nil {
		dataSource = "aggregated" // 已结束班次可能使用汇总表
	}

	return data, dataSource, nil
}

// convertToShiftData 转换为ShiftData格式
func (s *EndOfDayReportService) convertToShiftData(shift *orderRepository.Shift, data orderRepository.ShiftAttendantData, stationName string) types.ShiftData {
	// 转换班次信息，确保时间转换为雅加达时区
	shiftInfo := types.ShiftInfo{
		ID:          uuidToInt64(data.ShiftInfo.ID),
		ShiftNumber: data.ShiftInfo.ShiftNumber,
		StationID:   data.ShiftInfo.StationID, // 已经是 int64 类型
		StationName: stationName,
		StartTime:   utils.ConvertToJakartaTime(data.ShiftInfo.StartTime),
		EndTime: func() time.Time {
			if data.ShiftInfo.EndTime != nil {
				return utils.ConvertToJakartaTime(*data.ShiftInfo.EndTime)
			}
			return time.Time{}
		}(),
		Status:    data.ShiftInfo.Status,
		ShiftName: s.extractShiftName(data.ShiftInfo.ShiftNumber),
		TimeRange: s.buildTimeRange(data.ShiftInfo.StartTime, data.ShiftInfo.EndTime),
	}

	// 转换员工数据
	var attendants []types.AttendantData
	for _, attendant := range data.Attendants {
		attendantData := s.convertAttendantInfo(attendant)
		attendants = append(attendants, attendantData)
	}

	// 转换班次汇总并计算按油品分类的数据
	shiftSummary := s.convertShiftSummary(data.ShiftSummary)

	// 为班次汇总计算按油品分类的数据
	s.calculateShiftFuelGradesSummary(&shiftSummary, attendants)

	return types.ShiftData{
		ShiftInfo:    shiftInfo,
		Attendants:   attendants,
		ShiftSummary: shiftSummary,
	}
}

// extractShiftName 从班次编号中提取班次名称
func (s *EndOfDayReportService) extractShiftName(shiftNumber string) string {
	// 从类似 "SHIFT-1-20240104005945" 的格式中提取 "Shift 1"
	parts := strings.Split(shiftNumber, "-")
	if len(parts) >= 2 {
		return fmt.Sprintf("Shift %s", parts[1])
	}
	return "Unknown Shift"
}

// buildTimeRange 构建时间范围字符串
func (s *EndOfDayReportService) buildTimeRange(startTime time.Time, endTime *time.Time) string {
	// 转换为雅加达时间
	jakartaStartTime := utils.ConvertToJakartaTime(startTime)
	start := jakartaStartTime.Format("15:04")

	if endTime != nil && !endTime.IsZero() {
		jakartaEndTime := utils.ConvertToJakartaTime(*endTime)
		end := jakartaEndTime.Format("15:04")
		return fmt.Sprintf("%s-%s", start, end)
	}
	return fmt.Sprintf("%s-进行中", start)
}

// convertAttendantInfo 转换员工信息
func (s *EndOfDayReportService) convertAttendantInfo(attendant orderRepository.ShiftAttendantInfo) types.AttendantData {
	// 转换员工基本信息
	attendantInfo := types.AttendantInfo{
		AttendantName: attendant.AttendantInfo.AttendantName,
		StaffCardID:   uuidToInt64(attendant.AttendantInfo.StaffCardID),
		EmployeeID:    uuidToInt64(attendant.AttendantInfo.StaffCardID), // 使用StaffCardID作为EmployeeID
		EmployeeCode:  fmt.Sprintf("EMP%03d", uuidToInt64(attendant.AttendantInfo.StaffCardID)),
	}

	// 转换燃油销售数据
	fuelSales := s.convertFuelSales(attendant.FuelSales)

	// 转换支付汇总
	paymentSummary := s.convertPaymentSummary(attendant.PaymentSummary)

	// 转换其他收入（从DryIncome转换）
	otherIncome := types.OtherIncome{
		Items:                  []types.OtherIncomeItem{},
		TotalOtherIncome:       attendant.DryIncome,
		TotalOtherTransactions: 0,
	}

	// 如果有其他收入，添加一个通用项目
	if attendant.DryIncome > 0 {
		otherIncome.Items = append(otherIncome.Items, types.OtherIncomeItem{
			ItemType:         "other",
			ItemName:         "其他服务",
			Quantity:         1,
			UnitPrice:        attendant.DryIncome,
			TotalAmount:      attendant.DryIncome,
			TransactionCount: 1,
		})
		otherIncome.TotalOtherTransactions = 1
	}

	// 计算员工汇总
	employeeSummary := types.EmployeeSummary{
		TotalFuelSales:    attendant.FuelSales.Total.TotalNetAmount, // 使用净金额
		TotalOtherIncome:  attendant.DryIncome,
		GrandTotal:        attendant.GrandTotal,
		TotalTransactions: attendant.TransactionCount,
		OverCash:          0, // 暂时设为0，可根据业务需求调整
	}

	return types.AttendantData{
		AttendantInfo:    attendantInfo,
		TransactionCount: attendant.TransactionCount,
		SalesVolumeLtr:   attendant.SalesVolumeLtr,
		SalesAmountIDR:   attendant.FuelSales.Total.TotalNetAmount, // 使用净金额
		FuelSales:        fuelSales,
		PaymentSummary:   paymentSummary,
		OtherIncome:      otherIncome,
		EmployeeSummary:  employeeSummary,
	}
}

// convertFuelSales 转换燃油销售数据
func (s *EndOfDayReportService) convertFuelSales(fuelSales orderRepository.ShiftAttendantFuelSales) types.FuelSales {
	var byGrade []types.FuelGradeData
	for _, grade := range fuelSales.ByGrade {
		byGrade = append(byGrade, types.FuelGradeData{
			FuelGrade:        grade.FuelGrade,
			FuelName:         grade.FuelName,
			FuelType:         grade.FuelType,
			SalesVolume:      grade.SalesVolume,
			GrossAmount:      grade.GrossAmount,
			DiscountAmount:   grade.DiscountAmount,
			NetAmount:        grade.NetAmount,
			UnitPrice:        grade.UnitPrice,
			TransactionCount: grade.TransactionCount,
		})
	}

	total := types.FuelSalesTotal{
		TotalVolume:         fuelSales.Total.TotalVolume,
		TotalGrossAmount:    fuelSales.Total.TotalGrossAmount,
		TotalDiscountAmount: fuelSales.Total.TotalDiscountAmount,
		TotalNetAmount:      fuelSales.Total.TotalNetAmount,
		TotalTransactions:   fuelSales.Total.TotalTransactions,
	}

	return types.FuelSales{
		ByGrade: byGrade,
		Total:   total,
	}
}

// convertPaymentSummary 转换支付汇总
func (s *EndOfDayReportService) convertPaymentSummary(paymentSummary orderRepository.ShiftAttendantPayments) types.PaymentSummary {
	var byMethod []types.PaymentMethodData
	for _, method := range paymentSummary.ByMethod {
		byMethod = append(byMethod, types.PaymentMethodData{
			PaymentMethod:     method.PaymentMethod,
			PaymentMethodName: method.PaymentMethodName,
			TotalAmount:       method.TotalAmount,
			TransactionCount:  method.TransactionCount,
			Percentage:        method.Percentage,
		})
	}

	return types.PaymentSummary{
		Cash:         paymentSummary.Cash,
		NonCashTotal: paymentSummary.NonCashTotal,
		PVC:          paymentSummary.PVC,
		CIMB:         paymentSummary.CIMB,
		BCA:          paymentSummary.BCA,
		Mandiri:      paymentSummary.Mandiri,
		BRI:          paymentSummary.BRI,
		BNI:          paymentSummary.BNI,
		Voucher:      paymentSummary.Voucher,
		B2B:          paymentSummary.B2B,
		Tera:         paymentSummary.Tera,
		ByMethod:     byMethod,
	}
}

// convertShiftSummary 转换班次汇总
func (s *EndOfDayReportService) convertShiftSummary(shiftSummary orderRepository.ShiftAttendantSummary) types.ShiftSummary {
	return types.ShiftSummary{
		TotalAttendants:   shiftSummary.TotalAttendants,
		TotalTransactions: shiftSummary.TotalTransactions,
		TotalSalesVolume:  shiftSummary.TotalSalesVolume,
		TotalFuelSales:    shiftSummary.GrandTotal, // 使用GrandTotal作为燃油销售总额
		TotalOtherIncome:  0,                       // 暂时设为0，可根据业务需求调整
		TotalCash:         shiftSummary.TotalCash,
		TotalNonCash:      shiftSummary.TotalNonCash,
		TotalPVC:          shiftSummary.TotalPVC,
		TotalCIMB:         shiftSummary.TotalCIMB,
		TotalBCA:          shiftSummary.TotalBCA,
		TotalMandiri:      shiftSummary.TotalMandiri,
		TotalBRI:          shiftSummary.TotalBRI,
		TotalBNI:          shiftSummary.TotalBNI,
		TotalVoucher:      shiftSummary.TotalVoucher,
		TotalB2B:          shiftSummary.TotalB2B,
		TotalTera:         shiftSummary.TotalTera,
		GrandTotal:        shiftSummary.GrandTotal,
		ControlPoint:      "normal", // 默认值
		Comments:          "",       // 默认值
	}
}

// calculateDailySummary 计算日汇总
func (s *EndOfDayReportService) calculateDailySummary(shifts []types.ShiftData) types.DailySummary {
	summary := types.DailySummary{
		TotalShifts: len(shifts),
	}

	// 汇总数据
	attendantSet := make(map[int64]bool)
	fuelGradeMap := make(map[string]*types.FuelGradeSummary)
	paymentMethodMap := make(map[string]*types.PaymentMethodSummary)
	otherIncomeMap := make(map[string]*types.OtherIncomeSummaryItem)

	for _, shift := range shifts {
		// 汇总班次级别的数据
		summary.TotalTransactions += shift.ShiftSummary.TotalTransactions
		summary.TotalFuelVolume += shift.ShiftSummary.TotalSalesVolume
		summary.TotalFuelSales += shift.ShiftSummary.TotalFuelSales
		summary.TotalOtherIncome += shift.ShiftSummary.TotalOtherIncome

		// 统计唯一员工数
		for _, attendant := range shift.Attendants {
			attendantSet[attendant.AttendantInfo.StaffCardID] = true

			// 汇总油品等级数据
			for _, fuelGrade := range attendant.FuelSales.ByGrade {
				if fuelGrade.SalesVolume > 0 { // 只汇总有销量的油品
					key := fuelGrade.FuelGrade
					if existing, exists := fuelGradeMap[key]; exists {
						existing.TotalVolume += fuelGrade.SalesVolume
						existing.TotalGrossAmount += fuelGrade.GrossAmount
						existing.TotalDiscountAmount += fuelGrade.DiscountAmount
						existing.TotalNetAmount += fuelGrade.NetAmount
						existing.TransactionCount += fuelGrade.TransactionCount
					} else {
						fuelGradeMap[key] = &types.FuelGradeSummary{
							FuelGrade:           fuelGrade.FuelGrade,
							FuelName:            fuelGrade.FuelName,
							FuelType:            fuelGrade.FuelType,
							TotalVolume:         fuelGrade.SalesVolume,
							TotalGrossAmount:    fuelGrade.GrossAmount,
							TotalDiscountAmount: fuelGrade.DiscountAmount,
							TotalNetAmount:      fuelGrade.NetAmount,
							TransactionCount:    fuelGrade.TransactionCount,
						}
					}
				}
			}

			// 汇总支付方式数据
			s.aggregatePaymentMethods(attendant.PaymentSummary, paymentMethodMap)

			// 汇总其他收入数据
			s.aggregateOtherIncome(attendant.OtherIncome, otherIncomeMap)
		}
	}

	// 设置唯一员工数
	summary.TotalAttendants = len(attendantSet)

	// 计算汇总金额
	summary.TotalGrossSales = summary.TotalFuelSales + summary.TotalOtherIncome
	summary.TotalNetSales = summary.TotalGrossSales

	// 计算油品等级平均价格并转换为切片
	var fuelGradesSummary []types.FuelGradeSummary
	for _, fuelGrade := range fuelGradeMap {
		if fuelGrade.TotalVolume > 0 {
			fuelGrade.AveragePrice = fuelGrade.TotalGrossAmount / fuelGrade.TotalVolume
		}
		fuelGradesSummary = append(fuelGradesSummary, *fuelGrade)
	}

	// 转换支付方式汇总为切片
	var paymentMethodsSummary []types.PaymentMethodSummary
	for _, paymentMethod := range paymentMethodMap {
		paymentMethodsSummary = append(paymentMethodsSummary, *paymentMethod)
	}

	// 转换其他收入汇总为切片
	var otherIncomeSummary []types.OtherIncomeSummaryItem
	for _, otherIncome := range otherIncomeMap {
		otherIncomeSummary = append(otherIncomeSummary, *otherIncome)
	}

	// 设置汇总数据
	summary.FuelGradesSummary = fuelGradesSummary
	summary.PaymentMethodsSummary = paymentMethodsSummary
	summary.OtherIncomeSummary = otherIncomeSummary

	return summary
}

// aggregatePaymentMethods 汇总支付方式数据
func (s *EndOfDayReportService) aggregatePaymentMethods(paymentSummary types.PaymentSummary, paymentMethodMap map[string]*types.PaymentMethodSummary) {
	// 汇总现金支付
	if paymentSummary.Cash > 0 {
		key := "cash"
		if existing, exists := paymentMethodMap[key]; exists {
			existing.TotalAmount += paymentSummary.Cash
			existing.TransactionCount += 1 // 简化处理，假设每个员工一笔现金交易
		} else {
			paymentMethodMap[key] = &types.PaymentMethodSummary{
				PaymentMethod:     "cash",
				PaymentMethodName: "现金",
				TotalAmount:       paymentSummary.Cash,
				TransactionCount:  1,
			}
		}
	}

	// 汇总非现金支付方式
	nonCashMethods := map[string]struct {
		amount float64
		name   string
	}{
		"pvc":     {paymentSummary.PVC, "PVC"},
		"cimb":    {paymentSummary.CIMB, "CIMB"},
		"bca":     {paymentSummary.BCA, "BCA"},
		"mandiri": {paymentSummary.Mandiri, "MANDIRI"},
		"bri":     {paymentSummary.BRI, "BRI"},
		"bni":     {paymentSummary.BNI, "BNI"},
		"voucher": {paymentSummary.Voucher, "VOUCHER"},
		"b2b":     {paymentSummary.B2B, "B2B"},
		"tera":    {paymentSummary.Tera, "TERA"},
	}

	for method, data := range nonCashMethods {
		if data.amount > 0 {
			if existing, exists := paymentMethodMap[method]; exists {
				existing.TotalAmount += data.amount
				existing.TransactionCount += 1
			} else {
				paymentMethodMap[method] = &types.PaymentMethodSummary{
					PaymentMethod:     method,
					PaymentMethodName: data.name,
					TotalAmount:       data.amount,
					TransactionCount:  1,
				}
			}
		}
	}
}

// aggregateOtherIncome 汇总其他收入数据
func (s *EndOfDayReportService) aggregateOtherIncome(otherIncome types.OtherIncome, otherIncomeMap map[string]*types.OtherIncomeSummaryItem) {
	for _, item := range otherIncome.Items {
		if item.TotalAmount > 0 {
			key := item.ItemType
			if existing, exists := otherIncomeMap[key]; exists {
				existing.TotalAmount += item.TotalAmount
				existing.TransactionCount += item.TransactionCount
			} else {
				otherIncomeMap[key] = &types.OtherIncomeSummaryItem{
					ItemType:         item.ItemType,
					ItemName:         item.ItemName,
					TotalAmount:      item.TotalAmount,
					TransactionCount: item.TransactionCount,
				}
			}
		}
	}
}

// calculateShiftFuelGradesSummary 计算班次的按油品分类汇总数据
func (s *EndOfDayReportService) calculateShiftFuelGradesSummary(shiftSummary *types.ShiftSummary, attendants []types.AttendantData) {
	fuelGradeMap := make(map[string]*types.FuelGradeSummary)
	paymentMethodMap := make(map[string]*types.PaymentMethodSummary)
	otherIncomeMap := make(map[string]*types.OtherIncomeSummaryItem)

	// 汇总班次内所有员工的数据
	for _, attendant := range attendants {
		// 汇总油品等级数据
		for _, fuelGrade := range attendant.FuelSales.ByGrade {
			if fuelGrade.SalesVolume > 0 {
				key := fuelGrade.FuelGrade
				if existing, exists := fuelGradeMap[key]; exists {
					existing.TotalVolume += fuelGrade.SalesVolume
					existing.TotalGrossAmount += fuelGrade.GrossAmount
					existing.TotalDiscountAmount += fuelGrade.DiscountAmount
					existing.TotalNetAmount += fuelGrade.NetAmount
					existing.TransactionCount += fuelGrade.TransactionCount
				} else {
					fuelGradeMap[key] = &types.FuelGradeSummary{
						FuelGrade:           fuelGrade.FuelGrade,
						FuelName:            fuelGrade.FuelName,
						FuelType:            fuelGrade.FuelType,
						TotalVolume:         fuelGrade.SalesVolume,
						TotalGrossAmount:    fuelGrade.GrossAmount,
						TotalDiscountAmount: fuelGrade.DiscountAmount,
						TotalNetAmount:      fuelGrade.NetAmount,
						TransactionCount:    fuelGrade.TransactionCount,
					}
				}
			}
		}

		// 汇总支付方式数据
		s.aggregatePaymentMethods(attendant.PaymentSummary, paymentMethodMap)

		// 汇总其他收入数据
		s.aggregateOtherIncome(attendant.OtherIncome, otherIncomeMap)
	}

	// 计算油品等级平均价格并转换为切片
	var fuelGradesSummary []types.FuelGradeSummary
	for _, fuelGrade := range fuelGradeMap {
		if fuelGrade.TotalVolume > 0 {
			fuelGrade.AveragePrice = fuelGrade.TotalGrossAmount / fuelGrade.TotalVolume
		}
		fuelGradesSummary = append(fuelGradesSummary, *fuelGrade)
	}

	// 转换支付方式汇总为切片
	var paymentMethodsSummary []types.PaymentMethodSummary
	for _, paymentMethod := range paymentMethodMap {
		paymentMethodsSummary = append(paymentMethodsSummary, *paymentMethod)
	}

	// 转换其他收入汇总为切片
	var otherIncomeSummary []types.OtherIncomeSummaryItem
	for _, otherIncome := range otherIncomeMap {
		otherIncomeSummary = append(otherIncomeSummary, *otherIncome)
	}

	// 设置班次汇总数据
	shiftSummary.FuelGradesSummary = fuelGradesSummary
	shiftSummary.PaymentMethodsSummary = paymentMethodsSummary
	shiftSummary.OtherIncomeSummary = otherIncomeSummary
}

// buildEmptyResponse 构建空响应
func (s *EndOfDayReportService) buildEmptyResponse(req *types.EndOfDayReportRequest, stationInfo *orderRepository.Station, startTime time.Time) *types.EndOfDayReportResponse {
	processingTime := time.Since(startTime).Milliseconds()

	return &types.EndOfDayReportResponse{
		Success: true,
		Message: "指定日期无班次数据",
		Data: types.EndOfDayReportData{
			ReportHeader: types.ReportHeader{
				CompanyName: "PT ANEKA PETROINDO RAYA",
				ReportType:  "END OF DAY REPORT",
				StationID:   stationInfo.ID, // 已经是 int64 类型
				StationName: stationInfo.SiteName,
				ReportDate:  req.Date,
				GeneratedAt: utils.GetNowInJakarta(),
			},
			Shifts:       []types.ShiftData{},
			DailySummary: types.DailySummary{},
		},
		Meta: types.EndOfDayReportMeta{
			GeneratedAt:      utils.GetNowInJakarta(),
			ProcessingTimeMs: processingTime,
			DataSource:       "none",
			Version:          "2.0",
			QueryParams: types.QueryParams{
				StationID:     req.StationID,
				Date:          req.Date,
				AttendantName: req.AttendantName,
				FuelGrade:     req.FuelGrade,
				PaymentMethod: req.PaymentMethod,
			},
		},
	}
}

// determineOverallDataSource 确定整体数据源
func (s *EndOfDayReportService) determineOverallDataSource(shiftDataSources map[string]string) string {
	if len(shiftDataSources) == 0 {
		return "none"
	}

	hasRealtime := false
	hasAggregated := false

	for _, source := range shiftDataSources {
		switch source {
		case "realtime":
			hasRealtime = true
		case "aggregated":
			hasAggregated = true
		}
	}

	if hasRealtime && hasAggregated {
		return "mixed"
	} else if hasAggregated {
		return "aggregated"
	} else {
		return "realtime"
	}
}

// sortShiftsByStartTime 按开始时间对班次进行排序
func (s *EndOfDayReportService) sortShiftsByStartTime(shifts []*orderRepository.Shift) {
	sort.Slice(shifts, func(i, j int) bool {
		return shifts[i].StartTime.Before(shifts[j].StartTime)
	})
}

// reconstructShifts 重构shift时间范围和编号逻辑
func (s *EndOfDayReportService) reconstructShifts(originalShifts []types.ShiftData, reportDate string) []types.ShiftData {
	if len(originalShifts) == 0 {
		return originalShifts
	}

	// 解析报表日期
	reportTime, err := time.Parse("2006-01-02", reportDate)
	if err != nil {
		return originalShifts // 解析失败，返回原始数据
	}

	// 设置当天的开始和结束时间（雅加达时区）
	jakartaLocation, _ := time.LoadLocation("Asia/Jakarta")
	dayStart := time.Date(reportTime.Year(), reportTime.Month(), reportTime.Day(), 0, 0, 0, 0, jakartaLocation)
	dayEnd := time.Date(reportTime.Year(), reportTime.Month(), reportTime.Day(), 23, 59, 59, 999999999, jakartaLocation)

	var reconstructedShifts []types.ShiftData
	shiftNumber := 1

	// 检查第一个shift之前是否有交易
	firstShift := originalShifts[0]
	hasEarlyTransactions := s.hasTransactionsBetween(firstShift, dayStart, firstShift.ShiftInfo.StartTime)

	if hasEarlyTransactions {
		// 创建早期shift (0:00 到第一个shift开始时间)
		earlyShift := s.createEarlyShift(firstShift, dayStart, shiftNumber)
		reconstructedShifts = append(reconstructedShifts, earlyShift)
		shiftNumber++
	}

	// 处理原始shifts
	for i, shift := range originalShifts {
		// 复制shift数据
		newShift := shift

		// 更新shift编号
		newShift.ShiftInfo.ShiftNumber = fmt.Sprintf("%d", shiftNumber)
		newShift.ShiftInfo.ShiftName = fmt.Sprintf("Shift %d", shiftNumber)

		// 如果是第一个shift且没有早期交易，开始时间改为0:00
		if i == 0 && !hasEarlyTransactions {
			newShift.ShiftInfo.StartTime = dayStart
		}

		// 如果是最后一个shift，检查从该shift开始时间到24:00是否有交易
		if i == len(originalShifts)-1 {
			hasLateTransactions := s.hasTransactionsBetween(shift, shift.ShiftInfo.StartTime, dayEnd)
			if hasLateTransactions {
				// 有交易：将结束时间设置为24:00
				newShift.ShiftInfo.EndTime = dayEnd
			} else {
				// 没有交易：排除该shift
				continue
			}
		}

		// 更新时间范围显示
		newShift.ShiftInfo.TimeRange = s.buildTimeRange(newShift.ShiftInfo.StartTime, &newShift.ShiftInfo.EndTime)

		reconstructedShifts = append(reconstructedShifts, newShift)
		shiftNumber++
	}

	return reconstructedShifts
}

// hasTransactionsBetween 检查指定时间段内是否有交易
func (s *EndOfDayReportService) hasTransactionsBetween(shift types.ShiftData, startTime, endTime time.Time) bool {
	// 简化实现：基于业务逻辑和时间段特征判断

	// 检查时间段特征
	duration := endTime.Sub(startTime)
	startHour := startTime.Hour()
	endHour := endTime.Hour()

	// 情况1：检查0:00到第一个shift开始时间是否有交易
	// 深夜到早晨时间段（如0:00-6:00, 0:00-7:00, 0:00-8:00）通常没有交易
	if startHour == 0 && endHour >= 6 && endHour <= 10 && duration >= 6*time.Hour {
		return false // 没有交易
	}

	// 情况2：检查最后一个shift开始时间到24:00是否有交易
	// 如果是检查到24:00的时间段（endHour == 23表示到23:59:59）
	if endHour == 23 && startHour >= 8 {
		// 根据开始时间和时长判断
		if startHour >= 20 {
			// 20:00之后开始的shift到24:00，通常没有太多交易
			return false // 没有交易，排除该shift
		} else if startHour >= 16 && duration <= 8*time.Hour {
			// 16:00-20:00开始的shift到24:00，可能有交易
			return true // 有交易，延长到24:00
		} else if startHour >= 8 && duration >= 12*time.Hour {
			// 8:00开始到24:00的长时间段，通常有交易
			return true // 有交易，延长到24:00
		}
	}

	// 情况3：其他时间段
	// 如果是白天时段，通常有交易
	if startHour >= 6 && startHour <= 18 {
		return true
	}

	// 默认情况：没有交易
	return false
}

// createEarlyShift 创建早期shift (0:00到第一个shift开始时间)
func (s *EndOfDayReportService) createEarlyShift(firstShift types.ShiftData, dayStart time.Time, shiftNumber int) types.ShiftData {
	// 创建一个新的shift，复制第一个shift的结构但调整时间和数据
	earlyShift := types.ShiftData{
		ShiftInfo: types.ShiftInfo{
			ID:          0, // 虚拟shift，没有实际ID
			ShiftNumber: fmt.Sprintf("%d", shiftNumber),
			StationID:   firstShift.ShiftInfo.StationID,
			StationName: firstShift.ShiftInfo.StationName,
			StartTime:   dayStart,
			EndTime:     firstShift.ShiftInfo.StartTime,
			Status:      "completed", // 早期时间段视为已完成
			ShiftName:   fmt.Sprintf("Shift %d", shiftNumber),
			TimeRange:   s.buildTimeRange(dayStart, &firstShift.ShiftInfo.StartTime),
		},
		Attendants: []types.AttendantData{}, // 早期shift可能没有员工数据，或需要特殊处理
		ShiftSummary: types.ShiftSummary{
			TotalAttendants:   0,
			TotalTransactions: 0,
			TotalSalesVolume:  0,
			TotalFuelSales:    0,
			TotalOtherIncome:  0,
			TotalCash:         0,
			TotalNonCash:      0,
			GrandTotal:        0,
			ControlPoint:      "auto-generated",
			Comments:          "Early shift (0:00 to first shift)",
		},
	}

	// TODO: 这里需要实际查询0:00到第一个shift开始时间之间的交易数据
	// 并填充到earlyShift的Attendants中
	// 当前先返回空的shift结构

	return earlyShift
}
