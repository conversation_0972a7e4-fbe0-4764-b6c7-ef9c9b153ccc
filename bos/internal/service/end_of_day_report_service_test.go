package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/types"
	orderRepository "gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// MockShiftRepository 模拟班次仓储
type MockShiftRepository struct {
	mock.Mock
}

func (m *MockShiftRepository) ListShifts(ctx context.Context, filter *orderRepository.ShiftFilter, pagination *orderRepository.Pagination, sort *orderRepository.SortOrder) ([]*orderRepository.Shift, int, error) {
	args := m.Called(ctx, filter, pagination, sort)
	return args.Get(0).([]*orderRepository.Shift), args.Int(1), args.Error(2)
}

func (m *MockShiftRepository) CreateShift(ctx context.Context, shift *orderRepository.Shift) (*orderRepository.Shift, error) {
	args := m.Called(ctx, shift)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) UpdateShift(ctx context.Context, shift *orderRepository.Shift) (*orderRepository.Shift, error) {
	args := m.Called(ctx, shift)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) UpdateShiftEndTime(ctx context.Context, shiftID orderRepository.ID, endTime time.Time) (*orderRepository.Shift, error) {
	args := m.Called(ctx, shiftID, endTime)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) GetShiftByID(ctx context.Context, id orderRepository.ID) (*orderRepository.Shift, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) GetShiftByNumber(ctx context.Context, shiftNumber string) (*orderRepository.Shift, error) {
	args := m.Called(ctx, shiftNumber)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) FindActiveShiftByStationID(ctx context.Context, stationID orderRepository.ID) (*orderRepository.Shift, error) {
	args := m.Called(ctx, stationID)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) FindActiveShiftByStationIDWithLock(ctx context.Context, stationID orderRepository.ID) (*orderRepository.Shift, error) {
	args := m.Called(ctx, stationID)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) FindLastCompletedShiftByStationID(ctx context.Context, stationID orderRepository.ID) (*orderRepository.Shift, error) {
	args := m.Called(ctx, stationID)
	return args.Get(0).(*orderRepository.Shift), args.Error(1)
}

func (m *MockShiftRepository) GetDailyShiftCount(ctx context.Context, stationID orderRepository.ID, date time.Time) (int, error) {
	args := m.Called(ctx, stationID, date)
	return args.Int(0), args.Error(1)
}

func (m *MockShiftRepository) SoftDeleteShift(ctx context.Context, shiftID orderRepository.ID) error {
	args := m.Called(ctx, shiftID)
	return args.Error(0)
}

func (m *MockShiftRepository) RestoreShift(ctx context.Context, shiftID orderRepository.ID) error {
	args := m.Called(ctx, shiftID)
	return args.Error(0)
}

// MockShiftAttendantService 模拟班次员工服务
type MockShiftAttendantService struct {
	mock.Mock
}

func (m *MockShiftAttendantService) GetShiftAttendants(ctx context.Context, request orderRepository.ShiftAttendantRequest) (orderRepository.ShiftAttendantData, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(orderRepository.ShiftAttendantData), args.Error(1)
}

// MockStationService 模拟站点服务
type MockStationService struct {
	mock.Mock
}

func (m *MockStationService) GetStationByID(ctx context.Context, id orderRepository.ID) (*orderRepository.Station, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*orderRepository.Station), args.Error(1)
}

func (m *MockStationService) GetStationName(ctx context.Context, id orderRepository.ID) (string, error) {
	args := m.Called(ctx, id)
	return args.String(0), args.Error(1)
}

func (m *MockStationService) GetStationsBatch(ctx context.Context, ids []orderRepository.ID) (map[orderRepository.ID]*orderRepository.Station, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).(map[orderRepository.ID]*orderRepository.Station), args.Error(1)
}

// TestEndOfDayReportServiceRedesigned_GetEndOfDayReport 测试获取日终报表
func TestEndOfDayReportServiceRedesigned_GetEndOfDayReport(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	stationID := int64(1)
	date := "2024-01-04"

	// 创建模拟对象
	mockShiftRepo := new(MockShiftRepository)
	mockShiftAttendantService := new(MockShiftAttendantService)
	mockStationService := new(MockStationService)

	// 创建服务实例
	service := NewEndOfDayReportService(
		mockShiftRepo,
		mockShiftAttendantService,
		mockStationService,
	)

	// 模拟站点信息
	station := &orderRepository.Station{
		ID:       orderRepository.ID(stationID),
		SiteCode: "TEST001",
		SiteName: "Test Station",
	}

	// 模拟班次数据
	endTime := time.Now()
	shifts := []*orderRepository.Shift{
		{
			ID:          1,
			ShiftNumber: "SHIFT-1-20240104005945",
			StationID:   orderRepository.ID(stationID),
			StartTime:   time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC),
			EndTime:     &endTime,
		},
	}

	// 模拟班次员工数据
	shiftAttendantData := orderRepository.ShiftAttendantData{
		ShiftInfo: orderRepository.ShiftAttendantShiftInfo{
			ID:          1,
			ShiftNumber: "SHIFT-1-20240104005945",
			StationID:   orderRepository.ID(stationID),
			StationName: "Test Station",
			StartTime:   time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC),
			EndTime:     &endTime,
			Status:      "closed",
		},
		Attendants: []orderRepository.ShiftAttendantInfo{
			{
				AttendantInfo: orderRepository.AttendantBasicInfo{
					AttendantName: "Test Employee",
					StaffCardID:   1,
				},
				TransactionCount: 10,
				SalesVolumeLtr:   100.5,
				SalesAmountIDR:   1500000,
				FuelSales: orderRepository.ShiftAttendantFuelSales{
					ByGrade: []orderRepository.ShiftAttendantFuelGrade{
						{
							FuelGrade:        "BP 92",
							FuelName:         "BP 92",
							FuelType:         "oil_92",
							SalesVolume:      100.5,
							GrossAmount:      1500000,
							DiscountAmount:   50000,
							NetAmount:        1450000,
							UnitPrice:        15000,
							TransactionCount: 10,
						},
					},
					Total: orderRepository.ShiftAttendantFuelTotal{
						TotalVolume:         100.5,
						TotalGrossAmount:    1500000,
						TotalDiscountAmount: 50000,
						TotalNetAmount:      1450000,
						TotalTransactions:   10,
					},
				},
				PaymentSummary: orderRepository.ShiftAttendantPayments{
					Cash:         1000000,
					NonCashTotal: 450000,
					BCA:          450000,
					ByMethod: []orderRepository.ShiftAttendantPaymentMethod{
						{
							PaymentMethod:     "cash",
							PaymentMethodName: "现金",
							TotalAmount:       1000000,
							TransactionCount:  7,
							Percentage:        69.0,
						},
						{
							PaymentMethod:     "bca",
							PaymentMethodName: "BCA",
							TotalAmount:       450000,
							TransactionCount:  3,
							Percentage:        31.0,
						},
					},
				},
				DryIncome:  0,
				GrandTotal: 1450000,
			},
		},
		ShiftSummary: orderRepository.ShiftAttendantSummary{
			TotalAttendants:   1,
			TotalTransactions: 10,
			TotalSalesVolume:  100.5,
			TotalCash:         1000000,
			TotalNonCash:      450000,
			TotalBCA:          450000,
			GrandTotal:        1450000,
		},
	}

	// 设置模拟期望
	mockStationService.On("GetStationByID", ctx, orderRepository.ID(stationID)).Return(station, nil)
	mockShiftRepo.On("ListShifts", ctx, mock.AnythingOfType("*repository.ShiftFilter"), mock.Anything, mock.AnythingOfType("*repository.SortOrder")).Return(shifts, 1, nil)
	mockShiftAttendantService.On("GetShiftAttendants", ctx, mock.AnythingOfType("repository.ShiftAttendantRequest")).Return(shiftAttendantData, nil)

	// 执行测试
	req := &types.EndOfDayReportRequest{
		StationID:      stationID,
		Date:           date,
		IncludeSummary: func() *bool { b := true; return &b }(),
	}

	response, err := service.GetEndOfDayReport(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.True(t, response.Success)
	assert.Equal(t, "日终报表获取成功", response.Message)

	// 验证报表头部
	assert.Equal(t, "PT ANEKA PETROINDO RAYA", response.Data.ReportHeader.CompanyName)
	assert.Equal(t, "END OF DAY REPORT", response.Data.ReportHeader.ReportType)
	assert.Equal(t, stationID, response.Data.ReportHeader.StationID)
	assert.Equal(t, "Test Station", response.Data.ReportHeader.StationName)
	assert.Equal(t, date, response.Data.ReportHeader.ReportDate)

	// 验证班次数据
	assert.Len(t, response.Data.Shifts, 1)
	shift := response.Data.Shifts[0]
	assert.Equal(t, int64(1), shift.ShiftInfo.ID)
	assert.Equal(t, "SHIFT-1-20240104005945", shift.ShiftInfo.ShiftNumber)
	assert.Equal(t, "Shift 1", shift.ShiftInfo.ShiftName)

	// 验证员工数据
	assert.Len(t, shift.Attendants, 1)
	attendant := shift.Attendants[0]
	assert.Equal(t, "Test Employee", attendant.AttendantInfo.AttendantName)
	assert.Equal(t, int64(1), attendant.AttendantInfo.StaffCardID)
	assert.Equal(t, 10, attendant.TransactionCount)
	assert.Equal(t, 100.5, attendant.SalesVolumeLtr)
	assert.Equal(t, 1450000.0, attendant.SalesAmountIDR)

	// 验证燃油销售数据
	assert.Len(t, attendant.FuelSales.ByGrade, 1)
	fuelGrade := attendant.FuelSales.ByGrade[0]
	assert.Equal(t, "BP 92", fuelGrade.FuelGrade)
	assert.Equal(t, 100.5, fuelGrade.SalesVolume)
	assert.Equal(t, 1500000.0, fuelGrade.GrossAmount)
	assert.Equal(t, 50000.0, fuelGrade.DiscountAmount)
	assert.Equal(t, 1450000.0, fuelGrade.NetAmount)

	// 验证支付数据
	assert.Equal(t, 1000000.0, attendant.PaymentSummary.Cash)
	assert.Equal(t, 450000.0, attendant.PaymentSummary.NonCashTotal)
	assert.Equal(t, 450000.0, attendant.PaymentSummary.BCA)
	assert.Len(t, attendant.PaymentSummary.ByMethod, 2)

	// 验证日汇总
	assert.NotNil(t, response.Data.DailySummary)
	dailySummary := response.Data.DailySummary
	assert.Equal(t, 1, dailySummary.TotalShifts)
	assert.Equal(t, 1, dailySummary.TotalAttendants)
	assert.Equal(t, 10, dailySummary.TotalTransactions)
	assert.Equal(t, 100.5, dailySummary.TotalFuelVolume)
	assert.Equal(t, 1450000.0, dailySummary.TotalFuelSales)

	// 验证元数据
	assert.Equal(t, "2.0", response.Meta.Version)
	assert.Equal(t, stationID, response.Meta.QueryParams.StationID)
	assert.Equal(t, date, response.Meta.QueryParams.Date)

	// 验证模拟对象的调用
	mockStationService.AssertExpectations(t)
	mockShiftRepo.AssertExpectations(t)
	mockShiftAttendantService.AssertExpectations(t)
}
