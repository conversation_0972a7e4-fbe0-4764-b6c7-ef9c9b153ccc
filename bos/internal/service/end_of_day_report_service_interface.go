package service

import (
	"context"
	"gitlab4.weicheche.cn/indo-bp/bos/internal/types"
)

/**
 * End of Day Report Service Interface (Redesigned)
 * 
 * 重构后的日终报表服务接口
 */
type EndOfDayReportServiceRedesignedInterface interface {
	// GetEndOfDayReport 获取日终报表
	// 基于新的设计方案，复用shifts/attendants逻辑，智能选择数据源
	GetEndOfDayReport(ctx context.Context, req *types.EndOfDayReportRequest) (*types.EndOfDayReportResponse, error)
}
