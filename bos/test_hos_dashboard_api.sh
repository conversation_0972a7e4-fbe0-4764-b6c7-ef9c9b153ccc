#!/bin/bash

# HOS Dashboard API兼容性测试脚本
# 测试后端API是否支持HOS系统的全站点模式

echo "=== HOS Dashboard API兼容性测试 ==="

# API基础URL
API_BASE_URL="http://localhost:8080/api/v1"

# 测试用的认证token（如果需要）
# TOKEN="your_jwt_token_here"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local test_name="$1"
    local url="$2"
    local expected_status="$3"
    
    echo -e "\n${YELLOW}测试: $test_name${NC}"
    echo "URL: $url"
    
    # 发送请求并获取状态码
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json")
    
    # 分离响应体和状态码
    body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
    status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    
    echo "状态码: $status"
    
    # 检查状态码
    if [ "$status" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 状态码正确${NC}"
    else
        echo -e "${RED}❌ 状态码错误，期望: $expected_status，实际: $status${NC}"
    fi
    
    # 检查响应体是否为有效JSON
    if echo "$body" | jq . > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 响应为有效JSON${NC}"
        
        # 检查是否有success字段
        success=$(echo "$body" | jq -r '.success // "null"')
        if [ "$success" = "true" ]; then
            echo -e "${GREEN}✅ API调用成功${NC}"
            
            # 显示数据摘要
            data_exists=$(echo "$body" | jq -r '.data // "null"')
            if [ "$data_exists" != "null" ]; then
                echo -e "${GREEN}✅ 包含数据字段${NC}"
                
                # 尝试显示一些关键数据
                revenue=$(echo "$body" | jq -r '.data.today_revenue // .data.TotalRevenue // "N/A"')
                transactions=$(echo "$body" | jq -r '.data.today_transactions // .data.TotalTransactions // "N/A"')
                echo "收入: $revenue, 交易数: $transactions"
            else
                echo -e "${YELLOW}⚠️  无数据字段${NC}"
            fi
        else
            echo -e "${RED}❌ API调用失败${NC}"
            error_msg=$(echo "$body" | jq -r '.error.message // .message // "未知错误"')
            echo "错误信息: $error_msg"
        fi
    else
        echo -e "${RED}❌ 响应不是有效JSON${NC}"
        echo "响应内容: $body"
    fi
}

echo -e "\n${YELLOW}开始测试Dashboard API兼容性...${NC}"

# 测试1: 传统BOS模式 - 带station_id参数
echo -e "\n${YELLOW}=== 测试1: 传统BOS模式（带station_id） ===${NC}"
test_api "Dashboard汇总 - BOS模式" \
    "$API_BASE_URL/dashboard/summary?station_id=1&date=2025-01-20" \
    "200"

test_api "销售趋势 - BOS模式" \
    "$API_BASE_URL/dashboard/sales-trend?station_id=1&date=2025-01-20&days=7" \
    "200"

test_api "燃油销售组合 - BOS模式" \
    "$API_BASE_URL/dashboard/fuel-sales-mix?station_id=1&date=2025-01-20" \
    "200"

# 测试2: HOS全站点模式 - 不带station_id参数
echo -e "\n${YELLOW}=== 测试2: HOS全站点模式（无station_id） ===${NC}"
test_api "Dashboard汇总 - HOS全站点模式" \
    "$API_BASE_URL/dashboard/summary?date=2025-01-20" \
    "200"

test_api "销售趋势 - HOS全站点模式" \
    "$API_BASE_URL/dashboard/sales-trend?date=2025-01-20&days=7" \
    "200"

test_api "燃油销售组合 - HOS全站点模式" \
    "$API_BASE_URL/dashboard/fuel-sales-mix?date=2025-01-20" \
    "200"

# 测试3: HOS模式 - 带site_ids参数（前端优化后的调用方式）
echo -e "\n${YELLOW}=== 测试3: HOS模式（带site_ids参数） ===${NC}"
test_api "Dashboard汇总 - HOS site_ids模式" \
    "$API_BASE_URL/dashboard/summary?site_ids=1,2,3&date=2025-01-20" \
    "200"

test_api "销售趋势 - HOS site_ids模式" \
    "$API_BASE_URL/dashboard/sales-trend?site_ids=1,2,3&date=2025-01-20&days=7" \
    "200"

test_api "燃油销售组合 - HOS site_ids模式" \
    "$API_BASE_URL/dashboard/fuel-sales-mix?site_ids=1,2,3&date=2025-01-20" \
    "200"

# 测试4: 错误情况测试
echo -e "\n${YELLOW}=== 测试4: 错误情况测试 ===${NC}"
test_api "无效日期格式" \
    "$API_BASE_URL/dashboard/summary?station_id=1&date=invalid-date" \
    "400"

test_api "无效station_id" \
    "$API_BASE_URL/dashboard/summary?station_id=invalid&date=2025-01-20" \
    "400"

# 测试5: 数据对比测试
echo -e "\n${YELLOW}=== 测试5: 数据对比测试 ===${NC}"
echo "比较单站点数据和全站点数据..."

# 获取单站点数据
single_response=$(curl -s "$API_BASE_URL/dashboard/summary?station_id=1&date=2025-01-20")
single_revenue=$(echo "$single_response" | jq -r '.data.today_revenue // 0')

# 获取全站点数据
all_response=$(curl -s "$API_BASE_URL/dashboard/summary?date=2025-01-20")
all_revenue=$(echo "$all_response" | jq -r '.data.today_revenue // 0')

echo "单站点收入: $single_revenue"
echo "全站点收入: $all_revenue"

# 比较数据合理性
if [ "$all_revenue" != "0" ] && [ "$single_revenue" != "0" ]; then
    if (( $(echo "$all_revenue >= $single_revenue" | bc -l) )); then
        echo -e "${GREEN}✅ 数据合理性检查通过（全站点收入 >= 单站点收入）${NC}"
    else
        echo -e "${RED}❌ 数据合理性检查失败（全站点收入 < 单站点收入）${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  无法进行数据合理性检查（存在零值）${NC}"
fi

echo -e "\n${YELLOW}=== 测试完成 ===${NC}"
echo -e "\n${GREEN}总结:${NC}"
echo "1. 传统BOS模式应该继续正常工作"
echo "2. HOS全站点模式应该返回聚合数据"
echo "3. 错误处理应该正确响应"
echo "4. 全站点数据应该大于等于单站点数据"
echo -e "\n如果所有测试都通过，说明后端API已兼容HOS系统的全站点模式。"
