database:
  host: *************
  port: 5432
  database: bos_db
  username: postgres
  password: GGPoaJhs00Zq
  sslMode: disable
  maxOpenConns: 25
  maxIdleConns: 5
  maxLifetime: 1h
  schema: core_schema  # 添加schema配置

jwt:
  secretKey: 12345678901234567890123456789012abcd
  expiresIn: 1h
  refreshExpiresIn: 168h  # 7 days
  issuer: bos-core-auth

cache:
  redis:
    addr: localhost:6379
    password: ""
    db: 0
    poolSize: 10
    minIdleConns: 2
    dialTimeout: 5s
    readTimeout: 3s
    writeTimeout: 3s

log:
  level: info
  format: json
  outputPath: stdout
  maxSize: 100
  maxBackups: 10
  maxAge: 30
  compress: true

security:
  passwordPolicy:
    minLength: 8
    requireUpper: true
    requireLower: true
    requireDigit: true
    requireSymbol: false
    maxAge: 90
    historyCount: 5
  loginPolicy:
    maxFailedAttempts: 5
    lockoutDuration: 30m
    requireCaptcha: 3
  sessionPolicy:
    maxConcurrentSessions: 5
    idleTimeout: 30m
    absoluteTimeout: 8h 