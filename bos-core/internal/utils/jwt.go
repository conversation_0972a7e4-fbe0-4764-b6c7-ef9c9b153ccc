package utils

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// TokenClaims JWT声明结构
type TokenClaims struct {
	UserID      string   `json:"user_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	Roles       []string `json:"roles"`
	SessionID   string   `json:"session_id"`
	TokenType   string   `json:"token_type"`   // access, refresh
	System      string   `json:"system"`       // HOS, BOS, EDC
	AccessLevel string   `json:"access_level"` // admin, manager, supervisor, operator
	ScopeType   string   `json:"scope_type"`   // global, station, operation
	ScopeIDs    []int64  `json:"scope_ids"`    // accessible scope IDs (station IDs for station scope)
	StationIDs  []int64  `json:"station_ids"`  // accessible station IDs (for backward compatibility)
	Permissions []string `json:"permissions"`  // user permissions for the system
	jwt.RegisteredClaims
}

// JWTManager JWT管理器
type JWTManager struct {
	secretKey        []byte
	accessExpiresIn  time.Duration
	refreshExpiresIn time.Duration
	issuer           string
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(secretKey string, accessExpiresIn, refreshExpiresIn time.Duration, issuer string) *JWTManager {
	return &JWTManager{
		secretKey:        []byte(secretKey),
		accessExpiresIn:  accessExpiresIn,
		refreshExpiresIn: refreshExpiresIn,
		issuer:           issuer,
	}
}

// GenerateAccessToken 生成访问令牌
func (j *JWTManager) GenerateAccessToken(userID, username, email string, roles []string, sessionID, system, accessLevel string, stationIDs []int64) (string, error) {
	// 为了保持向后兼容性，使用旧的方法签名，内部转换为新的结构
	scopeType := "station"
	scopeIDs := stationIDs
	permissions := []string{}

	// 根据系统类型设置scope_type
	switch system {
	case "HOS":
		scopeType = "global"
		scopeIDs = []int64{} // HOS系统使用全局权限，不需要具体的scope IDs
	case "BOS":
		scopeType = "station"
		scopeIDs = stationIDs
	case "EDC":
		scopeType = "operation"
		scopeIDs = stationIDs
	}

	return j.GenerateAccessTokenWithScope(userID, username, email, roles, sessionID, system, accessLevel, scopeType, scopeIDs, permissions)
}

// GenerateAccessTokenWithScope 生成带有权限范围的访问令牌
func (j *JWTManager) GenerateAccessTokenWithScope(userID, username, email string, roles []string, sessionID, system, accessLevel, scopeType string, scopeIDs []int64, permissions []string) (string, error) {
	now := time.Now()
	claims := &TokenClaims{
		UserID:      userID,
		Username:    username,
		Email:       email,
		Roles:       roles,
		SessionID:   sessionID,
		TokenType:   "access",
		System:      system,
		AccessLevel: accessLevel,
		ScopeType:   scopeType,
		ScopeIDs:    scopeIDs,
		StationIDs:  scopeIDs, // 为了向后兼容性保留
		Permissions: permissions,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Issuer:    j.issuer,
			Subject:   userID,
			Audience:  []string{"bos-core-auth"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.accessExpiresIn)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// GenerateRefreshToken 生成刷新令牌
func (j *JWTManager) GenerateRefreshToken(userID, sessionID string) (string, error) {
	now := time.Now()
	claims := &TokenClaims{
		UserID:    userID,
		SessionID: sessionID,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Issuer:    j.issuer,
			Subject:   userID,
			Audience:  []string{"bos-core-auth"},
			ExpiresAt: jwt.NewNumericDate(now.Add(j.refreshExpiresIn)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// ValidateToken 验证令牌
func (j *JWTManager) ValidateToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("令牌解析失败: %w", err)
	}

	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid {
		// 验证令牌是否过期
		if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
			return nil, fmt.Errorf("令牌已过期")
		}

		// 验证令牌是否生效
		if claims.NotBefore != nil && claims.NotBefore.After(time.Now()) {
			return nil, fmt.Errorf("令牌尚未生效")
		}

		return claims, nil
	}

	return nil, fmt.Errorf("无效的令牌声明")
}

// RefreshAccessToken 刷新访问令牌
func (j *JWTManager) RefreshAccessToken(refreshToken string) (string, error) {
	claims, err := j.ValidateToken(refreshToken)
	if err != nil {
		return "", fmt.Errorf("刷新令牌验证失败: %w", err)
	}

	if claims.TokenType != "refresh" {
		return "", fmt.Errorf("无效的令牌类型")
	}

	// 这里需要从数据库获取用户信息来生成新的访问令牌
	// 暂时返回错误，实际实现需要在service层
	return "", fmt.Errorf("需要在service层实现")
}

// GetTokenClaims 从令牌字符串中提取声明（不验证签名）
func (j *JWTManager) GetTokenClaims(tokenString string) (*TokenClaims, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &TokenClaims{})
	if err != nil {
		return nil, fmt.Errorf("令牌解析失败: %w", err)
	}

	if claims, ok := token.Claims.(*TokenClaims); ok {
		return claims, nil
	}

	return nil, fmt.Errorf("无效的令牌声明")
}

// IsTokenExpired 检查令牌是否过期
func (j *JWTManager) IsTokenExpired(tokenString string) bool {
	claims, err := j.GetTokenClaims(tokenString)
	if err != nil {
		return true
	}

	if claims.ExpiresAt == nil {
		return false
	}

	return claims.ExpiresAt.Before(time.Now())
}
