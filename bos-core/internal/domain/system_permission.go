package domain

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SystemPermissionStatus 系统权限状态
type SystemPermissionStatus string

const (
	SystemPermissionStatusActive   SystemPermissionStatus = "active"   // 生效
	SystemPermissionStatusInactive SystemPermissionStatus = "inactive" // 失效
)

// PermissionAction 权限操作类型
type PermissionAction string

const (
	PermissionActionCreate PermissionAction = "create" // 创建
	PermissionActionRead   PermissionAction = "read"   // 读取
	PermissionActionUpdate PermissionAction = "update" // 更新
	PermissionActionDelete PermissionAction = "delete" // 删除
	PermissionActionExecute PermissionAction = "execute" // 执行
	PermissionActionManage PermissionAction = "manage" // 管理
	PermissionActionView   PermissionAction = "view"   // 查看
	PermissionActionExport PermissionAction = "export" // 导出
	PermissionActionImport PermissionAction = "import" // 导入
)

// SystemPermission 系统权限定义实体
type SystemPermission struct {
	ID             uuid.UUID              `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	SystemCode     SystemCode             `gorm:"type:varchar(20);not null;index" json:"system_code"`
	PermissionCode string                 `gorm:"type:varchar(100);not null;index" json:"permission_code"`
	PermissionName string                 `gorm:"type:varchar(200);not null" json:"permission_name"`
	ScopeType      ScopeType              `gorm:"type:varchar(20);not null;index" json:"scope_type"`
	Resource       string                 `gorm:"type:varchar(100);not null;index" json:"resource"`
	Action         PermissionAction       `gorm:"type:varchar(50);not null" json:"action"`
	Description    *string                `gorm:"type:text" json:"description"`
	IsSystem       bool                   `gorm:"type:boolean;default:false;not null" json:"is_system"`
	Status         SystemPermissionStatus `gorm:"type:varchar(20);not null;default:'active'" json:"status"`
	CreatedAt      time.Time              `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time              `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy      *uuid.UUID             `gorm:"type:uuid" json:"created_by"`
	UpdatedBy      *uuid.UUID             `gorm:"type:uuid" json:"updated_by"`
	DeletedAt      gorm.DeletedAt         `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	CreatedByUser *User `gorm:"foreignKey:CreatedBy" json:"created_by_user,omitempty"`
	UpdatedByUser *User `gorm:"foreignKey:UpdatedBy" json:"updated_by_user,omitempty"`
}

// TableName 指定表名
func (SystemPermission) TableName() string {
	return "system_permissions"
}

// IsActive 检查权限是否活跃
func (sp *SystemPermission) IsActive() bool {
	return sp.Status == SystemPermissionStatusActive
}

// IsSystemPermission 检查是否为系统权限
func (sp *SystemPermission) IsSystemPermission() bool {
	return sp.IsSystem
}

// GetFullPermissionCode 获取完整权限代码
func (sp *SystemPermission) GetFullPermissionCode() string {
	return string(sp.SystemCode) + ":" + sp.PermissionCode
}

// GetResourceAction 获取资源:操作格式的权限标识
func (sp *SystemPermission) GetResourceAction() string {
	return sp.Resource + ":" + string(sp.Action)
}

// GetSystemDisplayName 获取系统显示名称
func (sp *SystemPermission) GetSystemDisplayName() string {
	switch sp.SystemCode {
	case SystemCodeHOS:
		return "总部管理系统"
	case SystemCodeBOS:
		return "站点后台系统"
	case SystemCodeEDC:
		return "手持设备系统"
	default:
		return string(sp.SystemCode)
	}
}

// GetScopeTypeDisplayName 获取权限范围显示名称
func (sp *SystemPermission) GetScopeTypeDisplayName() string {
	switch sp.ScopeType {
	case ScopeTypeGlobal:
		return "全局权限"
	case ScopeTypeStation:
		return "站点权限"
	case ScopeTypeOperation:
		return "操作权限"
	default:
		return string(sp.ScopeType)
	}
}

// GetActionDisplayName 获取操作显示名称
func (sp *SystemPermission) GetActionDisplayName() string {
	switch sp.Action {
	case PermissionActionCreate:
		return "创建"
	case PermissionActionRead:
		return "读取"
	case PermissionActionUpdate:
		return "更新"
	case PermissionActionDelete:
		return "删除"
	case PermissionActionExecute:
		return "执行"
	case PermissionActionManage:
		return "管理"
	case PermissionActionView:
		return "查看"
	case PermissionActionExport:
		return "导出"
	case PermissionActionImport:
		return "导入"
	default:
		return string(sp.Action)
	}
}

// Activate 激活权限
func (sp *SystemPermission) Activate() {
	sp.Status = SystemPermissionStatusActive
	sp.UpdatedAt = time.Now()
}

// Deactivate 停用权限
func (sp *SystemPermission) Deactivate() {
	sp.Status = SystemPermissionStatusInactive
	sp.UpdatedAt = time.Now()
}

// UserSystemPermission 用户系统权限分配实体
type UserSystemPermission struct {
	ID                 uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID             uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	SystemPermissionID uuid.UUID  `gorm:"type:uuid;not null;index" json:"system_permission_id"`
	GrantedBy          *uuid.UUID `gorm:"type:uuid" json:"granted_by"`
	GrantedAt          time.Time  `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"granted_at"`
	ExpiresAt          *time.Time `gorm:"type:timestamp" json:"expires_at"`
	CreatedAt          time.Time  `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联关系
	User             *User             `gorm:"foreignKey:UserID" json:"user,omitempty"`
	SystemPermission *SystemPermission `gorm:"foreignKey:SystemPermissionID" json:"system_permission,omitempty"`
	GrantedByUser    *User             `gorm:"foreignKey:GrantedBy" json:"granted_by_user,omitempty"`
}

// TableName 指定表名
func (UserSystemPermission) TableName() string {
	return "user_system_permissions"
}

// IsActive 检查权限分配是否活跃
func (usp *UserSystemPermission) IsActive() bool {
	// 检查是否过期
	if usp.ExpiresAt != nil && usp.ExpiresAt.Before(time.Now()) {
		return false
	}
	
	return true
}

// SetExpiration 设置过期时间
func (usp *UserSystemPermission) SetExpiration(expiresAt time.Time) {
	usp.ExpiresAt = &expiresAt
	usp.UpdatedAt = time.Now()
}

// ClearExpiration 清除过期时间（设为永久有效）
func (usp *UserSystemPermission) ClearExpiration() {
	usp.ExpiresAt = nil
	usp.UpdatedAt = time.Now()
}

// SystemAccessInfo 系统访问信息（用于JWT和响应）
type SystemAccessInfo struct {
	System       string   `json:"system"`        // 系统代码
	AccessLevel  string   `json:"access_level"`  // 访问级别
	ScopeType    string   `json:"scope_type"`    // 权限范围类型
	ScopeIDs     []int64  `json:"scope_ids"`     // 权限范围ID列表
	StationCount int      `json:"station_count"` // 可访问站点数量
	Permissions  []string `json:"permissions"`   // 权限列表
}

// GetSystemAccessInfo 从UserSystemAccess构建SystemAccessInfo
func (usa *UserSystemAccess) GetSystemAccessInfo() *SystemAccessInfo {
	return &SystemAccessInfo{
		System:       string(usa.SystemCode),
		AccessLevel:  string(usa.AccessLevel),
		ScopeType:    string(usa.ScopeType),
		ScopeIDs:     usa.ScopeIDs,
		StationCount: len(usa.ScopeIDs),
		Permissions:  []string{}, // 需要从权限分配中获取
	}
}
