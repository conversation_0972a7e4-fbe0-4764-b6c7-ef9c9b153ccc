package domain

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// DataPermissionScopeType 数据权限范围类型枚举
type DataPermissionScopeType string

const (
	DataPermissionScopeOwn          DataPermissionScopeType = "own"          // 自己的数据
	DataPermissionScopeDepartment   DataPermissionScopeType = "department"   // 部门数据
	DataPermissionScopeOrganization DataPermissionScopeType = "organization" // 组织数据
	DataPermissionScopeAll          DataPermissionScopeType = "all"          // 所有数据
)

// DataPermissionResourceType 数据权限资源类型枚举
type DataPermissionResourceType string

const (
	DataPermissionResourceOrganization DataPermissionResourceType = "organization" // 组织
	DataPermissionResourceUser         DataPermissionResourceType = "user"         // 用户
	DataPermissionResourceStation      DataPermissionResourceType = "station"      // 站点
	DataPermissionResourceOrder        DataPermissionResourceType = "order"        // 订单
	DataPermissionResourceTransaction  DataPermissionResourceType = "transaction"  // 交易
)

// DataPermissionStatus 数据权限状态枚举
type DataPermissionStatus string

const (
	DataPermissionStatusActive    DataPermissionStatus = "active"    // 活跃
	DataPermissionStatusInactive  DataPermissionStatus = "inactive"  // 非活跃
	DataPermissionStatusSuspended DataPermissionStatus = "suspended" // 暂停
)

// DataPermission 数据权限实体
type DataPermission struct {
	ID             uuid.UUID `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	PermissionCode string    `gorm:"type:varchar(100);uniqueIndex;not null" json:"permission_code"`
	PermissionName string    `gorm:"type:varchar(200);not null" json:"permission_name"`
	ResourceType   string    `gorm:"type:varchar(50);not null" json:"resource_type"`
	ScopeType      string    `gorm:"type:varchar(50);not null" json:"scope_type"`
	Description    *string   `gorm:"type:text" json:"description"`
	IsSystem       bool      `gorm:"default:false" json:"is_system"`
	Status         string    `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy      *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy      *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	UserDataPermissions []*UserDataPermission `gorm:"foreignKey:DataPermissionID" json:"user_data_permissions,omitempty"`
}

// TableName 指定表名
func (DataPermission) TableName() string {
	return "data_permissions"
}

// IsActive 检查权限是否活跃
func (dp *DataPermission) IsActive() bool {
	return dp.Status == string(DataPermissionStatusActive)
}

// IsSystemPermission 检查是否为系统权限
func (dp *DataPermission) IsSystemPermission() bool {
	return dp.IsSystem
}

// GetResourceType 获取资源类型
func (dp *DataPermission) GetResourceType() DataPermissionResourceType {
	return DataPermissionResourceType(dp.ResourceType)
}

// GetScopeType 获取范围类型
func (dp *DataPermission) GetScopeType() DataPermissionScopeType {
	return DataPermissionScopeType(dp.ScopeType)
}

// UserDataPermission 用户数据权限关联实体
type UserDataPermission struct {
	ID               uuid.UUID  `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	DataPermissionID uuid.UUID  `gorm:"type:uuid;not null;index" json:"data_permission_id"`
	ScopeValue       *string    `gorm:"type:jsonb" json:"scope_value"`
	ScopeConditions  *string    `gorm:"type:jsonb" json:"scope_conditions"`
	GrantedBy        *uuid.UUID `gorm:"type:uuid" json:"granted_by"`
	GrantedAt        time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"granted_at"`
	ExpiresAt        *time.Time `json:"expires_at"`
	Status           string     `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt        time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy        *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy        *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	User           *User           `gorm:"foreignKey:UserID" json:"user,omitempty"`
	DataPermission *DataPermission `gorm:"foreignKey:DataPermissionID" json:"data_permission,omitempty"`
	GrantedByUser  *User           `gorm:"foreignKey:GrantedBy" json:"granted_by_user,omitempty"`
}

// TableName 指定表名
func (UserDataPermission) TableName() string {
	return "user_data_permissions"
}

// IsActive 检查关联是否活跃
func (udp *UserDataPermission) IsActive() bool {
	return udp.Status == string(DataPermissionStatusActive) &&
		(udp.ExpiresAt == nil || udp.ExpiresAt.After(time.Now()))
}

// IsExpired 检查是否过期
func (udp *UserDataPermission) IsExpired() bool {
	return udp.ExpiresAt != nil && udp.ExpiresAt.Before(time.Now())
}

// Activate 激活权限
func (udp *UserDataPermission) Activate() {
	udp.Status = string(DataPermissionStatusActive)
	udp.UpdatedAt = time.Now()
}

// Deactivate 停用权限
func (udp *UserDataPermission) Deactivate() {
	udp.Status = string(DataPermissionStatusInactive)
	udp.UpdatedAt = time.Now()
}

// Suspend 暂停权限
func (udp *UserDataPermission) Suspend() {
	udp.Status = string(DataPermissionStatusSuspended)
	udp.UpdatedAt = time.Now()
}

// SetExpiration 设置过期时间
func (udp *UserDataPermission) SetExpiration(expiresAt time.Time) {
	udp.ExpiresAt = &expiresAt
	udp.UpdatedAt = time.Now()
}

// ClearExpiration 清除过期时间（设为永久有效）
func (udp *UserDataPermission) ClearExpiration() {
	udp.ExpiresAt = nil
	udp.UpdatedAt = time.Now()
}

// OrganizationPermissionTemplate 组织权限模板实体
type OrganizationPermissionTemplate struct {
	ID             uuid.UUID `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	OrganizationID int64     `gorm:"not null;index" json:"organization_id"`
	RoleType       string    `gorm:"type:varchar(50);not null" json:"role_type"`
	TemplateName   string    `gorm:"type:varchar(100);not null" json:"template_name"`
	Description    *string   `gorm:"type:text" json:"description"`
	Permissions    string    `gorm:"type:jsonb;not null" json:"permissions"`
	DataPermissions *string  `gorm:"type:jsonb" json:"data_permissions"`
	IsDefault      bool      `gorm:"default:false" json:"is_default"`
	IsSystem       bool      `gorm:"default:false" json:"is_system"`
	Priority       int       `gorm:"default:0" json:"priority"`
	Status         string    `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy      *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy      *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	Organization *Organization `gorm:"foreignKey:OrganizationID" json:"organization,omitempty"`
}

// TableName 指定表名
func (OrganizationPermissionTemplate) TableName() string {
	return "organization_permission_templates"
}

// IsActive 检查模板是否活跃
func (opt *OrganizationPermissionTemplate) IsActive() bool {
	return opt.Status == string(DataPermissionStatusActive)
}

// IsDefaultTemplate 检查是否为默认模板
func (opt *OrganizationPermissionTemplate) IsDefaultTemplate() bool {
	return opt.IsDefault
}

// IsSystemTemplate 检查是否为系统模板
func (opt *OrganizationPermissionTemplate) IsSystemTemplate() bool {
	return opt.IsSystem
}

// PermissionInheritanceRule 权限继承规则实体
type PermissionInheritanceRule struct {
	ID              uuid.UUID `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	RuleName        string    `gorm:"type:varchar(100);not null" json:"rule_name"`
	RuleCode        string    `gorm:"type:varchar(50);uniqueIndex;not null" json:"rule_code"`
	Description     *string   `gorm:"type:text" json:"description"`
	SourceType      string    `gorm:"type:varchar(50);not null" json:"source_type"`
	TargetType      string    `gorm:"type:varchar(50);not null" json:"target_type"`
	InheritanceType string    `gorm:"type:varchar(50);not null" json:"inheritance_type"`
	Conditions      *string   `gorm:"type:jsonb" json:"conditions"`
	Transformations *string   `gorm:"type:jsonb" json:"transformations"`
	IsActive        bool      `gorm:"default:true" json:"is_active"`
	Priority        int       `gorm:"default:0" json:"priority"`
	CreatedAt       time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy       *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy       *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
}

// TableName 指定表名
func (PermissionInheritanceRule) TableName() string {
	return "permission_inheritance_rules"
}

// IsRuleActive 检查规则是否活跃
func (pir *PermissionInheritanceRule) IsRuleActive() bool {
	return pir.IsActive
}

// PermissionEvaluationCache 权限评估缓存实体
type PermissionEvaluationCache struct {
	ID                uuid.UUID `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID            uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	ResourceType      string    `gorm:"type:varchar(50);not null" json:"resource_type"`
	ResourceID        *string   `gorm:"type:varchar(100)" json:"resource_id"`
	PermissionCode    string    `gorm:"type:varchar(100);not null" json:"permission_code"`
	IsGranted         bool      `gorm:"not null" json:"is_granted"`
	EvaluationContext *string   `gorm:"type:jsonb" json:"evaluation_context"`
	ComputedScope     *string   `gorm:"type:jsonb" json:"computed_scope"`
	CacheKey          string    `gorm:"type:varchar(255);uniqueIndex;not null" json:"cache_key"`
	ExpiresAt         time.Time `gorm:"not null" json:"expires_at"`
	CreatedAt         time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联关系
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (PermissionEvaluationCache) TableName() string {
	return "permission_evaluation_cache"
}

// IsExpired 检查缓存是否过期
func (pec *PermissionEvaluationCache) IsExpired() bool {
	return pec.ExpiresAt.Before(time.Now())
}

// IsValid 检查缓存是否有效
func (pec *PermissionEvaluationCache) IsValid() bool {
	return !pec.IsExpired()
}
