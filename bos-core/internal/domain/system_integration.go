package domain

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SystemType 系统类型枚举
type SystemType string

const (
	SystemTypeWeb     SystemType = "web"     // Web系统
	SystemTypeMobile  SystemType = "mobile"  // 移动系统
	SystemTypeAPI     SystemType = "api"     // API系统
	SystemTypeService SystemType = "service" // 服务系统
)

// IntegrationType 集成类型枚举
type IntegrationType string

const (
	IntegrationTypeSSO      IntegrationType = "SSO"      // 单点登录
	IntegrationTypeAPI      IntegrationType = "API"      // API集成
	IntegrationTypeDatabase IntegrationType = "DATABASE" // 数据库集成
	IntegrationTypeWebhook  IntegrationType = "WEBHOOK"  // Webhook集成
)

// SystemStatus 系统状态枚举
type SystemStatus string

const (
	SystemStatusActive      SystemStatus = "active"      // 活跃
	SystemStatusInactive    SystemStatus = "inactive"    // 非活跃
	SystemStatusMaintenance SystemStatus = "maintenance" // 维护中
)

// SyncFrequency 同步频率枚举
type SyncFrequency string

const (
	SyncFrequencyManual SyncFrequency = "manual" // 手动
	SyncFrequencyHourly SyncFrequency = "hourly" // 每小时
	SyncFrequencyDaily  SyncFrequency = "daily"  // 每天
)

// SystemIntegration 系统集成实体
type SystemIntegration struct {
	ID              int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	SystemCode      string    `gorm:"type:varchar(50);uniqueIndex;not null" json:"system_code"`
	SystemName      string    `gorm:"type:varchar(100);not null" json:"system_name"`
	SystemType      string    `gorm:"type:varchar(50);not null" json:"system_type"`
	IntegrationType string    `gorm:"type:varchar(50);not null" json:"integration_type"`
	BaseURL         *string   `gorm:"type:varchar(500)" json:"base_url"`
	APIEndpoint     *string   `gorm:"type:varchar(500)" json:"api_endpoint"`
	AuthEndpoint    *string   `gorm:"type:varchar(500)" json:"auth_endpoint"`
	CallbackURL     *string   `gorm:"type:varchar(500)" json:"callback_url"`
	ConfigData      string    `gorm:"type:jsonb;not null" json:"config_data"`
	AuthConfig      *string   `gorm:"type:jsonb" json:"auth_config"`
	PermissionMapping *string `gorm:"type:jsonb" json:"permission_mapping"`
	Version         *string   `gorm:"type:varchar(50)" json:"version"`
	Vendor          *string   `gorm:"type:varchar(100)" json:"vendor"`
	Description     *string   `gorm:"type:text" json:"description"`
	Status          string    `gorm:"type:varchar(20);default:'active'" json:"status"`
	IsSSOEnabled    bool      `gorm:"default:false" json:"is_sso_enabled"`
	IsAutoProvision bool      `gorm:"default:false" json:"is_auto_provision"`
	SyncFrequency   string    `gorm:"type:varchar(50);default:'manual'" json:"sync_frequency"`
	CreatedAt       time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy       *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy       *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	UserProfiles []*UserSystemProfile `gorm:"foreignKey:SystemCode;references:SystemCode" json:"user_profiles,omitempty"`
}

// TableName 指定表名
func (SystemIntegration) TableName() string {
	return "system_integrations"
}

// IsActive 检查系统是否活跃
func (si *SystemIntegration) IsActive() bool {
	return si.Status == string(SystemStatusActive)
}

// IsSSOSupported 检查是否支持SSO
func (si *SystemIntegration) IsSSOSupported() bool {
	return si.IsSSOEnabled
}

// GetSystemType 获取系统类型
func (si *SystemIntegration) GetSystemType() SystemType {
	return SystemType(si.SystemType)
}

// GetIntegrationType 获取集成类型
func (si *SystemIntegration) GetIntegrationType() IntegrationType {
	return IntegrationType(si.IntegrationType)
}

// UserSystemProfile 用户系统配置文件实体
type UserSystemProfile struct {
	ID                uuid.UUID  `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID            uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	SystemCode        string     `gorm:"type:varchar(50);not null" json:"system_code"`
	ExternalUserID    *string    `gorm:"type:varchar(100)" json:"external_user_id"`
	ExternalUsername  *string    `gorm:"type:varchar(100)" json:"external_username"`
	ExternalEmail     *string    `gorm:"type:varchar(255)" json:"external_email"`
	ProfileData       *string    `gorm:"type:jsonb" json:"profile_data"`
	PermissionOverrides *string  `gorm:"type:jsonb" json:"permission_overrides"`
	Preferences       *string    `gorm:"type:jsonb" json:"preferences"`
	LastSyncAt        *time.Time `json:"last_sync_at"`
	SyncStatus        string     `gorm:"type:varchar(20);default:'pending'" json:"sync_status"`
	SyncError         *string    `gorm:"type:text" json:"sync_error"`
	Status            string     `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt         time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy         *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy         *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	User   *User              `gorm:"foreignKey:UserID" json:"user,omitempty"`
	System *SystemIntegration `gorm:"foreignKey:SystemCode;references:SystemCode" json:"system,omitempty"`
}

// TableName 指定表名
func (UserSystemProfile) TableName() string {
	return "user_system_profiles"
}

// IsActive 检查配置文件是否活跃
func (usp *UserSystemProfile) IsActive() bool {
	return usp.Status == string(SystemStatusActive)
}

// IsSynced 检查是否已同步
func (usp *UserSystemProfile) IsSynced() bool {
	return usp.SyncStatus == "synced"
}

// TokenType 令牌类型枚举
type TokenType string

const (
	TokenTypeAccess  TokenType = "access_token"  // 访问令牌
	TokenTypeRefresh TokenType = "refresh_token" // 刷新令牌
	TokenTypeID      TokenType = "id_token"      // ID令牌
)

// TokenStatus 令牌状态枚举
type TokenStatus string

const (
	TokenStatusActive  TokenStatus = "active"  // 活跃
	TokenStatusExpired TokenStatus = "expired" // 过期
	TokenStatusRevoked TokenStatus = "revoked" // 撤销
	TokenStatusUsed    TokenStatus = "used"    // 已使用
)

// SSOToken SSO令牌实体
type SSOToken struct {
	ID            uuid.UUID  `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID        uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	TokenType     string     `gorm:"type:varchar(50);not null" json:"token_type"`
	TokenValue    string     `gorm:"type:varchar(1000);not null" json:"token_value"`
	TokenHash     string     `gorm:"type:varchar(255);uniqueIndex;not null" json:"token_hash"`
	SourceSystem  string     `gorm:"type:varchar(50);not null" json:"source_system"`
	TargetSystems []string   `gorm:"type:varchar(200)[]" json:"target_systems"`
	Scopes        []string   `gorm:"type:varchar(500)[]" json:"scopes"`
	IssuedAt      time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"issued_at"`
	ExpiresAt     time.Time  `gorm:"not null" json:"expires_at"`
	UsedAt        *time.Time `json:"used_at"`
	RevokedAt     *time.Time `json:"revoked_at"`
	ClientID      *string    `gorm:"type:varchar(100)" json:"client_id"`
	SessionID     *string    `gorm:"type:varchar(255)" json:"session_id"`
	DeviceInfo    *string    `gorm:"type:jsonb" json:"device_info"`
	IPAddress     *string    `gorm:"type:inet" json:"ip_address"`
	UserAgent     *string    `gorm:"type:text" json:"user_agent"`
	Status        string     `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt     time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联关系
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (SSOToken) TableName() string {
	return "sso_tokens"
}

// IsActive 检查令牌是否活跃
func (st *SSOToken) IsActive() bool {
	return st.Status == string(TokenStatusActive) && st.ExpiresAt.After(time.Now())
}

// IsExpired 检查令牌是否过期
func (st *SSOToken) IsExpired() bool {
	return st.ExpiresAt.Before(time.Now()) || st.Status == string(TokenStatusExpired)
}

// IsRevoked 检查令牌是否被撤销
func (st *SSOToken) IsRevoked() bool {
	return st.RevokedAt != nil || st.Status == string(TokenStatusRevoked)
}

// GetTokenType 获取令牌类型
func (st *SSOToken) GetTokenType() TokenType {
	return TokenType(st.TokenType)
}

// Revoke 撤销令牌
func (st *SSOToken) Revoke() {
	now := time.Now()
	st.RevokedAt = &now
	st.Status = string(TokenStatusRevoked)
	st.UpdatedAt = now
}

// MarkAsUsed 标记令牌为已使用
func (st *SSOToken) MarkAsUsed() {
	now := time.Now()
	st.UsedAt = &now
	st.UpdatedAt = now
}

// MappingType 映射类型枚举
type MappingType string

const (
	MappingTypeDirect       MappingType = "direct"       // 直接映射
	MappingTypeTransformed  MappingType = "transformed"  // 转换映射
	MappingTypeConditional  MappingType = "conditional"  // 条件映射
)

// SystemPermissionMapping 系统权限映射实体
type SystemPermissionMapping struct {
	ID                  uuid.UUID `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	SourceSystem        string    `gorm:"type:varchar(50);not null" json:"source_system"`
	TargetSystem        string    `gorm:"type:varchar(50);not null" json:"target_system"`
	SourcePermission    string    `gorm:"type:varchar(200);not null" json:"source_permission"`
	TargetPermission    string    `gorm:"type:varchar(200);not null" json:"target_permission"`
	MappingType         string    `gorm:"type:varchar(50);not null" json:"mapping_type"`
	TransformationRules *string   `gorm:"type:jsonb" json:"transformation_rules"`
	Conditions          *string   `gorm:"type:jsonb" json:"conditions"`
	Priority            int       `gorm:"default:0" json:"priority"`
	Description         *string   `gorm:"type:text" json:"description"`
	IsBidirectional     bool      `gorm:"default:false" json:"is_bidirectional"`
	IsActive            bool      `gorm:"default:true" json:"is_active"`
	CreatedAt           time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt           time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy           *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy           *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
}

// TableName 指定表名
func (SystemPermissionMapping) TableName() string {
	return "system_permission_mappings"
}

// IsActiveMapping 检查映射是否活跃
func (spm *SystemPermissionMapping) IsActiveMapping() bool {
	return spm.IsActive
}

// GetMappingType 获取映射类型
func (spm *SystemPermissionMapping) GetMappingType() MappingType {
	return MappingType(spm.MappingType)
}

// SyncResult 同步结果枚举
type SyncResult string

const (
	SyncResultSuccess SyncResult = "success" // 成功
	SyncResultFailed  SyncResult = "failed"  // 失败
	SyncResultPartial SyncResult = "partial" // 部分成功
)

// SystemSyncLog 系统同步日志实体
type SystemSyncLog struct {
	ID               uuid.UUID  `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	SyncType         string     `gorm:"type:varchar(50);not null" json:"sync_type"`
	SourceSystem     string     `gorm:"type:varchar(50);not null" json:"source_system"`
	TargetSystem     string     `gorm:"type:varchar(50);not null" json:"target_system"`
	Operation        string     `gorm:"type:varchar(50);not null" json:"operation"`
	EntityType       string     `gorm:"type:varchar(50);not null" json:"entity_type"`
	EntityID         *string    `gorm:"type:varchar(100)" json:"entity_id"`
	SyncData         *string    `gorm:"type:jsonb" json:"sync_data"`
	SyncResult       string     `gorm:"type:varchar(20);not null" json:"sync_result"`
	ErrorMessage     *string    `gorm:"type:text" json:"error_message"`
	ErrorDetails     *string    `gorm:"type:jsonb" json:"error_details"`
	SyncBatchID      *uuid.UUID `gorm:"type:uuid" json:"sync_batch_id"`
	SyncDurationMs   *int64     `json:"sync_duration_ms"`
	RecordsProcessed int        `gorm:"default:0" json:"records_processed"`
	RecordsSuccess   int        `gorm:"default:0" json:"records_success"`
	RecordsFailed    int        `gorm:"default:0" json:"records_failed"`
	StartedAt        time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at"`
	CreatedBy        *uuid.UUID `gorm:"type:uuid" json:"created_by"`
}

// TableName 指定表名
func (SystemSyncLog) TableName() string {
	return "system_sync_logs"
}

// IsSuccess 检查同步是否成功
func (ssl *SystemSyncLog) IsSuccess() bool {
	return ssl.SyncResult == string(SyncResultSuccess)
}

// GetSyncResult 获取同步结果
func (ssl *SystemSyncLog) GetSyncResult() SyncResult {
	return SyncResult(ssl.SyncResult)
}

// MarkCompleted 标记同步完成
func (ssl *SystemSyncLog) MarkCompleted(result SyncResult, errorMessage *string) {
	now := time.Now()
	ssl.CompletedAt = &now
	ssl.SyncResult = string(result)
	ssl.ErrorMessage = errorMessage
	
	if ssl.StartedAt.IsZero() {
		ssl.StartedAt = now
	}
	
	duration := now.Sub(ssl.StartedAt).Milliseconds()
	ssl.SyncDurationMs = &duration
}
