package domain

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrganizationType 组织类型枚举
type OrganizationType string

const (
	OrganizationTypeHQ      OrganizationType = "HQ"      // 总部
	OrganizationTypeRegion  OrganizationType = "REGION"  // 区域
	OrganizationTypeStation OrganizationType = "STATION" // 站点
)

// OrganizationStatus 组织状态枚举
type OrganizationStatus string

const (
	OrganizationStatusActive    OrganizationStatus = "active"    // 活跃
	OrganizationStatusInactive  OrganizationStatus = "inactive"  // 非活跃
	OrganizationStatusSuspended OrganizationStatus = "suspended" // 暂停
)

// UserOrganizationRole 用户在组织中的角色枚举
type UserOrganizationRole string

const (
	UserOrganizationRoleAdmin   UserOrganizationRole = "admin"   // 管理员
	UserOrganizationRoleManager UserOrganizationRole = "manager" // 经理
	UserOrganizationRoleMember  UserOrganizationRole = "member"  // 成员
)

// UserOrganizationStatus 用户组织关联状态枚举
type UserOrganizationStatus string

const (
	UserOrganizationStatusActive    UserOrganizationStatus = "active"    // 活跃
	UserOrganizationStatusInactive  UserOrganizationStatus = "inactive"  // 非活跃
	UserOrganizationStatusSuspended UserOrganizationStatus = "suspended" // 暂停
)

// OrganizationTypeEntity 组织类型实体
type OrganizationTypeEntity struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TypeCode    string    `gorm:"type:varchar(50);uniqueIndex;not null" json:"type_code"`
	TypeName    string    `gorm:"type:varchar(100);not null" json:"type_name"`
	LevelOrder  int       `gorm:"not null" json:"level_order"`
	Description *string   `gorm:"type:text" json:"description"`
	IsSystem    bool      `gorm:"default:false" json:"is_system"`
	Status      string    `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy   *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy   *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
}

// TableName 指定表名
func (OrganizationTypeEntity) TableName() string {
	return "organization_types"
}

// Organization 组织实体
type Organization struct {
	ID                 int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	OrgCode            string     `gorm:"type:varchar(50);uniqueIndex;not null" json:"org_code"`
	OrgName            string     `gorm:"type:varchar(200);not null" json:"org_name"`
	OrgTypeID          int64      `gorm:"not null" json:"org_type_id"`
	ParentID           *int64     `gorm:"index" json:"parent_id"`
	LevelPath          *string    `gorm:"type:varchar(500)" json:"level_path"`
	LevelDepth         int        `gorm:"default:1" json:"level_depth"`
	SortOrder          int        `gorm:"default:0" json:"sort_order"`
	Status             string     `gorm:"type:varchar(20);default:'active'" json:"status"`
	ContactInfo        *string    `gorm:"type:jsonb" json:"contact_info"`
	Address            *string    `gorm:"type:jsonb" json:"address"`
	BusinessLicense    *string    `gorm:"type:varchar(100)" json:"business_license"`
	TaxNumber          *string    `gorm:"type:varchar(100)" json:"tax_number"`
	LegalRepresentative *string   `gorm:"type:varchar(100)" json:"legal_representative"`
	ExternalSystemID   *string    `gorm:"type:varchar(100)" json:"external_system_id"`
	SyncStatus         string     `gorm:"type:varchar(20);default:'pending'" json:"sync_status"`
	LastSyncAt         *time.Time `json:"last_sync_at"`
	CreatedAt          time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy          *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy          *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt          gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	OrgType    *OrganizationTypeEntity `gorm:"foreignKey:OrgTypeID" json:"org_type,omitempty"`
	Parent     *Organization           `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
	Children   []*Organization         `gorm:"foreignKey:ParentID" json:"children,omitempty"`
	Users      []*UserOrganization     `gorm:"foreignKey:OrganizationID" json:"users,omitempty"`
	Stations   []*Station              `gorm:"foreignKey:OrganizationID" json:"stations,omitempty"`
}

// TableName 指定表名
func (Organization) TableName() string {
	return "organizations"
}

// IsActive 检查组织是否活跃
func (o *Organization) IsActive() bool {
	return o.Status == string(OrganizationStatusActive)
}

// IsHeadquarters 检查是否为总部
func (o *Organization) IsHeadquarters() bool {
	return o.OrgType != nil && o.OrgType.TypeCode == string(OrganizationTypeHQ)
}

// IsRegion 检查是否为区域
func (o *Organization) IsRegion() bool {
	return o.OrgType != nil && o.OrgType.TypeCode == string(OrganizationTypeRegion)
}

// IsStation 检查是否为站点
func (o *Organization) IsStation() bool {
	return o.OrgType != nil && o.OrgType.TypeCode == string(OrganizationTypeStation)
}

// GetDisplayName 获取显示名称
func (o *Organization) GetDisplayName() string {
	if o.OrgType != nil {
		return o.OrgType.TypeName + " - " + o.OrgName
	}
	return o.OrgName
}

// UserOrganization 用户组织关联实体
type UserOrganization struct {
	ID                uuid.UUID  `gorm:"type:uuid;primaryKey;default:gen_random_uuid()" json:"id"`
	UserID            uuid.UUID  `gorm:"type:uuid;not null;index" json:"user_id"`
	OrganizationID    int64      `gorm:"not null;index" json:"organization_id"`
	IsPrimary         bool       `gorm:"default:false" json:"is_primary"`
	RoleInOrg         *string    `gorm:"type:varchar(50)" json:"role_in_org"`
	ResponsibilityArea *string   `gorm:"type:varchar(200)" json:"responsibility_area"`
	GrantedBy         *uuid.UUID `gorm:"type:uuid" json:"granted_by"`
	GrantedAt         time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"granted_at"`
	ExpiresAt         *time.Time `json:"expires_at"`
	Status            string     `gorm:"type:varchar(20);default:'active'" json:"status"`
	CreatedAt         time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy         *uuid.UUID `gorm:"type:uuid" json:"created_by"`
	UpdatedBy         *uuid.UUID `gorm:"type:uuid" json:"updated_by"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	User         *User         `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Organization *Organization `gorm:"foreignKey:OrganizationID" json:"organization,omitempty"`
	GrantedByUser *User        `gorm:"foreignKey:GrantedBy" json:"granted_by_user,omitempty"`
}

// TableName 指定表名
func (UserOrganization) TableName() string {
	return "user_organizations"
}

// IsActive 检查关联是否活跃
func (uo *UserOrganization) IsActive() bool {
	return uo.Status == string(UserOrganizationStatusActive) && 
		   (uo.ExpiresAt == nil || uo.ExpiresAt.After(time.Now()))
}

// IsAdmin 检查是否为管理员角色
func (uo *UserOrganization) IsAdmin() bool {
	return uo.RoleInOrg != nil && *uo.RoleInOrg == string(UserOrganizationRoleAdmin)
}

// IsManager 检查是否为经理角色
func (uo *UserOrganization) IsManager() bool {
	return uo.RoleInOrg != nil && *uo.RoleInOrg == string(UserOrganizationRoleManager)
}

// IsMember 检查是否为成员角色
func (uo *UserOrganization) IsMember() bool {
	return uo.RoleInOrg != nil && *uo.RoleInOrg == string(UserOrganizationRoleMember)
}

// CanManage 检查是否有管理权限
func (uo *UserOrganization) CanManage() bool {
	return uo.IsActive() && (uo.IsAdmin() || uo.IsManager())
}

// GetRoleDisplayName 获取角色显示名称
func (uo *UserOrganization) GetRoleDisplayName() string {
	if uo.RoleInOrg == nil {
		return "未指定"
	}
	
	switch UserOrganizationRole(*uo.RoleInOrg) {
	case UserOrganizationRoleAdmin:
		return "管理员"
	case UserOrganizationRoleManager:
		return "经理"
	case UserOrganizationRoleMember:
		return "成员"
	default:
		return *uo.RoleInOrg
	}
}

// Activate 激活关联
func (uo *UserOrganization) Activate() {
	uo.Status = string(UserOrganizationStatusActive)
	uo.UpdatedAt = time.Now()
}

// Deactivate 停用关联
func (uo *UserOrganization) Deactivate() {
	uo.Status = string(UserOrganizationStatusInactive)
	uo.UpdatedAt = time.Now()
}

// Suspend 暂停关联
func (uo *UserOrganization) Suspend() {
	uo.Status = string(UserOrganizationStatusSuspended)
	uo.UpdatedAt = time.Now()
}

// SetExpiration 设置过期时间
func (uo *UserOrganization) SetExpiration(expiresAt time.Time) {
	uo.ExpiresAt = &expiresAt
	uo.UpdatedAt = time.Now()
}

// ClearExpiration 清除过期时间（设为永久有效）
func (uo *UserOrganization) ClearExpiration() {
	uo.ExpiresAt = nil
	uo.UpdatedAt = time.Now()
}
