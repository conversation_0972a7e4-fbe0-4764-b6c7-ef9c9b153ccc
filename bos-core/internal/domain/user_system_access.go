package domain

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SystemCode 系统代码类型
type SystemCode string

const (
	SystemCodeHOS SystemCode = "HOS" // 总部管理系统
	SystemCodeBOS SystemCode = "BOS" // 站点后台管理系统
	SystemCodeEDC SystemCode = "EDC" // 手持设备系统
)

// AccessLevel 访问级别类型
type AccessLevel string

const (
	AccessLevelAdmin      AccessLevel = "admin"      // 管理员
	AccessLevelManager    AccessLevel = "manager"    // 经理
	AccessLevelSupervisor AccessLevel = "supervisor" // 主管
	AccessLevelOperator   AccessLevel = "operator"   // 操作员
)

// ScopeType 权限范围类型
type ScopeType string

const (
	ScopeTypeGlobal    ScopeType = "global"    // 全局权限
	ScopeTypeStation   ScopeType = "station"   // 站点权限
	ScopeTypeOperation ScopeType = "operation" // 操作权限
)

// UserSystemAccessStatus 用户系统访问状态
type UserSystemAccessStatus string

const (
	UserSystemAccessStatusActive    UserSystemAccessStatus = "active"    // 生效
	UserSystemAccessStatusInactive  UserSystemAccessStatus = "inactive"  // 失效
	UserSystemAccessStatusSuspended UserSystemAccessStatus = "suspended" // 暂停
)

// UserSystemAccess 用户系统访问权限实体
type UserSystemAccess struct {
	ID          uuid.UUID               `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      uuid.UUID               `gorm:"type:uuid;not null;index" json:"user_id"`
	SystemCode  SystemCode              `gorm:"type:varchar(20);not null;index" json:"system_code"`
	AccessLevel AccessLevel             `gorm:"type:varchar(50);not null" json:"access_level"`
	ScopeType   ScopeType               `gorm:"type:varchar(20);not null;index" json:"scope_type"`
	ScopeIDs    []int64                 `gorm:"type:jsonb" json:"scope_ids"` // 站点ID列表，全局权限时为空
	Status      UserSystemAccessStatus  `gorm:"type:varchar(20);not null;default:'active'" json:"status"`
	GrantedBy   *uuid.UUID              `gorm:"type:uuid" json:"granted_by"`
	GrantedAt   time.Time               `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"granted_at"`
	ExpiresAt   *time.Time              `gorm:"type:timestamp" json:"expires_at"`
	CreatedAt   time.Time               `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time               `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy   *uuid.UUID              `gorm:"type:uuid" json:"created_by"`
	UpdatedBy   *uuid.UUID              `gorm:"type:uuid" json:"updated_by"`
	DeletedAt   gorm.DeletedAt          `gorm:"index" json:"deleted_at,omitempty"`

	// 关联关系
	User      *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	GrantedByUser *User `gorm:"foreignKey:GrantedBy" json:"granted_by_user,omitempty"`
}

// TableName 指定表名
func (UserSystemAccess) TableName() string {
	return "user_system_access"
}

// IsActive 检查权限是否活跃
func (usa *UserSystemAccess) IsActive() bool {
	if usa.Status != UserSystemAccessStatusActive {
		return false
	}
	
	// 检查是否过期
	if usa.ExpiresAt != nil && usa.ExpiresAt.Before(time.Now()) {
		return false
	}
	
	return true
}

// IsGlobalAccess 检查是否为全局权限
func (usa *UserSystemAccess) IsGlobalAccess() bool {
	return usa.ScopeType == ScopeTypeGlobal
}

// IsStationAccess 检查是否为站点权限
func (usa *UserSystemAccess) IsStationAccess() bool {
	return usa.ScopeType == ScopeTypeStation
}

// IsOperationAccess 检查是否为操作权限
func (usa *UserSystemAccess) IsOperationAccess() bool {
	return usa.ScopeType == ScopeTypeOperation
}

// HasStationAccess 检查是否有指定站点的访问权限
func (usa *UserSystemAccess) HasStationAccess(stationID int64) bool {
	if !usa.IsActive() {
		return false
	}
	
	// 全局权限可以访问所有站点
	if usa.IsGlobalAccess() {
		return true
	}
	
	// 检查站点权限
	if usa.IsStationAccess() {
		for _, id := range usa.ScopeIDs {
			if id == stationID {
				return true
			}
		}
	}
	
	return false
}

// GetAccessLevelValue 获取访问级别数值（数字越大权限越高）
func (usa *UserSystemAccess) GetAccessLevelValue() int {
	switch usa.AccessLevel {
	case AccessLevelOperator:
		return 1
	case AccessLevelSupervisor:
		return 2
	case AccessLevelManager:
		return 3
	case AccessLevelAdmin:
		return 4
	default:
		return 0
	}
}

// GetSystemDisplayName 获取系统显示名称
func (usa *UserSystemAccess) GetSystemDisplayName() string {
	switch usa.SystemCode {
	case SystemCodeHOS:
		return "总部管理系统"
	case SystemCodeBOS:
		return "站点后台系统"
	case SystemCodeEDC:
		return "手持设备系统"
	default:
		return string(usa.SystemCode)
	}
}

// GetAccessLevelDisplayName 获取访问级别显示名称
func (usa *UserSystemAccess) GetAccessLevelDisplayName() string {
	switch usa.AccessLevel {
	case AccessLevelAdmin:
		return "管理员"
	case AccessLevelManager:
		return "经理"
	case AccessLevelSupervisor:
		return "主管"
	case AccessLevelOperator:
		return "操作员"
	default:
		return string(usa.AccessLevel)
	}
}

// GetScopeTypeDisplayName 获取权限范围显示名称
func (usa *UserSystemAccess) GetScopeTypeDisplayName() string {
	switch usa.ScopeType {
	case ScopeTypeGlobal:
		return "全局权限"
	case ScopeTypeStation:
		return "站点权限"
	case ScopeTypeOperation:
		return "操作权限"
	default:
		return string(usa.ScopeType)
	}
}

// Activate 激活权限
func (usa *UserSystemAccess) Activate() {
	usa.Status = UserSystemAccessStatusActive
	usa.UpdatedAt = time.Now()
}

// Deactivate 停用权限
func (usa *UserSystemAccess) Deactivate() {
	usa.Status = UserSystemAccessStatusInactive
	usa.UpdatedAt = time.Now()
}

// Suspend 暂停权限
func (usa *UserSystemAccess) Suspend() {
	usa.Status = UserSystemAccessStatusSuspended
	usa.UpdatedAt = time.Now()
}

// SetExpiration 设置过期时间
func (usa *UserSystemAccess) SetExpiration(expiresAt time.Time) {
	usa.ExpiresAt = &expiresAt
	usa.UpdatedAt = time.Now()
}

// ClearExpiration 清除过期时间（设为永久有效）
func (usa *UserSystemAccess) ClearExpiration() {
	usa.ExpiresAt = nil
	usa.UpdatedAt = time.Now()
}

// CanManageUser 检查是否可以管理用户
func (usa *UserSystemAccess) CanManageUser() bool {
	return usa.IsActive() && 
		   usa.SystemCode == SystemCodeHOS && 
		   usa.IsGlobalAccess() && 
		   (usa.AccessLevel == AccessLevelAdmin || usa.AccessLevel == AccessLevelManager)
}

// CanManageStation 检查是否可以管理站点
func (usa *UserSystemAccess) CanManageStation() bool {
	return usa.IsActive() && 
		   (usa.SystemCode == SystemCodeHOS || usa.SystemCode == SystemCodeBOS) &&
		   (usa.AccessLevel == AccessLevelAdmin || usa.AccessLevel == AccessLevelManager)
}

// CanViewReports 检查是否可以查看报表
func (usa *UserSystemAccess) CanViewReports() bool {
	return usa.IsActive() && 
		   (usa.AccessLevel == AccessLevelAdmin || 
		    usa.AccessLevel == AccessLevelManager || 
		    usa.AccessLevel == AccessLevelSupervisor)
}
