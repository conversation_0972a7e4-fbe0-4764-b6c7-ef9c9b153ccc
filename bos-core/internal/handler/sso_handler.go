package handler

import (
	"net/http"
	"strconv"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

// SSOHandler SSO处理器
type SSOHandler struct {
	ssoService service.SSOService
	logger     logger.Logger
}

// NewSSOHandler 创建SSO处理器
func NewSSOHandler(ssoService service.SSOService, logger logger.Logger) *SSOHandler {
	return &SSOHandler{
		ssoService: ssoService,
		logger:     logger,
	}
}

// InitiateSSO 发起SSO认证
func (h *SSOHandler) InitiateSSO(c echo.Context) error {
	var req service.InitiateSSORequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 获取客户端信息
	req.IPAddress = c.RealIP()
	req.UserAgent = c.Request().UserAgent()

	response, err := h.ssoService.InitiateSSO(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("发起SSO认证失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "发起SSO认证失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "SSO认证发起成功",
		"data":    response,
	})
}

// ValidateSSO 验证SSO认证
func (h *SSOHandler) ValidateSSO(c echo.Context) error {
	var req service.ValidateSSORequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	response, err := h.ssoService.ValidateSSO(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("验证SSO认证失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "验证SSO认证失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "SSO认证验证成功",
		"data":    response,
	})
}

// RefreshSSOToken 刷新SSO令牌
func (h *SSOHandler) RefreshSSOToken(c echo.Context) error {
	var req service.RefreshSSOTokenRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	response, err := h.ssoService.RefreshSSOToken(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("刷新SSO令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "刷新SSO令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "SSO令牌刷新成功",
		"data":    response,
	})
}

// RevokeSSOToken 撤销SSO令牌
func (h *SSOHandler) RevokeSSOToken(c echo.Context) error {
	var req struct {
		TokenHash string `json:"token_hash" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	err := h.ssoService.RevokeSSOToken(c.Request().Context(), req.TokenHash)
	if err != nil {
		h.logger.Error("撤销SSO令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "撤销SSO令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "SSO令牌撤销成功",
	})
}

// CreateSSOToken 创建SSO令牌
func (h *SSOHandler) CreateSSOToken(c echo.Context) error {
	var req service.CreateSSOTokenRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 获取客户端信息
	req.IPAddress = c.RealIP()
	req.UserAgent = c.Request().UserAgent()

	token, err := h.ssoService.CreateSSOToken(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建SSO令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "创建SSO令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "SSO令牌创建成功",
		"data":    token,
	})
}

// GetSSOToken 获取SSO令牌
func (h *SSOHandler) GetSSOToken(c echo.Context) error {
	tokenHash := c.Param("tokenHash")
	if tokenHash == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少令牌哈希参数",
		})
	}

	token, err := h.ssoService.GetSSOToken(c.Request().Context(), tokenHash)
	if err != nil {
		h.logger.Error("获取SSO令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取SSO令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取SSO令牌成功",
		"data":    token,
	})
}

// ValidateToken 验证令牌
func (h *SSOHandler) ValidateToken(c echo.Context) error {
	tokenHash := c.Param("tokenHash")
	if tokenHash == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少令牌哈希参数",
		})
	}

	targetSystem := c.QueryParam("targetSystem")
	if targetSystem == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "缺少目标系统参数",
		})
	}

	result, err := h.ssoService.ValidateToken(c.Request().Context(), tokenHash, targetSystem)
	if err != nil {
		h.logger.Error("验证令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "验证令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "令牌验证成功",
		"data":    result,
	})
}

// RevokeUserTokens 撤销用户令牌
func (h *SSOHandler) RevokeUserTokens(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的用户ID",
		})
	}

	sourceSystem := c.QueryParam("sourceSystem")
	if sourceSystem == "" {
		sourceSystem = "BOS" // 默认为BOS系统
	}

	err = h.ssoService.RevokeUserTokens(c.Request().Context(), userID, sourceSystem)
	if err != nil {
		h.logger.Error("撤销用户令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "撤销用户令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "用户令牌撤销成功",
	})
}

// CleanupExpiredTokens 清理过期令牌
func (h *SSOHandler) CleanupExpiredTokens(c echo.Context) error {
	count, err := h.ssoService.CleanupExpiredTokens(c.Request().Context())
	if err != nil {
		h.logger.Error("清理过期令牌失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "清理过期令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "过期令牌清理成功",
		"data": map[string]interface{}{
			"cleaned_count": count,
		},
	})
}

// RegisterSystem 注册系统
func (h *SSOHandler) RegisterSystem(c echo.Context) error {
	var req service.RegisterSystemRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为创建者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.CreatedBy = userID

	system, err := h.ssoService.RegisterSystem(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("注册系统失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "注册系统失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "系统注册成功",
		"data":    system,
	})
}

// ListSystemIntegrations 系统集成列表
func (h *SSOHandler) ListSystemIntegrations(c echo.Context) error {
	filter := parseSystemIntegrationFilter(c)

	systems, err := h.ssoService.ListSystemIntegrations(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取系统集成列表失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取系统集成列表失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取系统集成列表成功",
		"data":    systems,
	})
}

// GetSystemIntegration 获取系统集成详情
func (h *SSOHandler) GetSystemIntegration(c echo.Context) error {
	systemCode := c.Param("systemCode")
	if systemCode == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少系统代码参数",
		})
	}

	system, err := h.ssoService.GetSystemIntegration(c.Request().Context(), systemCode)
	if err != nil {
		h.logger.Error("获取系统集成详情失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取系统集成详情失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取系统集成详情成功",
		"data":    system,
	})
}

// UpdateSystemIntegration 更新系统集成
func (h *SSOHandler) UpdateSystemIntegration(c echo.Context) error {
	systemCode := c.Param("systemCode")
	if systemCode == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少系统代码参数",
		})
	}

	var req service.UpdateSystemIntegrationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为更新者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.UpdatedBy = userID

	system, err := h.ssoService.UpdateSystemIntegration(c.Request().Context(), systemCode, &req)
	if err != nil {
		h.logger.Error("更新系统集成失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "更新系统集成失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "系统集成更新成功",
		"data":    system,
	})
}

// DeleteSystemIntegration 删除系统集成
func (h *SSOHandler) DeleteSystemIntegration(c echo.Context) error {
	systemCode := c.Param("systemCode")
	if systemCode == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少系统代码参数",
		})
	}

	// 这里需要实现删除系统集成的方法
	return c.JSON(http.StatusNotImplemented, map[string]interface{}{
		"code":    5002,
		"message": "功能未实现",
	})
}

// CreateUserSystemProfile 创建用户系统配置文件
func (h *SSOHandler) CreateUserSystemProfile(c echo.Context) error {
	var req service.CreateUserSystemProfileRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为创建者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.CreatedBy = userID

	profile, err := h.ssoService.CreateUserSystemProfile(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建用户系统配置文件失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "创建用户系统配置文件失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "用户系统配置文件创建成功",
		"data":    profile,
	})
}

// GetUserSystemProfiles 获取用户系统配置文件
func (h *SSOHandler) GetUserSystemProfiles(c echo.Context) error {
	// 暂时不解析用户ID

	// 这里需要实现获取用户系统配置文件列表的方法
	return c.JSON(http.StatusNotImplemented, map[string]interface{}{
		"code":    5002,
		"message": "功能未实现",
	})
}

// GetUserSystemProfile 获取配置文件详情
func (h *SSOHandler) GetUserSystemProfile(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// UpdateUserSystemProfile 更新配置文件
func (h *SSOHandler) UpdateUserSystemProfile(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// DeleteUserSystemProfile 删除配置文件
func (h *SSOHandler) DeleteUserSystemProfile(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// SyncUserToSystem 同步用户到系统
func (h *SSOHandler) SyncUserToSystem(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的用户ID",
		})
	}

	systemCode := c.Param("systemCode")
	if systemCode == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少系统代码参数",
		})
	}

	err = h.ssoService.SyncUserToSystem(c.Request().Context(), userID, systemCode)
	if err != nil {
		h.logger.Error("同步用户到系统失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "同步用户到系统失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "用户同步成功",
	})
}

// GetUserAccessibleSystems 获取用户可访问系统
func (h *SSOHandler) GetUserAccessibleSystems(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的用户ID",
		})
	}

	systems, err := h.ssoService.GetUserAccessibleSystems(c.Request().Context(), userID)
	if err != nil {
		h.logger.Error("获取用户可访问系统失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取用户可访问系统失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取用户可访问系统成功",
		"data":    systems,
	})
}

// CreatePermissionMapping 创建权限映射
func (h *SSOHandler) CreatePermissionMapping(c echo.Context) error {
	var req service.CreatePermissionMappingRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为创建者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.CreatedBy = userID

	mapping, err := h.ssoService.CreatePermissionMapping(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建权限映射失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "创建权限映射失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限映射创建成功",
		"data":    mapping,
	})
}

// GetPermissionMappings 获取权限映射列表
func (h *SSOHandler) GetPermissionMappings(c echo.Context) error {
	sourceSystem := c.QueryParam("sourceSystem")
	targetSystem := c.QueryParam("targetSystem")

	mappings, err := h.ssoService.GetPermissionMappings(c.Request().Context(), sourceSystem, targetSystem)
	if err != nil {
		h.logger.Error("获取权限映射列表失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取权限映射列表失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取权限映射列表成功",
		"data":    mappings,
	})
}

// GetPermissionMapping 获取权限映射详情
func (h *SSOHandler) GetPermissionMapping(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// UpdatePermissionMapping 更新权限映射
func (h *SSOHandler) UpdatePermissionMapping(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// DeletePermissionMapping 删除权限映射
func (h *SSOHandler) DeletePermissionMapping(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// MapPermissions 映射权限
func (h *SSOHandler) MapPermissions(c echo.Context) error {
	var req struct {
		UserID            uuid.UUID `json:"user_id" validate:"required"`
		SourceSystem      string    `json:"source_system" validate:"required"`
		TargetSystem      string    `json:"target_system" validate:"required"`
		SourcePermissions []string  `json:"source_permissions" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	mappedPermissions, err := h.ssoService.MapPermissions(c.Request().Context(), req.UserID, req.SourceSystem, req.TargetSystem, req.SourcePermissions)
	if err != nil {
		h.logger.Error("权限映射失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "权限映射失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限映射成功",
		"data": map[string]interface{}{
			"mapped_permissions": mappedPermissions,
		},
	})
}

// SyncUserData 同步用户数据
func (h *SSOHandler) SyncUserData(c echo.Context) error {
	var req service.SyncUserDataRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	result, err := h.ssoService.SyncUserData(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("同步用户数据失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "同步用户数据失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "用户数据同步成功",
		"data":    result,
	})
}

// SyncPermissions 同步权限
func (h *SSOHandler) SyncPermissions(c echo.Context) error {
	var req service.SyncPermissionsRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	result, err := h.ssoService.SyncPermissions(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("同步权限失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "同步权限失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限同步成功",
		"data":    result,
	})
}

// GetSyncLogs 获取同步日志
func (h *SSOHandler) GetSyncLogs(c echo.Context) error {
	filter := parseSyncLogFilter(c)

	logs, err := h.ssoService.GetSyncLogs(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取同步日志失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取同步日志失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取同步日志成功",
		"data":    logs,
	})
}

// ValidateCrossSystemAccess 验证跨系统访问
func (h *SSOHandler) ValidateCrossSystemAccess(c echo.Context) error {
	var req struct {
		UserID       uuid.UUID `json:"user_id" validate:"required"`
		SourceSystem string    `json:"source_system" validate:"required"`
		TargetSystem string    `json:"target_system" validate:"required"`
		Resource     string    `json:"resource" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	isAllowed, err := h.ssoService.ValidateCrossSystemAccess(c.Request().Context(), req.UserID, req.SourceSystem, req.TargetSystem, req.Resource)
	if err != nil {
		h.logger.Error("验证跨系统访问失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "验证跨系统访问失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "跨系统访问验证成功",
		"data": map[string]interface{}{
			"is_allowed": isAllowed,
		},
	})
}

// GetUserSystemPermissions 获取用户系统权限
func (h *SSOHandler) GetUserSystemPermissions(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的用户ID",
		})
	}

	systemCode := c.Param("systemCode")
	if systemCode == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "缺少系统代码参数",
		})
	}

	permissions, err := h.ssoService.GetUserSystemPermissions(c.Request().Context(), userID, systemCode)
	if err != nil {
		h.logger.Error("获取用户系统权限失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取用户系统权限失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取用户系统权限成功",
		"data":    permissions,
	})
}

// parseSystemIntegrationFilter 解析系统集成查询过滤器
func parseSystemIntegrationFilter(c echo.Context) *service.SystemIntegrationFilter {
	filter := &service.SystemIntegrationFilter{}

	// 解析系统类型过滤
	if systemType := c.QueryParam("systemType"); systemType != "" {
		filter.SystemType = []string{systemType}
	}

	// 解析集成类型过滤
	if integrationType := c.QueryParam("integrationType"); integrationType != "" {
		filter.IntegrationType = []string{integrationType}
	}

	// 解析状态过滤
	if status := c.QueryParam("status"); status != "" {
		filter.Status = []string{status}
	}

	// 解析SSO启用过滤
	if isSSOEnabledStr := c.QueryParam("isSSOEnabled"); isSSOEnabledStr != "" {
		if isSSOEnabled, err := strconv.ParseBool(isSSOEnabledStr); err == nil {
			filter.IsSSOEnabled = &isSSOEnabled
		}
	}

	// 解析关键词搜索
	if keyword := c.QueryParam("keyword"); keyword != "" {
		filter.Keyword = &keyword
	}

	// 解析分页参数
	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			filter.Offset = &offset
		}
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			filter.Limit = &limit
		}
	}

	// 解析排序参数
	if sortBy := c.QueryParam("sortBy"); sortBy != "" {
		filter.SortBy = &sortBy
	}

	if sortOrder := c.QueryParam("sortOrder"); sortOrder != "" {
		filter.SortOrder = &sortOrder
	}

	return filter
}

// parseSyncLogFilter 解析同步日志查询过滤器
func parseSyncLogFilter(c echo.Context) *service.SyncLogFilter {
	filter := &service.SyncLogFilter{}

	// 解析同步类型过滤
	if syncType := c.QueryParam("syncType"); syncType != "" {
		filter.SyncType = []string{syncType}
	}

	// 解析源系统过滤
	if sourceSystem := c.QueryParam("sourceSystem"); sourceSystem != "" {
		filter.SourceSystem = []string{sourceSystem}
	}

	// 解析目标系统过滤
	if targetSystem := c.QueryParam("targetSystem"); targetSystem != "" {
		filter.TargetSystem = []string{targetSystem}
	}

	// 解析同步结果过滤
	if syncResult := c.QueryParam("syncResult"); syncResult != "" {
		filter.SyncResult = []string{syncResult}
	}

	// 解析实体类型过滤
	if entityType := c.QueryParam("entityType"); entityType != "" {
		filter.EntityType = []string{entityType}
	}

	// 解析用户ID过滤
	if userIDStr := c.QueryParam("userId"); userIDStr != "" {
		if userID, err := uuid.Parse(userIDStr); err == nil {
			filter.UserID = &userID
		}
	}

	// 解析日期范围过滤
	if startDate := c.QueryParam("startDate"); startDate != "" {
		filter.StartDate = &startDate
	}

	if endDate := c.QueryParam("endDate"); endDate != "" {
		filter.EndDate = &endDate
	}

	// 解析分页参数
	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			filter.Offset = &offset
		}
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			filter.Limit = &limit
		}
	}

	// 解析排序参数
	if sortBy := c.QueryParam("sortBy"); sortBy != "" {
		filter.SortBy = &sortBy
	}

	if sortOrder := c.QueryParam("sortOrder"); sortOrder != "" {
		filter.SortOrder = &sortOrder
	}

	return filter
}
