package handler

import (
	"time"

	"github.com/labstack/echo/v4"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

// SystemAccessSimpleHandler 简化的系统访问权限处理器
type SystemAccessSimpleHandler struct {
	*Handler
	userSystemAccessSvc service.UserSystemAccessService
}

// NewSystemAccessSimpleHandler 创建简化的系统访问权限处理器
func NewSystemAccessSimpleHandler(
	baseHandler *Handler,
	userSystemAccessSvc service.UserSystemAccessService,
) *SystemAccessSimpleHandler {
	return &SystemAccessSimpleHandler{
		Handler:             baseHandler,
		userSystemAccessSvc: userSystemAccessSvc,
	}
}

// GrantHOSAccessRequest 授予HOS系统访问权限请求
type GrantHOSAccessRequest struct {
	UserID      string `json:"user_id" validate:"required"`
	AccessLevel string `json:"access_level" validate:"required,oneof=admin manager"`
}

// SystemAccessInfo 系统访问信息响应
type SystemAccessInfo struct {
	ID          string `json:"id"`
	UserID      string `json:"user_id"`
	Username    string `json:"username,omitempty"`
	SystemCode  string `json:"system_code"`
	AccessLevel string `json:"access_level"`
	ScopeType   string `json:"scope_type"`
	Status      string `json:"status"`
	GrantedAt   string `json:"granted_at"`
	ExpiresAt   string `json:"expires_at,omitempty"`
}

// GrantHOSAccess 授予用户HOS系统访问权限
// @Summary 授予用户HOS系统访问权限
// @Description 为用户授予HOS系统的全局管理权限
// @Tags 系统权限管理
// @Accept json
// @Produce json
// @Param request body GrantHOSAccessRequest true "授权请求"
// @Success 200 {object} Response{data=SystemAccessInfo}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/system-access/hos/grant [post]
func (h *SystemAccessSimpleHandler) GrantHOSAccess(c echo.Context) error {
	var req GrantHOSAccessRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Invalid request", zap.Error(err))
		return h.ErrorResponse(c, 4000, "Invalid request parameters", err.Error())
	}

	// 解析用户ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		h.logger.Error("Invalid user ID", zap.Error(err))
		return h.ErrorResponse(c, 4000, "Invalid user ID", err.Error())
	}

	// 获取当前用户信息（用于审计）
	var grantedBy *uuid.UUID
	if currentUserID := c.Get("user_id"); currentUserID != nil {
		if userIDStr, ok := currentUserID.(string); ok {
			if id, err := uuid.Parse(userIDStr); err == nil {
				grantedBy = &id
			}
		}
	}

	// 构建授权请求
	grantReq := &service.GrantSystemAccessRequest{
		UserID:      userID,
		SystemCode:  domain.SystemCodeHOS,
		AccessLevel: domain.AccessLevel(req.AccessLevel),
		ScopeType:   domain.ScopeTypeGlobal,
		ScopeIDs:    []int64{}, // HOS系统使用全局权限
		GrantedBy:   grantedBy,
		ExpiresAt:   nil, // 永久有效
	}

	// 授予权限
	if err := h.userSystemAccessSvc.GrantSystemAccess(c.Request().Context(), grantReq); err != nil {
		h.logger.Error("Failed to grant HOS access", zap.Error(err))
		return h.ErrorResponse(c, 5000, "Failed to grant HOS access", err.Error())
	}

	// 获取授权后的权限信息
	access, err := h.userSystemAccessSvc.GetUserSystemAccessBySystem(c.Request().Context(), userID, domain.SystemCodeHOS)
	if err != nil {
		h.logger.Error("Failed to get granted access", zap.Error(err))
		return h.ErrorResponse(c, 5000, "Failed to get granted access", err.Error())
	}

	// 构建响应
	response := &SystemAccessInfo{
		ID:          access.ID.String(),
		UserID:      access.UserID.String(),
		SystemCode:  string(access.SystemCode),
		AccessLevel: string(access.AccessLevel),
		ScopeType:   string(access.ScopeType),
		Status:      string(access.Status),
		GrantedAt:   access.GrantedAt.Format(time.RFC3339),
	}

	if access.User != nil {
		response.Username = access.User.Username
	}

	if access.ExpiresAt != nil {
		response.ExpiresAt = access.ExpiresAt.Format(time.RFC3339)
	}

	return h.SuccessResponse(c, response, "HOS access granted successfully")
}

// RevokeHOSAccess 撤销用户HOS系统访问权限
// @Summary 撤销用户HOS系统访问权限
// @Description 撤销用户对HOS系统的访问权限
// @Tags 系统权限管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/system-access/hos/{user_id} [delete]
func (h *SystemAccessSimpleHandler) RevokeHOSAccess(c echo.Context) error {
	userIDStr := c.Param("user_id")

	// 解析用户ID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.logger.Error("Invalid user ID", zap.Error(err))
		return h.ErrorResponse(c, 4000, "Invalid user ID", err.Error())
	}

	// 撤销权限
	if err := h.userSystemAccessSvc.RevokeSystemAccess(c.Request().Context(), userID, domain.SystemCodeHOS); err != nil {
		h.logger.Error("Failed to revoke HOS access", zap.Error(err))
		return h.ErrorResponse(c, 5000, "Failed to revoke HOS access", err.Error())
	}

	return h.SuccessResponse(c, nil, "HOS access revoked successfully")
}

// GetUserHOSAccess 获取用户HOS系统访问权限
// @Summary 获取用户HOS系统访问权限
// @Description 获取指定用户的HOS系统访问权限
// @Tags 系统权限管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} Response{data=SystemAccessInfo}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/system-access/hos/{user_id} [get]
func (h *SystemAccessSimpleHandler) GetUserHOSAccess(c echo.Context) error {
	userIDStr := c.Param("user_id")

	// 解析用户ID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.logger.Error("Invalid user ID", zap.Error(err))
		return h.ErrorResponse(c, 4000, "Invalid user ID", err.Error())
	}

	// 获取用户HOS系统访问权限
	access, err := h.userSystemAccessSvc.GetUserSystemAccessBySystem(c.Request().Context(), userID, domain.SystemCodeHOS)
	if err != nil {
		h.logger.Error("Failed to get user HOS access", zap.Error(err))
		return h.ErrorResponse(c, 5000, "Failed to get user HOS access", err.Error())
	}

	// 构建响应
	response := &SystemAccessInfo{
		ID:          access.ID.String(),
		UserID:      access.UserID.String(),
		SystemCode:  string(access.SystemCode),
		AccessLevel: string(access.AccessLevel),
		ScopeType:   string(access.ScopeType),
		Status:      string(access.Status),
		GrantedAt:   access.GrantedAt.Format(time.RFC3339),
	}

	if access.User != nil {
		response.Username = access.User.Username
	}

	if access.ExpiresAt != nil {
		response.ExpiresAt = access.ExpiresAt.Format(time.RFC3339)
	}

	return h.SuccessResponse(c, response, "User HOS access retrieved successfully")
}

// ListUsersWithHOSAccess 列出拥有HOS系统访问权限的用户
// @Summary 列出拥有HOS系统访问权限的用户
// @Description 获取所有拥有HOS系统访问权限的用户列表
// @Tags 系统权限管理
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]SystemAccessInfo}
// @Failure 500 {object} Response
// @Router /api/v1/system-access/hos/users [get]
func (h *SystemAccessSimpleHandler) ListUsersWithHOSAccess(c echo.Context) error {
	// 获取所有HOS系统访问权限
	accesses, err := h.userSystemAccessSvc.GetUsersBySystemAccess(c.Request().Context(), domain.SystemCodeHOS, "", domain.ScopeTypeGlobal)
	if err != nil {
		h.logger.Error("Failed to get users with HOS access", zap.Error(err))
		return h.ErrorResponse(c, 5000, "Failed to get users with HOS access", err.Error())
	}

	// 构建响应
	var responses []SystemAccessInfo
	for _, user := range accesses {
		// 获取用户的HOS访问权限详情
		access, err := h.userSystemAccessSvc.GetUserSystemAccessBySystem(c.Request().Context(), user.ID, domain.SystemCodeHOS)
		if err != nil {
			continue // 跳过错误的记录
		}

		response := SystemAccessInfo{
			ID:          access.ID.String(),
			UserID:      access.UserID.String(),
			Username:    user.Username,
			SystemCode:  string(access.SystemCode),
			AccessLevel: string(access.AccessLevel),
			ScopeType:   string(access.ScopeType),
			Status:      string(access.Status),
			GrantedAt:   access.GrantedAt.Format(time.RFC3339),
		}

		if access.ExpiresAt != nil {
			response.ExpiresAt = access.ExpiresAt.Format(time.RFC3339)
		}

		responses = append(responses, response)
	}

	return h.SuccessResponse(c, responses, "Users with HOS access retrieved successfully")
}
