package handler

import (
	"net/http"
	"strconv"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

// OrganizationHandler 组织管理处理器
type OrganizationHandler struct {
	organizationService service.OrganizationService
	logger              logger.Logger
}

// NewOrganizationHandler 创建组织管理处理器
func NewOrganizationHandler(organizationService service.OrganizationService, logger logger.Logger) *OrganizationHandler {
	return &OrganizationHandler{
		organizationService: organizationService,
		logger:              logger,
	}
}

// CreateOrganization 创建组织
func (h *OrganizationHandler) CreateOrganization(c echo.Context) error {
	var req service.CreateOrganizationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为创建者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.CreatedBy = userID

	org, err := h.organizationService.CreateOrganization(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("创建组织失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "创建组织失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "组织创建成功",
		"data":    org,
	})
}

// GetOrganization 获取组织详情
func (h *OrganizationHandler) GetOrganization(c echo.Context) error {
	orgID, err := strconv.ParseInt(c.Param("orgId"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的组织ID",
		})
	}

	org, err := h.organizationService.GetOrganization(c.Request().Context(), orgID)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"code":    3002,
			"message": "组织不存在",
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取组织成功",
		"data":    org,
	})
}

// UpdateOrganization 更新组织
func (h *OrganizationHandler) UpdateOrganization(c echo.Context) error {
	orgID, err := strconv.ParseInt(c.Param("orgId"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的组织ID",
		})
	}

	var req service.UpdateOrganizationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为更新者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.UpdatedBy = userID

	org, err := h.organizationService.UpdateOrganization(c.Request().Context(), orgID, &req)
	if err != nil {
		h.logger.Error("更新组织失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "更新组织失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "组织更新成功",
		"data":    org,
	})
}

// DeleteOrganization 删除组织
func (h *OrganizationHandler) DeleteOrganization(c echo.Context) error {
	orgID, err := strconv.ParseInt(c.Param("orgId"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的组织ID",
		})
	}

	err = h.organizationService.DeleteOrganization(c.Request().Context(), orgID)
	if err != nil {
		h.logger.Error("删除组织失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "删除组织失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "组织删除成功",
	})
}

// ListOrganizations 列出组织
func (h *OrganizationHandler) ListOrganizations(c echo.Context) error {
	// 解析查询参数
	filter := parseOrganizationFilter(c)

	orgs, err := h.organizationService.ListOrganizations(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取组织列表失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取组织列表失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取组织列表成功",
		"data":    orgs,
	})
}

// GetOrganizationHierarchy 获取组织层级结构
func (h *OrganizationHandler) GetOrganizationHierarchy(c echo.Context) error {
	var rootID *int64
	if rootIDStr := c.QueryParam("rootId"); rootIDStr != "" {
		if id, err := strconv.ParseInt(rootIDStr, 10, 64); err == nil {
			rootID = &id
		}
	}

	hierarchy, err := h.organizationService.GetOrganizationHierarchy(c.Request().Context(), rootID)
	if err != nil {
		h.logger.Error("获取组织层级结构失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取组织层级结构失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取组织层级结构成功",
		"data":    hierarchy,
	})
}

// AssignUserToOrganization 分配用户到组织
func (h *OrganizationHandler) AssignUserToOrganization(c echo.Context) error {
	var req service.AssignUserToOrganizationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为授权者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.GrantedBy = userID

	err = h.organizationService.AssignUserToOrganization(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("分配用户到组织失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "分配用户到组织失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "用户分配成功",
	})
}

// GetUserOrganizations 获取用户的组织关联
func (h *OrganizationHandler) GetUserOrganizations(c echo.Context) error {
	userIDStr := c.Param("userId")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的用户ID",
		})
	}

	userOrgs, err := h.organizationService.GetUserOrganizations(c.Request().Context(), userID)
	if err != nil {
		h.logger.Error("获取用户组织关联失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取用户组织关联失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取用户组织关联成功",
		"data":    userOrgs,
	})
}

// GetOrganizationTypes 获取组织类型列表
func (h *OrganizationHandler) GetOrganizationTypes(c echo.Context) error {
	orgTypes, err := h.organizationService.GetOrganizationTypes(c.Request().Context())
	if err != nil {
		h.logger.Error("获取组织类型列表失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "获取组织类型列表失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "获取组织类型列表成功",
		"data":    orgTypes,
	})
}



// parseOrganizationFilter 解析组织查询过滤器
func parseOrganizationFilter(c echo.Context) *repository.OrganizationFilter {
	filter := &repository.OrganizationFilter{}

	// 解析状态过滤
	if status := c.QueryParam("status"); status != "" {
		filter.Status = []string{status}
	}

	// 解析组织类型ID
	if orgTypeIDStr := c.QueryParam("orgTypeId"); orgTypeIDStr != "" {
		if orgTypeID, err := strconv.ParseInt(orgTypeIDStr, 10, 64); err == nil {
			filter.OrgTypeID = &orgTypeID
		}
	}

	// 解析父组织ID
	if parentIDStr := c.QueryParam("parentId"); parentIDStr != "" {
		if parentID, err := strconv.ParseInt(parentIDStr, 10, 64); err == nil {
			filter.ParentID = &parentID
		}
	}

	// 解析关键词搜索
	if keyword := c.QueryParam("keyword"); keyword != "" {
		filter.Keyword = &keyword
	}

	// 解析分页参数
	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			filter.Offset = &offset
		}
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			filter.Limit = &limit
		}
	}

	// 解析排序参数
	if sortBy := c.QueryParam("sortBy"); sortBy != "" {
		filter.SortBy = &sortBy
	}

	if sortOrder := c.QueryParam("sortOrder"); sortOrder != "" {
		filter.SortOrder = &sortOrder
	}

	// 解析包含关联数据的参数
	filter.IncludeType = c.QueryParam("includeType") == "true"
	filter.IncludeParent = c.QueryParam("includeParent") == "true"
	filter.IncludeChildren = c.QueryParam("includeChildren") == "true"
	filter.IncludeUsers = c.QueryParam("includeUsers") == "true"

	return filter
}
