package handler

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

// PermissionHandler 权限处理器
type PermissionHandler struct {
	permissionService service.PermissionService
	logger            logger.Logger
}

// NewPermissionHandler 创建权限处理器
func NewPermissionHandler(permissionService service.PermissionService, logger logger.Logger) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
		logger:            logger,
	}
}

// ListPermissions 获取权限列表
func (h *PermissionHandler) ListPermissions(c echo.Context) error {
	// 解析查询参数
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.QueryParam("page_size"))
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 构建过滤条件
	filters := make(map[string]interface{})
	if module := c.QueryParam("module"); module != "" {
		filters["module"] = module
	}
	if resource := c.QueryParam("resource"); resource != "" {
		filters["resource"] = resource
	}
	if action := c.QueryParam("action"); action != "" {
		filters["action"] = action
	}
	if isSystem := c.QueryParam("is_system"); isSystem != "" {
		if isSystem == "true" {
			filters["is_system"] = true
		} else if isSystem == "false" {
			filters["is_system"] = false
		}
	}
	if search := c.QueryParam("search"); search != "" {
		filters["search"] = search
	}

	// 排序参数
	sortBy := c.QueryParam("sort_by")
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := c.QueryParam("sort_order")
	if sortOrder == "" {
		sortOrder = "desc"
	}
	filters["sort_by"] = sortBy
	filters["sort_order"] = sortOrder

	// 调用服务
	req := &service.ListPermissionsRequest{
		Page:     page,
		PageSize: pageSize,
		Filters:  filters,
	}

	resp, err := h.permissionService.ListPermissions(c.Request().Context(), req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    1001,
			"message": "获取权限列表失败",
			"error":   err.Error(),
		})
	}

	// 统计信息
	moduleStats := make(map[string]int)
	systemPermissions := 0
	customPermissions := 0

	for _, permission := range resp.Permissions {
		moduleStats[permission.Module]++
		if permission.IsSystem {
			systemPermissions++
		} else {
			customPermissions++
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限列表获取成功",
		"data": map[string]interface{}{
			"permissions": resp.Permissions,
			"pagination": map[string]interface{}{
				"page":        resp.Page,
				"page_size":   resp.PageSize,
				"total":       resp.Total,
				"total_pages": resp.TotalPages,
			},
			"summary": map[string]interface{}{
				"total_permissions":  resp.Total,
				"system_permissions": systemPermissions,
				"custom_permissions": customPermissions,
				"modules":            moduleStats,
			},
		},
	})
}

// GetPermission 获取权限详情
func (h *PermissionHandler) GetPermission(c echo.Context) error {
	permissionID, err := uuid.Parse(c.Param("permissionId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的权限ID",
		})
	}

	permission, err := h.permissionService.GetPermission(c.Request().Context(), permissionID)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]interface{}{
			"code":    3003,
			"message": "权限不存在",
		})
	}

	// 获取关联的角色
	roles, err := h.permissionService.GetPermissionRoles(c.Request().Context(), permissionID)
	if err != nil {
		h.logger.Error("获取权限关联角色失败")
		roles = []*domain.Role{} // 继续执行，但返回空角色列表
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限详情获取成功",
		"data": map[string]interface{}{
			"permission": permission,
			"roles":      roles,
			"usage": map[string]interface{}{
				"role_count": len(roles),
				"can_delete": permission.CanBeDeleted(),
			},
		},
	})
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c echo.Context) error {
	var req service.CreatePermissionRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 验证权限代码格式
	if !isValidPermissionCode(req.Code) {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    3011,
			"message": "权限代码格式无效，应为 module:resource:action 格式",
		})
	}

	// 验证权限代码与模块、资源、操作的一致性
	expectedCode := domain.GeneratePermissionCode(req.Module, req.Resource, req.Action)
	if req.Code != expectedCode {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":     3011,
			"message":  "权限代码与模块、资源、操作不一致",
			"expected": expectedCode,
		})
	}

	permission, err := h.permissionService.CreatePermission(c.Request().Context(), &req)
	if err != nil {
		if strings.Contains(err.Error(), "权限代码已存在") {
			return c.JSON(http.StatusConflict, map[string]interface{}{
				"code":    3010,
				"message": "权限代码已存在",
			})
		}
		if strings.Contains(err.Error(), "无效的操作类型") {
			return c.JSON(http.StatusBadRequest, map[string]interface{}{
				"code":    3012,
				"message": "权限操作类型无效",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    1004,
			"message": "创建权限失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusCreated, map[string]interface{}{
		"code":    0,
		"message": "权限创建成功",
		"data": map[string]interface{}{
			"permission": permission,
		},
	})
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c echo.Context) error {
	permissionID, err := uuid.Parse(c.Param("permissionId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的权限ID",
		})
	}

	var req service.UpdatePermissionRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	permission, err := h.permissionService.UpdatePermission(c.Request().Context(), permissionID, &req)
	if err != nil {
		if strings.Contains(err.Error(), "权限不存在") {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"code":    3003,
				"message": "权限不存在",
			})
		}
		if strings.Contains(err.Error(), "系统权限受保护") {
			return c.JSON(http.StatusForbidden, map[string]interface{}{
				"code":    3004,
				"message": "系统权限受保护，不能修改",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    1005,
			"message": "更新权限失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限更新成功",
		"data": map[string]interface{}{
			"permission": permission,
		},
	})
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c echo.Context) error {
	permissionID, err := uuid.Parse(c.Param("permissionId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的权限ID",
		})
	}

	err = h.permissionService.DeletePermission(c.Request().Context(), permissionID)
	if err != nil {
		if strings.Contains(err.Error(), "权限不存在") {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"code":    3003,
				"message": "权限不存在",
			})
		}
		if strings.Contains(err.Error(), "系统权限受保护") {
			return c.JSON(http.StatusForbidden, map[string]interface{}{
				"code":    3004,
				"message": "系统权限受保护，不能删除",
			})
		}
		if strings.Contains(err.Error(), "权限正在使用中") {
			return c.JSON(http.StatusConflict, map[string]interface{}{
				"code":    3013,
				"message": "权限正在使用中，不能删除",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    1006,
			"message": "删除权限失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限删除成功",
	})
}

// EvaluatePermission 评估权限
func (h *PermissionHandler) EvaluatePermission(c echo.Context) error {
	var req service.PermissionEvaluationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// EvaluateDataPermission 评估数据权限
func (h *PermissionHandler) EvaluateDataPermission(c echo.Context) error {
	var req service.DataPermissionEvaluationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// BatchEvaluatePermissions 批量评估权限
func (h *PermissionHandler) BatchEvaluatePermissions(c echo.Context) error {
	var req service.BatchPermissionEvaluationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// CreateDataPermission 创建数据权限
func (h *PermissionHandler) CreateDataPermission(c echo.Context) error {
	var req service.CreateDataPermissionRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为创建者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.CreatedBy = userID

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// GetDataPermissions 获取数据权限列表
func (h *PermissionHandler) GetDataPermissions(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// GetDataPermission 获取数据权限详情
func (h *PermissionHandler) GetDataPermission(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// UpdateDataPermission 更新数据权限
func (h *PermissionHandler) UpdateDataPermission(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// DeleteDataPermission 删除数据权限
func (h *PermissionHandler) DeleteDataPermission(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// GrantDataPermissionToUser 授予用户数据权限
func (h *PermissionHandler) GrantDataPermissionToUser(c echo.Context) error {
	var req service.GrantDataPermissionRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为授权者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.GrantedBy = userID

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// RevokeDataPermissionFromUser 撤销用户数据权限
func (h *PermissionHandler) RevokeDataPermissionFromUser(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// GetUserDataPermissions 获取用户数据权限
func (h *PermissionHandler) GetUserDataPermissions(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// GetUserEffectivePermissions 获取用户有效权限
func (h *PermissionHandler) GetUserEffectivePermissions(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// CreatePermissionTemplate 创建权限模板
func (h *PermissionHandler) CreatePermissionTemplate(c echo.Context) error {
	var req service.CreatePermissionTemplateRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为创建者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.CreatedBy = userID

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// GetPermissionTemplates 获取权限模板列表
func (h *PermissionHandler) GetPermissionTemplates(c echo.Context) error {
	// 这里需要实现模板列表查询
	return c.JSON(http.StatusNotImplemented, map[string]interface{}{
		"code":    5002,
		"message": "功能未实现",
	})
}

// GetPermissionTemplate 获取权限模板详情
func (h *PermissionHandler) GetPermissionTemplate(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// UpdatePermissionTemplate 更新权限模板
func (h *PermissionHandler) UpdatePermissionTemplate(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// DeletePermissionTemplate 删除权限模板
func (h *PermissionHandler) DeletePermissionTemplate(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// ApplyPermissionTemplate 应用权限模板
func (h *PermissionHandler) ApplyPermissionTemplate(c echo.Context) error {
	templateIDStr := c.Param("templateId")
	templateID, err := uuid.Parse(templateIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的模板ID",
		})
	}

	var req service.ApplyPermissionTemplateRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 从JWT中获取用户ID作为应用者
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}
	req.TemplateID = templateID
	req.AppliedBy = userID

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
	if err != nil {
		h.logger.Error("应用权限模板失败")
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"code":    5001,
			"message": "应用权限模板失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "权限模板应用成功",
	})
}

// GetUserInheritedPermissions 获取用户继承权限
func (h *PermissionHandler) GetUserInheritedPermissions(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// RefreshUserPermissions 刷新用户权限
func (h *PermissionHandler) RefreshUserPermissions(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// ValidateUserAccess 验证用户访问权限
func (h *PermissionHandler) ValidateUserAccess(c echo.Context) error {
	var req struct {
		UserID       uuid.UUID `json:"user_id" validate:"required"`
		ResourceType string    `json:"resource_type" validate:"required"`
		ResourceID   string    `json:"resource_id" validate:"required"`
		Action       string    `json:"action" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// ValidateOrganizationAccess 验证组织访问权限
func (h *PermissionHandler) ValidateOrganizationAccess(c echo.Context) error {
	var req struct {
		UserID             uuid.UUID `json:"user_id" validate:"required"`
		OrganizationID     int64     `json:"organization_id" validate:"required"`
		RequiredPermission string    `json:"required_permission" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// ValidateStationAccess 验证站点访问权限
func (h *PermissionHandler) ValidateStationAccess(c echo.Context) error {
	var req struct {
		UserID             uuid.UUID `json:"user_id" validate:"required"`
		StationID          int64     `json:"station_id" validate:"required"`
		RequiredPermission string    `json:"required_permission" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// InvalidateUserPermissionCache 清除用户权限缓存
func (h *PermissionHandler) InvalidateUserPermissionCache(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// InvalidateResourcePermissionCache 清除资源权限缓存
func (h *PermissionHandler) InvalidateResourcePermissionCache(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})
}

// GetPermissionCacheStatistics 获取权限缓存统计
func (h *PermissionHandler) GetPermissionCacheStatistics(c echo.Context) error {
	// 暂时返回未实现
	return c.JSON(501, map[string]interface{}{
		"code":    5001,
		"message": "功能未实现",
	})

}

// parseDataPermissionFilter 解析数据权限查询过滤器
func parseDataPermissionFilter(c echo.Context) interface{} {
	// 暂时不使用过滤器
	filter := struct{}{}

	// 暂时不解析过滤器参数

	return filter
}

// GetUserPermissions 获取用户权限
func (h *PermissionHandler) GetUserPermissions(c echo.Context) error {
	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"code":    1002,
			"message": "无效的用户ID",
		})
	}

	// 这里需要调用用户服务获取用户权限
	// 暂时返回空实现
	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    0,
		"message": "用户权限获取成功",
		"data": map[string]interface{}{
			"user_id":     userID,
			"permissions": []interface{}{},
			"roles":       []interface{}{},
		},
	})
}

// isValidPermissionCode 验证权限代码格式
func isValidPermissionCode(code string) bool {
	parts := strings.Split(code, ":")
	return len(parts) == 3 && parts[0] != "" && parts[1] != "" && parts[2] != ""
}
