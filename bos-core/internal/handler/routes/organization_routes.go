package routes

import (
	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/handler"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/middleware"
)

// RegisterOrganizationRoutes 注册组织管理相关路由
func RegisterOrganizationRoutes(e *echo.Echo, orgHandler *handler.OrganizationHandler, authMiddleware middleware.AuthMiddleware) {
	// 组织管理路由组
	orgGroup := e.Group("/api/v1/organizations")
	orgGroup.Use(authMiddleware.RequireAuth())

	// 组织CRUD操作
	orgGroup.POST("", orgHandler.CreateOrganization)                    // 创建组织
	orgGroup.GET("/:orgId", orgHandler.GetOrganization)                 // 获取组织详情
	orgGroup.PUT("/:orgId", orgHandler.UpdateOrganization)              // 更新组织
	orgGroup.DELETE("/:orgId", orgHandler.DeleteOrganization)           // 删除组织
	orgGroup.GET("", orgHandler.ListOrganizations)                      // 组织列表

	// 组织层级结构
	orgGroup.GET("/hierarchy", orgHandler.GetOrganizationHierarchy)     // 获取组织层级结构
	orgGroup.GET("/:orgId/children", orgHandler.GetOrganizationChildren) // 获取子组织
	orgGroup.GET("/:orgId/descendants", orgHandler.GetOrganizationDescendants) // 获取所有后代组织
	orgGroup.GET("/:orgId/ancestors", orgHandler.GetOrganizationAncestors) // 获取祖先组织
	orgGroup.PUT("/:orgId/move", orgHandler.MoveOrganization)            // 移动组织

	// 组织用户管理
	orgGroup.POST("/users/assign", orgHandler.AssignUserToOrganization)  // 分配用户到组织
	orgGroup.DELETE("/users/:userId/organizations/:orgId", orgHandler.RemoveUserFromOrganization) // 移除用户组织关联
	orgGroup.PUT("/users/:userId/primary-organization", orgHandler.SetUserPrimaryOrganization) // 设置主要组织
	orgGroup.GET("/users/:userId/organizations", orgHandler.GetUserOrganizations) // 获取用户组织关联
	orgGroup.GET("/:orgId/users", orgHandler.GetOrganizationUsers)       // 获取组织用户列表

	// 批量操作
	orgGroup.POST("/users/batch-assign", orgHandler.BatchAssignUsersToOrganization) // 批量分配用户
	orgGroup.POST("/users/batch-remove", orgHandler.BatchRemoveUsersFromOrganization) // 批量移除用户

	// 组织统计
	orgGroup.GET("/statistics", orgHandler.GetOrganizationStatistics)    // 组织统计信息
	orgGroup.GET("/users/:userId/statistics", orgHandler.GetUserOrganizationStatistics) // 用户组织统计

	// 组织类型管理路由组
	typeGroup := e.Group("/api/v1/organization-types")
	typeGroup.Use(authMiddleware.RequireAuth())

	typeGroup.POST("", orgHandler.CreateOrganizationType)               // 创建组织类型
	typeGroup.GET("", orgHandler.GetOrganizationTypes)                  // 获取组织类型列表
	typeGroup.GET("/:typeId", orgHandler.GetOrganizationType)           // 获取组织类型详情
	typeGroup.PUT("/:typeId", orgHandler.UpdateOrganizationType)        // 更新组织类型
	typeGroup.DELETE("/:typeId", orgHandler.DeleteOrganizationType)     // 删除组织类型
}

// RegisterPermissionRoutes 注册权限管理相关路由
func RegisterPermissionRoutes(e *echo.Echo, permHandler *handler.PermissionHandler, authMiddleware middleware.AuthMiddleware) {
	// 权限管理路由组
	permGroup := e.Group("/api/v1/permissions")
	permGroup.Use(authMiddleware.RequireAuth())

	// 权限评估
	permGroup.POST("/evaluate", permHandler.EvaluatePermission)          // 评估权限
	permGroup.POST("/evaluate/data", permHandler.EvaluateDataPermission) // 评估数据权限
	permGroup.POST("/evaluate/batch", permHandler.BatchEvaluatePermissions) // 批量评估权限

	// 数据权限管理
	dataPermGroup := permGroup.Group("/data")
	dataPermGroup.POST("", permHandler.CreateDataPermission)             // 创建数据权限
	dataPermGroup.GET("", permHandler.GetDataPermissions)                // 获取数据权限列表
	dataPermGroup.GET("/:permissionId", permHandler.GetDataPermission)   // 获取数据权限详情
	dataPermGroup.PUT("/:permissionId", permHandler.UpdateDataPermission) // 更新数据权限
	dataPermGroup.DELETE("/:permissionId", permHandler.DeleteDataPermission) // 删除数据权限

	// 用户数据权限管理
	userPermGroup := permGroup.Group("/users")
	userPermGroup.POST("/grant", permHandler.GrantDataPermissionToUser)  // 授予用户数据权限
	userPermGroup.DELETE("/:userId/permissions/:permissionId", permHandler.RevokeDataPermissionFromUser) // 撤销用户数据权限
	userPermGroup.GET("/:userId/permissions", permHandler.GetUserDataPermissions) // 获取用户数据权限
	userPermGroup.GET("/:userId/effective-permissions", permHandler.GetUserEffectivePermissions) // 获取用户有效权限

	// 权限模板管理
	templateGroup := permGroup.Group("/templates")
	templateGroup.POST("", permHandler.CreatePermissionTemplate)         // 创建权限模板
	templateGroup.GET("", permHandler.GetPermissionTemplates)            // 获取权限模板列表
	templateGroup.GET("/:templateId", permHandler.GetPermissionTemplate) // 获取权限模板详情
	templateGroup.PUT("/:templateId", permHandler.UpdatePermissionTemplate) // 更新权限模板
	templateGroup.DELETE("/:templateId", permHandler.DeletePermissionTemplate) // 删除权限模板
	templateGroup.POST("/:templateId/apply", permHandler.ApplyPermissionTemplate) // 应用权限模板

	// 权限继承和计算
	permGroup.GET("/users/:userId/inherited", permHandler.GetUserInheritedPermissions) // 获取用户继承权限
	permGroup.POST("/users/:userId/refresh", permHandler.RefreshUserPermissions) // 刷新用户权限

	// 权限验证
	validationGroup := permGroup.Group("/validate")
	validationGroup.POST("/access", permHandler.ValidateUserAccess)      // 验证用户访问权限
	validationGroup.POST("/organization", permHandler.ValidateOrganizationAccess) // 验证组织访问权限
	validationGroup.POST("/station", permHandler.ValidateStationAccess)  // 验证站点访问权限

	// 权限缓存管理
	cacheGroup := permGroup.Group("/cache")
	cacheGroup.DELETE("/users/:userId", permHandler.InvalidateUserPermissionCache) // 清除用户权限缓存
	cacheGroup.DELETE("/resources/:resourceType/:resourceId", permHandler.InvalidateResourcePermissionCache) // 清除资源权限缓存
	cacheGroup.GET("/statistics", permHandler.GetPermissionCacheStatistics) // 获取权限缓存统计
}

// RegisterSSORoutes 注册SSO相关路由
func RegisterSSORoutes(e *echo.Echo, ssoHandler *handler.SSOHandler, authMiddleware middleware.AuthMiddleware) {
	// SSO路由组
	ssoGroup := e.Group("/api/v1/sso")

	// SSO认证流程（部分接口不需要认证）
	ssoGroup.POST("/initiate", ssoHandler.InitiateSSO)                   // 发起SSO认证
	ssoGroup.POST("/validate", ssoHandler.ValidateSSO)                   // 验证SSO认证
	ssoGroup.POST("/refresh", ssoHandler.RefreshSSOToken)                // 刷新SSO令牌
	ssoGroup.POST("/revoke", ssoHandler.RevokeSSOToken)                  // 撤销SSO令牌

	// 需要认证的SSO管理接口
	ssoAuthGroup := ssoGroup.Group("")
	ssoAuthGroup.Use(authMiddleware.RequireAuth())

	// 令牌管理
	tokenGroup := ssoAuthGroup.Group("/tokens")
	tokenGroup.POST("", ssoHandler.CreateSSOToken)                       // 创建SSO令牌
	tokenGroup.GET("/:tokenHash", ssoHandler.GetSSOToken)                // 获取SSO令牌
	tokenGroup.POST("/:tokenHash/validate", ssoHandler.ValidateToken)    // 验证令牌
	tokenGroup.DELETE("/users/:userId", ssoHandler.RevokeUserTokens)     // 撤销用户令牌
	tokenGroup.DELETE("/cleanup", ssoHandler.CleanupExpiredTokens)       // 清理过期令牌

	// 系统集成管理
	systemGroup := ssoAuthGroup.Group("/systems")
	systemGroup.POST("", ssoHandler.RegisterSystem)                      // 注册系统
	systemGroup.GET("", ssoHandler.ListSystemIntegrations)               // 系统集成列表
	systemGroup.GET("/:systemCode", ssoHandler.GetSystemIntegration)     // 获取系统集成详情
	systemGroup.PUT("/:systemCode", ssoHandler.UpdateSystemIntegration)  // 更新系统集成
	systemGroup.DELETE("/:systemCode", ssoHandler.DeleteSystemIntegration) // 删除系统集成

	// 用户系统配置文件管理
	profileGroup := ssoAuthGroup.Group("/profiles")
	profileGroup.POST("", ssoHandler.CreateUserSystemProfile)            // 创建用户系统配置文件
	profileGroup.GET("/users/:userId", ssoHandler.GetUserSystemProfiles) // 获取用户系统配置文件
	profileGroup.GET("/:profileId", ssoHandler.GetUserSystemProfile)     // 获取配置文件详情
	profileGroup.PUT("/:profileId", ssoHandler.UpdateUserSystemProfile)  // 更新配置文件
	profileGroup.DELETE("/:profileId", ssoHandler.DeleteUserSystemProfile) // 删除配置文件
	profileGroup.POST("/users/:userId/sync/:systemCode", ssoHandler.SyncUserToSystem) // 同步用户到系统
	profileGroup.GET("/users/:userId/accessible-systems", ssoHandler.GetUserAccessibleSystems) // 获取用户可访问系统

	// 权限映射管理
	mappingGroup := ssoAuthGroup.Group("/mappings")
	mappingGroup.POST("", ssoHandler.CreatePermissionMapping)            // 创建权限映射
	mappingGroup.GET("", ssoHandler.GetPermissionMappings)               // 获取权限映射列表
	mappingGroup.GET("/:mappingId", ssoHandler.GetPermissionMapping)     // 获取权限映射详情
	mappingGroup.PUT("/:mappingId", ssoHandler.UpdatePermissionMapping)  // 更新权限映射
	mappingGroup.DELETE("/:mappingId", ssoHandler.DeletePermissionMapping) // 删除权限映射
	mappingGroup.POST("/map", ssoHandler.MapPermissions)                 // 映射权限

	// 系统同步
	syncGroup := ssoAuthGroup.Group("/sync")
	syncGroup.POST("/users", ssoHandler.SyncUserData)                    // 同步用户数据
	syncGroup.POST("/permissions", ssoHandler.SyncPermissions)           // 同步权限
	syncGroup.GET("/logs", ssoHandler.GetSyncLogs)                       // 获取同步日志

	// 跨系统操作
	crossGroup := ssoAuthGroup.Group("/cross-system")
	crossGroup.POST("/validate-access", ssoHandler.ValidateCrossSystemAccess) // 验证跨系统访问
	crossGroup.GET("/users/:userId/permissions/:systemCode", ssoHandler.GetUserSystemPermissions) // 获取用户系统权限
}

// RegisterEnhancedAuthRoutes 注册增强认证路由
func RegisterEnhancedAuthRoutes(e *echo.Echo, authHandler *handler.AuthHandler, authMiddleware middleware.AuthMiddleware) {
	// 认证路由组
	authGroup := e.Group("/api/v1/auth")

	// 基础认证（不需要认证）
	authGroup.POST("/login", authHandler.Login)                          // 基础登录
	authGroup.POST("/login/organization", authHandler.LoginWithOrganization) // 组织登录
	authGroup.POST("/logout", authHandler.Logout)                        // 登出
	authGroup.POST("/refresh", authHandler.RefreshToken)                 // 刷新令牌

	// 需要认证的接口
	authAuthGroup := authGroup.Group("")
	authAuthGroup.Use(authMiddleware.RequireAuth())

	// 令牌验证
	authAuthGroup.POST("/validate", authHandler.ValidateToken)           // 验证令牌
	authAuthGroup.GET("/context", authHandler.GetAuthContext)            // 获取认证上下文

	// 密码管理
	passwordGroup := authAuthGroup.Group("/password")
	passwordGroup.POST("/change", authHandler.ChangePassword)            // 修改密码
	passwordGroup.POST("/reset", authHandler.ResetPassword)              // 重置密码

	// 会话管理
	sessionGroup := authAuthGroup.Group("/sessions")
	sessionGroup.GET("", authHandler.GetUserSessions)                    // 获取用户会话
	sessionGroup.DELETE("/:sessionId", authHandler.TerminateSession)     // 终止会话
	sessionGroup.DELETE("/all", authHandler.TerminateAllSessions)        // 终止所有会话

	// 组织认证
	orgAuthGroup := authAuthGroup.Group("/organization")
	orgAuthGroup.POST("/validate-access", authHandler.ValidateOrganizationAccess) // 验证组织访问
	orgAuthGroup.GET("/context", authHandler.GetUserOrganizationContext) // 获取用户组织上下文
	orgAuthGroup.POST("/switch/:orgId", authHandler.SwitchOrganizationContext) // 切换组织上下文

	// SSO集成
	ssoAuthGroup := authAuthGroup.Group("/sso")
	ssoAuthGroup.POST("/initiate", authHandler.InitiateSSO)               // 发起SSO
	ssoAuthGroup.POST("/validate", authHandler.ValidateSSO)               // 验证SSO
	ssoAuthGroup.GET("/token/:targetSystem", authHandler.GetSSOToken)     // 获取SSO令牌
}
