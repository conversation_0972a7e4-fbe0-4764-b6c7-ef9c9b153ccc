package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service/impl"
	"go.uber.org/zap"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Username   string `json:"username" validate:"required,min=3,max=100"` // 支持用户名或邮箱地址
	Password   string `json:"password" validate:"required,min=8,max=128"`
	System     string `json:"system" validate:"required,oneof=BOS EDC HOS"`     // 目标系统：BOS-站点后台系统，EDC-手持设备系统，HOS-总部管理系统
	AuthType   string `json:"authType" validate:"omitempty,oneof=local ldap ad"`
	RememberMe bool   `json:"rememberMe"`
	Captcha    string `json:"captcha"`
	CaptchaID  string `json:"captchaId"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" validate:"required"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	CurrentPassword string `json:"currentPassword" validate:"required"`
	NewPassword     string `json:"newPassword" validate:"required,min=8,max=128"`
	ConfirmPassword string `json:"confirmPassword" validate:"required"`
}

// VerifyPermissionRequest 权限验证请求
type VerifyPermissionRequest struct {
	Resource string                 `json:"resource" validate:"required"`
	Action   string                 `json:"action" validate:"required"`
	Context  map[string]interface{} `json:"context"`
}

// Login 用户登录
func (h *Handler) Login(c echo.Context) error {
	h.logRequest(c, "Login")

	var req LoginRequest
	if err := c.Bind(&req); err != nil {
		h.logError(c, "Login", err)
		return h.ValidationErrorResponse(c, "Invalid request format")
	}

	// TODO: 添加参数验证
	if req.Username == "" || req.Password == "" {
		return h.ErrorResponse(c, 1001, "Username and password are required", nil)
	}

	if req.System == "" {
		return h.ErrorResponse(c, 4003, "System parameter is required", nil)
	}

	// 设置默认认证类型
	if req.AuthType == "" {
		req.AuthType = "local"
	}

	// 获取客户端信息
	ipAddress, userAgent := h.getClientInfo(c)

	// 调用认证服务
	ctx := context.Background()
	result, err := h.services.Auth().Login(ctx, req.Username, req.Password, req.System, ipAddress, userAgent)
	if err != nil {
		h.logError(c, "Login", err)
		// Handle specific system access errors
		if systemErr, ok := err.(*impl.SystemAccessError); ok {
			switch systemErr.Code {
			case "NO_STATION_ASSOCIATION":
				return h.ErrorResponse(c, 4001, systemErr.Message, nil)
			case "INSUFFICIENT_PERMISSIONS":
				return h.ErrorResponse(c, 4002, systemErr.Message, nil)
			case "UNKNOWN_SYSTEM":
				return h.ErrorResponse(c, 4003, systemErr.Message, nil)
			default:
				return h.ErrorResponse(c, 4004, systemErr.Message, nil)
			}
		}
		// TODO: 根据具体错误类型返回不同的错误码
		return h.ErrorResponse(c, 1001, "Invalid username or password", nil)
	}

	// 获取用户角色和权限信息（与GetCurrentUser保持一致）
	userID := result.User.ID

	// 获取用户角色
	userRoles, err := h.services.User().GetUserRoles(ctx, userID)
	if err != nil {
		h.logError(c, "Login", err)
		userRoles = []*domain.Role{} // 设置为空数组
	}

	// 提取角色名称
	roleNames := make([]string, len(userRoles))
	for i, role := range userRoles {
		roleNames[i] = role.Name
	}

	// 获取用户权限
	userPermissions, err := h.services.User().GetUserPermissions(ctx, userID)
	if err != nil {
		h.logError(c, "Login", err)
		userPermissions = []*domain.Permission{} // 设置为空数组
	}

	// 提取权限代码
	permissionCodes := make([]string, len(userPermissions))
	for i, permission := range userPermissions {
		permissionCodes[i] = permission.Code
	}

	// 获取用户关联的真实站点数据
	sites := make([]map[string]interface{}, 0)
	stations := make([]map[string]interface{}, 0)

	// 如果用户有站点权限，获取真实的站点信息
	if len(result.SystemAccess.StationIDs) > 0 {
		for i, stationID := range result.SystemAccess.StationIDs {
			// 从数据库获取真实的站点信息
			stationData, err := h.services.Station().GetStation(ctx, stationID)
			if err != nil {
				h.logError(c, "Login", fmt.Errorf("failed to get station %d: %w", stationID, err))
				// 如果获取失败，使用默认数据
				site := map[string]interface{}{
					"siteId":   fmt.Sprintf("%d", stationID),
					"siteName": fmt.Sprintf("Station %d", stationID),
					"role":     "manager",
					"isDefault": i == 0,
				}
				sites = append(sites, site)

				station := map[string]interface{}{
					"stationId":           stationID,
					"roleType":           "manager",
					"isDefault":          i == 0,
					"hasManagePermission": true,
					"station": map[string]interface{}{
						"id":              stationID,
						"site_name":       fmt.Sprintf("Station %d", stationID),
						"site_code":       fmt.Sprintf("ST%03d", stationID),
						"business_status": "ACTIVE",
					},
				}
				stations = append(stations, station)
				continue
			}

			// 解析地址信息
			var addressInfo map[string]interface{}
			if stationData.Address != "" {
				if err := json.Unmarshal([]byte(stationData.Address), &addressInfo); err != nil {
					h.logError(c, "Login", fmt.Errorf("failed to parse address for station %d: %w", stationID, err))
					addressInfo = make(map[string]interface{})
				}
			}

			// 构造兼容的站点信息
			site := map[string]interface{}{
				"siteId":   fmt.Sprintf("%d", stationData.ID),
				"siteName": stationData.SiteName,
				"role":     "manager", // TODO: 从用户站点角色获取真实角色
				"isDefault": i == 0,
			}
			sites = append(sites, site)

			// 构造站点详细信息
			station := map[string]interface{}{
				"stationId":           stationData.ID,
				"roleType":           "manager", // TODO: 从用户站点角色获取真实角色
				"isDefault":          i == 0,
				"hasManagePermission": true, // TODO: 根据角色确定权限
				"station": map[string]interface{}{
					"id":              stationData.ID,
					"site_name":       stationData.SiteName,
					"site_code":       stationData.SiteCode,
					"business_status": stationData.BusinessStatus,
					"address":         addressInfo,
					"latitude":        stationData.Latitude,
					"longitude":       stationData.Longitude,
				},
			}
			stations = append(stations, station)
		}
	}

	// 构造响应数据（与GetCurrentUser保持一致的结构）
	responseData := map[string]interface{}{
		"accessToken":  result.AccessToken,
		"refreshToken": result.RefreshToken,
		"tokenType":    "Bearer",
		"expiresIn":    3600, // TODO: 从配置或结果中获取
		"user": map[string]interface{}{
			"id":          result.User.ID,
			"username":    result.User.Username,
			"email":       result.User.Email,
			"fullName":    result.User.FullName,
			"phone":       result.User.Phone,
			"status":      result.User.Status,
			"roles":       roleNames,
			"permissions": permissionCodes,
			"lastLoginAt": result.User.LastLoginAt,
			"language":    "en", // TODO: 从用户设置获取
			"sites":       sites,    // 添加站点信息
			"stations":    stations, // 添加站点详细信息
		},
		"systemAccess": map[string]interface{}{
			"system":       result.SystemAccess.System,
			"accessLevel":  result.SystemAccess.AccessLevel,
			"stationIds":   result.SystemAccess.StationIDs,
			"stationCount": result.SystemAccess.StationCount,
		},
	}

	return h.SuccessResponse(c, responseData, "Login successful")
}

// Logout 用户登出
func (h *Handler) Logout(c echo.Context) error {
	h.logRequest(c, "Logout")

	// 从Authorization头获取token
	authHeader := c.Request().Header.Get("Authorization")
	if authHeader == "" {
		return h.ErrorResponse(c, 1010, "Missing authorization header", nil)
	}

	// 解析Bearer token
	token := ""
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		token = authHeader[7:]
	}

	if token == "" {
		return h.ErrorResponse(c, 1006, "Invalid token format", nil)
	}

	// 调用登出服务
	ctx := context.Background()
	err := h.services.Auth().Logout(ctx, token)
	if err != nil {
		h.logError(c, "Logout", err)
		return h.ErrorResponse(c, 1006, "Invalid token", nil)
	}

	return h.SuccessResponse(c, nil, "Logout successful")
}

// RefreshToken 刷新令牌
func (h *Handler) RefreshToken(c echo.Context) error {
	h.logRequest(c, "RefreshToken")

	var req RefreshTokenRequest
	if err := c.Bind(&req); err != nil {
		h.logError(c, "RefreshToken", err)
		return h.ValidationErrorResponse(c, "Invalid request format")
	}

	if req.RefreshToken == "" {
		return h.ErrorResponse(c, 1006, "Refresh token is required", nil)
	}

	// 调用刷新令牌服务
	ctx := context.Background()
	result, err := h.services.Auth().RefreshToken(ctx, req.RefreshToken)
	if err != nil {
		h.logError(c, "RefreshToken", err)
		return h.ErrorResponse(c, 1005, "Token expired or invalid", nil)
	}

	// 构造响应数据
	responseData := map[string]interface{}{
		"accessToken":  result.AccessToken,
		"refreshToken": result.RefreshToken,
		"tokenType":    "Bearer",
		"expiresIn":    3600, // TODO: 从配置或结果中获取
	}

	return h.SuccessResponse(c, responseData, "Token refreshed successfully")
}

// ChangePassword 修改密码
func (h *Handler) ChangePassword(c echo.Context) error {
	h.logRequest(c, "ChangePassword")

	// 获取当前用户ID
	userIDStr, err := h.getCurrentUserID(c)
	if err != nil {
		return err
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return h.ErrorResponse(c, 1010, "Invalid user ID", nil)
	}

	var req ChangePasswordRequest
	if err := c.Bind(&req); err != nil {
		h.logError(c, "ChangePassword", err)
		return h.ValidationErrorResponse(c, "Invalid request format")
	}

	// 验证参数
	if req.CurrentPassword == "" || req.NewPassword == "" || req.ConfirmPassword == "" {
		return h.ErrorResponse(c, 1000, "All password fields are required", nil)
	}

	if req.NewPassword != req.ConfirmPassword {
		return h.ErrorResponse(c, 2010, "New password and confirm password do not match", nil)
	}

	// 调用修改密码服务
	ctx := context.Background()
	err = h.services.Auth().ChangePassword(ctx, userID, req.CurrentPassword, req.NewPassword)
	if err != nil {
		h.logError(c, "ChangePassword", err)
		// 根据具体错误类型返回不同的错误码
		if err.Error() == "crypto/bcrypt: hashedPassword is not the hash of the given password" {
			return h.ErrorResponse(c, 2009, "Current password is incorrect", nil)
		}
		if err.Error() == "密码强度不足" {
			return h.ErrorResponse(c, 2011, "Password strength is insufficient", nil)
		}
		// 其他错误统一返回当前密码错误（安全考虑）
		return h.ErrorResponse(c, 2009, "Current password is incorrect", nil)
	}

	return h.SuccessResponse(c, nil, "Password changed successfully")
}

// VerifyPermission 权限验证
func (h *Handler) VerifyPermission(c echo.Context) error {
	h.logRequest(c, "VerifyPermission")

	// 获取当前用户ID
	userIDStr, err := h.getCurrentUserID(c)
	if err != nil {
		return err
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return h.ErrorResponse(c, 1010, "Invalid user ID", nil)
	}

	var req VerifyPermissionRequest
	if err := c.Bind(&req); err != nil {
		h.logError(c, "VerifyPermission", err)
		return h.ValidationErrorResponse(c, "Invalid request format")
	}

	// 验证参数
	if req.Resource == "" || req.Action == "" {
		return h.ErrorResponse(c, 1000, "Resource and action are required", nil)
	}

	// 构造权限代码
	permissionCode := req.Resource + ":" + req.Action

	// 获取客户端信息
	ipAddress, userAgent := h.getClientInfo(c)

	// 调用权限验证服务
	ctx := context.Background()
	granted, err := h.services.Permission().CheckPermission(ctx, userID, permissionCode, req.Resource)
	if err != nil {
		h.logError(c, "VerifyPermission", err)

		// 记录权限验证失败日志
		h.createPermissionLog(ctx, userID, req.Resource, req.Action, permissionCode, "denied",
			"Permission check failed: "+err.Error(), ipAddress, userAgent, c.Request().URL.Path, c.Request().Method)

		return h.SystemErrorResponse(c, "Permission check failed")
	}

	// 记录权限验证日志
	result := "denied"
	denialReason := "Insufficient permissions"
	if granted {
		result = "granted"
		denialReason = ""
	}

	h.createPermissionLog(ctx, userID, req.Resource, req.Action, permissionCode, result,
		denialReason, ipAddress, userAgent, c.Request().URL.Path, c.Request().Method)

	// 构造响应数据
	responseData := map[string]interface{}{
		"granted":     granted,
		"permissions": []string{permissionCode},
		"restrictions": map[string]interface{}{
			"scope":      "global", // TODO: 根据实际权限范围设置
			"conditions": []string{},
		},
	}

	message := "Permission denied"
	if granted {
		message = "Permission granted"
	}

	return h.SuccessResponse(c, responseData, message)
}

// GetCurrentUser 获取当前用户信息
func (h *Handler) GetCurrentUser(c echo.Context) error {
	h.logRequest(c, "GetCurrentUser")

	// 获取当前用户ID
	userIDStr, err := h.getCurrentUserID(c)
	if err != nil {
		return err
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return h.ErrorResponse(c, 1010, "Invalid user ID", nil)
	}

	// 调用用户服务获取用户信息
	ctx := context.Background()
	user, err := h.services.User().GetUser(ctx, userID)
	if err != nil {
		h.logError(c, "GetCurrentUser", err)
		return h.ErrorResponse(c, 2001, "User not found", nil)
	}

	// 获取用户角色
	roles, err := h.services.User().GetUserRoles(ctx, userID)
	if err != nil {
		h.logError(c, "GetCurrentUser", err)
		roles = []*domain.Role{} // 设置为空数组而不是返回错误
	}

	// 获取用户权限
	permissions, err := h.services.User().GetUserPermissions(ctx, userID)
	if err != nil {
		h.logError(c, "GetCurrentUser", err)
		permissions = []*domain.Permission{} // 设置为空数组而不是返回错误
	}

	// 构造角色列表
	roleNames := make([]string, len(roles))
	for i, role := range roles {
		roleNames[i] = role.Name
	}

	// 构造权限列表
	permissionCodes := make([]string, len(permissions))
	for i, permission := range permissions {
		permissionCodes[i] = permission.Code
	}

	// 从JWT token中获取station信息
	// 直接从echo context中获取JWT claims数据
	stationIDsInterface := c.Get("station_ids")
	accessLevelInterface := c.Get("access_level")

	// 获取station_ids
	var stationIDs []int64
	if stationIDsSlice, ok := stationIDsInterface.([]int64); ok {
		stationIDs = stationIDsSlice
	} else {
		// 如果没有从context中获取到station_ids，使用默认值
		stationIDs = []int64{1} // 默认站点ID为1
	}

	// 获取访问级别
	accessLevel := "user"
	if accessLevelStr, ok := accessLevelInterface.(string); ok {
		accessLevel = accessLevelStr
	}

	systemAccess := &service.SystemAccessInfo{
		System:       "BOS",
		AccessLevel:  accessLevel,
		StationIDs:   stationIDs,
		StationCount: len(stationIDs),
	}

	// 获取用户关联的真实站点数据
	sites := make([]map[string]interface{}, 0)
	stations := make([]map[string]interface{}, 0)

	// 如果用户有站点权限，获取真实的站点信息
	if len(systemAccess.StationIDs) > 0 {
		for i, stationID := range systemAccess.StationIDs {
			// 从数据库获取真实的站点信息
			stationData, err := h.services.Station().GetStation(ctx, stationID)
			if err != nil {
				h.logError(c, "GetCurrentUser", fmt.Errorf("failed to get station %d: %w", stationID, err))
				// 如果获取失败，使用默认数据
				site := map[string]interface{}{
					"siteId":   fmt.Sprintf("%d", stationID),
					"siteName": fmt.Sprintf("Station %d", stationID),
					"role":     "manager",
					"isDefault": i == 0,
				}
				sites = append(sites, site)

				station := map[string]interface{}{
					"stationId":           stationID,
					"roleType":           "manager",
					"isDefault":          i == 0,
					"hasManagePermission": true,
					"station": map[string]interface{}{
						"id":              stationID,
						"site_name":       fmt.Sprintf("Station %d", stationID),
						"site_code":       fmt.Sprintf("ST%03d", stationID),
						"business_status": "ACTIVE",
					},
				}
				stations = append(stations, station)
				continue
			}

			// 解析地址信息
			var addressInfo map[string]interface{}
			if stationData.Address != "" {
				if err := json.Unmarshal([]byte(stationData.Address), &addressInfo); err != nil {
					h.logError(c, "GetCurrentUser", fmt.Errorf("failed to parse address for station %d: %w", stationID, err))
					addressInfo = make(map[string]interface{})
				}
			}

			// 构造兼容的站点信息
			site := map[string]interface{}{
				"siteId":   fmt.Sprintf("%d", stationData.ID),
				"siteName": stationData.SiteName,
				"role":     "manager", // TODO: 从用户站点角色获取真实角色
				"isDefault": i == 0,
			}
			sites = append(sites, site)

			// 构造站点详细信息
			station := map[string]interface{}{
				"stationId":           stationData.ID,
				"roleType":           "manager", // TODO: 从用户站点角色获取真实角色
				"isDefault":          i == 0,
				"hasManagePermission": true, // TODO: 根据角色确定权限
				"station": map[string]interface{}{
					"id":              stationData.ID,
					"site_name":       stationData.SiteName,
					"site_code":       stationData.SiteCode,
					"business_status": stationData.BusinessStatus,
					"address":         addressInfo,
					"latitude":        stationData.Latitude,
					"longitude":       stationData.Longitude,
				},
			}
			stations = append(stations, station)
		}
	}

	// 构造响应数据（与登录接口保持一致）
	responseData := map[string]interface{}{
		"id":          user.ID,
		"username":    user.Username,
		"email":       user.Email,
		"fullName":    user.FullName,
		"phone":       user.Phone,
		"status":      user.Status,
		"roles":       roleNames,
		"permissions": permissionCodes,
		"lastLoginAt": user.LastLoginAt,
		"language":    "en", // TODO: 从用户设置获取
		"sites":       sites,    // 添加站点信息
		"stations":    stations, // 添加站点详细信息
		"systemAccess": map[string]interface{}{
			"system":       systemAccess.System,
			"accessLevel":  systemAccess.AccessLevel,
			"stationIds":   systemAccess.StationIDs,
			"stationCount": systemAccess.StationCount,
		},
	}

	return h.SuccessResponse(c, responseData, "User information retrieved successfully")
}

// HealthCheck 健康检查接口
func (h *Handler) HealthCheck(c echo.Context) error {
	h.logRequest(c, "HealthCheck")

	// 检查数据库连接
	// TODO: 通过服务层检查数据库连接状态

	return h.SuccessResponse(c, map[string]interface{}{
		"status":    "healthy",
		"module":    "bos-core",
		"version":   "1.0.0",
		"timestamp": time.Now().Format(time.RFC3339),
	}, "Service is healthy")
}

// VerifyToken 验证令牌接口
func (h *Handler) VerifyToken(c echo.Context) error {
	h.logRequest(c, "VerifyToken")

	// 获取Authorization头
	authHeader := c.Request().Header.Get("Authorization")
	if authHeader == "" {
		return h.ErrorResponse(c, 1001, "Authorization header is required", nil)
	}

	// 解析Bearer token
	if !strings.HasPrefix(authHeader, "Bearer ") {
		return h.ErrorResponse(c, 1001, "Invalid authorization header format", nil)
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")
	if token == "" {
		return h.ErrorResponse(c, 1001, "Token is required", nil)
	}

	// 调用认证服务验证令牌
	ctx := context.Background()
	user, err := h.services.Auth().ValidateToken(ctx, token)
	if err != nil {
		h.logError(c, "VerifyToken", err)
		return h.ErrorResponse(c, 1002, "Invalid or expired token", nil)
	}

	// 构造响应数据
	responseData := map[string]interface{}{
		"valid":    true,
		"userId":   user.ID,
		"username": user.Username,
		"email":    user.Email,
		"fullName": user.FullName,
		"status":   user.Status,
	}

	return h.SuccessResponse(c, responseData, "Token is valid")
}

// createPermissionLog 创建权限验证日志的辅助方法
func (h *Handler) createPermissionLog(ctx context.Context, userID uuid.UUID, resource, action, permissionCode, result, denialReason, ipAddress, userAgent, requestPath, requestMethod string) {
	// 构造权限日志请求
	logReq := &service.CreatePermissionLogRequest{
		UserID:         userID,
		PermissionCode: permissionCode,
		Resource:       resource,
		Action:         action,
		Result:         domain.LogResult(result),
		IPAddress:      ipAddress,
		UserAgent:      userAgent,
	}

	// 异步记录日志，不影响主要业务流程
	go func() {
		if err := h.services.Log().CreatePermissionLog(context.Background(), logReq); err != nil {
			h.logger.Error("Failed to create permission log",
				zap.Error(err),
				zap.String("user_id", userID.String()),
				zap.String("resource", resource),
				zap.String("action", action),
				zap.String("denial_reason", denialReason),
				zap.String("request_path", requestPath),
				zap.String("request_method", requestMethod),
			)
		}
	}()
}

// LoginWithOrganization 组织登录
func (h *Handler) LoginWithOrganization(c echo.Context) error {
	h.logRequest(c, "LoginWithOrganization")

	var req service.OrganizationLoginRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定组织登录请求失败", zap.Error(err))
		return c.JSON(400, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 获取客户端信息
	req.IPAddress = c.RealIP()
	req.UserAgent = c.Request().UserAgent()

	result, err := h.services.Auth().LoginWithOrganization(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("组织登录失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "组织登录失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "组织登录成功",
		"data":    result,
	})
}

// ValidateTokenWithContext 验证令牌并获取上下文
func (h *Handler) ValidateTokenWithContext(c echo.Context) error {
	h.logRequest(c, "ValidateTokenWithContext")

	token := c.Request().Header.Get("Authorization")
	if token == "" {
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "缺少认证令牌",
		})
	}

	// 移除 "Bearer " 前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = token[7:]
	}

	authContext, err := h.services.Auth().ValidateTokenWithContext(c.Request().Context(), token)
	if err != nil {
		h.logger.Error("令牌验证失败", zap.Error(err))
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "令牌验证失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "令牌验证成功",
		"data":    authContext,
	})
}

// GetAuthContext 获取认证上下文
func (h *Handler) GetAuthContext(c echo.Context) error {
	h.logRequest(c, "GetAuthContext")

	token := c.Request().Header.Get("Authorization")
	if token == "" {
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "缺少认证令牌",
		})
	}

	// 移除 "Bearer " 前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = token[7:]
	}

	authContext, err := h.services.Auth().ValidateTokenWithContext(c.Request().Context(), token)
	if err != nil {
		h.logger.Error("获取认证上下文失败", zap.Error(err))
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "获取认证上下文失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "获取认证上下文成功",
		"data":    authContext,
	})
}

// ValidateOrganizationAccess 验证组织访问权限
func (h *Handler) ValidateOrganizationAccess(c echo.Context) error {
	h.logRequest(c, "ValidateOrganizationAccess")

	var req struct {
		UserID         uuid.UUID `json:"user_id" validate:"required"`
		OrganizationID int64     `json:"organization_id" validate:"required"`
		RequiredRole   string    `json:"required_role" validate:"required"`
	}

	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定组织访问验证请求失败", zap.Error(err))
		return c.JSON(400, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	isAllowed, err := h.services.Auth().ValidateOrganizationAccess(c.Request().Context(), req.UserID, req.OrganizationID, req.RequiredRole)
	if err != nil {
		h.logger.Error("验证组织访问权限失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "验证组织访问权限失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "组织访问权限验证成功",
		"data": map[string]interface{}{
			"is_allowed": isAllowed,
		},
	})
}

// GetUserOrganizationContext 获取用户组织上下文
func (h *Handler) GetUserOrganizationContext(c echo.Context) error {
	h.logRequest(c, "GetUserOrganizationContext")

	// 从JWT中获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}

	orgContext, err := h.services.Auth().GetUserOrganizationContext(c.Request().Context(), userID)
	if err != nil {
		h.logger.Error("获取用户组织上下文失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "获取用户组织上下文失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "获取用户组织上下文成功",
		"data":    orgContext,
	})
}

// SwitchOrganizationContext 切换组织上下文
func (h *Handler) SwitchOrganizationContext(c echo.Context) error {
	h.logRequest(c, "SwitchOrganizationContext")

	// 从JWT中获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}

	// 获取组织ID参数
	orgIDStr := c.Param("orgId")
	orgID, err := strconv.ParseInt(orgIDStr, 10, 64)
	if err != nil {
		return c.JSON(400, map[string]interface{}{
			"code":    1002,
			"message": "无效的组织ID",
		})
	}

	orgContext, err := h.services.Auth().SwitchOrganizationContext(c.Request().Context(), userID, orgID)
	if err != nil {
		h.logger.Error("切换组织上下文失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "切换组织上下文失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "组织上下文切换成功",
		"data":    orgContext,
	})
}

// InitiateSSO 发起SSO认证
func (h *Handler) InitiateSSO(c echo.Context) error {
	h.logRequest(c, "InitiateSSO")

	var req service.InitiateSSORequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定SSO发起请求失败", zap.Error(err))
		return c.JSON(400, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	// 获取客户端信息
	req.IPAddress = c.RealIP()
	req.UserAgent = c.Request().UserAgent()

	response, err := h.services.Auth().InitiateSSO(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("发起SSO认证失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "发起SSO认证失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "SSO认证发起成功",
		"data":    response,
	})
}

// ValidateSSO 验证SSO认证
func (h *Handler) ValidateSSO(c echo.Context) error {
	h.logRequest(c, "ValidateSSO")

	var req service.ValidateSSORequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("绑定SSO验证请求失败", zap.Error(err))
		return c.JSON(400, map[string]interface{}{
			"code":    1003,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
	}

	response, err := h.services.Auth().ValidateSSO(c.Request().Context(), &req)
	if err != nil {
		h.logger.Error("验证SSO认证失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "验证SSO认证失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "SSO认证验证成功",
		"data":    response,
	})
}

// GetSSOToken 获取SSO令牌
func (h *Handler) GetSSOToken(c echo.Context) error {
	h.logRequest(c, "GetSSOToken")

	// 从JWT中获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		return c.JSON(401, map[string]interface{}{
			"code":    1001,
			"message": "未授权访问",
		})
	}

	targetSystem := c.Param("targetSystem")
	if targetSystem == "" {
		return c.JSON(400, map[string]interface{}{
			"code":    1002,
			"message": "缺少目标系统参数",
		})
	}

	tokenInfo, err := h.services.Auth().GetSSOToken(c.Request().Context(), userID, targetSystem)
	if err != nil {
		h.logger.Error("获取SSO令牌失败", zap.Error(err))
		return c.JSON(500, map[string]interface{}{
			"code":    5001,
			"message": "获取SSO令牌失败",
			"error":   err.Error(),
		})
	}

	return c.JSON(200, map[string]interface{}{
		"code":    0,
		"message": "获取SSO令牌成功",
		"data":    tokenInfo,
	})
}

// getUserIDFromContext 从上下文中获取用户ID
func getUserIDFromContext(c echo.Context) (uuid.UUID, error) {
	// 从JWT中间件设置的用户信息中获取用户ID
	user := c.Get("user")
	if user == nil {
		return uuid.Nil, fmt.Errorf("用户信息不存在")
	}

	// 尝试从不同的可能格式中获取用户ID
	switch u := user.(type) {
	case *domain.User:
		return u.ID, nil
	case map[string]interface{}:
		if userIDStr, ok := u["user_id"].(string); ok {
			return uuid.Parse(userIDStr)
		}
		if userIDStr, ok := u["id"].(string); ok {
			return uuid.Parse(userIDStr)
		}
	case string:
		return uuid.Parse(u)
	}

	return uuid.Nil, fmt.Errorf("无法解析用户ID")
}
