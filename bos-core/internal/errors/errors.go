package errors

import (
	"fmt"
)

// AuthError 认证相关错误
type AuthError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *AuthError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s", e.Message, e.Details)
	}
	return e.Message
}

// NewAuthError 创建认证错误
func NewAuthError(code int, message string, details ...string) *AuthError {
	err := &AuthError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// 错误码常量定义
const (
	// 系统级错误
	ErrSystemBusy = -1 // 系统繁忙

	// 认证相关错误 (1000-1999)
	ErrInvalidCredentials = 1001 // 用户名或密码错误
	ErrAccountLocked      = 1002 // 账户已锁定
	ErrAccountDisabled    = 1003 // 账户已禁用
	ErrPasswordExpired    = 1004 // 密码已过期
	ErrTokenExpired       = 1005 // 令牌已过期
	ErrTokenInvalid       = 1006 // 令牌无效
	ErrCaptchaRequired    = 1007 // 需要验证码
	ErrCaptchaInvalid     = 1008 // 验证码错误
	ErrTooManyAttempts    = 1009 // 登录尝试过多
	ErrNotAuthenticated   = 1010 // 未登录
	ErrSessionExpired     = 1011 // 会话已过期
	ErrExternalAuthFailed = 1012 // 外部认证失败

	// 用户管理错误 (2000-2999)
	ErrUserNotFound       = 2001 // 用户不存在
	ErrUsernameExists     = 2002 // 用户名已存在
	ErrEmailExists        = 2003 // 邮箱已存在
	ErrWeakPassword       = 2004 // 密码强度不足
	ErrPasswordReused     = 2005 // 密码不能重复使用
	ErrInvalidEmail       = 2006 // 邮箱格式无效
	ErrInvalidPhone       = 2007 // 手机号格式无效
	ErrInvalidUserStatus  = 2008 // 用户状态异常
	ErrWrongPassword      = 2009 // 原密码错误
	ErrPasswordMismatch   = 2010 // 确认密码不一致
	ErrIncompleteUserInfo = 2011 // 用户信息不完整
	ErrCannotDeleteSelf   = 2012 // 无法删除自己

	// 角色权限错误 (3000-3999)
	ErrRoleNotFound        = 3001 // 角色不存在
	ErrRoleNameExists      = 3002 // 角色名称已存在
	ErrPermissionNotFound  = 3003 // 权限不存在
	ErrSystemRoleProtected = 3004 // 系统角色受保护
	ErrRoleInUse           = 3005 // 角色正在使用中
	ErrRoleInheritanceLoop = 3006 // 角色继承循环
	ErrInsufficientLevel   = 3007 // 角色级别不足
	ErrPermissionDenied    = 3008 // 权限分配失败
	ErrRoleLevelExceeded   = 3009 // 角色层级超限

	// 会话管理错误 (4000-4999)
	ErrSessionNotFound    = 4001 // 会话不存在
	ErrSessionTerminated  = 4003 // 会话已终止
	ErrTooManySessions    = 4004 // 并发会话超限
	ErrDeviceUnauthorized = 4005 // 设备未授权
	ErrIPRestricted       = 4006 // IP地址受限
)

// 预定义错误实例
var (
	// 认证错误
	ErrInvalidCredentialsError = NewAuthError(ErrInvalidCredentials, "用户名或密码错误")
	ErrAccountLockedError      = NewAuthError(ErrAccountLocked, "账户已锁定")
	ErrAccountDisabledError    = NewAuthError(ErrAccountDisabled, "账户已禁用")
	ErrTokenExpiredError       = NewAuthError(ErrTokenExpired, "令牌已过期")
	ErrTokenInvalidError       = NewAuthError(ErrTokenInvalid, "令牌无效")
	ErrNotAuthenticatedError   = NewAuthError(ErrNotAuthenticated, "未登录")

	// 用户管理错误
	ErrUserNotFoundError     = NewAuthError(ErrUserNotFound, "用户不存在")
	ErrUsernameExistsError   = NewAuthError(ErrUsernameExists, "用户名已存在")
	ErrEmailExistsError      = NewAuthError(ErrEmailExists, "邮箱已存在")
	ErrWrongPasswordError    = NewAuthError(ErrWrongPassword, "原密码错误")
	ErrPasswordMismatchError = NewAuthError(ErrPasswordMismatch, "确认密码不一致")

	// 角色权限错误
	ErrRoleNotFoundError       = NewAuthError(ErrRoleNotFound, "角色不存在")
	ErrRoleExistsError         = NewAuthError(ErrRoleNameExists, "角色名称已存在")
	ErrPermissionNotFoundError = NewAuthError(ErrPermissionNotFound, "权限不存在")
	ErrPermissionDeniedError   = NewAuthError(ErrPermissionDenied, "权限不足")

	// 会话错误
	ErrSessionNotFoundError = NewAuthError(ErrSessionNotFound, "会话不存在")
	ErrSessionExpiredError  = NewAuthError(ErrSessionExpired, "会话已过期")

	// 用户状态错误
	ErrUserDisabledError = NewAuthError(ErrAccountDisabled, "用户已被禁用")
	ErrWeakPasswordError = NewAuthError(ErrWeakPassword, "密码强度不足")
	ErrInvalidTokenError = NewAuthError(ErrTokenInvalid, "令牌无效")
)

// IsAuthError 检查是否为认证错误
func IsAuthError(err error) bool {
	_, ok := err.(*AuthError)
	return ok
}

// GetErrorCode 获取错误码
func GetErrorCode(err error) int {
	if authErr, ok := err.(*AuthError); ok {
		return authErr.Code
	}
	return ErrSystemBusy
}

// 组织管理相关错误变量
var (
	// 组织相关错误
	ErrOrganizationCodeExists                = NewAuthError(3001, "Organization code already exists")
	ErrOrganizationNotFound                  = NewAuthError(3002, "Organization not found")
	ErrParentOrganizationNotFound            = NewAuthError(3003, "Parent organization not found")
	ErrOrganizationHasChildren               = NewAuthError(3004, "Organization has child organizations")
	ErrOrganizationHasUsers                  = NewAuthError(3005, "Organization has associated users")
	ErrInvalidOrganizationHierarchy          = NewAuthError(3006, "Invalid organization hierarchy")

	// 组织类型相关错误
	ErrOrganizationTypeCodeExists            = NewAuthError(3011, "Organization type code already exists")
	ErrOrganizationTypeNotFound              = NewAuthError(3012, "Organization type not found")
	ErrSystemOrganizationTypeNotModifiable   = NewAuthError(3013, "System organization type cannot be modified")
	ErrSystemOrganizationTypeNotDeletable    = NewAuthError(3014, "System organization type cannot be deleted")
	ErrOrganizationTypeInUse                 = NewAuthError(3015, "Organization type is in use")

	// 用户组织关联相关错误
	ErrUserOrganizationAssociationExists     = NewAuthError(3021, "User organization association already exists")
	ErrUserOrganizationAssociationNotFound   = NewAuthError(3022, "User organization association not found")
	ErrUserNotInOrganization                 = NewAuthError(3023, "User is not in the organization")
	ErrInsufficientOrganizationPermission    = NewAuthError(3024, "Insufficient organization permission")
	ErrCannotRemovePrimaryOrganization       = NewAuthError(3025, "Cannot remove primary organization")
)
