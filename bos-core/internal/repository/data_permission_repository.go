package repository

import (
	"context"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// DataPermissionRepository 数据权限仓库接口
type DataPermissionRepository interface {
	// DataPermission CRUD operations
	Create(ctx context.Context, permission *domain.DataPermission) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.DataPermission, error)
	GetByCode(ctx context.Context, permissionCode string) (*domain.DataPermission, error)
	Update(ctx context.Context, permission *domain.DataPermission) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// DataPermission listing and search
	List(ctx context.Context, filter *DataPermissionFilter) ([]*domain.DataPermission, error)
	Count(ctx context.Context, filter *DataPermissionFilter) (int64, error)
	Search(ctx context.Context, keyword string, limit int) ([]*domain.DataPermission, error)
	
	// DataPermission by resource type
	GetByResourceType(ctx context.Context, resourceType string) ([]*domain.DataPermission, error)
	GetByScopeType(ctx context.Context, scopeType string) ([]*domain.DataPermission, error)
	
	// Validation
	ExistsByCode(ctx context.Context, permissionCode string) (bool, error)
}

// UserDataPermissionRepository 用户数据权限关联仓库接口
type UserDataPermissionRepository interface {
	// UserDataPermission CRUD operations
	Create(ctx context.Context, userPermission *domain.UserDataPermission) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.UserDataPermission, error)
	Update(ctx context.Context, userPermission *domain.UserDataPermission) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// User permission operations
	GrantPermissionToUser(ctx context.Context, userID, permissionID uuid.UUID, scopeValue, scopeConditions *string, grantedBy *uuid.UUID, expiresAt *string) error
	RevokePermissionFromUser(ctx context.Context, userID, permissionID uuid.UUID) error
	
	// Query operations
	GetUserPermissions(ctx context.Context, userID uuid.UUID) ([]*domain.UserDataPermission, error)
	GetActiveUserPermissions(ctx context.Context, userID uuid.UUID) ([]*domain.UserDataPermission, error)
	GetUserPermissionsByResource(ctx context.Context, userID uuid.UUID, resourceType string) ([]*domain.UserDataPermission, error)
	GetUsersWithPermission(ctx context.Context, permissionID uuid.UUID) ([]*domain.UserDataPermission, error)
	
	// Permission checking
	HasPermission(ctx context.Context, userID, permissionID uuid.UUID) (bool, error)
	HasPermissionByCode(ctx context.Context, userID uuid.UUID, permissionCode string) (bool, error)
	
	// Batch operations
	BatchGrantPermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID, grantedBy *uuid.UUID) error
	BatchRevokePermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID) error
	
	// Expired permissions
	GetExpiredPermissions(ctx context.Context) ([]*domain.UserDataPermission, error)
	CleanupExpiredPermissions(ctx context.Context) (int64, error)
}

// OrganizationPermissionTemplateRepository 组织权限模板仓库接口
type OrganizationPermissionTemplateRepository interface {
	// Template CRUD operations
	Create(ctx context.Context, template *domain.OrganizationPermissionTemplate) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.OrganizationPermissionTemplate, error)
	Update(ctx context.Context, template *domain.OrganizationPermissionTemplate) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Template query operations
	GetByOrganization(ctx context.Context, organizationID int64) ([]*domain.OrganizationPermissionTemplate, error)
	GetByOrganizationAndRole(ctx context.Context, organizationID int64, roleType string) ([]*domain.OrganizationPermissionTemplate, error)
	GetDefaultTemplate(ctx context.Context, organizationID int64, roleType string) (*domain.OrganizationPermissionTemplate, error)
	
	// Template listing
	List(ctx context.Context, filter *OrganizationPermissionTemplateFilter) ([]*domain.OrganizationPermissionTemplate, error)
	Count(ctx context.Context, filter *OrganizationPermissionTemplateFilter) (int64, error)
	
	// Template operations
	SetAsDefault(ctx context.Context, templateID uuid.UUID) error
	ClearDefaultTemplates(ctx context.Context, organizationID int64, roleType string) error
}

// PermissionInheritanceRuleRepository 权限继承规则仓库接口
type PermissionInheritanceRuleRepository interface {
	// Rule CRUD operations
	Create(ctx context.Context, rule *domain.PermissionInheritanceRule) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.PermissionInheritanceRule, error)
	GetByCode(ctx context.Context, ruleCode string) (*domain.PermissionInheritanceRule, error)
	Update(ctx context.Context, rule *domain.PermissionInheritanceRule) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Rule query operations
	GetActiveRules(ctx context.Context) ([]*domain.PermissionInheritanceRule, error)
	GetRulesBySourceType(ctx context.Context, sourceType string) ([]*domain.PermissionInheritanceRule, error)
	GetRulesByTargetType(ctx context.Context, targetType string) ([]*domain.PermissionInheritanceRule, error)
	
	// Rule listing
	List(ctx context.Context, filter *PermissionInheritanceRuleFilter) ([]*domain.PermissionInheritanceRule, error)
	Count(ctx context.Context, filter *PermissionInheritanceRuleFilter) (int64, error)
	
	// Rule operations
	ActivateRule(ctx context.Context, ruleID uuid.UUID) error
	DeactivateRule(ctx context.Context, ruleID uuid.UUID) error
}

// PermissionEvaluationCacheRepository 权限评估缓存仓库接口
type PermissionEvaluationCacheRepository interface {
	// Cache CRUD operations
	Set(ctx context.Context, cache *domain.PermissionEvaluationCache) error
	Get(ctx context.Context, cacheKey string) (*domain.PermissionEvaluationCache, error)
	Delete(ctx context.Context, cacheKey string) error
	
	// Cache operations
	GetUserPermissionCache(ctx context.Context, userID uuid.UUID, resourceType, resourceID, permissionCode string) (*domain.PermissionEvaluationCache, error)
	InvalidateUserCache(ctx context.Context, userID uuid.UUID) error
	InvalidateResourceCache(ctx context.Context, resourceType, resourceID string) error
	
	// Cache maintenance
	CleanupExpiredCache(ctx context.Context) (int64, error)
	GetCacheStatistics(ctx context.Context) (*PermissionCacheStatistics, error)
}

// DataPermissionFilter 数据权限查询过滤器
type DataPermissionFilter struct {
	// Basic filters
	ResourceType []string `json:"resource_type"`
	ScopeType    []string `json:"scope_type"`
	Status       []string `json:"status"`
	IsSystem     *bool    `json:"is_system"`
	
	// Search filters
	Keyword *string `json:"keyword"`
	
	// Pagination
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
	
	// Sorting
	SortBy    *string `json:"sort_by"`
	SortOrder *string `json:"sort_order"`
}

// UserDataPermissionFilter 用户数据权限关联查询过滤器
type UserDataPermissionFilter struct {
	// Basic filters
	UserID           *uuid.UUID `json:"user_id"`
	DataPermissionID *uuid.UUID `json:"data_permission_id"`
	Status           []string   `json:"status"`
	
	// Resource filters
	ResourceType []string `json:"resource_type"`
	ScopeType    []string `json:"scope_type"`
	
	// Time filters
	GrantedAfter  *string `json:"granted_after"`
	GrantedBefore *string `json:"granted_before"`
	ExpiresAfter  *string `json:"expires_after"`
	ExpiresBefore *string `json:"expires_before"`
	
	// Pagination
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
	
	// Sorting
	SortBy    *string `json:"sort_by"`
	SortOrder *string `json:"sort_order"`
	
	// Include related data
	IncludeUser           bool `json:"include_user"`
	IncludeDataPermission bool `json:"include_data_permission"`
	IncludeGrantedBy      bool `json:"include_granted_by"`
}

// OrganizationPermissionTemplateFilter 组织权限模板查询过滤器
type OrganizationPermissionTemplateFilter struct {
	// Basic filters
	OrganizationID *int64   `json:"organization_id"`
	RoleType       []string `json:"role_type"`
	Status         []string `json:"status"`
	IsDefault      *bool    `json:"is_default"`
	IsSystem       *bool    `json:"is_system"`
	
	// Search filters
	Keyword *string `json:"keyword"`
	
	// Pagination
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
	
	// Sorting
	SortBy    *string `json:"sort_by"`
	SortOrder *string `json:"sort_order"`
	
	// Include related data
	IncludeOrganization bool `json:"include_organization"`
}

// PermissionInheritanceRuleFilter 权限继承规则查询过滤器
type PermissionInheritanceRuleFilter struct {
	// Basic filters
	SourceType      []string `json:"source_type"`
	TargetType      []string `json:"target_type"`
	InheritanceType []string `json:"inheritance_type"`
	IsActive        *bool    `json:"is_active"`
	
	// Search filters
	Keyword *string `json:"keyword"`
	
	// Pagination
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
	
	// Sorting
	SortBy    *string `json:"sort_by"`
	SortOrder *string `json:"sort_order"`
}

// PermissionCacheStatistics 权限缓存统计信息
type PermissionCacheStatistics struct {
	TotalCacheEntries   int64   `json:"total_cache_entries"`
	ExpiredCacheEntries int64   `json:"expired_cache_entries"`
	HitRate             float64 `json:"hit_rate"`
	MissRate            float64 `json:"miss_rate"`
	AverageEvaluationTime float64 `json:"average_evaluation_time"`
	
	// By resource type
	CacheByResourceType map[string]int64 `json:"cache_by_resource_type"`
	
	// By user
	TopUsersByCache []UserCacheInfo `json:"top_users_by_cache"`
}

// UserCacheInfo 用户缓存信息
type UserCacheInfo struct {
	UserID     uuid.UUID `json:"user_id"`
	Username   string    `json:"username"`
	CacheCount int64     `json:"cache_count"`
}
