package repository

import (
	"context"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// OrganizationRepository 组织仓库接口
type OrganizationRepository interface {
	// Organization CRUD operations
	Create(ctx context.Context, org *domain.Organization) error
	GetByID(ctx context.Context, id int64) (*domain.Organization, error)
	GetByCode(ctx context.Context, orgCode string) (*domain.Organization, error)
	Update(ctx context.Context, org *domain.Organization) error
	Delete(ctx context.Context, id int64) error
	
	// Organization hierarchy operations
	GetChildren(ctx context.Context, parentID int64) ([]*domain.Organization, error)
	GetDescendants(ctx context.Context, parentID int64) ([]*domain.Organization, error)
	GetAncestors(ctx context.Context, orgID int64) ([]*domain.Organization, error)
	GetRootOrganizations(ctx context.Context) ([]*domain.Organization, error)
	GetByType(ctx context.Context, orgType string) ([]*domain.Organization, error)
	
	// Organization search and listing
	List(ctx context.Context, filter *OrganizationFilter) ([]*domain.Organization, error)
	Count(ctx context.Context, filter *OrganizationFilter) (int64, error)
	Search(ctx context.Context, keyword string, limit int) ([]*domain.Organization, error)
	
	// Organization validation
	ExistsByCode(ctx context.Context, orgCode string) (bool, error)
	ExistsByID(ctx context.Context, id int64) (bool, error)
	ValidateHierarchy(ctx context.Context, orgID, parentID int64) error
}

// OrganizationTypeRepository 组织类型仓库接口
type OrganizationTypeRepository interface {
	// OrganizationType CRUD operations
	Create(ctx context.Context, orgType *domain.OrganizationTypeEntity) error
	GetByID(ctx context.Context, id int64) (*domain.OrganizationTypeEntity, error)
	GetByCode(ctx context.Context, typeCode string) (*domain.OrganizationTypeEntity, error)
	Update(ctx context.Context, orgType *domain.OrganizationTypeEntity) error
	Delete(ctx context.Context, id int64) error
	
	// OrganizationType listing
	List(ctx context.Context) ([]*domain.OrganizationTypeEntity, error)
	GetByLevelOrder(ctx context.Context, levelOrder int) (*domain.OrganizationTypeEntity, error)
	
	// OrganizationType validation
	ExistsByCode(ctx context.Context, typeCode string) (bool, error)
}

// UserOrganizationRepository 用户组织关联仓库接口
type UserOrganizationRepository interface {
	// UserOrganization CRUD operations
	Create(ctx context.Context, userOrg *domain.UserOrganization) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.UserOrganization, error)
	Update(ctx context.Context, userOrg *domain.UserOrganization) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// User-Organization association operations
	AssignUserToOrganization(ctx context.Context, userID uuid.UUID, orgID int64, role string, isPrimary bool, grantedBy *uuid.UUID) error
	RemoveUserFromOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error
	SetPrimaryOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error
	
	// Query operations
	GetUserOrganizations(ctx context.Context, userID uuid.UUID) ([]*domain.UserOrganization, error)
	GetOrganizationUsers(ctx context.Context, orgID int64) ([]*domain.UserOrganization, error)
	GetUserPrimaryOrganization(ctx context.Context, userID uuid.UUID) (*domain.UserOrganization, error)
	GetUsersByOrganization(ctx context.Context, orgID int64, includeDescendants bool) ([]*domain.User, error)
	
	// Validation operations
	ExistsByUserAndOrganization(ctx context.Context, userID uuid.UUID, orgID int64) (bool, error)
	HasPrimaryOrganization(ctx context.Context, userID uuid.UUID) (bool, error)
	
	// Batch operations
	BatchAssignUsers(ctx context.Context, userIDs []uuid.UUID, orgID int64, role string, grantedBy *uuid.UUID) error
	BatchRemoveUsers(ctx context.Context, userIDs []uuid.UUID, orgID int64) error
	
	// Advanced queries
	GetUserOrganizationsByRole(ctx context.Context, userID uuid.UUID, role string) ([]*domain.UserOrganization, error)
	GetActiveUserOrganizations(ctx context.Context, userID uuid.UUID) ([]*domain.UserOrganization, error)
	GetExpiredUserOrganizations(ctx context.Context) ([]*domain.UserOrganization, error)
}

// OrganizationFilter 组织查询过滤器
type OrganizationFilter struct {
	// Basic filters
	Status    []string `json:"status"`
	OrgTypeID *int64   `json:"org_type_id"`
	ParentID  *int64   `json:"parent_id"`
	
	// Hierarchy filters
	LevelDepth    *int    `json:"level_depth"`
	LevelPathLike *string `json:"level_path_like"`
	
	// Search filters
	Keyword *string `json:"keyword"`
	
	// Pagination
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
	
	// Sorting
	SortBy    *string `json:"sort_by"`
	SortOrder *string `json:"sort_order"` // asc, desc
	
	// Include related data
	IncludeType     bool `json:"include_type"`
	IncludeParent   bool `json:"include_parent"`
	IncludeChildren bool `json:"include_children"`
	IncludeUsers    bool `json:"include_users"`
}

// UserOrganizationFilter 用户组织关联查询过滤器
type UserOrganizationFilter struct {
	// Basic filters
	UserID         *uuid.UUID `json:"user_id"`
	OrganizationID *int64     `json:"organization_id"`
	Status         []string   `json:"status"`
	RoleInOrg      []string   `json:"role_in_org"`
	IsPrimary      *bool      `json:"is_primary"`
	
	// Time filters
	GrantedAfter  *string `json:"granted_after"`
	GrantedBefore *string `json:"granted_before"`
	ExpiresAfter  *string `json:"expires_after"`
	ExpiresBefore *string `json:"expires_before"`
	
	// Pagination
	Offset *int `json:"offset"`
	Limit  *int `json:"limit"`
	
	// Sorting
	SortBy    *string `json:"sort_by"`
	SortOrder *string `json:"sort_order"`
	
	// Include related data
	IncludeUser         bool `json:"include_user"`
	IncludeOrganization bool `json:"include_organization"`
	IncludeGrantedBy    bool `json:"include_granted_by"`
}

// OrganizationHierarchyNode 组织层级节点
type OrganizationHierarchyNode struct {
	Organization *domain.Organization         `json:"organization"`
	Children     []*OrganizationHierarchyNode `json:"children"`
	Level        int                          `json:"level"`
	HasChildren  bool                         `json:"has_children"`
}

// OrganizationStatistics 组织统计信息
type OrganizationStatistics struct {
	TotalOrganizations int64 `json:"total_organizations"`
	ActiveOrganizations int64 `json:"active_organizations"`
	TotalUsers         int64 `json:"total_users"`
	ActiveUsers        int64 `json:"active_users"`
	TotalStations      int64 `json:"total_stations"`
	ActiveStations     int64 `json:"active_stations"`
	
	// By organization type
	HeadquartersCount int64 `json:"headquarters_count"`
	RegionsCount      int64 `json:"regions_count"`
	StationsCount     int64 `json:"stations_count"`
	
	// Hierarchy depth
	MaxDepth     int `json:"max_depth"`
	AvgDepth     float64 `json:"avg_depth"`
}

// UserOrganizationStatistics 用户组织关联统计信息
type UserOrganizationStatistics struct {
	TotalAssociations   int64 `json:"total_associations"`
	ActiveAssociations  int64 `json:"active_associations"`
	ExpiredAssociations int64 `json:"expired_associations"`
	
	// By role
	AdminCount   int64 `json:"admin_count"`
	ManagerCount int64 `json:"manager_count"`
	MemberCount  int64 `json:"member_count"`
	
	// Primary organizations
	UsersWithPrimary    int64 `json:"users_with_primary"`
	UsersWithoutPrimary int64 `json:"users_without_primary"`
}
