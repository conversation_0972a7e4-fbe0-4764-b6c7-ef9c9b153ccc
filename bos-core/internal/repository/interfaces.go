package repository

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// UserRepository 用户仓库接口
type UserRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, user *domain.User) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error)
	GetByUsername(ctx context.Context, username string) (*domain.User, error)
	GetByEmail(ctx context.Context, email string) (*domain.User, error)
	Update(ctx context.Context, user *domain.User) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.User, int64, error)
	ExistsByUsername(ctx context.Context, username string) (bool, error)
	ExistsByEmail(ctx context.Context, email string) (bool, error)

	// 用户状态管理
	UpdateStatus(ctx context.Context, id uuid.UUID, status domain.UserStatus) error
	UpdateLastLogin(ctx context.Context, id uuid.UUID, loginTime time.Time, ip string) error
	IncrementFailedAttempts(ctx context.Context, id uuid.UUID) error
	ResetFailedAttempts(ctx context.Context, id uuid.UUID) error
	LockUser(ctx context.Context, id uuid.UUID, lockUntil time.Time) error

	// 密码管理
	UpdatePassword(ctx context.Context, id uuid.UUID, passwordHash, salt string) error
	GetPasswordHistory(ctx context.Context, id uuid.UUID, limit int) ([]string, error)
	AddPasswordHistory(ctx context.Context, id uuid.UUID, passwordHash string) error

	// 角色关联
	AssignRoles(ctx context.Context, userID uuid.UUID, roleIDs []uuid.UUID) error
	RemoveRoles(ctx context.Context, userID uuid.UUID, roleIDs []uuid.UUID) error
	GetUserRoles(ctx context.Context, userID uuid.UUID) ([]*domain.Role, error)
	GetUserPermissions(ctx context.Context, userID uuid.UUID) ([]*domain.Permission, error)

	// 站点关联查询
	GetUserWithStationRoles(ctx context.Context, userID uuid.UUID) (*domain.User, error)
	GetUsersWithStationRoles(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.User, int64, error)
}

// RoleRepository 角色仓库接口
type RoleRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, role *domain.Role) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Role, error)
	GetByName(ctx context.Context, name string) (*domain.Role, error)
	Update(ctx context.Context, role *domain.Role) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.Role, int64, error)
	ExistsByName(ctx context.Context, name string) (bool, error)
	GetChildren(ctx context.Context, parentID uuid.UUID) ([]*domain.Role, error)
	GetAncestors(ctx context.Context, roleID uuid.UUID) ([]*domain.Role, error)

	// 权限关联
	AssignPermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	RemovePermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]*domain.Permission, error)
	GetAllPermissions(ctx context.Context, roleID uuid.UUID) ([]*domain.Permission, error) // 包含继承的权限

	// 用户关联
	GetRoleUsers(ctx context.Context, roleID uuid.UUID) ([]*domain.User, error)
	GetUserCount(ctx context.Context, roleID uuid.UUID) (int64, error)
}

// PermissionRepository 权限仓库接口
type PermissionRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, permission *domain.Permission) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Permission, error)
	GetByCode(ctx context.Context, code string) (*domain.Permission, error)
	Update(ctx context.Context, permission *domain.Permission) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.Permission, int64, error)
	ExistsByCode(ctx context.Context, code string) (bool, error)
	GetChildren(ctx context.Context, parentID uuid.UUID) ([]*domain.Permission, error)
	GetByType(ctx context.Context, permissionType domain.PermissionType) ([]*domain.Permission, error)
	GetByResource(ctx context.Context, resource string) ([]*domain.Permission, error)

	// 层级操作
	GetTree(ctx context.Context) ([]*domain.Permission, error)
	GetPath(ctx context.Context, permissionID uuid.UUID) ([]*domain.Permission, error)

	// 角色关联
	GetPermissionRoles(ctx context.Context, permissionID uuid.UUID) ([]*domain.Role, error)
}

// SessionRepository 会话仓库接口
type SessionRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, session *domain.Session) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error)
	GetBySessionID(ctx context.Context, sessionID string) (*domain.Session, error)
	GetByToken(ctx context.Context, token string) (*domain.Session, error)
	GetByRefreshToken(ctx context.Context, refreshToken string) (*domain.Session, error)
	Update(ctx context.Context, session *domain.Session) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 用户会话管理
	GetUserSessions(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error)
	GetActiveSessions(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error)
	CountActiveSessions(ctx context.Context, userID uuid.UUID) (int64, error)

	// 会话状态管理
	UpdateLastActive(ctx context.Context, sessionID uuid.UUID) error
	TerminateSession(ctx context.Context, sessionID uuid.UUID) error
	TerminateUserSessions(ctx context.Context, userID uuid.UUID, excludeSessionID *uuid.UUID) error
	TerminateExpiredSessions(ctx context.Context) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.Session, int64, error)
	GetByIPAddress(ctx context.Context, ipAddress string) ([]*domain.Session, error)
	GetByUserAgent(ctx context.Context, userAgent string) ([]*domain.Session, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error)
}

// LoginLogRepository 登录日志仓库接口
type LoginLogRepository interface {
	// 基础操作
	Create(ctx context.Context, log *domain.LoginLog) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.LoginLog, error)
	Update(ctx context.Context, log *domain.LoginLog) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.LoginLog, int64, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*domain.LoginLog, int64, error)
	GetByUsername(ctx context.Context, username string, offset, limit int) ([]*domain.LoginLog, int64, error)
	GetByIPAddress(ctx context.Context, ipAddress string, offset, limit int) ([]*domain.LoginLog, int64, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*domain.LoginLog, int64, error)

	// 统计操作
	GetFailedAttemptsCount(ctx context.Context, username string, since time.Time) (int64, error)
	GetLoginStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
	GetUserLoginHistory(ctx context.Context, userID uuid.UUID, limit int) ([]*domain.LoginLog, error)
}

// PermissionLogRepository 权限日志仓库接口
type PermissionLogRepository interface {
	// 基础操作
	Create(ctx context.Context, log *domain.PermissionLog) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.PermissionLog, error)

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.PermissionLog, int64, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*domain.PermissionLog, int64, error)
	GetByPermissionCode(ctx context.Context, permissionCode string, offset, limit int) ([]*domain.PermissionLog, int64, error)
	GetByResource(ctx context.Context, resource string, offset, limit int) ([]*domain.PermissionLog, int64, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*domain.PermissionLog, int64, error)

	// 统计操作
	GetPermissionStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
	GetDeniedPermissions(ctx context.Context, userID uuid.UUID, since time.Time) ([]*domain.PermissionLog, error)
}

// OperationLogRepository 操作日志仓库接口
type OperationLogRepository interface {
	// 基础操作
	Create(ctx context.Context, log *domain.OperationLog) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.OperationLog, error)

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.OperationLog, int64, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, offset, limit int) ([]*domain.OperationLog, int64, error)
	GetByOperationType(ctx context.Context, operationType string, offset, limit int) ([]*domain.OperationLog, int64, error)
	GetByResource(ctx context.Context, resource string, offset, limit int) ([]*domain.OperationLog, int64, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*domain.OperationLog, int64, error)

	// 统计操作
	GetOperationStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
	GetUserOperationHistory(ctx context.Context, userID uuid.UUID, limit int) ([]*domain.OperationLog, error)
}

// StationRepository 站点仓库接口
type StationRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, station *domain.Station) error
	GetByID(ctx context.Context, id int64) (*domain.Station, error)
	GetBySiteCode(ctx context.Context, siteCode string) (*domain.Station, error)
	Update(ctx context.Context, station *domain.Station) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.Station, int64, error)
	ExistsBySiteCode(ctx context.Context, siteCode string) (bool, error)
	ExistsBySiteName(ctx context.Context, siteName string) (bool, error)

	// 状态管理
	UpdateStatus(ctx context.Context, id int64, status domain.BusinessStatus, version int) error

	// 站点关联查询
	GetStationWithUsers(ctx context.Context, stationID int64) (*domain.Station, error)
	GetStationsWithUserCount(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.Station, int64, error)
}

// UserStationRoleRepository 用户站点角色关联仓库接口
type UserStationRoleRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, role *domain.UserStationRole) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.UserStationRole, error)
	Update(ctx context.Context, role *domain.UserStationRole) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.UserStationRole, int64, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserStationRole, error)
	GetByStationID(ctx context.Context, stationID int64) ([]*domain.UserStationRole, error)
	GetByUserAndStation(ctx context.Context, userID uuid.UUID, stationID int64) (*domain.UserStationRole, error)
	ExistsByUserAndStation(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)

	// 用户站点管理
	AssignUserToStation(ctx context.Context, userID uuid.UUID, stationID int64, roleType domain.UserStationRoleType, grantedBy *uuid.UUID, expiresAt *time.Time) error
	RemoveUserFromStation(ctx context.Context, userID uuid.UUID, stationID int64) error
	UpdateRoleStatus(ctx context.Context, id uuid.UUID, status domain.UserStationRoleStatus) error
	UpdateRoleType(ctx context.Context, id uuid.UUID, roleType domain.UserStationRoleType) error

	// 权限检查
	HasStationAccess(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)
	CanManageStation(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)
	CanOperateStation(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)

	// 查询关联数据
	GetUserStations(ctx context.Context, userID uuid.UUID, roleTypes []domain.UserStationRoleType) ([]*domain.Station, error)
	GetStationManagers(ctx context.Context, stationID int64) ([]*domain.User, error)
	GetStationUsers(ctx context.Context, stationID int64, roleTypes []domain.UserStationRoleType) ([]*domain.User, error)
	GetStationUsersDetail(ctx context.Context, stationID int64, roleTypes []domain.UserStationRoleType) ([]*domain.UserStationRole, error)

	// 批量操作
	BatchAssignStations(ctx context.Context, userID uuid.UUID, stationIDs []int64, roleType domain.UserStationRoleType, grantedBy *uuid.UUID) error
	BatchRemoveStations(ctx context.Context, userID uuid.UUID, stationIDs []int64) error
	BatchUpdateStatus(ctx context.Context, ids []uuid.UUID, status domain.UserStationRoleStatus) error

	// 转移管理权
	TransferStationManagement(ctx context.Context, fromUserID, toUserID uuid.UUID, stationID int64, grantedBy *uuid.UUID) error

	// 过期管理
	GetExpiredRoles(ctx context.Context, beforeTime time.Time) ([]*domain.UserStationRole, error)
	CleanupExpiredRoles(ctx context.Context, beforeTime time.Time) (int64, error)
	UpdateExpiration(ctx context.Context, id uuid.UUID, expiresAt *time.Time) error

	// 统计查询
	GetUserStationCount(ctx context.Context, userID uuid.UUID) (int64, error)
	GetStationUserCount(ctx context.Context, stationID int64) (int64, error)
	GetRoleTypeStats(ctx context.Context, stationID *int64) (map[string]int64, error)
}

// StationEquipmentRepository 站点设备仓库接口
type StationEquipmentRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, equipment *domain.StationEquipment) error
	GetByID(ctx context.Context, id int64) (*domain.StationEquipment, error)
	GetByCode(ctx context.Context, stid int64, equipmentCode string) (*domain.StationEquipment, error)
	Update(ctx context.Context, equipment *domain.StationEquipment) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, stid *int64, filters map[string]interface{}, offset, limit int, orderBy, orderDir string) ([]*domain.StationEquipment, int64, error)
	GetByStationID(ctx context.Context, stid int64, offset, limit int, filters map[string]interface{}) ([]*domain.StationEquipment, int64, error)
	ExistsByCode(ctx context.Context, stid int64, equipmentCode string) (bool, error)
	ExistsByCodeInStation(ctx context.Context, stid int64, equipmentCode string, excludeID *int64) (bool, error)
	ExistsBySerialNumber(ctx context.Context, serialNumber string) (bool, error)
	ExistsByIPAddress(ctx context.Context, stid int64, ipAddress string) (bool, error)
	ExistsByIPInStation(ctx context.Context, stid int64, ipAddress string, excludeID *int64) (bool, error)

	// 状态管理
	UpdateStatus(ctx context.Context, id int64, status domain.EquipmentStatus, version int) error

	// 关联管理
	GetLinkedEquipments(ctx context.Context, equipmentID int64) ([]*domain.StationEquipment, error)
	HasLinkedEquipments(ctx context.Context, equipmentID int64) (bool, error)
}

// MaintenanceRecordRepository 维护记录仓库接口
type MaintenanceRecordRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, record *domain.EquipmentMaintenanceRecord) error
	GetByID(ctx context.Context, id int64) (*domain.EquipmentMaintenanceRecord, error)
	Update(ctx context.Context, record *domain.EquipmentMaintenanceRecord) error

	// 查询操作
	List(ctx context.Context, equipmentID *int64, filters map[string]interface{}, startTime, endTime *time.Time, offset, limit int, orderBy, orderDir string) ([]*domain.EquipmentMaintenanceRecord, int64, error)
	GetByEquipmentID(ctx context.Context, equipmentID int64, offset, limit int, filters map[string]interface{}) ([]*domain.EquipmentMaintenanceRecord, int64, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*domain.EquipmentMaintenanceRecord, int64, error)
}

// OperationParamRepository 运营参数仓库接口
type OperationParamRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, param *domain.OperationParam) error
	GetByID(ctx context.Context, id int64) (*domain.OperationParam, error)
	GetByKey(ctx context.Context, stid int64, category, key string) (*domain.OperationParam, error)
	Update(ctx context.Context, param *domain.OperationParam) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.OperationParam, int64, error)
	GetByStationID(ctx context.Context, stid int64, offset, limit int, filters map[string]interface{}) ([]*domain.OperationParam, int64, error)
	ExistsByKey(ctx context.Context, stid int64, category, key string) (bool, error)

	// 历史记录
	AddChangeHistory(ctx context.Context, history *domain.ParamChangeHistory) error
	CreateChangeHistory(ctx context.Context, history *domain.ParamChangeHistory) error
	GetChangeHistory(ctx context.Context, paramID int64, offset, limit int, filters map[string]interface{}) ([]*domain.ParamChangeHistory, int64, error)
}

// ConfigItemRepository 配置项仓库接口
type ConfigItemRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, item *domain.ConfigItem) error
	GetByID(ctx context.Context, id int64) (*domain.ConfigItem, error)
	GetByKey(ctx context.Context, configKey string) (*domain.ConfigItem, error)
	Update(ctx context.Context, item *domain.ConfigItem) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.ConfigItem, int64, error)
	ExistsByKey(ctx context.Context, configKey string) (bool, error)
	GetByCategory(ctx context.Context, category string, offset, limit int) ([]*domain.ConfigItem, int64, error)
	GetByModule(ctx context.Context, category, module string, offset, limit int) ([]*domain.ConfigItem, int64, error)
	GetEnabledConfigs(ctx context.Context, offset, limit int) ([]*domain.ConfigItem, int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, items []*domain.ConfigItem) error
	BatchUpdate(ctx context.Context, items []*domain.ConfigItem) error
	BatchGetByKeys(ctx context.Context, keys []string) ([]*domain.ConfigItem, error)

	// 分类和模块统计
	GetCategories(ctx context.Context) ([]map[string]interface{}, error)
	GetCategoryModules(ctx context.Context, category string) ([]map[string]interface{}, error)

	// 搜索功能
	Search(ctx context.Context, keyword string, offset, limit int) ([]*domain.ConfigItem, int64, error)
}

// ConfigLogRepository 配置变更日志仓库接口
type ConfigLogRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, log *domain.ConfigLog) error
	GetByID(ctx context.Context, id int64) (*domain.ConfigLog, error)
	Update(ctx context.Context, log *domain.ConfigLog) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.ConfigLog, int64, error)
	GetByConfigItemID(ctx context.Context, configItemID int64, offset, limit int) ([]*domain.ConfigLog, int64, error)
	GetByAction(ctx context.Context, action domain.ActionType, offset, limit int) ([]*domain.ConfigLog, int64, error)
	GetByStatus(ctx context.Context, status domain.StatusType, offset, limit int) ([]*domain.ConfigLog, int64, error)
	GetByCreatedBy(ctx context.Context, createdBy int64, offset, limit int) ([]*domain.ConfigLog, int64, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, offset, limit int) ([]*domain.ConfigLog, int64, error)

	// 审批相关
	GetPendingLogs(ctx context.Context, offset, limit int) ([]*domain.ConfigLog, int64, error)
	ApproveLog(ctx context.Context, id int64, approverID int64) error
	RejectLog(ctx context.Context, id int64, approverID int64) error

	// 批量操作
	BatchCreate(ctx context.Context, logs []*domain.ConfigLog) error

	// 统计操作
	GetLogStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
}

// OperationLogsRepository 操作日志仓储接口
type OperationLogsRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, log *domain.NewOperationLog) error
	CreateBatch(ctx context.Context, logs []*domain.NewOperationLog) (*domain.BatchLogResponse, error)
	GetByID(ctx context.Context, id int64) (*domain.NewOperationLog, error)
	GetByLogID(ctx context.Context, logID string) (*domain.NewOperationLog, error)
	Update(ctx context.Context, log *domain.NewOperationLog) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, req *domain.LogQueryRequest) ([]*domain.NewOperationLog, int64, error)
	GetByUserID(ctx context.Context, userID string, req *domain.LogQueryRequest) ([]*domain.NewOperationLog, int64, error)
	GetByOperationType(ctx context.Context, operationType string, req *domain.LogQueryRequest) ([]*domain.NewOperationLog, int64, error)
	GetByResourceType(ctx context.Context, resourceType string, req *domain.LogQueryRequest) ([]*domain.NewOperationLog, int64, error)
	GetBySystemModule(ctx context.Context, systemModule string, req *domain.LogQueryRequest) ([]*domain.NewOperationLog, int64, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, req *domain.LogQueryRequest) ([]*domain.NewOperationLog, int64, error)

	// 统计操作
	GetStatistics(ctx context.Context, req *domain.StatisticsRequest) (*domain.StatisticsResponse, error)
	GetSummaryStats(ctx context.Context, startTime, endTime time.Time, filters map[string]interface{}) (*domain.SummaryStats, error)
	GetTimeSeriesStats(ctx context.Context, startTime, endTime time.Time, groupBy string, filters map[string]interface{}) ([]*domain.TimeSeriesStats, error)
	GetModuleBreakdown(ctx context.Context, startTime, endTime time.Time, filters map[string]interface{}) ([]*domain.ModuleBreakdown, error)

	// 数据清理
	CleanupExpiredLogs(ctx context.Context, retentionMap map[string]int) (int64, error)
	DeleteByTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error)

	// 导出相关
	GetLogsForExport(ctx context.Context, req *domain.ExportRequest) ([]*domain.NewOperationLog, error)
	CountLogsForExport(ctx context.Context, req *domain.ExportRequest) (int64, error)

	// 便捷查询方法
	GetLoginLogs(ctx context.Context, userID string, startTime, endTime time.Time) ([]*domain.NewOperationLog, error)
	GetConfigChangeLogs(ctx context.Context, configKey string, startTime, endTime time.Time) ([]*domain.NewOperationLog, error)
	GetSecurityEvents(ctx context.Context, userID string, startTime, endTime time.Time) ([]*domain.NewOperationLog, error)
}

// DictCategoryRepository 数据字典分类仓库接口
type DictCategoryRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, category *domain.DictCategory) error
	GetByID(ctx context.Context, id int64) (*domain.DictCategory, error)
	GetByCode(ctx context.Context, code string) (*domain.DictCategory, error)
	Update(ctx context.Context, category *domain.DictCategory) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.DictCategory, int64, error)
	ExistsByCode(ctx context.Context, code string) (bool, error)
	ExistsByName(ctx context.Context, name string) (bool, error)
	Search(ctx context.Context, keyword string, offset, limit int) ([]*domain.DictCategory, int64, error)

	// 统计操作
	GetCategoryStats(ctx context.Context) ([]*domain.DictCategory, error)
	UpdateItemCount(ctx context.Context, categoryID int64) error
}

// DictItemRepository 数据字典项目仓库接口
type DictItemRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, item *domain.DictItem) error
	GetByID(ctx context.Context, id int64) (*domain.DictItem, error)
	GetByCode(ctx context.Context, categoryID int64, code string) (*domain.DictItem, error)
	Update(ctx context.Context, item *domain.DictItem) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.DictItem, int64, error)
	GetByCategoryID(ctx context.Context, categoryID int64, offset, limit int, filters map[string]interface{}) ([]*domain.DictItem, int64, error)
	ExistsByCode(ctx context.Context, categoryID int64, code string) (bool, error)
	Search(ctx context.Context, keyword string, offset, limit int) ([]*domain.DictItem, int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, items []*domain.DictItem) error
	BatchUpdate(ctx context.Context, items []*domain.DictItem) error
	BatchDelete(ctx context.Context, ids []int64) error
}

// DictValueRepository 数据字典值仓库接口
type DictValueRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, value *domain.DictValue) error
	GetByID(ctx context.Context, id int64) (*domain.DictValue, error)
	GetByValue(ctx context.Context, itemID int64, value string) (*domain.DictValue, error)
	Update(ctx context.Context, value *domain.DictValue) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.DictValue, int64, error)
	GetByItemID(ctx context.Context, itemID int64, offset, limit int, filters map[string]interface{}) ([]*domain.DictValue, int64, error)
	ExistsByValue(ctx context.Context, itemID int64, value string) (bool, error)
	Search(ctx context.Context, keyword string, offset, limit int) ([]*domain.DictValue, int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, values []*domain.DictValue) error
	BatchUpdate(ctx context.Context, values []*domain.DictValue) error
	BatchDelete(ctx context.Context, ids []int64) error

	// 同步相关
	GetBySourceSystem(ctx context.Context, sourceSystem string, offset, limit int) ([]*domain.DictValue, int64, error)
	UpdateSyncInfo(ctx context.Context, id int64, syncID string, syncTime time.Time) error
}

// DictQueryRepository 数据字典查询仓库接口
type DictQueryRepository interface {
	// 通用查询
	QueryDict(ctx context.Context, req *domain.DictQueryRequest) ([]*domain.DictTreeValue, error)
	BatchQueryDict(ctx context.Context, req *domain.DictBatchQueryRequest) (map[string][]*domain.DictTreeValue, error)
	QueryDictTree(ctx context.Context, categoryCode string, maxDepth int) ([]*domain.DictTreeNode, error)

	// 验证查询
	ValidateValues(ctx context.Context, validations []domain.DictValidationItem) ([]domain.DictValidationResult, error)

	// 完整数据查询
	GetFullDictData(ctx context.Context, categoryCode string, includeInactive bool) ([]*domain.DictTreeNode, error)
	GetCategoryWithItems(ctx context.Context, categoryCode string) (*domain.DictCategory, error)
	GetItemWithValues(ctx context.Context, categoryCode, itemCode string) (*domain.DictItem, error)
}

// DictSyncRepository 数据字典同步仓库接口
type DictSyncRepository interface {
	// 同步状态管理
	CreateSyncStatus(ctx context.Context, status *domain.DictSyncStatus) error
	GetSyncStatus(ctx context.Context, syncID string) (*domain.DictSyncStatus, error)
	UpdateSyncStatus(ctx context.Context, syncID string, status string, endTime *time.Time, stats map[string]int, errors []string) error

	// HOS数据同步
	SyncHOSCategories(ctx context.Context, categoryCode []string, syncMode string, forceUpdate bool) error
	SyncHOSItems(ctx context.Context, categoryID int64, syncMode string) error
	SyncHOSValues(ctx context.Context, itemID int64, syncMode string) error

	// 同步历史
	GetSyncHistory(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.DictSyncStatus, int64, error)
	CleanupOldSyncRecords(ctx context.Context, retentionDays int) error
}

// DictCacheRepository 数据字典缓存仓库接口
type DictCacheRepository interface {
	// 缓存操作
	RefreshCache(ctx context.Context, scope, target string) (*domain.DictCacheRefreshResponse, error)
	GetCacheStats(ctx context.Context) (*domain.DictCacheStats, error)
	ClearCache(ctx context.Context, pattern string) error

	// 缓存预热
	WarmupCache(ctx context.Context, categoryCode string) error
	WarmupAllCache(ctx context.Context) error
}

// DictStatsRepository 数据字典统计仓库接口
type DictStatsRepository interface {
	// 使用统计
	RecordQuery(ctx context.Context, categoryCode, itemCode string) error
	GetUsageStats(ctx context.Context, startDate, endDate string, categoryCode string) (*domain.DictUsageStats, error)
	GetTrendData(ctx context.Context, period string, days int, categoryCode string) (*domain.DictTrendData, error)

	// 数据统计
	GetDataStats(ctx context.Context) (map[string]interface{}, error)
	GetCategoryItemCount(ctx context.Context, categoryID int64) (int64, error)
	GetItemValueCount(ctx context.Context, itemID int64) (int64, error)
}

// DictImportExportRepository 数据字典导入导出仓库接口
type DictImportExportRepository interface {
	// 导出操作
	ExportDict(ctx context.Context, req *domain.DictExportRequest) (*domain.DictExportResponse, error)
	GetExportData(ctx context.Context, categoryCodes []string, includeInactive bool) ([]*domain.DictCategory, error)

	// 导入操作
	ImportDict(ctx context.Context, req *domain.DictImportRequest, data []byte) (*domain.DictImportStatus, error)
	GetImportStatus(ctx context.Context, importID string) (*domain.DictImportStatus, error)
	ProcessImportData(ctx context.Context, importID string, data []byte, format, mergeMode string) error

	// 文件管理
	SaveExportFile(ctx context.Context, fileName string, data []byte) (string, error)
	GetExportFile(ctx context.Context, fileName string) ([]byte, error)
	CleanupExpiredFiles(ctx context.Context, expiredBefore time.Time) error
}

// TagGroupRepository 标签分组仓库接口
type TagGroupRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, group *domain.TagGroup) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.TagGroup, error)
	GetByCode(ctx context.Context, code string) (*domain.TagGroup, error)
	Update(ctx context.Context, group *domain.TagGroup) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.TagGroup, int64, error)
	ExistsByCode(ctx context.Context, code string) (bool, error)
	ExistsByName(ctx context.Context, name string) (bool, error)
	GetByParentID(ctx context.Context, parentID *uuid.UUID, offset, limit int) ([]*domain.TagGroup, int64, error)
	GetByEntityType(ctx context.Context, entityType string, offset, limit int) ([]*domain.TagGroup, int64, error)
	Search(ctx context.Context, keyword string, offset, limit int) ([]*domain.TagGroup, int64, error)

	// 层级操作
	GetChildren(ctx context.Context, parentID uuid.UUID) ([]*domain.TagGroup, error)
	GetAncestors(ctx context.Context, groupID uuid.UUID) ([]*domain.TagGroup, error)
	GetTree(ctx context.Context, entityType *string) ([]*domain.TagGroup, error)
	UpdateLevel(ctx context.Context, groupID uuid.UUID, level int) error

	// 统计操作
	GetTagCount(ctx context.Context, groupID uuid.UUID) (int64, error)
	GetUsageStats(ctx context.Context, groupID uuid.UUID) (map[string]interface{}, error)
}

// TagRepository 标签仓库接口
type TagRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, tag *domain.Tag) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Tag, error)
	GetByCode(ctx context.Context, code string) (*domain.Tag, error)
	Update(ctx context.Context, tag *domain.Tag) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.Tag, int64, error)
	ExistsByCode(ctx context.Context, code string) (bool, error)
	ExistsByNameInGroup(ctx context.Context, groupID uuid.UUID, name string) (bool, error)
	GetByGroupID(ctx context.Context, groupID uuid.UUID, offset, limit int, filters map[string]interface{}) ([]*domain.Tag, int64, error)
	Search(ctx context.Context, keyword string, offset, limit int, filters map[string]interface{}) ([]*domain.Tag, int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, tags []*domain.Tag) error
	BatchUpdate(ctx context.Context, tags []*domain.Tag) error
	BatchDelete(ctx context.Context, ids []uuid.UUID) error

	// 使用统计
	IncrementUsageCount(ctx context.Context, tagID uuid.UUID) error
	DecrementUsageCount(ctx context.Context, tagID uuid.UUID) error
	UpdateUsageCount(ctx context.Context, tagID uuid.UUID, count int) error
	GetPopularTags(ctx context.Context, limit int, entityType *string) ([]*domain.Tag, error)

	// 状态管理
	UpdateStatus(ctx context.Context, tagID uuid.UUID, status domain.TagStatus) error
	GetByStatus(ctx context.Context, status domain.TagStatus, offset, limit int) ([]*domain.Tag, int64, error)
}

// EntityTagRepository 实体标签关联仓库接口
type EntityTagRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, entityTag *domain.EntityTag) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.EntityTag, error)
	Update(ctx context.Context, entityTag *domain.EntityTag) error
	Delete(ctx context.Context, id uuid.UUID) error

	// 实体标签关联查询
	GetByEntity(ctx context.Context, entityType domain.EntityType, entityID uuid.UUID) ([]*domain.EntityTag, error)
	GetByTag(ctx context.Context, tagID uuid.UUID, offset, limit int) ([]*domain.EntityTag, int64, error)
	GetByEntityAndTag(ctx context.Context, entityType domain.EntityType, entityID, tagID uuid.UUID) (*domain.EntityTag, error)
	ExistsByEntityAndTag(ctx context.Context, entityType domain.EntityType, entityID, tagID uuid.UUID) (bool, error)

	// 批量操作
	BatchCreate(ctx context.Context, entityTags []*domain.EntityTag) error
	BatchDelete(ctx context.Context, entityType domain.EntityType, entityID uuid.UUID, tagIDs []uuid.UUID) error
	BatchDeleteByEntity(ctx context.Context, entityType domain.EntityType, entityID uuid.UUID) error
	BatchDeleteByTag(ctx context.Context, tagID uuid.UUID) error

	// 查询操作
	List(ctx context.Context, offset, limit int, filters map[string]interface{}) ([]*domain.EntityTag, int64, error)
	GetEntitiesByTag(ctx context.Context, tagID uuid.UUID, entityType *domain.EntityType, offset, limit int) ([]*domain.EntityTag, int64, error)
	GetTagsByEntity(ctx context.Context, entityType domain.EntityType, entityID uuid.UUID, includeExpired bool) ([]*domain.EntityTag, error)

	// 过期管理
	GetExpiredTags(ctx context.Context, beforeTime time.Time, offset, limit int) ([]*domain.EntityTag, int64, error)
	CleanupExpiredTags(ctx context.Context, beforeTime time.Time) (int64, error)
	UpdateExpiration(ctx context.Context, id uuid.UUID, expiresAt *time.Time) error

	// 统计查询
	GetEntityTagCount(ctx context.Context, entityType domain.EntityType, entityID uuid.UUID) (int64, error)
	GetTagUsageCount(ctx context.Context, tagID uuid.UUID) (int64, error)
	GetTagUsageByEntityType(ctx context.Context, tagID uuid.UUID) (map[string]int64, error)
}

// TagStatisticsRepository 标签统计仓库接口
type TagStatisticsRepository interface {
	// 标签统计
	GetTagStatistics(ctx context.Context, tagID uuid.UUID) (*domain.TagStatistics, error)
	GetTagsStatistics(ctx context.Context, tagIDs []uuid.UUID) ([]*domain.TagStatistics, error)
	GetGroupStatistics(ctx context.Context, groupID uuid.UUID) ([]*domain.TagStatistics, error)

	// 实体标签分布统计
	GetEntityTagDistribution(ctx context.Context, entityType domain.EntityType) (*domain.EntityTagDistribution, error)
	GetAllEntityTagDistribution(ctx context.Context) ([]*domain.EntityTagDistribution, error)

	// 趋势统计
	GetTagUsageTrend(ctx context.Context, tagID uuid.UUID, startTime, endTime time.Time, groupBy string) ([]map[string]interface{}, error)
	GetEntityTaggingTrend(ctx context.Context, entityType domain.EntityType, startTime, endTime time.Time, groupBy string) ([]map[string]interface{}, error)

	// 热门标签统计
	GetHotTags(ctx context.Context, entityType *domain.EntityType, limit int, timeRange *time.Duration) ([]*domain.Tag, error)
	GetRecentlyUsedTags(ctx context.Context, entityType *domain.EntityType, limit int) ([]*domain.Tag, error)

	// 实体分析
	GetUntaggedEntities(ctx context.Context, entityType domain.EntityType, offset, limit int) ([]uuid.UUID, int64, error)
	GetMostTaggedEntities(ctx context.Context, entityType domain.EntityType, limit int) ([]map[string]interface{}, error)
}

// RepositoryManager 仓库管理器接口
type RepositoryManager interface {
	User() UserRepository
	Role() RoleRepository
	Permission() PermissionRepository
	Session() SessionRepository
	LoginLog() LoginLogRepository
	PermissionLog() PermissionLogRepository
	OperationLog() OperationLogRepository

	// 新的操作日志仓库
	OperationLogs() OperationLogsRepository

	// 站点管理相关仓库
	Station() StationRepository
	StationEquipment() StationEquipmentRepository
	MaintenanceRecord() MaintenanceRecordRepository
	OperationParam() OperationParamRepository
	UserStationRole() UserStationRoleRepository

	// 系统权限管理相关仓库
	UserSystemAccess() UserSystemAccessRepository
	SystemPermission() SystemPermissionRepository
	UserSystemPermission() UserSystemPermissionRepository

	// 配置管理相关仓库
	ConfigItem() ConfigItemRepository
	ConfigLog() ConfigLogRepository

	// 数据字典管理相关仓库
	DictCategory() DictCategoryRepository
	DictItem() DictItemRepository
	DictValue() DictValueRepository
	DictQuery() DictQueryRepository
	DictSync() DictSyncRepository
	DictCache() DictCacheRepository
	DictStats() DictStatsRepository
	DictImportExport() DictImportExportRepository

	// 标签系统相关仓库
	TagGroup() TagGroupRepository
	Tag() TagRepository
	EntityTag() EntityTagRepository
	TagStatistics() TagStatisticsRepository

	// 组织管理相关仓库
	Organization() OrganizationRepository
	OrganizationType() OrganizationTypeRepository
	UserOrganization() UserOrganizationRepository

	// 事务管理
	WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error
}
