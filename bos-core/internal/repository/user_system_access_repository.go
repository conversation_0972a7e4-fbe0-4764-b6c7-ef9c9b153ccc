package repository

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// UserSystemAccessRepository 用户系统访问权限仓储接口
type UserSystemAccessRepository interface {
	// Create 创建用户系统访问权限
	Create(ctx context.Context, access *domain.UserSystemAccess) error

	// GetByID 根据ID获取用户系统访问权限
	GetByID(ctx context.Context, id uuid.UUID) (*domain.UserSystemAccess, error)

	// GetByUserAndSystem 根据用户ID和系统代码获取访问权限
	GetByUserAndSystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*domain.UserSystemAccess, error)

	// GetByUserID 根据用户ID获取所有系统访问权限
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error)

	// GetActiveByUserID 根据用户ID获取所有活跃的系统访问权限
	GetActiveByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error)

	// GetBySystemCode 根据系统代码获取所有访问权限
	GetBySystemCode(ctx context.Context, systemCode domain.SystemCode) ([]*domain.UserSystemAccess, error)

	// Update 更新用户系统访问权限
	Update(ctx context.Context, access *domain.UserSystemAccess) error

	// Delete 删除用户系统访问权限
	Delete(ctx context.Context, id uuid.UUID) error

	// ExistsByUserAndSystem 检查用户是否已有指定系统的访问权限
	ExistsByUserAndSystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (bool, error)

	// HasSystemAccess 检查用户是否有指定系统的访问权限
	HasSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (bool, error)

	// HasStationAccess 检查用户是否有指定站点的访问权限
	HasStationAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, stationID int64) (bool, error)

	// GrantSystemAccess 授予用户系统访问权限
	GrantSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType, scopeIDs []int64, grantedBy *uuid.UUID, expiresAt *time.Time) error

	// RevokeSystemAccess 撤销用户系统访问权限
	RevokeSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) error

	// UpdateAccessLevel 更新用户系统访问级别
	UpdateAccessLevel(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel) error

	// UpdateScopeIDs 更新用户系统访问范围
	UpdateScopeIDs(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, scopeIDs []int64) error

	// GetUsersBySystemAccess 根据系统访问权限获取用户列表
	GetUsersBySystemAccess(ctx context.Context, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType) ([]*domain.User, error)

	// BatchGrantSystemAccess 批量授予用户系统访问权限
	BatchGrantSystemAccess(ctx context.Context, userIDs []uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType, scopeIDs []int64, grantedBy *uuid.UUID) error

	// BatchRevokeSystemAccess 批量撤销用户系统访问权限
	BatchRevokeSystemAccess(ctx context.Context, userIDs []uuid.UUID, systemCode domain.SystemCode) error

	// GetExpiredAccess 获取已过期的访问权限
	GetExpiredAccess(ctx context.Context) ([]*domain.UserSystemAccess, error)

	// CleanupExpiredAccess 清理已过期的访问权限
	CleanupExpiredAccess(ctx context.Context) error
}

// SystemPermissionRepository 系统权限仓储接口
type SystemPermissionRepository interface {
	// Create 创建系统权限
	Create(ctx context.Context, permission *domain.SystemPermission) error

	// GetByID 根据ID获取系统权限
	GetByID(ctx context.Context, id uuid.UUID) (*domain.SystemPermission, error)

	// GetByCode 根据权限代码获取系统权限
	GetByCode(ctx context.Context, systemCode domain.SystemCode, permissionCode string) (*domain.SystemPermission, error)

	// GetBySystemCode 根据系统代码获取所有权限
	GetBySystemCode(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error)

	// GetActiveBySystemCode 根据系统代码获取所有活跃权限
	GetActiveBySystemCode(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error)

	// GetByScopeType 根据权限范围类型获取权限
	GetByScopeType(ctx context.Context, systemCode domain.SystemCode, scopeType domain.ScopeType) ([]*domain.SystemPermission, error)

	// GetByResource 根据资源获取权限
	GetByResource(ctx context.Context, systemCode domain.SystemCode, resource string) ([]*domain.SystemPermission, error)

	// Update 更新系统权限
	Update(ctx context.Context, permission *domain.SystemPermission) error

	// Delete 删除系统权限
	Delete(ctx context.Context, id uuid.UUID) error

	// List 分页获取系统权限列表
	List(ctx context.Context, systemCode domain.SystemCode, offset, limit int) ([]*domain.SystemPermission, int64, error)

	// Search 搜索系统权限
	Search(ctx context.Context, systemCode domain.SystemCode, keyword string, offset, limit int) ([]*domain.SystemPermission, int64, error)

	// ExistsByCode 检查权限代码是否已存在
	ExistsByCode(ctx context.Context, systemCode domain.SystemCode, permissionCode string) (bool, error)

	// GetSystemPermissions 获取系统权限（用于初始化）
	GetSystemPermissions(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error)

	// BatchCreate 批量创建系统权限
	BatchCreate(ctx context.Context, permissions []*domain.SystemPermission) error
}

// UserSystemPermissionRepository 用户系统权限分配仓储接口
type UserSystemPermissionRepository interface {
	// Create 创建用户系统权限分配
	Create(ctx context.Context, assignment *domain.UserSystemPermission) error

	// GetByID 根据ID获取用户系统权限分配
	GetByID(ctx context.Context, id uuid.UUID) (*domain.UserSystemPermission, error)

	// GetByUserAndPermission 根据用户ID和权限ID获取分配记录
	GetByUserAndPermission(ctx context.Context, userID, permissionID uuid.UUID) (*domain.UserSystemPermission, error)

	// GetByUserID 根据用户ID获取所有权限分配
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemPermission, error)

	// GetActiveByUserID 根据用户ID获取所有活跃的权限分配
	GetActiveByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemPermission, error)

	// GetByUserAndSystem 根据用户ID和系统代码获取权限分配
	GetByUserAndSystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) ([]*domain.UserSystemPermission, error)

	// Update 更新用户系统权限分配
	Update(ctx context.Context, assignment *domain.UserSystemPermission) error

	// Delete 删除用户系统权限分配
	Delete(ctx context.Context, id uuid.UUID) error

	// GrantPermission 授予用户系统权限
	GrantPermission(ctx context.Context, userID, permissionID uuid.UUID, grantedBy *uuid.UUID, expiresAt *time.Time) error

	// RevokePermission 撤销用户系统权限
	RevokePermission(ctx context.Context, userID, permissionID uuid.UUID) error

	// BatchGrantPermissions 批量授予用户系统权限
	BatchGrantPermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID, grantedBy *uuid.UUID, expiresAt *time.Time) error

	// BatchRevokePermissions 批量撤销用户系统权限
	BatchRevokePermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID) error

	// GetUserPermissionCodes 获取用户的权限代码列表
	GetUserPermissionCodes(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) ([]string, error)

	// HasPermission 检查用户是否有指定权限
	HasPermission(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, permissionCode string) (bool, error)

	// GetExpiredAssignments 获取已过期的权限分配
	GetExpiredAssignments(ctx context.Context) ([]*domain.UserSystemPermission, error)

	// CleanupExpiredAssignments 清理已过期的权限分配
	CleanupExpiredAssignments(ctx context.Context) error
}
