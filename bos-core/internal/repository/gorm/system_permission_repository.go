package gorm

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
)

type systemPermissionRepository struct {
	db *gorm.DB
}

// NewSystemPermissionRepository 创建系统权限仓储实例
func NewSystemPermissionRepository(db *gorm.DB) repository.SystemPermissionRepository {
	return &systemPermissionRepository{db: db}
}

// Create 创建系统权限
func (r *systemPermissionRepository) Create(ctx context.Context, permission *domain.SystemPermission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

// GetByID 根据ID获取系统权限
func (r *systemPermissionRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.SystemPermission, error) {
	var permission domain.SystemPermission
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		First(&permission, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &permission, nil
}

// GetByCode 根据权限代码获取系统权限
func (r *systemPermissionRepository) GetByCode(ctx context.Context, systemCode domain.SystemCode, permissionCode string) (*domain.SystemPermission, error) {
	var permission domain.SystemPermission
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		First(&permission, "system_code = ? AND permission_code = ?", systemCode, permissionCode).Error
	if err != nil {
		return nil, err
	}
	return &permission, nil
}

// GetBySystemCode 根据系统代码获取所有权限
func (r *systemPermissionRepository) GetBySystemCode(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error) {
	var permissions []*domain.SystemPermission
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		Find(&permissions, "system_code = ?", systemCode).Error
	return permissions, err
}

// GetActiveBySystemCode 根据系统代码获取所有活跃权限
func (r *systemPermissionRepository) GetActiveBySystemCode(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error) {
	var permissions []*domain.SystemPermission
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		Where("system_code = ? AND status = ?", systemCode, domain.SystemPermissionStatusActive).
		Find(&permissions).Error
	return permissions, err
}

// GetByScopeType 根据权限范围类型获取权限
func (r *systemPermissionRepository) GetByScopeType(ctx context.Context, systemCode domain.SystemCode, scopeType domain.ScopeType) ([]*domain.SystemPermission, error) {
	var permissions []*domain.SystemPermission
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		Where("system_code = ? AND scope_type = ? AND status = ?", systemCode, scopeType, domain.SystemPermissionStatusActive).
		Find(&permissions).Error
	return permissions, err
}

// GetByResource 根据资源获取权限
func (r *systemPermissionRepository) GetByResource(ctx context.Context, systemCode domain.SystemCode, resource string) ([]*domain.SystemPermission, error) {
	var permissions []*domain.SystemPermission
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		Where("system_code = ? AND resource = ? AND status = ?", systemCode, resource, domain.SystemPermissionStatusActive).
		Find(&permissions).Error
	return permissions, err
}

// Update 更新系统权限
func (r *systemPermissionRepository) Update(ctx context.Context, permission *domain.SystemPermission) error {
	return r.db.WithContext(ctx).Save(permission).Error
}

// Delete 删除系统权限
func (r *systemPermissionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&domain.SystemPermission{}, "id = ?", id).Error
}

// List 分页获取系统权限列表
func (r *systemPermissionRepository) List(ctx context.Context, systemCode domain.SystemCode, offset, limit int) ([]*domain.SystemPermission, int64, error) {
	var permissions []*domain.SystemPermission
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).
		Model(&domain.SystemPermission{}).
		Where("system_code = ?", systemCode).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.WithContext(ctx).
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		Where("system_code = ?", systemCode).
		Offset(offset).
		Limit(limit).
		Find(&permissions).Error

	return permissions, total, err
}

// Search 搜索系统权限
func (r *systemPermissionRepository) Search(ctx context.Context, systemCode domain.SystemCode, keyword string, offset, limit int) ([]*domain.SystemPermission, int64, error) {
	var permissions []*domain.SystemPermission
	var total int64

	query := r.db.WithContext(ctx).Model(&domain.SystemPermission{}).
		Where("system_code = ?", systemCode)

	if keyword != "" {
		searchPattern := "%" + keyword + "%"
		query = query.Where("permission_code ILIKE ? OR permission_name ILIKE ? OR resource ILIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.
		Preload("CreatedByUser").
		Preload("UpdatedByUser").
		Offset(offset).
		Limit(limit).
		Find(&permissions).Error

	return permissions, total, err
}

// ExistsByCode 检查权限代码是否已存在
func (r *systemPermissionRepository) ExistsByCode(ctx context.Context, systemCode domain.SystemCode, permissionCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.SystemPermission{}).
		Where("system_code = ? AND permission_code = ?", systemCode, permissionCode).
		Count(&count).Error
	return count > 0, err
}

// GetSystemPermissions 获取系统权限（用于初始化）
func (r *systemPermissionRepository) GetSystemPermissions(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error) {
	var permissions []*domain.SystemPermission
	err := r.db.WithContext(ctx).
		Where("system_code = ? AND is_system = ?", systemCode, true).
		Find(&permissions).Error
	return permissions, err
}

// BatchCreate 批量创建系统权限
func (r *systemPermissionRepository) BatchCreate(ctx context.Context, permissions []*domain.SystemPermission) error {
	if len(permissions) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, permission := range permissions {
			if err := tx.Create(permission).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

type userSystemPermissionRepository struct {
	db *gorm.DB
}

// NewUserSystemPermissionRepository 创建用户系统权限分配仓储实例
func NewUserSystemPermissionRepository(db *gorm.DB) repository.UserSystemPermissionRepository {
	return &userSystemPermissionRepository{db: db}
}

// Create 创建用户系统权限分配
func (r *userSystemPermissionRepository) Create(ctx context.Context, assignment *domain.UserSystemPermission) error {
	return r.db.WithContext(ctx).Create(assignment).Error
}

// GetByID 根据ID获取用户系统权限分配
func (r *userSystemPermissionRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.UserSystemPermission, error) {
	var assignment domain.UserSystemPermission
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("SystemPermission").
		Preload("GrantedByUser").
		First(&assignment, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &assignment, nil
}

// GetByUserAndPermission 根据用户ID和权限ID获取分配记录
func (r *userSystemPermissionRepository) GetByUserAndPermission(ctx context.Context, userID, permissionID uuid.UUID) (*domain.UserSystemPermission, error) {
	var assignment domain.UserSystemPermission
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("SystemPermission").
		Preload("GrantedByUser").
		First(&assignment, "user_id = ? AND system_permission_id = ?", userID, permissionID).Error
	if err != nil {
		return nil, err
	}
	return &assignment, nil
}

// GetByUserID 根据用户ID获取所有权限分配
func (r *userSystemPermissionRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemPermission, error) {
	var assignments []*domain.UserSystemPermission
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("SystemPermission").
		Preload("GrantedByUser").
		Find(&assignments, "user_id = ?", userID).Error
	return assignments, err
}

// GetActiveByUserID 根据用户ID获取所有活跃的权限分配
func (r *userSystemPermissionRepository) GetActiveByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemPermission, error) {
	var assignments []*domain.UserSystemPermission
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("SystemPermission").
		Preload("GrantedByUser").
		Where("user_id = ? AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)", userID).
		Find(&assignments).Error
	return assignments, err
}

// GetByUserAndSystem 根据用户ID和系统代码获取权限分配
func (r *userSystemPermissionRepository) GetByUserAndSystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) ([]*domain.UserSystemPermission, error) {
	var assignments []*domain.UserSystemPermission
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("SystemPermission").
		Preload("GrantedByUser").
		Joins("JOIN system_permissions sp ON user_system_permissions.system_permission_id = sp.id").
		Where("user_system_permissions.user_id = ? AND sp.system_code = ?", userID, systemCode).
		Find(&assignments).Error
	return assignments, err
}

// Update 更新用户系统权限分配
func (r *userSystemPermissionRepository) Update(ctx context.Context, assignment *domain.UserSystemPermission) error {
	return r.db.WithContext(ctx).Save(assignment).Error
}

// Delete 删除用户系统权限分配
func (r *userSystemPermissionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&domain.UserSystemPermission{}, "id = ?", id).Error
}

// GrantPermission 授予用户系统权限
func (r *userSystemPermissionRepository) GrantPermission(ctx context.Context, userID, permissionID uuid.UUID, grantedBy *uuid.UUID, expiresAt *time.Time) error {
	assignment := &domain.UserSystemPermission{
		ID:                 uuid.New(),
		UserID:             userID,
		SystemPermissionID: permissionID,
		GrantedBy:          grantedBy,
		GrantedAt:          time.Now(),
		ExpiresAt:          expiresAt,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}
	return r.Create(ctx, assignment)
}

// RevokePermission 撤销用户系统权限
func (r *userSystemPermissionRepository) RevokePermission(ctx context.Context, userID, permissionID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND system_permission_id = ?", userID, permissionID).
		Delete(&domain.UserSystemPermission{}).Error
}

// BatchGrantPermissions 批量授予用户系统权限
func (r *userSystemPermissionRepository) BatchGrantPermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID, grantedBy *uuid.UUID, expiresAt *time.Time) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, permissionID := range permissionIDs {
			assignment := &domain.UserSystemPermission{
				ID:                 uuid.New(),
				UserID:             userID,
				SystemPermissionID: permissionID,
				GrantedBy:          grantedBy,
				GrantedAt:          time.Now(),
				ExpiresAt:          expiresAt,
				CreatedAt:          time.Now(),
				UpdatedAt:          time.Now(),
			}
			if err := tx.Create(assignment).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchRevokePermissions 批量撤销用户系统权限
func (r *userSystemPermissionRepository) BatchRevokePermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Where("user_id = ? AND system_permission_id IN ?", userID, permissionIDs).
		Delete(&domain.UserSystemPermission{}).Error
}

// GetUserPermissionCodes 获取用户的权限代码列表
func (r *userSystemPermissionRepository) GetUserPermissionCodes(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) ([]string, error) {
	var codes []string
	err := r.db.WithContext(ctx).
		Table("user_system_permissions usp").
		Select("sp.permission_code").
		Joins("JOIN system_permissions sp ON usp.system_permission_id = sp.id").
		Where("usp.user_id = ? AND sp.system_code = ? AND sp.status = ? AND (usp.expires_at IS NULL OR usp.expires_at > CURRENT_TIMESTAMP)", 
			userID, systemCode, domain.SystemPermissionStatusActive).
		Pluck("sp.permission_code", &codes).Error
	return codes, err
}

// HasPermission 检查用户是否有指定权限
func (r *userSystemPermissionRepository) HasPermission(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, permissionCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("user_system_permissions usp").
		Joins("JOIN system_permissions sp ON usp.system_permission_id = sp.id").
		Where("usp.user_id = ? AND sp.system_code = ? AND sp.permission_code = ? AND sp.status = ? AND (usp.expires_at IS NULL OR usp.expires_at > CURRENT_TIMESTAMP)", 
			userID, systemCode, permissionCode, domain.SystemPermissionStatusActive).
		Count(&count).Error
	return count > 0, err
}

// GetExpiredAssignments 获取已过期的权限分配
func (r *userSystemPermissionRepository) GetExpiredAssignments(ctx context.Context) ([]*domain.UserSystemPermission, error) {
	var assignments []*domain.UserSystemPermission
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("SystemPermission").
		Where("expires_at IS NOT NULL AND expires_at <= CURRENT_TIMESTAMP").
		Find(&assignments).Error
	return assignments, err
}

// CleanupExpiredAssignments 清理已过期的权限分配
func (r *userSystemPermissionRepository) CleanupExpiredAssignments(ctx context.Context) error {
	return r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= CURRENT_TIMESTAMP").
		Delete(&domain.UserSystemPermission{}).Error
}
