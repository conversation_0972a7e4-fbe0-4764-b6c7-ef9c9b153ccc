package gorm

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gorm.io/gorm"
)

// repositoryManager 仓库管理器GORM实现
type repositoryManager struct {
	db *gorm.DB

	userRepo          repository.UserRepository
	roleRepo          repository.RoleRepository
	permissionRepo    repository.PermissionRepository
	sessionRepo       repository.SessionRepository
	loginLogRepo      repository.LoginLogRepository
	permissionLogRepo repository.PermissionLogRepository
	operationLogRepo  repository.OperationLogRepository

	// 操作日志仓库
	operationLogsRepo repository.OperationLogsRepository

	// 站点管理相关仓库
	stationRepo           repository.StationRepository
	stationEquipmentRepo  repository.StationEquipmentRepository
	maintenanceRecordRepo repository.MaintenanceRecordRepository
	operationParamRepo    repository.OperationParamRepository
	userStationRoleRepo   repository.UserStationRoleRepository

	// 系统权限管理相关仓库
	userSystemAccessRepo       repository.UserSystemAccessRepository
	systemPermissionRepo       repository.SystemPermissionRepository
	userSystemPermissionRepo   repository.UserSystemPermissionRepository

	// 配置管理相关仓库
	configItemRepo repository.ConfigItemRepository
	configLogRepo  repository.ConfigLogRepository

	// 数据字典管理相关仓库
	dictCategoryRepo     repository.DictCategoryRepository
	dictItemRepo         repository.DictItemRepository
	dictValueRepo        repository.DictValueRepository
	dictQueryRepo        repository.DictQueryRepository
	dictSyncRepo         repository.DictSyncRepository
	dictCacheRepo        repository.DictCacheRepository
	dictStatsRepo        repository.DictStatsRepository
	dictImportExportRepo repository.DictImportExportRepository

	// 标签系统相关仓库
	tagGroupRepo      repository.TagGroupRepository
	tagRepo           repository.TagRepository
	entityTagRepo     repository.EntityTagRepository
	tagStatisticsRepo repository.TagStatisticsRepository

	// 组织管理相关仓库
	organizationRepo     repository.OrganizationRepository
	organizationTypeRepo repository.OrganizationTypeRepository
	userOrganizationRepo repository.UserOrganizationRepository
}

// NewRepositoryManager 创建仓库管理器实例
func NewRepositoryManager(db *gorm.DB) repository.RepositoryManager {
	return &repositoryManager{
		db:                    db,
		userRepo:              NewUserRepository(db),
		roleRepo:              NewRoleRepository(db),
		permissionRepo:        NewPermissionRepository(db),
		sessionRepo:           NewSessionRepository(db),
		loginLogRepo:          NewLoginLogRepository(db),
		permissionLogRepo:     NewPermissionLogRepository(db),
		operationLogRepo:      NewOperationLogRepository(db),
		operationLogsRepo:     NewOperationLogsRepository(db),
		stationRepo:           NewStationRepository(db),
		stationEquipmentRepo:  NewStationEquipmentRepository(db),
		maintenanceRecordRepo: NewMaintenanceRecordRepository(db),
		operationParamRepo:    NewOperationParamRepository(db),
		userStationRoleRepo:   NewUserStationRoleRepository(db),
		configItemRepo:        NewConfigItemRepository(db),
		configLogRepo:         NewConfigLogRepository(db),
		dictCategoryRepo:      NewDictCategoryRepository(db),
		dictItemRepo:          NewDictItemRepository(db),
		dictValueRepo:         NewDictValueRepository(db),
		dictQueryRepo:         NewDictQueryRepository(db),
		// 注意：以下几个仓库需要额外实现，暂时设为nil
		dictSyncRepo:         NewDictSyncRepository(db),
		dictCacheRepo:        nil,                        // TODO: 实现DictCacheRepository
		dictStatsRepo:        NewDictStatsRepository(db), // 已实现
		dictImportExportRepo: NewDictImportExportRepository(db, nil),

		// 标签系统相关仓库
		tagGroupRepo:      NewTagGroupRepository(db),
		tagRepo:           NewTagRepository(db),
		entityTagRepo:     NewEntityTagRepository(db),
		tagStatisticsRepo: NewTagStatisticsRepository(db),

		// 组织管理相关仓库
		organizationRepo:     NewOrganizationRepository(db),
		organizationTypeRepo: NewOrganizationTypeRepository(db),
		userOrganizationRepo: NewUserOrganizationRepository(db),
	}
}

// User 获取用户仓库
func (m *repositoryManager) User() repository.UserRepository {
	return m.userRepo
}

// Role 获取角色仓库
func (m *repositoryManager) Role() repository.RoleRepository {
	return m.roleRepo
}

// Permission 获取权限仓库
func (m *repositoryManager) Permission() repository.PermissionRepository {
	return m.permissionRepo
}

// Session 获取会话仓库
func (m *repositoryManager) Session() repository.SessionRepository {
	return m.sessionRepo
}

// LoginLog 获取登录日志仓库
func (m *repositoryManager) LoginLog() repository.LoginLogRepository {
	return m.loginLogRepo
}

// PermissionLog 获取权限日志仓库
func (m *repositoryManager) PermissionLog() repository.PermissionLogRepository {
	return m.permissionLogRepo
}

// OperationLog 获取操作日志仓库
func (m *repositoryManager) OperationLog() repository.OperationLogRepository {
	return m.operationLogRepo
}

// OperationLogs 获取操作日志仓库
func (m *repositoryManager) OperationLogs() repository.OperationLogsRepository {
	return m.operationLogsRepo
}

// Station 获取站点仓库
func (m *repositoryManager) Station() repository.StationRepository {
	return m.stationRepo
}

// StationEquipment 获取站点设备仓库
func (m *repositoryManager) StationEquipment() repository.StationEquipmentRepository {
	return m.stationEquipmentRepo
}

// MaintenanceRecord 获取维护记录仓库
func (m *repositoryManager) MaintenanceRecord() repository.MaintenanceRecordRepository {
	return m.maintenanceRecordRepo
}

// OperationParam 获取运营参数仓库
func (m *repositoryManager) OperationParam() repository.OperationParamRepository {
	return m.operationParamRepo
}

// UserStationRole 获取用户站点角色关联仓库
func (m *repositoryManager) UserStationRole() repository.UserStationRoleRepository {
	return m.userStationRoleRepo
}

// ConfigItem 获取配置项仓库
func (m *repositoryManager) ConfigItem() repository.ConfigItemRepository {
	return m.configItemRepo
}

// ConfigLog 获取配置日志仓库
func (m *repositoryManager) ConfigLog() repository.ConfigLogRepository {
	return m.configLogRepo
}

// DictCategory 获取数据字典分类仓库
func (m *repositoryManager) DictCategory() repository.DictCategoryRepository {
	return m.dictCategoryRepo
}

// DictItem 获取数据字典项目仓库
func (m *repositoryManager) DictItem() repository.DictItemRepository {
	return m.dictItemRepo
}

// DictValue 获取数据字典值仓库
func (m *repositoryManager) DictValue() repository.DictValueRepository {
	return m.dictValueRepo
}

// DictQuery 获取数据字典查询仓库
func (m *repositoryManager) DictQuery() repository.DictQueryRepository {
	return m.dictQueryRepo
}

// DictSync 获取数据字典同步仓库
func (m *repositoryManager) DictSync() repository.DictSyncRepository {
	return m.dictSyncRepo
}

// DictCache 获取数据字典缓存仓库
func (m *repositoryManager) DictCache() repository.DictCacheRepository {
	return m.dictCacheRepo
}

// DictStats 获取数据字典统计仓库
func (m *repositoryManager) DictStats() repository.DictStatsRepository {
	return m.dictStatsRepo
}

// DictImportExport 获取数据字典导入导出仓库
func (m *repositoryManager) DictImportExport() repository.DictImportExportRepository {
	return m.dictImportExportRepo
}

// TagGroup 获取标签组仓库
func (m *repositoryManager) TagGroup() repository.TagGroupRepository {
	return m.tagGroupRepo
}

// Tag 获取标签仓库
func (m *repositoryManager) Tag() repository.TagRepository {
	return m.tagRepo
}

// EntityTag 获取实体标签仓库
func (m *repositoryManager) EntityTag() repository.EntityTagRepository {
	return m.entityTagRepo
}

// TagStatistics 获取标签统计仓库
func (m *repositoryManager) TagStatistics() repository.TagStatisticsRepository {
	return m.tagStatisticsRepo
}

// Organization 获取组织仓库
func (m *repositoryManager) Organization() repository.OrganizationRepository {
	return m.organizationRepo
}

// OrganizationType 获取组织类型仓库
func (m *repositoryManager) OrganizationType() repository.OrganizationTypeRepository {
	return m.organizationTypeRepo
}

// UserOrganization 获取用户组织关联仓库
func (m *repositoryManager) UserOrganization() repository.UserOrganizationRepository {
	return m.userOrganizationRepo
}

// UserSystemAccess 获取用户系统访问权限仓库
func (m *repositoryManager) UserSystemAccess() repository.UserSystemAccessRepository {
	if m.userSystemAccessRepo == nil {
		m.userSystemAccessRepo = NewUserSystemAccessRepository(m.db)
	}
	return m.userSystemAccessRepo
}

// SystemPermission 获取系统权限仓库
func (m *repositoryManager) SystemPermission() repository.SystemPermissionRepository {
	if m.systemPermissionRepo == nil {
		m.systemPermissionRepo = NewSystemPermissionRepository(m.db)
	}
	return m.systemPermissionRepo
}

// UserSystemPermission 获取用户系统权限分配仓库
func (m *repositoryManager) UserSystemPermission() repository.UserSystemPermissionRepository {
	if m.userSystemPermissionRepo == nil {
		m.userSystemPermissionRepo = NewUserSystemPermissionRepository(m.db)
	}
	return m.userSystemPermissionRepo
}

// WithTransaction 执行事务
func (m *repositoryManager) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建事务上下文的仓库管理器
		txManager := &repositoryManager{
			db:                    tx,
			userRepo:              NewUserRepository(tx),
			roleRepo:              NewRoleRepository(tx),
			permissionRepo:        NewPermissionRepository(tx),
			sessionRepo:           NewSessionRepository(tx),
			loginLogRepo:          NewLoginLogRepository(tx),
			permissionLogRepo:     NewPermissionLogRepository(tx),
			operationLogRepo:      NewOperationLogRepository(tx),
			operationLogsRepo:     NewOperationLogsRepository(tx),
			stationRepo:           NewStationRepository(tx),
			stationEquipmentRepo:  NewStationEquipmentRepository(tx),
			maintenanceRecordRepo: NewMaintenanceRecordRepository(tx),
			operationParamRepo:    NewOperationParamRepository(tx),
			configItemRepo:        NewConfigItemRepository(tx),
			configLogRepo:         NewConfigLogRepository(tx),
			dictCategoryRepo:      NewDictCategoryRepository(tx),
			dictItemRepo:          NewDictItemRepository(tx),
			dictValueRepo:         NewDictValueRepository(tx),
			dictQueryRepo:         NewDictQueryRepository(tx),
			// 注意：以下几个仓库需要额外实现，暂时设为nil
			dictSyncRepo:         NewDictSyncRepository(tx),
			dictCacheRepo:        nil,                        // TODO: 实现DictCacheRepository
			dictStatsRepo:        NewDictStatsRepository(tx), // 已实现
			dictImportExportRepo: NewDictImportExportRepository(tx, nil),

			// 标签系统相关仓库
			tagGroupRepo:      NewTagGroupRepository(tx),
			tagRepo:           NewTagRepository(tx),
			entityTagRepo:     NewEntityTagRepository(tx),
			tagStatisticsRepo: NewTagStatisticsRepository(tx),

			// 组织管理相关仓库
			organizationRepo:     NewOrganizationRepository(tx),
			organizationTypeRepo: NewOrganizationTypeRepository(tx),
			userOrganizationRepo: NewUserOrganizationRepository(tx),

			// 系统权限管理相关仓库
			userSystemAccessRepo:     NewUserSystemAccessRepository(tx),
			systemPermissionRepo:     NewSystemPermissionRepository(tx),
			userSystemPermissionRepo: NewUserSystemPermissionRepository(tx),
		}

		// 将事务管理器存储到上下文中
		txCtx := context.WithValue(ctx, "tx_manager", txManager)

		return fn(txCtx)
	})
}
