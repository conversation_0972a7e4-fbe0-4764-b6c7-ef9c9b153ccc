package gorm

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gorm.io/gorm"
)

// organizationRepository 组织仓库GORM实现
type organizationRepository struct {
	db *gorm.DB
}

// NewOrganizationRepository 创建组织仓库实例
func NewOrganizationRepository(db *gorm.DB) repository.OrganizationRepository {
	return &organizationRepository{db: db}
}

// Create 创建组织
func (r *organizationRepository) Create(ctx context.Context, org *domain.Organization) error {
	return r.db.WithContext(ctx).Create(org).Error
}

// GetByID 根据ID获取组织
func (r *organizationRepository) GetByID(ctx context.Context, id int64) (*domain.Organization, error) {
	var org domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Preload("Parent").
		First(&org, id).Error
	if err != nil {
		return nil, err
	}
	return &org, nil
}

// GetByCode 根据编码获取组织
func (r *organizationRepository) GetByCode(ctx context.Context, orgCode string) (*domain.Organization, error) {
	var org domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Preload("Parent").
		Where("org_code = ?", orgCode).
		First(&org).Error
	if err != nil {
		return nil, err
	}
	return &org, nil
}

// Update 更新组织
func (r *organizationRepository) Update(ctx context.Context, org *domain.Organization) error {
	org.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(org).Error
}

// Delete 删除组织（软删除）
func (r *organizationRepository) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&domain.Organization{}, id).Error
}

// GetChildren 获取直接子组织
func (r *organizationRepository) GetChildren(ctx context.Context, parentID int64) ([]*domain.Organization, error) {
	var orgs []*domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Where("parent_id = ?", parentID).
		Order("sort_order, org_name").
		Find(&orgs).Error
	return orgs, err
}

// GetDescendants 获取所有后代组织
func (r *organizationRepository) GetDescendants(ctx context.Context, parentID int64) ([]*domain.Organization, error) {
	// 首先获取父组织的level_path
	var parent domain.Organization
	if err := r.db.WithContext(ctx).Select("level_path").First(&parent, parentID).Error; err != nil {
		return nil, err
	}

	var orgs []*domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Where("level_path LIKE ?", *parent.LevelPath+"/%").
		Order("level_path").
		Find(&orgs).Error
	return orgs, err
}

// GetAncestors 获取所有祖先组织
func (r *organizationRepository) GetAncestors(ctx context.Context, orgID int64) ([]*domain.Organization, error) {
	// 首先获取当前组织的level_path
	var org domain.Organization
	if err := r.db.WithContext(ctx).Select("level_path").First(&org, orgID).Error; err != nil {
		return nil, err
	}

	if org.LevelPath == nil {
		return []*domain.Organization{}, nil
	}

	// 解析level_path获取所有祖先ID
	pathParts := strings.Split(strings.Trim(*org.LevelPath, "/"), "/")
	if len(pathParts) <= 1 {
		return []*domain.Organization{}, nil
	}

	// 移除最后一个（自己）
	ancestorIDs := pathParts[:len(pathParts)-1]

	var ancestors []*domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Where("id IN ?", ancestorIDs).
		Order("level_depth").
		Find(&ancestors).Error
	return ancestors, err
}

// GetRootOrganizations 获取根组织
func (r *organizationRepository) GetRootOrganizations(ctx context.Context) ([]*domain.Organization, error) {
	var orgs []*domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Where("parent_id IS NULL").
		Order("sort_order, org_name").
		Find(&orgs).Error
	return orgs, err
}

// GetByType 根据类型获取组织
func (r *organizationRepository) GetByType(ctx context.Context, orgType string) ([]*domain.Organization, error) {
	var orgs []*domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Joins("JOIN organization_types ot ON organizations.org_type_id = ot.id").
		Where("ot.type_code = ?", orgType).
		Order("level_path").
		Find(&orgs).Error
	return orgs, err
}

// List 列出组织
func (r *organizationRepository) List(ctx context.Context, filter *repository.OrganizationFilter) ([]*domain.Organization, error) {
	query := r.db.WithContext(ctx)

	// 应用预加载
	if filter.IncludeType {
		query = query.Preload("OrgType")
	}
	if filter.IncludeParent {
		query = query.Preload("Parent")
	}
	if filter.IncludeChildren {
		query = query.Preload("Children")
	}
	if filter.IncludeUsers {
		query = query.Preload("Users")
	}

	// 应用过滤条件
	query = r.applyFilters(query, filter)

	// 应用排序
	if filter.SortBy != nil {
		order := *filter.SortBy
		if filter.SortOrder != nil && *filter.SortOrder == "desc" {
			order += " DESC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("level_path")
	}

	// 应用分页
	if filter.Offset != nil {
		query = query.Offset(*filter.Offset)
	}
	if filter.Limit != nil {
		query = query.Limit(*filter.Limit)
	}

	var orgs []*domain.Organization
	err := query.Find(&orgs).Error
	return orgs, err
}

// Count 统计组织数量
func (r *organizationRepository) Count(ctx context.Context, filter *repository.OrganizationFilter) (int64, error) {
	query := r.db.WithContext(ctx).Model(&domain.Organization{})
	query = r.applyFilters(query, filter)

	var count int64
	err := query.Count(&count).Error
	return count, err
}

// Search 搜索组织
func (r *organizationRepository) Search(ctx context.Context, keyword string, limit int) ([]*domain.Organization, error) {
	var orgs []*domain.Organization
	err := r.db.WithContext(ctx).
		Preload("OrgType").
		Where("org_name ILIKE ? OR org_code ILIKE ?", "%"+keyword+"%", "%"+keyword+"%").
		Order("org_name").
		Limit(limit).
		Find(&orgs).Error
	return orgs, err
}

// ExistsByCode 检查编码是否存在
func (r *organizationRepository) ExistsByCode(ctx context.Context, orgCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.Organization{}).
		Where("org_code = ?", orgCode).
		Count(&count).Error
	return count > 0, err
}

// ExistsByID 检查ID是否存在
func (r *organizationRepository) ExistsByID(ctx context.Context, id int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.Organization{}).
		Where("id = ?", id).
		Count(&count).Error
	return count > 0, err
}

// ValidateHierarchy 验证层级关系
func (r *organizationRepository) ValidateHierarchy(ctx context.Context, orgID, parentID int64) error {
	if orgID == parentID {
		return fmt.Errorf("organization cannot be its own parent")
	}

	// 检查是否会形成循环引用
	ancestors, err := r.GetAncestors(ctx, parentID)
	if err != nil {
		return err
	}

	for _, ancestor := range ancestors {
		if ancestor.ID == orgID {
			return fmt.Errorf("circular reference detected: organization %d is an ancestor of %d", orgID, parentID)
		}
	}

	return nil
}

// applyFilters 应用查询过滤条件
func (r *organizationRepository) applyFilters(query *gorm.DB, filter *repository.OrganizationFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	// 状态过滤
	if len(filter.Status) > 0 {
		query = query.Where("status IN ?", filter.Status)
	}

	// 组织类型过滤
	if filter.OrgTypeID != nil {
		query = query.Where("org_type_id = ?", *filter.OrgTypeID)
	}

	// 父组织过滤
	if filter.ParentID != nil {
		query = query.Where("parent_id = ?", *filter.ParentID)
	}

	// 层级深度过滤
	if filter.LevelDepth != nil {
		query = query.Where("level_depth = ?", *filter.LevelDepth)
	}

	// 层级路径过滤
	if filter.LevelPathLike != nil {
		query = query.Where("level_path LIKE ?", *filter.LevelPathLike)
	}

	// 关键词搜索
	if filter.Keyword != nil {
		keyword := "%" + *filter.Keyword + "%"
		query = query.Where("org_name ILIKE ? OR org_code ILIKE ?", keyword, keyword)
	}

	return query
}

// organizationTypeRepository 组织类型仓库GORM实现
type organizationTypeRepository struct {
	db *gorm.DB
}

// NewOrganizationTypeRepository 创建组织类型仓库实例
func NewOrganizationTypeRepository(db *gorm.DB) repository.OrganizationTypeRepository {
	return &organizationTypeRepository{db: db}
}

// Create 创建组织类型
func (r *organizationTypeRepository) Create(ctx context.Context, orgType *domain.OrganizationTypeEntity) error {
	return r.db.WithContext(ctx).Create(orgType).Error
}

// GetByID 根据ID获取组织类型
func (r *organizationTypeRepository) GetByID(ctx context.Context, id int64) (*domain.OrganizationTypeEntity, error) {
	var orgType domain.OrganizationTypeEntity
	err := r.db.WithContext(ctx).First(&orgType, id).Error
	if err != nil {
		return nil, err
	}
	return &orgType, nil
}

// GetByCode 根据编码获取组织类型
func (r *organizationTypeRepository) GetByCode(ctx context.Context, typeCode string) (*domain.OrganizationTypeEntity, error) {
	var orgType domain.OrganizationTypeEntity
	err := r.db.WithContext(ctx).Where("type_code = ?", typeCode).First(&orgType).Error
	if err != nil {
		return nil, err
	}
	return &orgType, nil
}

// Update 更新组织类型
func (r *organizationTypeRepository) Update(ctx context.Context, orgType *domain.OrganizationTypeEntity) error {
	orgType.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(orgType).Error
}

// Delete 删除组织类型
func (r *organizationTypeRepository) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&domain.OrganizationTypeEntity{}, id).Error
}

// List 列出所有组织类型
func (r *organizationTypeRepository) List(ctx context.Context) ([]*domain.OrganizationTypeEntity, error) {
	var orgTypes []*domain.OrganizationTypeEntity
	err := r.db.WithContext(ctx).Order("level_order").Find(&orgTypes).Error
	return orgTypes, err
}

// GetByLevelOrder 根据层级顺序获取组织类型
func (r *organizationTypeRepository) GetByLevelOrder(ctx context.Context, levelOrder int) (*domain.OrganizationTypeEntity, error) {
	var orgType domain.OrganizationTypeEntity
	err := r.db.WithContext(ctx).Where("level_order = ?", levelOrder).First(&orgType).Error
	if err != nil {
		return nil, err
	}
	return &orgType, nil
}

// ExistsByCode 检查编码是否存在
func (r *organizationTypeRepository) ExistsByCode(ctx context.Context, typeCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.OrganizationTypeEntity{}).
		Where("type_code = ?", typeCode).
		Count(&count).Error
	return count > 0, err
}
