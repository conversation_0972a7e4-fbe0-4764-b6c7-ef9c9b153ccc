package gorm

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gorm.io/gorm"
)

// userOrganizationRepository 用户组织关联仓库GORM实现
type userOrganizationRepository struct {
	db *gorm.DB
}

// NewUserOrganizationRepository 创建用户组织关联仓库实例
func NewUserOrganizationRepository(db *gorm.DB) repository.UserOrganizationRepository {
	return &userOrganizationRepository{db: db}
}

// Create 创建用户组织关联
func (r *userOrganizationRepository) Create(ctx context.Context, userOrg *domain.UserOrganization) error {
	return r.db.WithContext(ctx).Create(userOrg).Error
}

// GetByID 根据ID获取用户组织关联
func (r *userOrganizationRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.UserOrganization, error) {
	var userOrg domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Organization").
		Preload("Organization.OrgType").
		Preload("GrantedByUser").
		First(&userOrg, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &userOrg, nil
}

// Update 更新用户组织关联
func (r *userOrganizationRepository) Update(ctx context.Context, userOrg *domain.UserOrganization) error {
	userOrg.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(userOrg).Error
}

// Delete 删除用户组织关联（软删除）
func (r *userOrganizationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&domain.UserOrganization{}, "id = ?", id).Error
}

// AssignUserToOrganization 分配用户到组织
func (r *userOrganizationRepository) AssignUserToOrganization(ctx context.Context, userID uuid.UUID, orgID int64, role string, isPrimary bool, grantedBy *uuid.UUID) error {
	// 检查是否已存在关联
	exists, err := r.ExistsByUserAndOrganization(ctx, userID, orgID)
	if err != nil {
		return err
	}
	if exists {
		return fmt.Errorf("user already assigned to organization")
	}

	// 如果设置为主要组织，需要先清除其他主要组织标记
	if isPrimary {
		if err := r.clearPrimaryOrganization(ctx, userID); err != nil {
			return err
		}
	}

	userOrg := &domain.UserOrganization{
		ID:             uuid.New(),
		UserID:         userID,
		OrganizationID: orgID,
		IsPrimary:      isPrimary,
		RoleInOrg:      &role,
		GrantedBy:      grantedBy,
		GrantedAt:      time.Now(),
		Status:         string(domain.UserOrganizationStatusActive),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	return r.Create(ctx, userOrg)
}

// RemoveUserFromOrganization 从组织中移除用户
func (r *userOrganizationRepository) RemoveUserFromOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND organization_id = ?", userID, orgID).
		Delete(&domain.UserOrganization{}).Error
}

// SetPrimaryOrganization 设置主要组织
func (r *userOrganizationRepository) SetPrimaryOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 清除现有的主要组织标记
		if err := tx.Model(&domain.UserOrganization{}).
			Where("user_id = ? AND is_primary = true", userID).
			Update("is_primary", false).Error; err != nil {
			return err
		}

		// 设置新的主要组织
		return tx.Model(&domain.UserOrganization{}).
			Where("user_id = ? AND organization_id = ?", userID, orgID).
			Updates(map[string]interface{}{
				"is_primary":  true,
				"updated_at": time.Now(),
			}).Error
	})
}

// GetUserOrganizations 获取用户的组织关联
func (r *userOrganizationRepository) GetUserOrganizations(ctx context.Context, userID uuid.UUID) ([]*domain.UserOrganization, error) {
	var userOrgs []*domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("Organization").
		Preload("Organization.OrgType").
		Where("user_id = ?", userID).
		Order("is_primary DESC, granted_at DESC").
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetOrganizationUsers 获取组织的用户关联
func (r *userOrganizationRepository) GetOrganizationUsers(ctx context.Context, orgID int64) ([]*domain.UserOrganization, error) {
	var userOrgs []*domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("GrantedByUser").
		Where("organization_id = ?", orgID).
		Order("granted_at DESC").
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetUserPrimaryOrganization 获取用户的主要组织
func (r *userOrganizationRepository) GetUserPrimaryOrganization(ctx context.Context, userID uuid.UUID) (*domain.UserOrganization, error) {
	var userOrg domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("Organization").
		Preload("Organization.OrgType").
		Where("user_id = ? AND is_primary = true", userID).
		First(&userOrg).Error
	if err != nil {
		return nil, err
	}
	return &userOrg, nil
}

// GetUsersByOrganization 获取组织的用户列表
func (r *userOrganizationRepository) GetUsersByOrganization(ctx context.Context, orgID int64, includeDescendants bool) ([]*domain.User, error) {
	query := r.db.WithContext(ctx).
		Table("users u").
		Select("u.*").
		Joins("JOIN user_organizations uo ON u.id = uo.user_id").
		Where("uo.status = ?", domain.UserOrganizationStatusActive)

	if includeDescendants {
		// 获取组织的level_path
		var org domain.Organization
		if err := r.db.WithContext(ctx).Select("level_path").First(&org, orgID).Error; err != nil {
			return nil, err
		}

		// 包含所有后代组织的用户
		query = query.Joins("JOIN organizations o ON uo.organization_id = o.id").
			Where("o.id = ? OR o.level_path LIKE ?", orgID, *org.LevelPath+"/%")
	} else {
		query = query.Where("uo.organization_id = ?", orgID)
	}

	var users []*domain.User
	err := query.Find(&users).Error
	return users, err
}

// ExistsByUserAndOrganization 检查用户组织关联是否存在
func (r *userOrganizationRepository) ExistsByUserAndOrganization(ctx context.Context, userID uuid.UUID, orgID int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.UserOrganization{}).
		Where("user_id = ? AND organization_id = ?", userID, orgID).
		Count(&count).Error
	return count > 0, err
}

// HasPrimaryOrganization 检查用户是否有主要组织
func (r *userOrganizationRepository) HasPrimaryOrganization(ctx context.Context, userID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.UserOrganization{}).
		Where("user_id = ? AND is_primary = true", userID).
		Count(&count).Error
	return count > 0, err
}

// BatchAssignUsers 批量分配用户到组织
func (r *userOrganizationRepository) BatchAssignUsers(ctx context.Context, userIDs []uuid.UUID, orgID int64, role string, grantedBy *uuid.UUID) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, userID := range userIDs {
			// 检查是否已存在关联
			var count int64
			if err := tx.Model(&domain.UserOrganization{}).
				Where("user_id = ? AND organization_id = ?", userID, orgID).
				Count(&count).Error; err != nil {
				return err
			}

			if count > 0 {
				continue // 跳过已存在的关联
			}

			userOrg := &domain.UserOrganization{
				ID:             uuid.New(),
				UserID:         userID,
				OrganizationID: orgID,
				IsPrimary:      false,
				RoleInOrg:      &role,
				GrantedBy:      grantedBy,
				GrantedAt:      time.Now(),
				Status:         string(domain.UserOrganizationStatusActive),
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
			}

			if err := tx.Create(userOrg).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchRemoveUsers 批量从组织中移除用户
func (r *userOrganizationRepository) BatchRemoveUsers(ctx context.Context, userIDs []uuid.UUID, orgID int64) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Where("user_id IN ? AND organization_id = ?", userIDs, orgID).
		Delete(&domain.UserOrganization{}).Error
}

// GetUserOrganizationsByRole 根据角色获取用户组织关联
func (r *userOrganizationRepository) GetUserOrganizationsByRole(ctx context.Context, userID uuid.UUID, role string) ([]*domain.UserOrganization, error) {
	var userOrgs []*domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("Organization").
		Preload("Organization.OrgType").
		Where("user_id = ? AND role_in_org = ?", userID, role).
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetActiveUserOrganizations 获取活跃的用户组织关联
func (r *userOrganizationRepository) GetActiveUserOrganizations(ctx context.Context, userID uuid.UUID) ([]*domain.UserOrganization, error) {
	var userOrgs []*domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("Organization").
		Preload("Organization.OrgType").
		Where("user_id = ? AND status = ? AND (expires_at IS NULL OR expires_at > ?)", 
			userID, domain.UserOrganizationStatusActive, time.Now()).
		Find(&userOrgs).Error
	return userOrgs, err
}

// GetExpiredUserOrganizations 获取过期的用户组织关联
func (r *userOrganizationRepository) GetExpiredUserOrganizations(ctx context.Context) ([]*domain.UserOrganization, error) {
	var userOrgs []*domain.UserOrganization
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Organization").
		Where("expires_at IS NOT NULL AND expires_at <= ? AND status = ?", 
			time.Now(), domain.UserOrganizationStatusActive).
		Find(&userOrgs).Error
	return userOrgs, err
}

// clearPrimaryOrganization 清除用户的主要组织标记
func (r *userOrganizationRepository) clearPrimaryOrganization(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Model(&domain.UserOrganization{}).
		Where("user_id = ? AND is_primary = true", userID).
		Updates(map[string]interface{}{
			"is_primary":  false,
			"updated_at": time.Now(),
		}).Error
}
