package gorm

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
)

type userSystemAccessRepository struct {
	db *gorm.DB
}

// NewUserSystemAccessRepository 创建用户系统访问权限仓储实例
func NewUserSystemAccessRepository(db *gorm.DB) repository.UserSystemAccessRepository {
	return &userSystemAccessRepository{db: db}
}

// Create 创建用户系统访问权限
func (r *userSystemAccessRepository) Create(ctx context.Context, access *domain.UserSystemAccess) error {
	return r.db.WithContext(ctx).Create(access).Error
}

// GetByID 根据ID获取用户系统访问权限
func (r *userSystemAccessRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.UserSystemAccess, error) {
	var access domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("GrantedByUser").
		First(&access, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &access, nil
}

// GetByUserAndSystem 根据用户ID和系统代码获取访问权限
func (r *userSystemAccessRepository) GetByUserAndSystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*domain.UserSystemAccess, error) {
	var access domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("GrantedByUser").
		First(&access, "user_id = ? AND system_code = ?", userID, systemCode).Error
	if err != nil {
		return nil, err
	}
	return &access, nil
}

// GetByUserID 根据用户ID获取所有系统访问权限
func (r *userSystemAccessRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error) {
	var accesses []*domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("GrantedByUser").
		Find(&accesses, "user_id = ?", userID).Error
	return accesses, err
}

// GetActiveByUserID 根据用户ID获取所有活跃的系统访问权限
func (r *userSystemAccessRepository) GetActiveByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error) {
	var accesses []*domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("GrantedByUser").
		Where("user_id = ? AND status = ? AND (expires_at IS NULL OR expires_at > ?)", 
			userID, domain.UserSystemAccessStatusActive, time.Now()).
		Find(&accesses).Error
	return accesses, err
}

// GetBySystemCode 根据系统代码获取所有访问权限
func (r *userSystemAccessRepository) GetBySystemCode(ctx context.Context, systemCode domain.SystemCode) ([]*domain.UserSystemAccess, error) {
	var accesses []*domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("GrantedByUser").
		Find(&accesses, "system_code = ?", systemCode).Error
	return accesses, err
}

// Update 更新用户系统访问权限
func (r *userSystemAccessRepository) Update(ctx context.Context, access *domain.UserSystemAccess) error {
	access.UpdatedAt = time.Now()
	return r.db.WithContext(ctx).Save(access).Error
}

// Delete 删除用户系统访问权限
func (r *userSystemAccessRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&domain.UserSystemAccess{}, "id = ?", id).Error
}

// ExistsByUserAndSystem 检查用户是否已有指定系统的访问权限
func (r *userSystemAccessRepository) ExistsByUserAndSystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.UserSystemAccess{}).
		Where("user_id = ? AND system_code = ?", userID, systemCode).
		Count(&count).Error
	return count > 0, err
}

// HasSystemAccess 检查用户是否有指定系统的访问权限
func (r *userSystemAccessRepository) HasSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&domain.UserSystemAccess{}).
		Where("user_id = ? AND system_code = ? AND status = ? AND (expires_at IS NULL OR expires_at > ?)", 
			userID, systemCode, domain.UserSystemAccessStatusActive, time.Now()).
		Count(&count).Error
	return count > 0, err
}

// HasStationAccess 检查用户是否有指定站点的访问权限
func (r *userSystemAccessRepository) HasStationAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, stationID int64) (bool, error) {
	var access domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND system_code = ? AND status = ? AND (expires_at IS NULL OR expires_at > ?)", 
			userID, systemCode, domain.UserSystemAccessStatusActive, time.Now()).
		First(&access).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}
	
	return access.HasStationAccess(stationID), nil
}

// GrantSystemAccess 授予用户系统访问权限
func (r *userSystemAccessRepository) GrantSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType, scopeIDs []int64, grantedBy *uuid.UUID, expiresAt *time.Time) error {
	// 检查是否已存在
	exists, err := r.ExistsByUserAndSystem(ctx, userID, systemCode)
	if err != nil {
		return err
	}
	if exists {
		return fmt.Errorf("user already has access to system %s", systemCode)
	}

	access := &domain.UserSystemAccess{
		ID:          uuid.New(),
		UserID:      userID,
		SystemCode:  systemCode,
		AccessLevel: accessLevel,
		ScopeType:   scopeType,
		ScopeIDs:    scopeIDs,
		Status:      domain.UserSystemAccessStatusActive,
		GrantedBy:   grantedBy,
		GrantedAt:   time.Now(),
		ExpiresAt:   expiresAt,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return r.Create(ctx, access)
}

// RevokeSystemAccess 撤销用户系统访问权限
func (r *userSystemAccessRepository) RevokeSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND system_code = ?", userID, systemCode).
		Delete(&domain.UserSystemAccess{}).Error
}

// UpdateAccessLevel 更新用户系统访问级别
func (r *userSystemAccessRepository) UpdateAccessLevel(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel) error {
	return r.db.WithContext(ctx).
		Model(&domain.UserSystemAccess{}).
		Where("user_id = ? AND system_code = ?", userID, systemCode).
		Updates(map[string]interface{}{
			"access_level": accessLevel,
			"updated_at":   time.Now(),
		}).Error
}

// UpdateScopeIDs 更新用户系统访问范围
func (r *userSystemAccessRepository) UpdateScopeIDs(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, scopeIDs []int64) error {
	return r.db.WithContext(ctx).
		Model(&domain.UserSystemAccess{}).
		Where("user_id = ? AND system_code = ?", userID, systemCode).
		Updates(map[string]interface{}{
			"scope_ids":  scopeIDs,
			"updated_at": time.Now(),
		}).Error
}

// GetUsersBySystemAccess 根据系统访问权限获取用户列表
func (r *userSystemAccessRepository) GetUsersBySystemAccess(ctx context.Context, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType) ([]*domain.User, error) {
	var users []*domain.User
	err := r.db.WithContext(ctx).
		Table("users u").
		Select("u.*").
		Joins("JOIN user_system_access usa ON u.id = usa.user_id").
		Where("usa.system_code = ? AND usa.access_level = ? AND usa.scope_type = ? AND usa.status = ? AND (usa.expires_at IS NULL OR usa.expires_at > ?)", 
			systemCode, accessLevel, scopeType, domain.UserSystemAccessStatusActive, time.Now()).
		Find(&users).Error
	return users, err
}

// BatchGrantSystemAccess 批量授予用户系统访问权限
func (r *userSystemAccessRepository) BatchGrantSystemAccess(ctx context.Context, userIDs []uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType, scopeIDs []int64, grantedBy *uuid.UUID) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, userID := range userIDs {
			access := &domain.UserSystemAccess{
				ID:          uuid.New(),
				UserID:      userID,
				SystemCode:  systemCode,
				AccessLevel: accessLevel,
				ScopeType:   scopeType,
				ScopeIDs:    scopeIDs,
				Status:      domain.UserSystemAccessStatusActive,
				GrantedBy:   grantedBy,
				GrantedAt:   time.Now(),
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}
			if err := tx.Create(access).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchRevokeSystemAccess 批量撤销用户系统访问权限
func (r *userSystemAccessRepository) BatchRevokeSystemAccess(ctx context.Context, userIDs []uuid.UUID, systemCode domain.SystemCode) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Where("user_id IN ? AND system_code = ?", userIDs, systemCode).
		Delete(&domain.UserSystemAccess{}).Error
}

// GetExpiredAccess 获取已过期的访问权限
func (r *userSystemAccessRepository) GetExpiredAccess(ctx context.Context) ([]*domain.UserSystemAccess, error) {
	var accesses []*domain.UserSystemAccess
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Find(&accesses).Error
	return accesses, err
}

// CleanupExpiredAccess 清理已过期的访问权限
func (r *userSystemAccessRepository) CleanupExpiredAccess(ctx context.Context) error {
	return r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Delete(&domain.UserSystemAccess{}).Error
}
