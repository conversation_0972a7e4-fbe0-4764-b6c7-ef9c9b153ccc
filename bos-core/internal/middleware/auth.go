package middleware

import (
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
	"go.uber.org/zap"
)

// AuthConfig 认证中间件配置
type AuthConfig struct {
	Skipper    func(c echo.Context) bool // 跳过认证的路径判断函数
	JWTManager *utils.JWTManager         // JWT管理器
	SessionSvc service.SessionService    // 会话服务，用于验证会话状态
	Logger     *zap.Logger               // 日志记录器
}

// AuthMiddleware JWT认证中间件
func AuthMiddleware(config AuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// 1. 检查是否跳过认证
			if config.Skipper != nil && config.Skipper(c) {
				return next(c)
			}

			// 2. 获取Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				config.Logger.Warn("缺少Authorization header",
					zap.String("path", c.Request().URL.Path),
					zap.String("method", c.Request().Method),
				)
				return echo.NewHTTPError(http.StatusUnauthorized, "Missing authentication token")
			}

			// 3. 验证Token格式
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
				config.Logger.Warn("无效的Authorization header格式",
					zap.String("header", authHeader),
					zap.String("path", c.Request().URL.Path),
				)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid authentication token format")
			}

			tokenString := parts[1]

			// 4. 验证JWT Token
			claims, err := config.JWTManager.ValidateToken(tokenString)
			if err != nil {
				config.Logger.Warn("JWT令牌验证失败",
					zap.Error(err),
					zap.String("path", c.Request().URL.Path),
				)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid or expired token")
			}

			// 5. 验证令牌类型（只接受access token）
			if claims.TokenType != "access" {
				config.Logger.Warn("无效的令牌类型",
					zap.String("token_type", claims.TokenType),
					zap.String("user_id", claims.UserID),
				)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid token type")
			}

			// 6. 验证会话状态（可选，但推荐）
			if config.SessionSvc != nil && claims.SessionID != "" {
				ctx := c.Request().Context()
				isValid, err := config.SessionSvc.IsSessionValid(ctx, claims.SessionID)
				if err != nil {
					config.Logger.Error("会话验证失败",
						zap.Error(err),
						zap.String("session_id", claims.SessionID),
						zap.String("user_id", claims.UserID),
					)
					return echo.NewHTTPError(http.StatusInternalServerError, "Session validation failed")
				}

				if !isValid {
					config.Logger.Warn("会话已失效",
						zap.String("session_id", claims.SessionID),
						zap.String("user_id", claims.UserID),
					)
					return echo.NewHTTPError(http.StatusUnauthorized, "Session has expired")
				}
			}

			// 7. 将用户信息存入context，供后续handler使用
			c.Set("user_id", claims.UserID)
			c.Set("username", claims.Username)
			c.Set("email", claims.Email)
			c.Set("roles", claims.Roles)
			c.Set("session_id", claims.SessionID)
			c.Set("system", claims.System)
			c.Set("access_level", claims.AccessLevel)
			c.Set("scope_type", claims.ScopeType)
			c.Set("scope_ids", claims.ScopeIDs)
			c.Set("station_ids", claims.StationIDs) // 保持向后兼容性
			c.Set("permissions", claims.Permissions)

			// 8. 将完整的claims对象也存入context，供parseJWTClaims使用
			claimsMap := map[string]interface{}{
				"user_id":      claims.UserID,
				"username":     claims.Username,
				"email":        claims.Email,
				"roles":        claims.Roles,
				"session_id":   claims.SessionID,
				"token_type":   claims.TokenType,
				"system":       claims.System,
				"access_level": claims.AccessLevel,
				"scope_type":   claims.ScopeType,
				"scope_ids":    claims.ScopeIDs,
				"station_ids":  claims.StationIDs, // 保持向后兼容性
				"permissions":  claims.Permissions,
				"iss":          claims.Issuer,
				"sub":          claims.Subject,
				"aud":          claims.Audience,
				"exp":          claims.ExpiresAt,
				"nbf":          claims.NotBefore,
				"iat":          claims.IssuedAt,
				"jti":          claims.ID,
			}
			c.Set("claims", claimsMap)

			// 9. 记录成功的认证日志
			config.Logger.Debug("用户认证成功",
				zap.String("user_id", claims.UserID),
				zap.String("username", claims.Username),
				zap.String("path", c.Request().URL.Path),
				zap.String("method", c.Request().Method),
			)

			return next(c)
		}
	}
}

// DefaultSkipper 默认跳过认证的路径
func DefaultSkipper(c echo.Context) bool {
	path := c.Path()
	method := c.Request().Method

	// 跳过认证的路径
	skipPaths := map[string][]string{
		"/auth/login":   {"POST"},
		"/auth/refresh": {"POST"},
		"/auth/health":  {"GET"},
		"/health":       {"GET"},
	}

	if methods, exists := skipPaths[path]; exists {
		for _, m := range methods {
			if method == m {
				return true
			}
		}
	}

	return false
}
