package service

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// UserSystemAccessService 用户系统访问权限服务接口
type UserSystemAccessService interface {
	// GrantSystemAccess 授予用户系统访问权限
	GrantSystemAccess(ctx context.Context, req *GrantSystemAccessRequest) error

	// RevokeSystemAccess 撤销用户系统访问权限
	RevokeSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) error

	// GetUserSystemAccess 获取用户系统访问权限
	GetUserSystemAccess(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error)

	// GetActiveUserSystemAccess 获取用户活跃的系统访问权限
	GetActiveUserSystemAccess(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error)

	// GetUserSystemAccessBySystem 获取用户在指定系统的访问权限
	GetUserSystemAccessBySystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*domain.UserSystemAccess, error)

	// HasSystemAccess 检查用户是否有系统访问权限
	HasSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (bool, error)

	// HasStationAccess 检查用户是否有站点访问权限
	HasStationAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, stationID int64) (bool, error)

	// UpdateAccessLevel 更新用户系统访问级别
	UpdateAccessLevel(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel) error

	// UpdateScopeIDs 更新用户系统访问范围
	UpdateScopeIDs(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, scopeIDs []int64) error

	// GetSystemAccessInfo 获取用户系统访问信息（用于JWT）
	GetSystemAccessInfo(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*domain.SystemAccessInfo, error)

	// ValidateSystemLogin 验证用户系统登录权限
	ValidateSystemLogin(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*SystemLoginValidationResult, error)

	// BatchGrantSystemAccess 批量授予用户系统访问权限
	BatchGrantSystemAccess(ctx context.Context, req *BatchGrantSystemAccessRequest) error

	// BatchRevokeSystemAccess 批量撤销用户系统访问权限
	BatchRevokeSystemAccess(ctx context.Context, userIDs []uuid.UUID, systemCode domain.SystemCode) error

	// GetUsersBySystemAccess 根据系统访问权限获取用户列表
	GetUsersBySystemAccess(ctx context.Context, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType) ([]*domain.User, error)

	// CleanupExpiredAccess 清理过期的访问权限
	CleanupExpiredAccess(ctx context.Context) error
}

// GrantSystemAccessRequest 授予系统访问权限请求
type GrantSystemAccessRequest struct {
	UserID      uuid.UUID               `json:"user_id" validate:"required"`
	SystemCode  domain.SystemCode       `json:"system_code" validate:"required"`
	AccessLevel domain.AccessLevel      `json:"access_level" validate:"required"`
	ScopeType   domain.ScopeType        `json:"scope_type" validate:"required"`
	ScopeIDs    []int64                 `json:"scope_ids"`
	GrantedBy   *uuid.UUID              `json:"granted_by"`
	ExpiresAt   *time.Time              `json:"expires_at"`
}

// BatchGrantSystemAccessRequest 批量授予系统访问权限请求
type BatchGrantSystemAccessRequest struct {
	UserIDs     []uuid.UUID             `json:"user_ids" validate:"required"`
	SystemCode  domain.SystemCode       `json:"system_code" validate:"required"`
	AccessLevel domain.AccessLevel      `json:"access_level" validate:"required"`
	ScopeType   domain.ScopeType        `json:"scope_type" validate:"required"`
	ScopeIDs    []int64                 `json:"scope_ids"`
	GrantedBy   *uuid.UUID              `json:"granted_by"`
	ExpiresAt   *time.Time              `json:"expires_at"`
}

// SystemLoginValidationResult 系统登录验证结果
type SystemLoginValidationResult struct {
	HasAccess    bool                     `json:"has_access"`
	AccessLevel  domain.AccessLevel       `json:"access_level"`
	ScopeType    domain.ScopeType         `json:"scope_type"`
	ScopeIDs     []int64                  `json:"scope_ids"`
	StationCount int                      `json:"station_count"`
	Permissions  []string                 `json:"permissions"`
	ErrorMessage string                   `json:"error_message,omitempty"`
}

// SystemPermissionService 系统权限服务接口
type SystemPermissionService interface {
	// CreatePermission 创建系统权限
	CreatePermission(ctx context.Context, req *CreateSystemPermissionRequest) (*domain.SystemPermission, error)

	// GetPermission 获取系统权限
	GetPermission(ctx context.Context, id uuid.UUID) (*domain.SystemPermission, error)

	// GetPermissionByCode 根据权限代码获取系统权限
	GetPermissionByCode(ctx context.Context, systemCode domain.SystemCode, permissionCode string) (*domain.SystemPermission, error)

	// GetSystemPermissions 获取系统权限列表
	GetSystemPermissions(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error)

	// GetActiveSystemPermissions 获取活跃的系统权限列表
	GetActiveSystemPermissions(ctx context.Context, systemCode domain.SystemCode) ([]*domain.SystemPermission, error)

	// UpdatePermission 更新系统权限
	UpdatePermission(ctx context.Context, id uuid.UUID, req *UpdateSystemPermissionRequest) error

	// DeletePermission 删除系统权限
	DeletePermission(ctx context.Context, id uuid.UUID) error

	// GrantUserPermission 授予用户系统权限
	GrantUserPermission(ctx context.Context, userID, permissionID uuid.UUID, grantedBy *uuid.UUID, expiresAt *time.Time) error

	// RevokeUserPermission 撤销用户系统权限
	RevokeUserPermission(ctx context.Context, userID, permissionID uuid.UUID) error

	// GetUserPermissions 获取用户系统权限
	GetUserPermissions(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) ([]*domain.SystemPermission, error)

	// GetUserPermissionCodes 获取用户权限代码列表
	GetUserPermissionCodes(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) ([]string, error)

	// HasPermission 检查用户是否有指定权限
	HasPermission(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, permissionCode string) (bool, error)

	// BatchGrantUserPermissions 批量授予用户系统权限
	BatchGrantUserPermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID, grantedBy *uuid.UUID, expiresAt *time.Time) error

	// BatchRevokeUserPermissions 批量撤销用户系统权限
	BatchRevokeUserPermissions(ctx context.Context, userID uuid.UUID, permissionIDs []uuid.UUID) error

	// InitializeSystemPermissions 初始化系统权限
	InitializeSystemPermissions(ctx context.Context, systemCode domain.SystemCode) error
}

// CreateSystemPermissionRequest 创建系统权限请求
type CreateSystemPermissionRequest struct {
	SystemCode     domain.SystemCode       `json:"system_code" validate:"required"`
	PermissionCode string                  `json:"permission_code" validate:"required"`
	PermissionName string                  `json:"permission_name" validate:"required"`
	ScopeType      domain.ScopeType        `json:"scope_type" validate:"required"`
	Resource       string                  `json:"resource" validate:"required"`
	Action         domain.PermissionAction `json:"action" validate:"required"`
	Description    *string                 `json:"description"`
}

// UpdateSystemPermissionRequest 更新系统权限请求
type UpdateSystemPermissionRequest struct {
	PermissionName *string                  `json:"permission_name"`
	ScopeType      *domain.ScopeType        `json:"scope_type"`
	Resource       *string                  `json:"resource"`
	Action         *domain.PermissionAction `json:"action"`
	Description    *string                  `json:"description"`
	Status         *domain.SystemPermissionStatus `json:"status"`
}
