package service

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// AuthService 认证服务接口
type AuthService interface {
	// 用户认证
	Login(ctx context.Context, username, password, system, ipAddress, userAgent string) (*LoginResult, error)
	LoginWithOrganization(ctx context.Context, req *OrganizationLoginRequest) (*LoginResult, error)
	Logout(ctx context.Context, token string) error
	RefreshToken(ctx context.Context, refreshToken string) (*TokenResult, error)

	// 令牌验证
	ValidateToken(ctx context.Context, token string) (*domain.User, error)
	ValidateTokenWithContext(ctx context.Context, token string) (*AuthContext, error)

	// 密码管理
	ChangePassword(ctx context.Context, userID uuid.UUID, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, userID uuid.UUID, newPassword string) error

	// 会话管理
	GetUserSessions(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error)
	TerminateSession(ctx context.Context, sessionID uuid.UUID) error
	TerminateAllSessions(ctx context.Context, userID uuid.UUID, excludeSessionID *uuid.UUID) error

	// 组织认证
	ValidateOrganizationAccess(ctx context.Context, userID uuid.UUID, organizationID int64, requiredRole string) (bool, error)
	GetUserOrganizationContext(ctx context.Context, userID uuid.UUID) (*OrganizationContext, error)
	SwitchOrganizationContext(ctx context.Context, userID uuid.UUID, organizationID int64) (*OrganizationContext, error)

	// SSO集成
	InitiateSSO(ctx context.Context, req *InitiateSSORequest) (*SSOInitiationResponse, error)
	ValidateSSO(ctx context.Context, req *ValidateSSORequest) (*SSOValidationResponse, error)
	GetSSOToken(ctx context.Context, userID uuid.UUID, targetSystem string) (*SSOTokenInfo, error)
}

// SystemAccessService 系统访问控制服务接口
type SystemAccessService interface {
	// 验证用户是否可以访问指定系统
	CanAccessSystem(userRoles []domain.UserStationRole, system string) bool

	// 获取用户在指定系统的最高权限级别
	GetSystemAccessLevel(userRoles []domain.UserStationRole, system string) string

	// 获取用户在指定系统下可管理的站点ID
	GetSystemStationIDs(userRoles []domain.UserStationRole, system string) []int64

	// 获取用户可访问的系统列表
	GetAccessibleSystems(userRoles []domain.UserStationRole) []string

	// 验证系统标识是否有效
	IsValidSystem(system string) bool

	// 验证系统访问请求
	ValidateSystemAccessRequest(userRoles []domain.UserStationRole, system string) error
}

// UserService 用户服务接口
type UserService interface {
	// 用户管理
	CreateUser(ctx context.Context, req *CreateUserRequest) (*domain.User, error)
	GetUser(ctx context.Context, userID uuid.UUID) (*domain.User, error)
	GetUserByUsername(ctx context.Context, username string) (*domain.User, error)
	GetUserByEmail(ctx context.Context, email string) (*domain.User, error)
	UpdateUser(ctx context.Context, userID uuid.UUID, req *UpdateUserRequest) (*domain.User, error)
	DeleteUser(ctx context.Context, userID uuid.UUID) error

	// 用户列表
	ListUsers(ctx context.Context, req *ListUsersRequest) (*ListUsersResponse, error)

	// 用户状态管理
	EnableUser(ctx context.Context, userID uuid.UUID) error
	DisableUser(ctx context.Context, userID uuid.UUID) error
	LockUser(ctx context.Context, userID uuid.UUID, lockDuration time.Duration) error
	UnlockUser(ctx context.Context, userID uuid.UUID) error

	// 角色管理
	AssignRoles(ctx context.Context, userID uuid.UUID, roleIDs []uuid.UUID) error
	RemoveRoles(ctx context.Context, userID uuid.UUID, roleIDs []uuid.UUID) error
	GetUserRoles(ctx context.Context, userID uuid.UUID) ([]*domain.Role, error)
	GetUserPermissions(ctx context.Context, userID uuid.UUID) ([]*domain.Permission, error)
}

// RoleService 角色服务接口
type RoleService interface {
	// 角色管理
	CreateRole(ctx context.Context, req *CreateRoleRequest) (*domain.Role, error)
	GetRole(ctx context.Context, roleID uuid.UUID) (*domain.Role, error)
	GetRoleByName(ctx context.Context, name string) (*domain.Role, error)
	UpdateRole(ctx context.Context, roleID uuid.UUID, req *UpdateRoleRequest) (*domain.Role, error)
	DeleteRole(ctx context.Context, roleID uuid.UUID) error

	// 角色列表
	ListRoles(ctx context.Context, req *ListRolesRequest) (*ListRolesResponse, error)

	// 权限管理
	AssignPermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	RemovePermissions(ctx context.Context, roleID uuid.UUID, permissionIDs []uuid.UUID) error
	GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]*domain.Permission, error)
	GetAllPermissions(ctx context.Context, roleID uuid.UUID) ([]*domain.Permission, error)

	// 用户管理
	GetRoleUsers(ctx context.Context, roleID uuid.UUID) ([]*domain.User, error)
}

// PermissionService 权限服务接口
type PermissionService interface {
	// 权限管理
	CreatePermission(ctx context.Context, req *CreatePermissionRequest) (*domain.Permission, error)
	GetPermission(ctx context.Context, permissionID uuid.UUID) (*domain.Permission, error)
	GetPermissionByCode(ctx context.Context, code string) (*domain.Permission, error)
	UpdatePermission(ctx context.Context, permissionID uuid.UUID, req *UpdatePermissionRequest) (*domain.Permission, error)
	DeletePermission(ctx context.Context, permissionID uuid.UUID) error

	// 权限列表
	ListPermissions(ctx context.Context, req *ListPermissionsRequest) (*ListPermissionsResponse, error)
	GetPermissionTree(ctx context.Context) ([]*domain.Permission, error)

	// 权限验证
	CheckPermission(ctx context.Context, userID uuid.UUID, permissionCode string, resource string) (bool, error)
	CheckPermissions(ctx context.Context, userID uuid.UUID, permissionCodes []string, resource string) (map[string]bool, error)

	// 权限关联
	GetPermissionRoles(ctx context.Context, permissionID uuid.UUID) ([]*domain.Role, error)
}

// SessionService 会话服务接口
type SessionService interface {
	// 会话管理
	CreateSession(ctx context.Context, userID uuid.UUID, ipAddress, userAgent string) (*domain.Session, error)
	GetSession(ctx context.Context, sessionID uuid.UUID) (*domain.Session, error)
	GetSessionBySessionID(ctx context.Context, sessionID string) (*domain.Session, error)
	GetSessionByToken(ctx context.Context, token string) (*domain.Session, error)
	UpdateSession(ctx context.Context, sessionID uuid.UUID, req *UpdateSessionRequest) (*domain.Session, error)
	TerminateSession(ctx context.Context, sessionID uuid.UUID) error

	// 会话验证
	IsSessionValid(ctx context.Context, sessionID string) (bool, error)

	// 会话列表
	ListSessions(ctx context.Context, req *ListSessionsRequest) (*ListSessionsResponse, error)
	GetUserSessions(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error)

	// 会话清理
	CleanupExpiredSessions(ctx context.Context) error
	TerminateUserSessions(ctx context.Context, userID uuid.UUID, excludeSessionID *uuid.UUID) error
}

// LogService 日志服务接口
type LogService interface {
	// 登录日志
	CreateLoginLog(ctx context.Context, req *CreateLoginLogRequest) error
	GetLoginLogs(ctx context.Context, req *ListLogsRequest) (*ListLoginLogsResponse, error)
	GetLoginStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)

	// 权限日志
	CreatePermissionLog(ctx context.Context, req *CreatePermissionLogRequest) error
	GetPermissionLogs(ctx context.Context, req *ListLogsRequest) (*ListPermissionLogsResponse, error)
	GetPermissionStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)

	// 操作日志
	CreateOperationLog(ctx context.Context, req *CreateOperationLogRequest) error
	GetOperationLogs(ctx context.Context, req *ListLogsRequest) (*ListOperationLogsResponse, error)
	GetOperationStats(ctx context.Context, startTime, endTime time.Time) (map[string]interface{}, error)
}

// OperationLogsService 操作日志服务接口，基于增强的日志模型
type OperationLogsService interface {
	// 基础日志记录
	RecordOperation(ctx context.Context, req *domain.LogRequest) (string, error)
	RecordOperationAsync(ctx context.Context, req *domain.LogRequest) error
	RecordBatch(ctx context.Context, logs []*domain.LogRequest) (*domain.BatchOperationResult, error)

	// 便捷方法
	RecordLogin(ctx context.Context, userID, ipAddress, result string) error
	RecordConfigChange(ctx context.Context, userID, configKey string, changes map[string]interface{}) error
	RecordSecurityEvent(ctx context.Context, userID, eventType, description string) error

	// 日志查询
	QueryLogs(ctx context.Context, req *domain.LogQueryRequest) (*domain.PaginatedLogResponse, error)
	GetLogByID(ctx context.Context, logID string) (*domain.NewOperationLog, error)
	GetLogsByUser(ctx context.Context, userID string, req *domain.LogQueryRequest) (*domain.PaginatedLogResponse, error)
	GetLogsByResource(ctx context.Context, resourceType, resourceID string, req *domain.LogQueryRequest) (*domain.PaginatedLogResponse, error)

	// 统计分析
	GetStatistics(ctx context.Context, req *domain.StatisticsRequest) (*domain.StatisticsResponse, error)
	GetSummaryStats(ctx context.Context, req *domain.StatisticsRequest) (*domain.SummaryStats, error)
	GetTimeSeriesStats(ctx context.Context, req *domain.StatisticsRequest) ([]*domain.TimeSeriesStats, error)
	GetModuleBreakdown(ctx context.Context, req *domain.StatisticsRequest) ([]*domain.ModuleBreakdown, error)

	// 数据管理
	CleanupLogs(ctx context.Context, retentionDays int) (*domain.CleanupResult, error)
	DeleteLogsByTimeRange(ctx context.Context, startTime, endTime time.Time) error

	// 导出功能
	ExportLogs(ctx context.Context, req *domain.ExportRequest) (*domain.ExportTask, error)
	GetExportStatus(ctx context.Context, taskID string) (*domain.ExportTask, error)
}

// StationService 站点服务接口
type StationService interface {
	// 站点管理
	CreateStation(ctx context.Context, req *CreateStationRequest) (*domain.Station, error)
	GetStation(ctx context.Context, stationID int64) (*domain.Station, error)
	GetStationBySiteCode(ctx context.Context, siteCode string) (*domain.Station, error)
	UpdateStation(ctx context.Context, stationID int64, req *UpdateStationRequest) (*domain.Station, error)
	DeleteStation(ctx context.Context, stationID int64) error

	// 站点列表
	ListStations(ctx context.Context, req *ListStationsRequest) (*ListStationsResponse, error)

	// 站点状态管理
	UpdateStationStatus(ctx context.Context, stationID int64, status domain.BusinessStatus, version int) error
}

// EquipmentService 设备服务接口
type EquipmentService interface {
	// 设备管理
	CreateStationEquipment(ctx context.Context, req *CreateStationEquipmentRequest) (*domain.StationEquipment, error)
	GetStationEquipment(ctx context.Context, equipmentID int64) (*domain.StationEquipment, error)
	UpdateStationEquipment(ctx context.Context, equipmentID int64, req *UpdateStationEquipmentRequest) (*domain.StationEquipment, error)
	DeleteStationEquipment(ctx context.Context, equipmentID int64) error
	ListStationEquipments(ctx context.Context, req *ListStationEquipmentsRequest) (*ListStationEquipmentsResponse, error)
	UpdateEquipmentStatus(ctx context.Context, equipmentID int64, status domain.EquipmentStatus, version int) error

	// 设备维护记录管理
	CreateMaintenanceRecord(ctx context.Context, req *CreateMaintenanceRecordRequest) (*domain.EquipmentMaintenanceRecord, error)
	GetMaintenanceRecord(ctx context.Context, recordID int64) (*domain.EquipmentMaintenanceRecord, error)
	UpdateMaintenanceRecord(ctx context.Context, recordID int64, req *UpdateMaintenanceRecordRequest) (*domain.EquipmentMaintenanceRecord, error)
	ListMaintenanceRecords(ctx context.Context, req *ListMaintenanceRecordsRequest) (*ListMaintenanceRecordsResponse, error)

	// 运营参数管理
	CreateOperationParam(ctx context.Context, req *CreateOperationParamRequest) (*domain.OperationParam, error)
	GetOperationParam(ctx context.Context, paramID int64) (*domain.OperationParam, error)
	GetOperationParamByKey(ctx context.Context, stationID int64, category, key string) (*domain.OperationParam, error)
	UpdateOperationParam(ctx context.Context, paramID int64, req *UpdateOperationParamRequest) (*domain.OperationParam, error)
	DeleteOperationParam(ctx context.Context, paramID int64) error
	ListOperationParams(ctx context.Context, req *ListOperationParamsRequest) (*ListOperationParamsResponse, error)
	GetParamChangeHistory(ctx context.Context, paramID int64, req *ListParamChangeHistoryRequest) (*ListParamChangeHistoryResponse, error)
}

// ConfigService 配置管理服务接口
type ConfigService interface {
	// 配置项管理
	CreateConfigItem(ctx context.Context, req *CreateConfigItemRequest) (*domain.ConfigItem, error)
	GetConfigItem(ctx context.Context, itemID int64) (*domain.ConfigItem, error)
	GetConfigItemByKey(ctx context.Context, key string) (*GetConfigValueResponse, error)
	UpdateConfigItem(ctx context.Context, itemID int64, req *UpdateConfigItemRequest) (*domain.ConfigItem, error)
	DeleteConfigItem(ctx context.Context, itemID int64) error

	// 配置项列表和查询
	ListConfigItems(ctx context.Context, req *ListConfigItemsRequest) (*ListConfigItemsResponse, error)
	SearchConfigItems(ctx context.Context, req *SearchConfigItemsRequest) (*SearchConfigItemsResponse, error)
	GetConfigItemsByCategory(ctx context.Context, category string, req *ListConfigItemsRequest) (*ListConfigItemsResponse, error)

	// 批量操作
	BatchCreateConfigItems(ctx context.Context, req *BatchCreateConfigItemsRequest) (*BatchCreateConfigItemsResponse, error)
	BatchUpdateConfigItems(ctx context.Context, req *BatchUpdateConfigItemsRequest) (*BatchUpdateConfigItemsResponse, error)
	BatchDeleteConfigItems(ctx context.Context, itemIDs []int64) error
	BatchGetConfigValues(ctx context.Context, req *BatchGetConfigValuesRequest) (*BatchGetConfigValuesResponse, error)

	// 配置分类管理
	GetCategories(ctx context.Context) (*GetCategoriesResponse, error)
	GetCategoryStats(ctx context.Context) (*CategoryStatsResponse, error)
	GetDataTypeStats(ctx context.Context) (*DataTypeStatsResponse, error)

	// 配置日志管理
	CreateConfigLog(ctx context.Context, req *CreateConfigLogRequest) (*domain.ConfigLog, error)
	GetConfigLog(ctx context.Context, logID int64) (*domain.ConfigLog, error)
	ListConfigLogs(ctx context.Context, req *ListConfigLogsRequest) (*ListConfigLogsResponse, error)
	GetConfigItemLogs(ctx context.Context, itemID int64, req *ListConfigLogsRequest) (*ListConfigLogsResponse, error)

	// 审批管理
	SubmitConfigLogForApproval(ctx context.Context, logID int64, req *SubmitApprovalRequest) (*domain.ConfigLog, error)
	ApproveConfigLog(ctx context.Context, logID int64, req *ApprovalRequest) (*domain.ConfigLog, error)
	RejectConfigLog(ctx context.Context, logID int64, req *ApprovalRequest) (*domain.ConfigLog, error)

	// 统计分析
	GetConfigChangeStats(ctx context.Context, req *ConfigStatsRequest) (*ConfigStatsResponse, error)
	GetApprovalStats(ctx context.Context, req *ApprovalStatsRequest) (*ApprovalStatsResponse, error)
}

// DictCategoryService 数据字典分类服务接口
type DictCategoryService interface {
	// 分类管理
	CreateCategory(ctx context.Context, req *CreateDictCategoryRequest) (*domain.DictCategory, error)
	GetCategory(ctx context.Context, id int64) (*domain.DictCategory, error)
	GetCategoryByCode(ctx context.Context, code string) (*domain.DictCategory, error)
	UpdateCategory(ctx context.Context, id int64, req *UpdateDictCategoryRequest) (*domain.DictCategory, error)
	DeleteCategory(ctx context.Context, id int64) error

	// 分类列表
	ListCategories(ctx context.Context, req *ListDictCategoriesRequest) (*ListDictCategoriesResponse, error)
	SearchCategories(ctx context.Context, keyword string, req *ListDictCategoriesRequest) (*ListDictCategoriesResponse, error)

	// 分类统计
	GetCategoryStats(ctx context.Context) ([]*domain.DictCategory, error)
}

// DictItemService 数据字典项目服务接口
type DictItemService interface {
	// 项目管理
	CreateItem(ctx context.Context, req *CreateDictItemRequest) (*domain.DictItem, error)
	GetItem(ctx context.Context, id int64) (*domain.DictItem, error)
	GetItemByCode(ctx context.Context, categoryID int64, code string) (*domain.DictItem, error)
	UpdateItem(ctx context.Context, id int64, req *UpdateDictItemRequest) (*domain.DictItem, error)
	DeleteItem(ctx context.Context, id int64) error

	// 项目列表
	ListItems(ctx context.Context, req *ListDictItemsRequest) (*ListDictItemsResponse, error)
	GetItemsByCategory(ctx context.Context, categoryID int64, req *ListDictItemsRequest) (*ListDictItemsResponse, error)
	SearchItems(ctx context.Context, keyword string, req *ListDictItemsRequest) (*ListDictItemsResponse, error)

	// 批量操作
	BatchCreateItems(ctx context.Context, req *BatchCreateDictItemsRequest) (*BatchCreateDictItemsResponse, error)
	BatchUpdateItems(ctx context.Context, req *BatchUpdateDictItemsRequest) (*BatchUpdateDictItemsResponse, error)
	BatchDeleteItems(ctx context.Context, ids []int64) error
}

// DictValueService 数据字典值服务接口
type DictValueService interface {
	// 值管理
	CreateValue(ctx context.Context, req *CreateDictValueRequest) (*domain.DictValue, error)
	GetValue(ctx context.Context, id int64) (*domain.DictValue, error)
	GetValueByValue(ctx context.Context, itemID int64, value string) (*domain.DictValue, error)
	UpdateValue(ctx context.Context, id int64, req *UpdateDictValueRequest) (*domain.DictValue, error)
	DeleteValue(ctx context.Context, id int64) error

	// 值列表
	ListValues(ctx context.Context, req *ListDictValuesRequest) (*ListDictValuesResponse, error)
	GetValuesByItem(ctx context.Context, itemID int64, req *ListDictValuesRequest) (*ListDictValuesResponse, error)
	SearchValues(ctx context.Context, keyword string, req *ListDictValuesRequest) (*ListDictValuesResponse, error)

	// 批量操作
	BatchCreateValues(ctx context.Context, req *BatchCreateDictValuesRequest) (*BatchCreateDictValuesResponse, error)
	BatchUpdateValues(ctx context.Context, req *BatchUpdateDictValuesRequest) (*BatchUpdateDictValuesResponse, error)
	BatchDeleteValues(ctx context.Context, ids []int64) error

	// 同步管理
	GetValuesBySource(ctx context.Context, sourceSystem string, req *ListDictValuesRequest) (*ListDictValuesResponse, error)
	UpdateSyncInfo(ctx context.Context, id int64, syncID string) error
}

// DictQueryService 数据字典查询服务接口
type DictQueryService interface {
	// 通用查询
	QueryDict(ctx context.Context, req *domain.DictQueryRequest) ([]*domain.DictTreeValue, error)
	BatchQueryDict(ctx context.Context, req *domain.DictBatchQueryRequest) (map[string][]*domain.DictTreeValue, error)
	QueryDictTree(ctx context.Context, categoryCode string, maxDepth int) ([]*domain.DictTreeNode, error)

	// 验证查询
	ValidateValues(ctx context.Context, req *domain.DictValidationRequest) (*domain.DictValidationResponse, error)

	// 完整数据查询
	GetFullDictData(ctx context.Context, categoryCode string, includeInactive bool) ([]*domain.DictTreeNode, error)
	GetCategoryWithItems(ctx context.Context, categoryCode string) (*domain.DictCategory, error)
	GetItemWithValues(ctx context.Context, categoryCode, itemCode string) (*domain.DictItem, error)
}

// DictSyncService 数据字典同步服务接口
type DictSyncService interface {
	// HOS同步
	SyncHOSData(ctx context.Context, req *domain.DictSyncRequest) (*SyncHOSDataResponse, error)
	GetSyncStatus(ctx context.Context, syncID string) (*domain.DictSyncStatus, error)

	// 同步历史
	GetSyncHistory(ctx context.Context, req *ListSyncHistoryRequest) (*ListSyncHistoryResponse, error)
	CleanupOldSyncRecords(ctx context.Context, retentionDays int) error

	// 表结构同步
	SyncHOSSchemas(ctx context.Context, req *SyncHOSSchemasRequest) (*SyncHOSSchemasResponse, error)
}

// DictCacheService 数据字典缓存服务接口
type DictCacheService interface {
	// 缓存管理
	RefreshCache(ctx context.Context, req *domain.DictCacheRefreshRequest) (*domain.DictCacheRefreshResponse, error)
	GetCacheStats(ctx context.Context) (*domain.DictCacheStats, error)
	ClearCache(ctx context.Context, pattern string) error

	// 缓存预热
	WarmupCache(ctx context.Context, categoryCode string) error
	WarmupAllCache(ctx context.Context) error
}

// DictStatsService 数据字典统计服务接口
type DictStatsService interface {
	// 使用统计
	GetUsageStats(ctx context.Context, req *GetUsageStatsRequest) (*domain.DictUsageStats, error)
	GetTrendData(ctx context.Context, req *GetTrendDataRequest) (*domain.DictTrendData, error)

	// 数据统计
	GetDataStats(ctx context.Context) (map[string]interface{}, error)
	RecordQuery(ctx context.Context, categoryCode, itemCode string) error
}

// DictImportExportService 数据字典导入导出服务接口
type DictImportExportService interface {
	// 导出操作
	ExportDict(ctx context.Context, req *domain.DictExportRequest) (*domain.DictExportResponse, error)
	GetExportFile(ctx context.Context, fileName string) ([]byte, error)

	// 导入操作
	ImportDict(ctx context.Context, req *DictImportRequest, fileData []byte) (*domain.DictImportStatus, error)
	GetImportStatus(ctx context.Context, importID string) (*domain.DictImportStatus, error)

	// 文件管理
	CleanupExpiredFiles(ctx context.Context) error
}

// ServiceManager 服务管理器接口
// UserStationService 用户站点关联服务接口
type UserStationService interface {
	// 站点分配
	AssignUserToStation(ctx context.Context, req *AssignUserToStationRequest) error
	RemoveUserFromStation(ctx context.Context, userID uuid.UUID, stationID int64) error
	
	// 查询方法
	GetUserStations(ctx context.Context, userID uuid.UUID, roleTypes []domain.UserStationRoleType) ([]*domain.Station, error)
	GetStationManagers(ctx context.Context, stationID int64) ([]*domain.User, error)
	GetStationUsers(ctx context.Context, stationID int64, roleTypes []domain.UserStationRoleType) ([]*domain.User, error)
	GetStationUsersDetail(ctx context.Context, stationID int64, roleTypes []domain.UserStationRoleType) ([]*domain.UserStationRole, error)
	GetUserStationRole(ctx context.Context, userID uuid.UUID, stationID int64) (*domain.UserStationRole, error)
	
	// 权限检查
	CanUserManageStation(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)
	CanUserOperateStation(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)
	HasStationAccess(ctx context.Context, userID uuid.UUID, stationID int64) (bool, error)
	
	// 批量操作
	BatchAssignStations(ctx context.Context, userID uuid.UUID, stationIDs []int64, roleType domain.UserStationRoleType) error
	BatchRemoveStations(ctx context.Context, userID uuid.UUID, stationIDs []int64) error
	
	// 角色管理
	UpdateUserStationRole(ctx context.Context, userID uuid.UUID, stationID int64, roleType domain.UserStationRoleType) error
	UpdateUserStationStatus(ctx context.Context, userID uuid.UUID, stationID int64, status domain.UserStationRoleStatus) error
	TransferStationManagement(ctx context.Context, fromUserID, toUserID uuid.UUID, stationID int64) error
	
	// 列表查询
	ListUserStationRoles(ctx context.Context, req *ListUserStationRolesRequest) (*ListUserStationRolesResponse, error)
	
	// 过期管理
	SetRoleExpiration(ctx context.Context, userID uuid.UUID, stationID int64, expiresAt *time.Time) error
	CleanupExpiredRoles(ctx context.Context) (int64, error)
	
	// 统计查询
	GetUserStationCount(ctx context.Context, userID uuid.UUID) (int64, error)
	GetStationUserCount(ctx context.Context, stationID int64) (int64, error)
	GetRoleTypeStats(ctx context.Context, stationID *int64) (map[string]int64, error)
}

type ServiceManager interface {
	Auth() AuthService
	User() UserService
	Role() RoleService
	Permission() PermissionService
	Session() SessionService
	Log() LogService
	Station() StationService
	Equipment() EquipmentService
	Config() ConfigService

	// 操作日志服务
	OperationLogs() OperationLogsService

	// 用户站点关联服务
	UserStation() UserStationService

	// 系统访问控制服务
	SystemAccess() SystemAccessService

	// 用户系统访问权限服务
	UserSystemAccess() UserSystemAccessService

	// 数据字典服务
	DictCategory() DictCategoryService
	DictItem() DictItemService
	DictValue() DictValueService
	DictQuery() DictQueryService
	DictSync() DictSyncService
	DictCache() DictCacheService
	DictStats() DictStatsService
	DictImportExport() DictImportExportService

	// 标签系统服务
	TagGroup() TagGroupService
	Tag() TagService
	EntityTag() EntityTagService
	TagStatistics() TagStatisticsService

	// 组织管理服务
	Organization() OrganizationService
}

// 请求和响应结构体

// LoginResult 登录结果
type LoginResult struct {
	User         *domain.User     `json:"user"`
	AccessToken  string           `json:"access_token"`
	RefreshToken string           `json:"refresh_token"`
	ExpiresAt    time.Time        `json:"expires_at"`
	SessionID    uuid.UUID        `json:"session_id"`
	StationIDs   []int64          `json:"station_ids"`   // 用户关联的站点ID列表
	SystemAccess *SystemAccessInfo `json:"system_access"` // 系统访问信息
}

// SystemAccessInfo 系统访问信息
type SystemAccessInfo struct {
	System       string  `json:"system"`        // 当前登录的系统 (BOS/EDC)
	AccessLevel  string  `json:"access_level"`  // 用户在该系统的最高权限级别
	StationIDs   []int64 `json:"station_ids"`   // 在该系统下可管理的站点
	StationCount int     `json:"station_count"` // 站点数量
}

// TokenResult 令牌结果
type TokenResult struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// OrganizationLoginRequest 组织登录请求
type OrganizationLoginRequest struct {
	Username       string `json:"username" validate:"required"`
	Password       string `json:"password" validate:"required"`
	OrganizationID *int64 `json:"organization_id"`
	System         string `json:"system" validate:"required"`
	IPAddress      string `json:"ip_address"`
	UserAgent      string `json:"user_agent"`
}

// AuthContext 认证上下文
type AuthContext struct {
	User                *domain.User                    `json:"user"`
	PrimaryOrganization *domain.Organization            `json:"primary_organization"`
	UserOrganizations   []*domain.UserOrganization      `json:"user_organizations"`
	Permissions         []*domain.Permission            `json:"permissions"`
	DataPermissions     []*domain.UserDataPermission    `json:"data_permissions"`
	SessionID           uuid.UUID                       `json:"session_id"`
	System              string                          `json:"system"`
	AccessLevel         string                          `json:"access_level"`
}

// OrganizationContext 组织上下文
type OrganizationContext struct {
	CurrentOrganization *domain.Organization       `json:"current_organization"`
	UserRole            string                     `json:"user_role"`
	Permissions         []string                   `json:"permissions"`
	AccessibleSystems   []string                   `json:"accessible_systems"`
	ChildOrganizations  []*domain.Organization     `json:"child_organizations"`
	ParentOrganizations []*domain.Organization     `json:"parent_organizations"`
}

// SSOTokenInfo SSO令牌信息
type SSOTokenInfo struct {
	Token       string    `json:"token"`
	TokenType   string    `json:"token_type"`
	ExpiresAt   time.Time `json:"expires_at"`
	TargetSystem string   `json:"target_system"`
	Scopes      []string  `json:"scopes"`
}

// PermissionEvaluationRequest 权限评估请求
type PermissionEvaluationRequest struct {
	UserID         uuid.UUID `json:"user_id" validate:"required"`
	PermissionCode string    `json:"permission_code" validate:"required"`
	ResourceType   string    `json:"resource_type" validate:"required"`
	ResourceID     *string   `json:"resource_id"`
	Context        map[string]interface{} `json:"context"`
}

// PermissionEvaluationResult 权限评估结果
type PermissionEvaluationResult struct {
	IsGranted      bool                   `json:"is_granted"`
	PermissionCode string                 `json:"permission_code"`
	ResourceType   string                 `json:"resource_type"`
	ResourceID     *string                `json:"resource_id"`
	EffectiveScope map[string]interface{} `json:"effective_scope"`
	Source         string                 `json:"source"`
	ExpiresAt      *string                `json:"expires_at"`
}

// DataPermissionEvaluationRequest 数据权限评估请求
type DataPermissionEvaluationRequest struct {
	UserID      uuid.UUID              `json:"user_id" validate:"required"`
	ResourceType string                `json:"resource_type" validate:"required"`
	Action      string                 `json:"action" validate:"required"`
	ResourceIDs []string               `json:"resource_ids"`
	Context     map[string]interface{} `json:"context"`
}

// DataPermissionEvaluationResult 数据权限评估结果
type DataPermissionEvaluationResult struct {
	IsGranted           bool                   `json:"is_granted"`
	EffectiveScope      map[string]interface{} `json:"effective_scope"`
	AccessibleResources []string               `json:"accessible_resources"`
	DeniedResources     []string               `json:"denied_resources"`
}

// BatchPermissionEvaluationRequest 批量权限评估请求
type BatchPermissionEvaluationRequest struct {
	UserID      uuid.UUID                      `json:"user_id" validate:"required"`
	Permissions []PermissionEvaluationRequest  `json:"permissions" validate:"required"`
}

// GrantDataPermissionRequest 授予数据权限请求
type GrantDataPermissionRequest struct {
	UserID          uuid.UUID              `json:"user_id" validate:"required"`
	PermissionCode  string                 `json:"permission_code" validate:"required"`
	ScopeValue      map[string]interface{} `json:"scope_value"`
	ScopeConditions map[string]interface{} `json:"scope_conditions"`
	ExpiresAt       *time.Time             `json:"expires_at"`
	GrantedBy       uuid.UUID              `json:"granted_by" validate:"required"`
}

// EffectivePermission 有效权限
type EffectivePermission struct {
	PermissionCode  string                 `json:"permission_code"`
	PermissionName  string                 `json:"permission_name"`
	ResourceType    string                 `json:"resource_type"`
	ScopeType       string                 `json:"scope_type"`
	ScopeValue      map[string]interface{} `json:"scope_value"`
	ScopeConditions map[string]interface{} `json:"scope_conditions"`
	Source          string                 `json:"source"`
	GrantedAt       time.Time              `json:"granted_at"`
	ExpiresAt       *time.Time             `json:"expires_at"`
}

// CreatePermissionTemplateRequest 创建权限模板请求
type CreatePermissionTemplateRequest struct {
	OrganizationID  int64                  `json:"organization_id" validate:"required"`
	RoleType        string                 `json:"role_type" validate:"required"`
	TemplateName    string                 `json:"template_name" validate:"required"`
	Description     *string                `json:"description"`
	Permissions     []string               `json:"permissions"`
	DataPermissions map[string]interface{} `json:"data_permissions"`
	IsDefault       bool                   `json:"is_default"`
	Priority        int                    `json:"priority"`
	CreatedBy       uuid.UUID              `json:"created_by" validate:"required"`
}

// UpdatePermissionTemplateRequest 更新权限模板请求
type UpdatePermissionTemplateRequest struct {
	TemplateName    *string                `json:"template_name"`
	Description     *string                `json:"description"`
	Permissions     []string               `json:"permissions"`
	DataPermissions map[string]interface{} `json:"data_permissions"`
	IsDefault       *bool                  `json:"is_default"`
	Priority        *int                   `json:"priority"`
	Status          *string                `json:"status"`
	UpdatedBy       uuid.UUID              `json:"updated_by" validate:"required"`
}

// ApplyPermissionTemplateRequest 应用权限模板请求
type ApplyPermissionTemplateRequest struct {
	TemplateID uuid.UUID   `json:"template_id" validate:"required"`
	UserIDs    []uuid.UUID `json:"user_ids" validate:"required"`
	AppliedBy  uuid.UUID   `json:"applied_by" validate:"required"`
}

// InheritedPermission 继承权限
type InheritedPermission struct {
	PermissionCode    string                 `json:"permission_code"`
	InheritanceSource string                 `json:"inheritance_source"`
	SourceID          string                 `json:"source_id"`
	SourceName        string                 `json:"source_name"`
	ResourceType      string                 `json:"resource_type"`
	ScopeType         string                 `json:"scope_type"`
	TransformedScope  map[string]interface{} `json:"transformed_scope"`
	InheritanceRule   string                 `json:"inheritance_rule"`
}

// CreateDataPermissionRequest 创建数据权限请求
type CreateDataPermissionRequest struct {
	PermissionCode string    `json:"permission_code" validate:"required"`
	PermissionName string    `json:"permission_name" validate:"required"`
	ResourceType   string    `json:"resource_type" validate:"required"`
	ScopeType      string    `json:"scope_type" validate:"required"`
	Description    *string   `json:"description"`
	CreatedBy      uuid.UUID `json:"created_by" validate:"required"`
}

// UpdateDataPermissionRequest 更新数据权限请求
type UpdateDataPermissionRequest struct {
	PermissionName *string   `json:"permission_name"`
	Description    *string   `json:"description"`
	Status         *string   `json:"status"`
	UpdatedBy      uuid.UUID `json:"updated_by" validate:"required"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username   string      `json:"username" validate:"required,min=3,max=50"`
	Email      string      `json:"email" validate:"required,email"`
	Password   string      `json:"password" validate:"required,min=8"`
	FullName   string      `json:"full_name" validate:"required,max=100"`
	Phone      string      `json:"phone" validate:"omitempty,phone"`
	Department string      `json:"department" validate:"omitempty,max=100"`
	RoleIDs    []uuid.UUID `json:"role_ids"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email      *string `json:"email" validate:"omitempty,email"`
	FullName   *string `json:"full_name" validate:"omitempty,max=100"`
	Phone      *string `json:"phone" validate:"omitempty,phone"`
	Department *string `json:"department" validate:"omitempty,max=100"`
}

// ListUsersRequest 用户列表请求
type ListUsersRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListUsersResponse 用户列表响应
type ListUsersResponse struct {
	Users      []*domain.User `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name          string      `json:"name" validate:"required,min=2,max=100"`
	Code          string      `json:"code" validate:"required,min=2,max=50"`
	Description   string      `json:"description" validate:"max=500"`
	ParentID      *uuid.UUID  `json:"parent_id"`
	PermissionIDs []uuid.UUID `json:"permission_ids"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	DisplayName *string    `json:"display_name" validate:"omitempty,max=100"`
	Description *string    `json:"description" validate:"omitempty,max=500"`
	ParentID    *uuid.UUID `json:"parent_id"`
}

// ListRolesRequest 角色列表请求
type ListRolesRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListRolesResponse 角色列表响应
type ListRolesResponse struct {
	Roles      []*domain.Role `json:"roles"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=100"`
	Code        string `json:"code" validate:"required,min=1,max=100"`
	Resource    string `json:"resource" validate:"required,min=1,max=100"`
	Action      string `json:"action" validate:"required,oneof=create read update delete execute manage"`
	Description string `json:"description" validate:"max=500"`
	Module      string `json:"module" validate:"required,min=1,max=50"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        *string `json:"name" validate:"omitempty,min=1,max=100"`
	Description *string `json:"description" validate:"omitempty,max=500"`
	Module      *string `json:"module" validate:"omitempty,min=1,max=50"`
}

// ListPermissionsRequest 权限列表请求
type ListPermissionsRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListPermissionsResponse 权限列表响应
type ListPermissionsResponse struct {
	Permissions []*domain.Permission `json:"permissions"`
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	TotalPages  int                  `json:"total_pages"`
}

// UpdateSessionRequest 更新会话请求
type UpdateSessionRequest struct {
	IPAddress *string `json:"ip_address"`
	UserAgent *string `json:"user_agent"`
}

// ListSessionsRequest 会话列表请求
type ListSessionsRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListSessionsResponse 会话列表响应
type ListSessionsResponse struct {
	Sessions   []*domain.Session `json:"sessions"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// CreateLoginLogRequest 创建登录日志请求
type CreateLoginLogRequest struct {
	UserID    *uuid.UUID       `json:"user_id"`
	Username  string           `json:"username"`
	IPAddress string           `json:"ip_address"`
	UserAgent string           `json:"user_agent"`
	Result    domain.LogResult `json:"result"`
	Message   string           `json:"message"`
}

// CreatePermissionLogRequest 创建权限日志请求
type CreatePermissionLogRequest struct {
	UserID         uuid.UUID        `json:"user_id"`
	PermissionCode string           `json:"permission_code"`
	Resource       string           `json:"resource"`
	Action         string           `json:"action"`
	Result         domain.LogResult `json:"result"`
	IPAddress      string           `json:"ip_address"`
	UserAgent      string           `json:"user_agent"`
}

// CreateOperationLogRequest 创建操作日志请求
type CreateOperationLogRequest struct {
	UserID        uuid.UUID        `json:"user_id"`
	OperationType string           `json:"operation_type"`
	Resource      string           `json:"resource"`
	ResourceID    *string          `json:"resource_id"`
	Action        string           `json:"action"`
	Result        domain.LogResult `json:"result"`
	Details       string           `json:"details"`
	IPAddress     string           `json:"ip_address"`
	UserAgent     string           `json:"user_agent"`
}

// ListLogsRequest 日志列表请求
type ListLogsRequest struct {
	Page      int                    `json:"page" validate:"min=1"`
	PageSize  int                    `json:"page_size" validate:"min=1,max=100"`
	Filters   map[string]interface{} `json:"filters"`
	StartTime *time.Time             `json:"start_time"`
	EndTime   *time.Time             `json:"end_time"`
	OrderBy   string                 `json:"order_by"`
	OrderDir  string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListLoginLogsResponse 登录日志列表响应
type ListLoginLogsResponse struct {
	Logs       []*domain.LoginLog `json:"logs"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

// ListPermissionLogsResponse 权限日志列表响应
type ListPermissionLogsResponse struct {
	Logs       []*domain.PermissionLog `json:"logs"`
	Total      int64                   `json:"total"`
	Page       int                     `json:"page"`
	PageSize   int                     `json:"page_size"`
	TotalPages int                     `json:"total_pages"`
}

// ListOperationLogsResponse 操作日志列表响应
type ListOperationLogsResponse struct {
	Logs       []*domain.OperationLog `json:"logs"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// 站点管理相关的请求和响应结构体

// CreateStationRequest 创建站点请求
type CreateStationRequest struct {
	SiteCode               string                 `json:"site_code" validate:"required,max=50"`
	SiteName               string                 `json:"site_name" validate:"required,max=200"`
	Address                map[string]interface{} `json:"address" validate:"required"`
	Latitude               *float64               `json:"latitude" validate:"omitempty,min=-90,max=90"`
	Longitude              *float64               `json:"longitude" validate:"omitempty,min=-180,max=180"`
	AdministrativeDivision map[string]interface{} `json:"administrative_division"`
	BusinessHours          map[string]interface{} `json:"business_hours"`
	BusinessStatus         domain.BusinessStatus  `json:"business_status" validate:"omitempty,oneof=ACTIVE SUSPENDED CLOSED"`
	ContactInfo            map[string]interface{} `json:"contact_info"`
	ManagerID              *int64                 `json:"manager_id"`
	ManagementLevel        *string                `json:"management_level" validate:"omitempty,max=20"`
	ParentOrganization     *string                `json:"parent_organization" validate:"omitempty,max=100"`
}

// UpdateStationRequest 更新站点请求
type UpdateStationRequest struct {
	SiteName               *string                `json:"site_name" validate:"omitempty,max=200"`
	Address                map[string]interface{} `json:"address"`
	Latitude               *float64               `json:"latitude" validate:"omitempty,min=-90,max=90"`
	Longitude              *float64               `json:"longitude" validate:"omitempty,min=-180,max=180"`
	AdministrativeDivision map[string]interface{} `json:"administrative_division"`
	BusinessHours          map[string]interface{} `json:"business_hours"`
	ContactInfo            map[string]interface{} `json:"contact_info"`
	ManagerID              *int64                 `json:"manager_id"`
	ManagementLevel        *string                `json:"management_level" validate:"omitempty,max=20"`
	ParentOrganization     *string                `json:"parent_organization" validate:"omitempty,max=100"`
	Version                int                    `json:"version" validate:"required,min=1"`
}

// ListStationsRequest 站点列表请求
type ListStationsRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListStationsResponse 站点列表响应
type ListStationsResponse struct {
	Stations   []*domain.Station `json:"stations"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// AssignUserToStationRequest 分配用户到站点请求
type AssignUserToStationRequest struct {
	UserID    uuid.UUID                   `json:"user_id" validate:"required"`
	StationID int64                       `json:"station_id" validate:"required"`
	RoleType  domain.UserStationRoleType  `json:"role_type" validate:"required,oneof=manager assistant_manager supervisor operator"`
	ExpiresAt *time.Time                  `json:"expires_at"`
	GrantedBy *uuid.UUID                  `json:"granted_by"`
}

// ListUserStationRolesRequest 用户站点角色列表请求
type ListUserStationRolesRequest struct {
	Page      int                    `json:"page" validate:"min=1"`
	PageSize  int                    `json:"page_size" validate:"min=1,max=100"`
	UserID    *uuid.UUID             `json:"user_id"`
	StationID *int64                 `json:"station_id"`
	RoleType  *string                `json:"role_type"`
	Status    *string                `json:"status"`
	Filters   map[string]interface{} `json:"filters"`
	OrderBy   string                 `json:"order_by"`
	OrderDir  string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListUserStationRolesResponse 用户站点角色列表响应
type ListUserStationRolesResponse struct {
	Roles      []*domain.UserStationRole `json:"roles"`
	Total      int64                     `json:"total"`
	Page       int                       `json:"page"`
	PageSize   int                       `json:"page_size"`
	TotalPages int                       `json:"total_pages"`
}

// CreateStationEquipmentRequest 创建站点设备请求
type CreateStationEquipmentRequest struct {
	STID              int64                  `json:"stid" validate:"required"`
	EquipmentCode     string                 `json:"equipment_code" validate:"required,max=50"`
	EquipmentName     string                 `json:"equipment_name" validate:"required,max=200"`
	EquipmentType     domain.EquipmentType   `json:"equipment_type" validate:"required,oneof=EDC_POS EDC_PG"`
	EquipmentModel    *string                `json:"equipment_model" validate:"omitempty,max=100"`
	SerialNumber      *string                `json:"serial_number" validate:"omitempty,max=100"`
	Manufacturer      *string                `json:"manufacturer" validate:"omitempty,max=100"`
	InstallLocation   *string                `json:"install_location" validate:"omitempty,max=200"`
	InstallDate       *domain.Date           `json:"install_date"`
	Status            domain.EquipmentStatus `json:"status" validate:"omitempty,oneof=NORMAL FAULT MAINTENANCE OFFLINE"`
	LinkedEquipmentID *int64                 `json:"linked_equipment_id"`
	IPAddress         *string                `json:"ip_address" validate:"omitempty,ip"`
	Port              *int                   `json:"port" validate:"omitempty,min=1,max=65535"`
	NetworkConfig     map[string]interface{} `json:"network_config"`
	Configuration     map[string]interface{} `json:"configuration"`
	Specifications    map[string]interface{} `json:"specifications"`
}

// UpdateStationEquipmentRequest 更新站点设备请求
type UpdateStationEquipmentRequest struct {
	EquipmentName     *string                `json:"equipment_name" validate:"omitempty,max=200"`
	EquipmentModel    *string                `json:"equipment_model" validate:"omitempty,max=100"`
	SerialNumber      *string                `json:"serial_number" validate:"omitempty,max=100"`
	Manufacturer      *string                `json:"manufacturer" validate:"omitempty,max=100"`
	InstallLocation   *string                `json:"install_location" validate:"omitempty,max=200"`
	InstallDate       *domain.Date           `json:"install_date"`
	LinkedEquipmentID *int64                 `json:"linked_equipment_id"`
	IPAddress         *string                `json:"ip_address" validate:"omitempty,ip"`
	Port              *int                   `json:"port" validate:"omitempty,min=1,max=65535"`
	NetworkConfig     map[string]interface{} `json:"network_config"`
	Configuration     map[string]interface{} `json:"configuration"`
	Specifications    map[string]interface{} `json:"specifications"`
	Version           int                    `json:"version" validate:"required,min=1"`
}

// ListStationEquipmentsRequest 站点设备列表请求
type ListStationEquipmentsRequest struct {
	StationID *int64                 `json:"station_id"`
	Page      int                    `json:"page" validate:"min=1"`
	PageSize  int                    `json:"page_size" validate:"min=1,max=100"`
	Filters   map[string]interface{} `json:"filters"`
	OrderBy   string                 `json:"order_by"`
	OrderDir  string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListStationEquipmentsResponse 站点设备列表响应
type ListStationEquipmentsResponse struct {
	Equipments []*domain.StationEquipment `json:"equipments"`
	Total      int64                      `json:"total"`
	Page       int                        `json:"page"`
	PageSize   int                        `json:"page_size"`
	TotalPages int                        `json:"total_pages"`
}

// CreateMaintenanceRecordRequest 创建维护记录请求
type CreateMaintenanceRecordRequest struct {
	EquipmentID         int64                    `json:"equipment_id" validate:"required"`
	MaintenanceType     domain.MaintenanceType   `json:"maintenance_type" validate:"required,oneof=ROUTINE REPAIR UPGRADE"`
	MaintenanceDate     domain.DateTime          `json:"maintenance_date" validate:"required"`
	TechnicianName      *string                  `json:"technician_name" validate:"omitempty,max=100"`
	TechnicianCompany   *string                  `json:"technician_company" validate:"omitempty,max=200"`
	MaintenanceContent  *string                  `json:"maintenance_content"`
	FaultDescription    *string                  `json:"fault_description"`
	SolutionDescription *string                  `json:"solution_description"`
	MaintenanceResult   domain.MaintenanceResult `json:"maintenance_result" validate:"omitempty,oneof=SUCCESS FAILED PARTIAL"`
	NextMaintenanceDate *domain.Date             `json:"next_maintenance_date"`
	MaintenanceCost     *float64                 `json:"maintenance_cost" validate:"omitempty,min=0"`
	Attachments         []map[string]interface{} `json:"attachments"`
}

// UpdateMaintenanceRecordRequest 更新维护记录请求
type UpdateMaintenanceRecordRequest struct {
	TechnicianName      *string                   `json:"technician_name" validate:"omitempty,max=100"`
	TechnicianCompany   *string                   `json:"technician_company" validate:"omitempty,max=200"`
	MaintenanceContent  *string                   `json:"maintenance_content"`
	FaultDescription    *string                   `json:"fault_description"`
	SolutionDescription *string                   `json:"solution_description"`
	MaintenanceResult   *domain.MaintenanceResult `json:"maintenance_result" validate:"omitempty,oneof=SUCCESS FAILED PARTIAL"`
	NextMaintenanceDate *domain.Date              `json:"next_maintenance_date"`
	MaintenanceCost     *float64                  `json:"maintenance_cost" validate:"omitempty,min=0"`
	Attachments         []map[string]interface{}  `json:"attachments"`
}

// ListMaintenanceRecordsRequest 维护记录列表请求
type ListMaintenanceRecordsRequest struct {
	EquipmentID *int64                 `json:"equipment_id"`
	Page        int                    `json:"page" validate:"min=1"`
	PageSize    int                    `json:"page_size" validate:"min=1,max=100"`
	Filters     map[string]interface{} `json:"filters"`
	StartTime   *time.Time             `json:"start_time"`
	EndTime     *time.Time             `json:"end_time"`
	OrderBy     string                 `json:"order_by"`
	OrderDir    string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListMaintenanceRecordsResponse 维护记录列表响应
type ListMaintenanceRecordsResponse struct {
	Records    []*domain.EquipmentMaintenanceRecord `json:"records"`
	Total      int64                                `json:"total"`
	Page       int                                  `json:"page"`
	PageSize   int                                  `json:"page_size"`
	TotalPages int                                  `json:"total_pages"`
}

// CreateOperationParamRequest 创建运营参数请求
type CreateOperationParamRequest struct {
	STID             int64                  `json:"stid" validate:"required"`
	ParamCategory    string                 `json:"param_category" validate:"required,max=50"`
	ParamKey         string                 `json:"param_key" validate:"required,max=100"`
	ParamName        string                 `json:"param_name" validate:"required,max=200"`
	ParamDescription *string                `json:"param_description"`
	ParamValue       map[string]interface{} `json:"param_value" validate:"required"`
	ParamType        domain.ParamType       `json:"param_type" validate:"omitempty,oneof=STRING NUMBER BOOLEAN JSON ARRAY"`
	EffectiveTime    *time.Time             `json:"effective_time"`
	Status           domain.ParamStatus     `json:"status" validate:"omitempty,oneof=ACTIVE INACTIVE EXPIRED"`
	ValidationRules  map[string]interface{} `json:"validation_rules"`
	DefaultValue     map[string]interface{} `json:"default_value"`
}

// UpdateOperationParamRequest 更新运营参数请求
type UpdateOperationParamRequest struct {
	ParamDescription *string                `json:"param_description"`
	ParamValue       map[string]interface{} `json:"param_value"`
	ParamType        *domain.ParamType      `json:"param_type" validate:"omitempty,oneof=STRING NUMBER BOOLEAN JSON ARRAY"`
	EffectiveTime    *time.Time             `json:"effective_time"`
	Status           *domain.ParamStatus    `json:"status" validate:"omitempty,oneof=ACTIVE INACTIVE EXPIRED"`
	ValidationRules  map[string]interface{} `json:"validation_rules"`
	DefaultValue     map[string]interface{} `json:"default_value"`
	ChangeReason     *string                `json:"change_reason"`
	Version          int                    `json:"version" validate:"required,min=1"`
}

// ListOperationParamsRequest 运营参数列表请求
type ListOperationParamsRequest struct {
	StationID *int64                 `json:"station_id"`
	Page      int                    `json:"page" validate:"min=1"`
	PageSize  int                    `json:"page_size" validate:"min=1,max=100"`
	Filters   map[string]interface{} `json:"filters"`
	OrderBy   string                 `json:"order_by"`
	OrderDir  string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListOperationParamsResponse 运营参数列表响应
type ListOperationParamsResponse struct {
	Params     []*domain.OperationParam `json:"params"`
	Total      int64                    `json:"total"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"page_size"`
	TotalPages int                      `json:"total_pages"`
}

// ListParamChangeHistoryRequest 参数变更历史列表请求
type ListParamChangeHistoryRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListParamChangeHistoryResponse 参数变更历史列表响应
type ListParamChangeHistoryResponse struct {
	History    []*domain.ParamChangeHistory `json:"history"`
	Total      int64                        `json:"total"`
	Page       int                          `json:"page"`
	PageSize   int                          `json:"page_size"`
	TotalPages int                          `json:"total_pages"`
}

// 配置管理相关结构体（重新定义以匹配API文档）

// CreateConfigItemRequest 创建配置项请求
type CreateConfigItemRequest struct {
	ConfigCategory string  `json:"config_category" validate:"required,max=20,oneof=SYSTEM BUSINESS SITE"`
	ConfigModule   *string `json:"config_module" validate:"omitempty,max=50"`
	ConfigKey      string  `json:"config_key" validate:"required,max=200"`
	ConfigName     string  `json:"config_name" validate:"required,max=200"`
	Description    *string `json:"description"`
	DataType       string  `json:"data_type" validate:"omitempty,oneof=STRING INTEGER DECIMAL BOOLEAN JSON"`
	ConfigValue    *string `json:"config_value"`
}

// UpdateConfigItemRequest 更新配置项请求
type UpdateConfigItemRequest struct {
	ConfigName  *string `json:"config_name" validate:"omitempty,max=200"`
	Description *string `json:"description"`
	ConfigValue *string `json:"config_value"`
	IsEnabled   *bool   `json:"is_enabled"`
}

// ListConfigItemsRequest 配置项列表请求
type ListConfigItemsRequest struct {
	Page     int     `json:"page" validate:"min=1"`
	PageSize int     `json:"page_size" validate:"min=1,max=100"`
	Category *string `json:"category"`
	Module   *string `json:"module"`
	Enabled  *bool   `json:"enabled"`
	Keyword  *string `json:"keyword"`
	OrderBy  string  `json:"order_by"`
	OrderDir string  `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListConfigItemsResponse 配置项列表响应
type ListConfigItemsResponse struct {
	Items      []*domain.ConfigItem `json:"items"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
}

// SearchConfigItemsRequest 搜索配置项请求
type SearchConfigItemsRequest struct {
	Keyword  string  `json:"keyword" validate:"required,min=1"`
	Page     int     `json:"page" validate:"min=1"`
	PageSize int     `json:"page_size" validate:"min=1,max=100"`
	Category *string `json:"category"`
	Module   *string `json:"module"`
	OrderBy  string  `json:"order_by"`
	OrderDir string  `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// SearchConfigItemsResponse 搜索配置项响应
type SearchConfigItemsResponse struct {
	Items      []*domain.ConfigItem `json:"items"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	TotalPages int                  `json:"total_pages"`
	Keyword    string               `json:"keyword"`
}

// BatchCreateConfigItemsRequest 批量创建配置项请求
type BatchCreateConfigItemsRequest struct {
	Operation string                     `json:"operation" validate:"required,oneof=create"`
	Configs   []*CreateConfigItemRequest `json:"configs" validate:"required,min=1,max=100"`
}

// BatchCreateConfigItemsResponse 批量创建配置项响应
type BatchCreateConfigItemsResponse struct {
	SuccessCount int                    `json:"success_count"`
	FailedCount  int                    `json:"failed_count"`
	Results      []BatchOperationResult `json:"results"`
}

// BatchUpdateConfigItemsRequest 批量更新配置项请求
type BatchUpdateConfigItemsRequest struct {
	Operation string                  `json:"operation" validate:"required,oneof=update"`
	Configs   []BatchUpdateConfigItem `json:"configs" validate:"required,min=1,max=100"`
}

// BatchUpdateConfigItem 批量更新配置项
type BatchUpdateConfigItem struct {
	ID      int64                    `json:"id" validate:"required"`
	Request *UpdateConfigItemRequest `json:"request" validate:"required"`
}

// BatchUpdateConfigItemsResponse 批量更新配置项响应
type BatchUpdateConfigItemsResponse struct {
	SuccessCount int                    `json:"success_count"`
	FailedCount  int                    `json:"failed_count"`
	Results      []BatchOperationResult `json:"results"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	ConfigKey string `json:"config_key"`
	Status    string `json:"status"`
	ID        *int64 `json:"id,omitempty"`
	Error     string `json:"error,omitempty"`
}

// GetConfigValueResponse 获取配置值响应
type GetConfigValueResponse struct {
	ConfigKey   string `json:"config_key"`
	ConfigValue string `json:"config_value"`
	DataType    string `json:"data_type"`
}

// BatchGetConfigValuesRequest 批量获取配置值请求
type BatchGetConfigValuesRequest struct {
	Keys []string `json:"keys" validate:"required,min=1,max=100"`
}

// BatchGetConfigValuesResponse 批量获取配置值响应
type BatchGetConfigValuesResponse struct {
	Configs map[string]ConfigValueInfo `json:"configs"`
}

// ConfigValueInfo 配置值信息
type ConfigValueInfo struct {
	Value    string `json:"value"`
	DataType string `json:"data_type"`
}

// GetCategoriesResponse 获取分类响应
type GetCategoriesResponse struct {
	Categories []CategoryInfo `json:"categories"`
}

// CategoryInfo 分类信息
type CategoryInfo struct {
	Category string       `json:"category"`
	Name     string       `json:"name"`
	Modules  []ModuleInfo `json:"modules"`
}

// ModuleInfo 模块信息
type ModuleInfo struct {
	Module string `json:"module"`
	Name   string `json:"name"`
	Count  int64  `json:"count"`
}

// CategoryStatsResponse 分类统计响应
type CategoryStatsResponse struct {
	Categories []CategoryStat `json:"categories"`
	Total      int64          `json:"total"`
}

// CategoryStat 分类统计
type CategoryStat struct {
	Category string `json:"category"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
}

// DataTypeStatsResponse 数据类型统计响应
type DataTypeStatsResponse struct {
	DataTypes []DataTypeStat `json:"data_types"`
	Total     int64          `json:"total"`
}

// DataTypeStat 数据类型统计
type DataTypeStat struct {
	DataType string `json:"data_type"`
	Count    int64  `json:"count"`
	Name     string `json:"name"`
}

// CreateConfigLogRequest 创建配置日志请求
type CreateConfigLogRequest struct {
	ConfigItemID int64   `json:"config_item_id" validate:"required"`
	Action       string  `json:"action" validate:"required,oneof=CREATE UPDATE DELETE APPROVE ROLLBACK"`
	FieldName    *string `json:"field_name" validate:"omitempty,max=100"`
	OldValue     *string `json:"old_value"`
	NewValue     *string `json:"new_value"`
	ChangeReason *string `json:"change_reason" validate:"omitempty,max=500"`
}

// ListConfigLogsRequest 配置日志列表请求
type ListConfigLogsRequest struct {
	Page      int     `json:"page" validate:"min=1"`
	PageSize  int     `json:"page_size" validate:"min=1,max=100"`
	Action    *string `json:"action"`
	Status    *string `json:"status"`
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	OrderBy   string  `json:"order_by"`
	OrderDir  string  `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

// ListConfigLogsResponse 配置日志列表响应
type ListConfigLogsResponse struct {
	Items      []*ConfigLogWithDetails `json:"items"`
	Total      int64                   `json:"total"`
	Page       int                     `json:"page"`
	PageSize   int                     `json:"page_size"`
	TotalPages int                     `json:"total_pages"`
}

// ConfigLogWithDetails 带详情的配置日志
type ConfigLogWithDetails struct {
	*domain.ConfigLog
	ConfigKey      string  `json:"config_key"`
	CreatedByName  string  `json:"created_by_name"`
	ApprovedByName *string `json:"approved_by_name"`
}

// SubmitApprovalRequest 提交审批请求
type SubmitApprovalRequest struct {
	ChangeReason *string `json:"change_reason" validate:"omitempty,max=500"`
}

// ApprovalRequest 审批请求
type ApprovalRequest struct {
	ApprovalReason *string `json:"approval_reason" validate:"omitempty,max=500"`
}

// ConfigStatsRequest 配置变更统计请求
type ConfigStatsRequest struct {
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	Category  *string `json:"category"`
	Action    *string `json:"action"`
}

// ConfigStatsResponse 配置变更统计响应
type ConfigStatsResponse struct {
	TotalChanges    int64            `json:"total_changes"`
	ChangesByAction map[string]int64 `json:"changes_by_action"`
	ChangesByType   map[string]int64 `json:"changes_by_type"`
	ChangesByStatus map[string]int64 `json:"changes_by_status"`
}

// ApprovalStatsRequest 审批统计请求
type ApprovalStatsRequest struct {
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	Status    *string `json:"status"`
}

// ApprovalStatsResponse 审批统计响应
type ApprovalStatsResponse struct {
	TotalApprovals      int64            `json:"total_approvals"`
	PendingApprovals    int64            `json:"pending_approvals"`
	ApprovedCount       int64            `json:"approved_count"`
	RejectedCount       int64            `json:"rejected_count"`
	ApprovalsByStatus   map[string]int64 `json:"approvals_by_status"`
	ApprovalsByUser     map[string]int64 `json:"approvals_by_user"`
	AverageApprovalTime float64          `json:"average_approval_time"`
}

// CreateDictCategoryRequest 创建分类请求
type CreateDictCategoryRequest struct {
	CategoryName string  `json:"category_name" validate:"required,max=100"`
	CategoryCode string  `json:"category_code" validate:"required,max=50"`
	Description  *string `json:"description"`
}

// UpdateDictCategoryRequest 更新分类请求
type UpdateDictCategoryRequest struct {
	CategoryName *string `json:"category_name" validate:"omitempty,max=100"`
	Description  *string `json:"description"`
}

// ListDictCategoriesRequest 分类列表请求
type ListDictCategoriesRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Status   *string                `json:"status"`
	Keyword  *string                `json:"keyword"`
	Filters  map[string]interface{} `json:"filters"`
}

// ListDictCategoriesResponse 分类列表响应
type ListDictCategoriesResponse struct {
	Categories []*domain.DictCategory `json:"categories"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// CreateDictItemRequest 创建项目请求
type CreateDictItemRequest struct {
	CategoryID    int64   `json:"category_id" validate:"required"`
	ItemName      string  `json:"item_name" validate:"required,max=100"`
	ItemCode      string  `json:"item_code" validate:"required,max=50"`
	Description   *string `json:"description"`
	ValueType     string  `json:"value_type" validate:"required,oneof=string number boolean date"`
	ValueFormat   *string `json:"value_format"`
	MinValue      *string `json:"min_value"`
	MaxValue      *string `json:"max_value"`
	RegexPattern  *string `json:"regex_pattern"`
	AllowedValues *string `json:"allowed_values"`
	SortOrder     int     `json:"sort_order"`
}

// UpdateDictItemRequest 更新项目请求
type UpdateDictItemRequest struct {
	ItemName      *string `json:"item_name" validate:"omitempty,max=100"`
	Description   *string `json:"description"`
	ValueType     *string `json:"value_type" validate:"omitempty,oneof=string number boolean date"`
	ValueFormat   *string `json:"value_format"`
	MinValue      *string `json:"min_value"`
	MaxValue      *string `json:"max_value"`
	RegexPattern  *string `json:"regex_pattern"`
	AllowedValues *string `json:"allowed_values"`
	SortOrder     *int    `json:"sort_order"`
}

// ListDictItemsRequest 项目列表请求
type ListDictItemsRequest struct {
	Page       int                    `json:"page" validate:"min=1"`
	PageSize   int                    `json:"page_size" validate:"min=1,max=100"`
	CategoryID *int64                 `json:"category_id"`
	Status     *string                `json:"status"`
	ValueType  *string                `json:"value_type"`
	Keyword    *string                `json:"keyword"`
	Filters    map[string]interface{} `json:"filters"`
}

// ListDictItemsResponse 项目列表响应
type ListDictItemsResponse struct {
	Items      []*domain.DictItem `json:"items"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

// BatchCreateDictItemsRequest 批量创建项目请求
type BatchCreateDictItemsRequest struct {
	Items []*CreateDictItemRequest `json:"items" validate:"required,min=1,max=100"`
}

// BatchCreateDictItemsResponse 批量创建项目响应
type BatchCreateDictItemsResponse struct {
	SuccessCount int                        `json:"success_count"`
	FailedCount  int                        `json:"failed_count"`
	Results      []DictBatchOperationResult `json:"results"`
}

// BatchUpdateDictItemsRequest 批量更新项目请求
type BatchUpdateDictItemsRequest struct {
	Items []BatchUpdateDictItem `json:"items" validate:"required,min=1,max=100"`
}

// BatchUpdateDictItem 批量更新项目
type BatchUpdateDictItem struct {
	ID      int64                  `json:"id" validate:"required"`
	Request *UpdateDictItemRequest `json:"request" validate:"required"`
}

// BatchUpdateDictItemsResponse 批量更新项目响应
type BatchUpdateDictItemsResponse struct {
	SuccessCount int                        `json:"success_count"`
	FailedCount  int                        `json:"failed_count"`
	Results      []DictBatchOperationResult `json:"results"`
}

// CreateDictValueRequest 创建值请求
type CreateDictValueRequest struct {
	ItemID       int64   `json:"item_id" validate:"required"`
	DisplayName  string  `json:"display_name" validate:"required,max=200"`
	Value        string  `json:"value" validate:"required,max=500"`
	Description  *string `json:"description"`
	IsDefault    bool    `json:"is_default"`
	SortOrder    int     `json:"sort_order"`
	SourceSystem string  `json:"source_system" validate:"required,oneof=local hos external"`
}

// UpdateDictValueRequest 更新值请求
type UpdateDictValueRequest struct {
	DisplayName *string `json:"display_name" validate:"omitempty,max=200"`
	Description *string `json:"description"`
	IsDefault   *bool   `json:"is_default"`
	SortOrder   *int    `json:"sort_order"`
}

// ListDictValuesRequest 值列表请求
type ListDictValuesRequest struct {
	Page         int                    `json:"page" validate:"min=1"`
	PageSize     int                    `json:"page_size" validate:"min=1,max=100"`
	ItemID       *int64                 `json:"item_id"`
	Status       *string                `json:"status"`
	SourceSystem *string                `json:"source_system"`
	IsDefault    *bool                  `json:"is_default"`
	Keyword      *string                `json:"keyword"`
	Filters      map[string]interface{} `json:"filters"`
}

// ListDictValuesResponse 值列表响应
type ListDictValuesResponse struct {
	Values     []*domain.DictValue `json:"values"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

// BatchCreateDictValuesRequest 批量创建值请求
type BatchCreateDictValuesRequest struct {
	Values []*CreateDictValueRequest `json:"values" validate:"required,min=1,max=100"`
}

// BatchCreateDictValuesResponse 批量创建值响应
type BatchCreateDictValuesResponse struct {
	SuccessCount int                        `json:"success_count"`
	FailedCount  int                        `json:"failed_count"`
	Results      []DictBatchOperationResult `json:"results"`
}

// BatchUpdateDictValuesRequest 批量更新值请求
type BatchUpdateDictValuesRequest struct {
	Values []BatchUpdateDictValue `json:"values" validate:"required,min=1,max=100"`
}

// BatchUpdateDictValue 批量更新值
type BatchUpdateDictValue struct {
	ID      int64                   `json:"id" validate:"required"`
	Request *UpdateDictValueRequest `json:"request" validate:"required"`
}

// BatchUpdateDictValuesResponse 批量更新值响应
type BatchUpdateDictValuesResponse struct {
	SuccessCount int                        `json:"success_count"`
	FailedCount  int                        `json:"failed_count"`
	Results      []DictBatchOperationResult `json:"results"`
}

// DictBatchOperationResult 批量操作结果
type DictBatchOperationResult struct {
	ID     *int64 `json:"id,omitempty"`
	Status string `json:"status"`
	Error  string `json:"error,omitempty"`
}

// SyncHOSDataResponse HOS数据同步响应
type SyncHOSDataResponse struct {
	SyncID          string `json:"sync_id"`
	Status          string `json:"status"`
	CategoriesCount int    `json:"categories_count"`
	EstimatedTime   string `json:"estimated_time"`
}

// ListSyncHistoryRequest 同步历史列表请求
type ListSyncHistoryRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Status   *string                `json:"status"`
	Filters  map[string]interface{} `json:"filters"`
}

// ListSyncHistoryResponse 同步历史列表响应
type ListSyncHistoryResponse struct {
	History    []*domain.DictSyncStatus `json:"history"`
	Total      int64                    `json:"total"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"page_size"`
	TotalPages int                      `json:"total_pages"`
}

// SyncHOSSchemasRequest HOS表结构同步请求
type SyncHOSSchemasRequest struct {
	TableNames []string `json:"table_names" validate:"required,min=1"`
	SyncMode   string   `json:"sync_mode" validate:"required,oneof=incremental full"`
}

// SyncHOSSchemasResponse HOS表结构同步响应
type SyncHOSSchemasResponse struct {
	SyncID        string `json:"sync_id"`
	Status        string `json:"status"`
	TablesCount   int    `json:"tables_count"`
	EstimatedTime string `json:"estimated_time"`
}

// GetUsageStatsRequest 使用统计请求
type GetUsageStatsRequest struct {
	StartDate    *string `json:"start_date"`
	EndDate      *string `json:"end_date"`
	CategoryCode *string `json:"category_code"`
}

// GetTrendDataRequest 趋势数据请求
type GetTrendDataRequest struct {
	Period       string  `json:"period" validate:"required,oneof=daily weekly monthly"`
	Days         int     `json:"days" validate:"min=1,max=365"`
	CategoryCode *string `json:"category_code"`
}

// DictImportRequest 导入请求
type DictImportRequest struct {
	Format    string `json:"format" validate:"required,oneof=json excel csv"`
	MergeMode string `json:"merge_mode" validate:"required,oneof=replace merge append"`
	FileName  string `json:"file_name" validate:"required"`
}

// TagGroupService 标签组服务接口
type TagGroupService interface {
	// 标签组管理
	CreateTagGroup(ctx context.Context, req *CreateTagGroupRequest) (*domain.TagGroup, error)
	GetTagGroup(ctx context.Context, groupID uuid.UUID) (*domain.TagGroup, error)
	GetTagGroupByCode(ctx context.Context, code string) (*domain.TagGroup, error)
	UpdateTagGroup(ctx context.Context, groupID uuid.UUID, req *UpdateTagGroupRequest) (*domain.TagGroup, error)
	DeleteTagGroup(ctx context.Context, groupID uuid.UUID) error

	// 标签组列表
	ListTagGroups(ctx context.Context, req *ListTagGroupsRequest) (*ListTagGroupsResponse, error)
	GetTagGroupTree(ctx context.Context, entityType *domain.EntityType) ([]*domain.TagGroup, error)
	GetTagGroupChildren(ctx context.Context, parentID uuid.UUID) ([]*domain.TagGroup, error)

	// 标签组状态管理
	EnableTagGroup(ctx context.Context, groupID uuid.UUID) error
	DisableTagGroup(ctx context.Context, groupID uuid.UUID) error

	// 标签组统计
	GetTagGroupStats(ctx context.Context, groupID uuid.UUID) (*TagGroupStatsResponse, error)
}

// TagService 标签服务接口
type TagService interface {
	// 标签管理
	CreateTag(ctx context.Context, req *CreateTagRequest) (*domain.Tag, error)
	GetTag(ctx context.Context, tagID uuid.UUID) (*domain.Tag, error)
	GetTagByCode(ctx context.Context, code string) (*domain.Tag, error)
	UpdateTag(ctx context.Context, tagID uuid.UUID, req *UpdateTagRequest) (*domain.Tag, error)
	DeleteTag(ctx context.Context, tagID uuid.UUID) error

	// 标签列表
	ListTags(ctx context.Context, req *ListTagsRequest) (*ListTagsResponse, error)
	GetTagsByGroup(ctx context.Context, groupID uuid.UUID, req *ListTagsRequest) (*ListTagsResponse, error)
	SearchTags(ctx context.Context, keyword string, req *ListTagsRequest) (*ListTagsResponse, error)

	// 批量操作
	BatchCreateTags(ctx context.Context, req *BatchCreateTagsRequest) (*BatchCreateTagsResponse, error)
	BatchUpdateTags(ctx context.Context, req *BatchUpdateTagsRequest) (*BatchUpdateTagsResponse, error)
	BatchDeleteTags(ctx context.Context, tagIDs []uuid.UUID) error

	// 标签状态管理
	EnableTag(ctx context.Context, tagID uuid.UUID) error
	DisableTag(ctx context.Context, tagID uuid.UUID) error

	// 使用次数管理
	IncrementUsageCount(ctx context.Context, tagID uuid.UUID) error
	DecrementUsageCount(ctx context.Context, tagID uuid.UUID) error
	UpdateUsageCount(ctx context.Context, tagID uuid.UUID, count int) error

	// 热门标签
	GetPopularTags(ctx context.Context, entityType *domain.EntityType, limit int) ([]*domain.Tag, error)
}

// EntityTagService 实体标签服务接口
type EntityTagService interface {
	// 实体标签关联
	AddEntityTag(ctx context.Context, req *AddEntityTagRequest) (*domain.EntityTag, error)
	RemoveEntityTag(ctx context.Context, entityType domain.EntityType, entityID, tagID uuid.UUID) error
	GetEntityTags(ctx context.Context, entityType domain.EntityType, entityID uuid.UUID) ([]*domain.EntityTag, error)
	GetTagEntities(ctx context.Context, tagID uuid.UUID, entityType *domain.EntityType, req *ListTagEntitiesRequest) (*ListTagEntitiesResponse, error)

	// 批量操作
	BatchAddEntityTags(ctx context.Context, req *BatchAddEntityTagsRequest) (*BatchAddEntityTagsResponse, error)
	BatchRemoveEntityTags(ctx context.Context, req *BatchRemoveEntityTagsRequest) (*BatchRemoveEntityTagsResponse, error)
	ReplaceEntityTags(ctx context.Context, req *ReplaceEntityTagsRequest) error

	// 标签过期管理
	SetTagExpiration(ctx context.Context, entityType domain.EntityType, entityID, tagID uuid.UUID, expiresAt *time.Time) error
	GetExpiringTags(ctx context.Context, beforeTime time.Time, req *ListExpiringTagsRequest) (*ListExpiringTagsResponse, error)
	CleanupExpiredTags(ctx context.Context) (*CleanupExpiredTagsResponse, error)

	// 用户特定标签
	GetUserTags(ctx context.Context, userID uuid.UUID, req *ListUserTagsRequest) (*ListUserTagsResponse, error)
	GetUserTagsByGroup(ctx context.Context, userID, groupID uuid.UUID) ([]*domain.EntityTag, error)
	AddUserTag(ctx context.Context, userID, tagID uuid.UUID, req *AddUserTagRequest) (*domain.EntityTag, error)
	RemoveUserTag(ctx context.Context, userID, tagID uuid.UUID) error

	// 站点特定标签
	GetStationTags(ctx context.Context, stationID uuid.UUID, req *ListStationTagsRequest) (*ListStationTagsResponse, error)
	GetStationTagsByGroup(ctx context.Context, stationID, groupID uuid.UUID) ([]*domain.EntityTag, error)
	AddStationTag(ctx context.Context, stationID, tagID uuid.UUID, req *AddStationTagRequest) (*domain.EntityTag, error)
	RemoveStationTag(ctx context.Context, stationID, tagID uuid.UUID) error
}

// TagStatisticsService 标签统计服务接口
type TagStatisticsService interface {
	// 标签统计
	GetTagStatistics(ctx context.Context, tagID uuid.UUID) (*domain.TagStatistics, error)
	GetTagsStatistics(ctx context.Context, tagIDs []uuid.UUID) ([]*domain.TagStatistics, error)
	GetGroupStatistics(ctx context.Context, groupID uuid.UUID) ([]*domain.TagStatistics, error)

	// 实体标签分布统计
	GetEntityTagDistribution(ctx context.Context, entityType domain.EntityType) (*domain.EntityTagDistribution, error)
	GetAllEntityTagDistribution(ctx context.Context) ([]*domain.EntityTagDistribution, error)

	// 趋势分析
	GetTagUsageTrend(ctx context.Context, tagID uuid.UUID, req *TagUsageTrendRequest) ([]map[string]interface{}, error)
	GetEntityTaggingTrend(ctx context.Context, entityType domain.EntityType, req *EntityTaggingTrendRequest) ([]map[string]interface{}, error)

	// 热门和最近使用标签
	GetHotTags(ctx context.Context, entityType *domain.EntityType, limit int, timeRange *time.Duration) ([]*domain.Tag, error)
	GetRecentlyUsedTags(ctx context.Context, entityType *domain.EntityType, limit int) ([]*domain.Tag, error)

	// 实体统计
	GetUntaggedEntities(ctx context.Context, entityType domain.EntityType, req *ListUntaggedEntitiesRequest) (*ListUntaggedEntitiesResponse, error)
	GetMostTaggedEntities(ctx context.Context, entityType domain.EntityType, limit int) ([]map[string]interface{}, error)
}

// 标签组请求结构体
type CreateTagGroupRequest struct {
	Name               string     `json:"name" validate:"required,min=1,max=100"`
	Code               string     `json:"code" validate:"required,min=1,max=50"`
	Description        *string    `json:"description"`
	ParentID           *uuid.UUID `json:"parent_id"`
	ApplicableEntities []string   `json:"applicable_entities" validate:"required,min=1"`
	SortOrder          int        `json:"sort_order"`
	IsSystem           bool       `json:"is_system"`
}

type UpdateTagGroupRequest struct {
	Name               *string                `json:"name" validate:"omitempty,min=1,max=100"`
	Description        *string                `json:"description"`
	ApplicableEntities []string               `json:"applicable_entities"`
	SortOrder          *int                   `json:"sort_order"`
	Status             *domain.TagGroupStatus `json:"status"`
}

type ListTagGroupsRequest struct {
	Page       int                    `json:"page" validate:"min=1"`
	PageSize   int                    `json:"page_size" validate:"min=1,max=100"`
	EntityType *domain.EntityType     `json:"entity_type"`
	Status     *domain.TagGroupStatus `json:"status"`
	ParentID   *uuid.UUID             `json:"parent_id"`
	Keyword    *string                `json:"keyword"`
	Filters    map[string]interface{} `json:"filters"`
	OrderBy    string                 `json:"order_by"`
	OrderDir   string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListTagGroupsResponse struct {
	TagGroups  []*domain.TagGroup `json:"tag_groups"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

type TagGroupStatsResponse struct {
	GroupID      uuid.UUID      `json:"group_id"`
	GroupName    string         `json:"group_name"`
	TagCount     int            `json:"tag_count"`
	ActiveTags   int            `json:"active_tags"`
	InactiveTags int            `json:"inactive_tags"`
	TotalUsage   int            `json:"total_usage"`
	EntityCounts map[string]int `json:"entity_counts"`
}

// 标签请求结构体
type CreateTagRequest struct {
	GroupID     uuid.UUID `json:"group_id" validate:"required"`
	Name        string    `json:"name" validate:"required,min=1,max=100"`
	Code        string    `json:"code" validate:"required,min=1,max=100"`
	Description *string   `json:"description"`
	SortOrder   int       `json:"sort_order"`
	IsSystem    bool      `json:"is_system"`
	IsAuto      bool      `json:"is_auto"`
}

type UpdateTagRequest struct {
	Name        *string           `json:"name" validate:"omitempty,min=1,max=100"`
	Description *string           `json:"description"`
	SortOrder   *int              `json:"sort_order"`
	Status      *domain.TagStatus `json:"status"`
}

type ListTagsRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	GroupID  *uuid.UUID             `json:"group_id"`
	Status   *domain.TagStatus      `json:"status"`
	IsSystem *bool                  `json:"is_system"`
	IsAuto   *bool                  `json:"is_auto"`
	Keyword  *string                `json:"keyword"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListTagsResponse struct {
	Tags       []*domain.Tag `json:"tags"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	PageSize   int           `json:"page_size"`
	TotalPages int           `json:"total_pages"`
}

type BatchCreateTagsRequest struct {
	Tags []*CreateTagRequest `json:"tags" validate:"required,min=1,max=100"`
}

type BatchCreateTagsResponse struct {
	SuccessCount int                       `json:"success_count"`
	FailedCount  int                       `json:"failed_count"`
	Results      []TagBatchOperationResult `json:"results"`
}

type BatchUpdateTagsRequest struct {
	Tags []BatchUpdateTag `json:"tags" validate:"required,min=1,max=100"`
}

type BatchUpdateTag struct {
	ID      uuid.UUID         `json:"id" validate:"required"`
	Request *UpdateTagRequest `json:"request" validate:"required"`
}

type BatchUpdateTagsResponse struct {
	SuccessCount int                       `json:"success_count"`
	FailedCount  int                       `json:"failed_count"`
	Results      []TagBatchOperationResult `json:"results"`
}

type TagBatchOperationResult struct {
	ID     *uuid.UUID `json:"id,omitempty"`
	Code   string     `json:"code"`
	Status string     `json:"status"`
	Error  string     `json:"error,omitempty"`
}

// 实体标签请求结构体
type AddEntityTagRequest struct {
	EntityType domain.EntityType `json:"entity_type" validate:"required"`
	EntityID   uuid.UUID         `json:"entity_id" validate:"required"`
	TagID      uuid.UUID         `json:"tag_id" validate:"required"`
	Source     domain.TagSource  `json:"source"`
	ExpiresAt  *time.Time        `json:"expires_at"`
	Remarks    *string           `json:"remarks"`
}

type BatchAddEntityTagsRequest struct {
	EntityTags []*AddEntityTagRequest `json:"entity_tags" validate:"required,min=1,max=100"`
}

type BatchAddEntityTagsResponse struct {
	SuccessCount int                             `json:"success_count"`
	FailedCount  int                             `json:"failed_count"`
	Results      []EntityTagBatchOperationResult `json:"results"`
}

type BatchRemoveEntityTagsRequest struct {
	EntityTags []RemoveEntityTagRequest `json:"entity_tags" validate:"required,min=1,max=100"`
}

type RemoveEntityTagRequest struct {
	EntityType domain.EntityType `json:"entity_type" validate:"required"`
	EntityID   uuid.UUID         `json:"entity_id" validate:"required"`
	TagID      uuid.UUID         `json:"tag_id" validate:"required"`
}

type BatchRemoveEntityTagsResponse struct {
	SuccessCount int                             `json:"success_count"`
	FailedCount  int                             `json:"failed_count"`
	Results      []EntityTagBatchOperationResult `json:"results"`
}

type ReplaceEntityTagsRequest struct {
	EntityType domain.EntityType `json:"entity_type" validate:"required"`
	EntityID   uuid.UUID         `json:"entity_id" validate:"required"`
	TagIDs     []uuid.UUID       `json:"tag_ids" validate:"required"`
	Source     domain.TagSource  `json:"source"`
	ExpiresAt  *time.Time        `json:"expires_at"`
	Remarks    *string           `json:"remarks"`
}

type EntityTagBatchOperationResult struct {
	EntityType string    `json:"entity_type"`
	EntityID   uuid.UUID `json:"entity_id"`
	TagID      uuid.UUID `json:"tag_id"`
	Status     string    `json:"status"`
	Error      string    `json:"error,omitempty"`
}

type ListTagEntitiesRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Source   *domain.TagSource      `json:"source"`
	Active   *bool                  `json:"active"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListTagEntitiesResponse struct {
	EntityTags []*domain.EntityTag `json:"entity_tags"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

type ListExpiringTagsRequest struct {
	Page       int                    `json:"page" validate:"min=1"`
	PageSize   int                    `json:"page_size" validate:"min=1,max=100"`
	EntityType *domain.EntityType     `json:"entity_type"`
	Filters    map[string]interface{} `json:"filters"`
	OrderBy    string                 `json:"order_by"`
	OrderDir   string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListExpiringTagsResponse struct {
	EntityTags []*domain.EntityTag `json:"entity_tags"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

type CleanupExpiredTagsResponse struct {
	CleanedCount int `json:"cleaned_count"`
}

// 用户标签请求结构体
type ListUserTagsRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	GroupID  *uuid.UUID             `json:"group_id"`
	Source   *domain.TagSource      `json:"source"`
	Active   *bool                  `json:"active"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListUserTagsResponse struct {
	EntityTags []*domain.EntityTag `json:"entity_tags"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

type AddUserTagRequest struct {
	Source    domain.TagSource `json:"source"`
	ExpiresAt *time.Time       `json:"expires_at"`
	Remarks   *string          `json:"remarks"`
}

// 站点标签请求结构体
type ListStationTagsRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	GroupID  *uuid.UUID             `json:"group_id"`
	Source   *domain.TagSource      `json:"source"`
	Active   *bool                  `json:"active"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListStationTagsResponse struct {
	EntityTags []*domain.EntityTag `json:"entity_tags"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

type AddStationTagRequest struct {
	Source    domain.TagSource `json:"source"`
	ExpiresAt *time.Time       `json:"expires_at"`
	Remarks   *string          `json:"remarks"`
}

// 统计分析请求结构体
type TagUsageTrendRequest struct {
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
	GroupBy   string    `json:"group_by" validate:"required,oneof=day week month"`
}

type EntityTaggingTrendRequest struct {
	StartTime time.Time `json:"start_time" validate:"required"`
	EndTime   time.Time `json:"end_time" validate:"required"`
	GroupBy   string    `json:"group_by" validate:"required,oneof=day week month"`
}

type ListUntaggedEntitiesRequest struct {
	Page     int                    `json:"page" validate:"min=1"`
	PageSize int                    `json:"page_size" validate:"min=1,max=100"`
	Filters  map[string]interface{} `json:"filters"`
	OrderBy  string                 `json:"order_by"`
	OrderDir string                 `json:"order_dir" validate:"omitempty,oneof=asc desc"`
}

type ListUntaggedEntitiesResponse struct {
	EntityIDs  []uuid.UUID `json:"entity_ids"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}
