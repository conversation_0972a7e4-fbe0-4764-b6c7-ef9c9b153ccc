package service

import (
	"context"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
)

// SSOService SSO服务接口
type SSOService interface {
	// SSO Authentication
	InitiateSSO(ctx context.Context, req *InitiateSSORequest) (*SSOInitiationResponse, error)
	ValidateSSO(ctx context.Context, req *ValidateSSORequest) (*SSOValidationResponse, error)
	RefreshSSOToken(ctx context.Context, req *RefreshSSOTokenRequest) (*SSOTokenResponse, error)
	RevokeSSOToken(ctx context.Context, tokenHash string) error
	
	// Token Management
	CreateSSOToken(ctx context.Context, req *CreateSSOTokenRequest) (*domain.SSOToken, error)
	GetSSOToken(ctx context.Context, tokenHash string) (*domain.SSOToken, error)
	ValidateToken(ctx context.Context, tokenHash string, targetSystem string) (*TokenValidationResult, error)
	RevokeUserTokens(ctx context.Context, userID uuid.UUID, sourceSystem string) error
	CleanupExpiredTokens(ctx context.Context) (int64, error)
	
	// System Integration
	RegisterSystem(ctx context.Context, req *RegisterSystemRequest) (*domain.SystemIntegration, error)
	GetSystemIntegration(ctx context.Context, systemCode string) (*domain.SystemIntegration, error)
	UpdateSystemIntegration(ctx context.Context, systemCode string, req *UpdateSystemIntegrationRequest) (*domain.SystemIntegration, error)
	ListSystemIntegrations(ctx context.Context, filter *SystemIntegrationFilter) ([]*domain.SystemIntegration, error)
	
	// User System Profiles
	CreateUserSystemProfile(ctx context.Context, req *CreateUserSystemProfileRequest) (*domain.UserSystemProfile, error)
	GetUserSystemProfile(ctx context.Context, userID uuid.UUID, systemCode string) (*domain.UserSystemProfile, error)
	UpdateUserSystemProfile(ctx context.Context, profileID uuid.UUID, req *UpdateUserSystemProfileRequest) (*domain.UserSystemProfile, error)
	SyncUserToSystem(ctx context.Context, userID uuid.UUID, systemCode string) error
	GetUserAccessibleSystems(ctx context.Context, userID uuid.UUID) ([]*SystemAccessInfo, error)
	
	// Permission Mapping
	CreatePermissionMapping(ctx context.Context, req *CreatePermissionMappingRequest) (*domain.SystemPermissionMapping, error)
	GetPermissionMappings(ctx context.Context, sourceSystem, targetSystem string) ([]*domain.SystemPermissionMapping, error)
	MapPermissions(ctx context.Context, userID uuid.UUID, sourceSystem, targetSystem string, sourcePermissions []string) ([]string, error)
	
	// System Synchronization
	SyncUserData(ctx context.Context, req *SyncUserDataRequest) (*SyncResult, error)
	SyncPermissions(ctx context.Context, req *SyncPermissionsRequest) (*SyncResult, error)
	GetSyncLogs(ctx context.Context, filter *SyncLogFilter) ([]*domain.SystemSyncLog, error)
	
	// Cross-System Operations
	ValidateCrossSystemAccess(ctx context.Context, userID uuid.UUID, sourceSystem, targetSystem, resource string) (bool, error)
	GetUserSystemPermissions(ctx context.Context, userID uuid.UUID, systemCode string) ([]*SystemPermission, error)
}

// InitiateSSORequest SSO发起请求
type InitiateSSORequest struct {
	UserID       uuid.UUID              `json:"user_id" validate:"required"`
	SourceSystem string                 `json:"source_system" validate:"required"`
	TargetSystem string                 `json:"target_system" validate:"required"`
	ClientID     string                 `json:"client_id" validate:"required"`
	RedirectURI  string                 `json:"redirect_uri" validate:"required"`
	Scopes       []string               `json:"scopes"`
	DeviceInfo   map[string]interface{} `json:"device_info"`
	IPAddress    string                 `json:"ip_address"`
	UserAgent    string                 `json:"user_agent"`
}

// SSOInitiationResponse SSO发起响应
type SSOInitiationResponse struct {
	AuthorizationURL string `json:"authorization_url"`
	State           string `json:"state"`
	CodeChallenge   string `json:"code_challenge,omitempty"`
	ExpiresIn       int    `json:"expires_in"`
}

// ValidateSSORequest SSO验证请求
type ValidateSSORequest struct {
	AuthorizationCode string `json:"authorization_code" validate:"required"`
	State            string `json:"state" validate:"required"`
	ClientID         string `json:"client_id" validate:"required"`
	CodeVerifier     string `json:"code_verifier,omitempty"`
}

// SSOValidationResponse SSO验证响应
type SSOValidationResponse struct {
	AccessToken  string                 `json:"access_token"`
	RefreshToken string                 `json:"refresh_token,omitempty"`
	IDToken      string                 `json:"id_token,omitempty"`
	TokenType    string                 `json:"token_type"`
	ExpiresIn    int                    `json:"expires_in"`
	Scope        string                 `json:"scope"`
	UserInfo     map[string]interface{} `json:"user_info"`
}

// RefreshSSOTokenRequest SSO令牌刷新请求
type RefreshSSOTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
	ClientID     string `json:"client_id" validate:"required"`
	Scopes       []string `json:"scopes"`
}

// SSOTokenResponse SSO令牌响应
type SSOTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token,omitempty"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	Scope        string `json:"scope"`
}

// CreateSSOTokenRequest 创建SSO令牌请求
type CreateSSOTokenRequest struct {
	UserID        uuid.UUID `json:"user_id" validate:"required"`
	TokenType     string    `json:"token_type" validate:"required,oneof=access_token refresh_token id_token"`
	SourceSystem  string    `json:"source_system" validate:"required"`
	TargetSystems []string  `json:"target_systems" validate:"required"`
	Scopes        []string  `json:"scopes"`
	ExpiresIn     int       `json:"expires_in" validate:"required,min=1"`
	ClientID      string    `json:"client_id"`
	SessionID     string    `json:"session_id"`
	DeviceInfo    map[string]interface{} `json:"device_info"`
	IPAddress     string    `json:"ip_address"`
	UserAgent     string    `json:"user_agent"`
}

// TokenValidationResult 令牌验证结果
type TokenValidationResult struct {
	IsValid      bool                   `json:"is_valid"`
	UserID       uuid.UUID              `json:"user_id"`
	TokenType    string                 `json:"token_type"`
	SourceSystem string                 `json:"source_system"`
	Scopes       []string               `json:"scopes"`
	ExpiresAt    string                 `json:"expires_at"`
	UserInfo     map[string]interface{} `json:"user_info"`
	Permissions  []string               `json:"permissions"`
}

// RegisterSystemRequest 注册系统请求
type RegisterSystemRequest struct {
	SystemCode        string                 `json:"system_code" validate:"required,min=2,max=50"`
	SystemName        string                 `json:"system_name" validate:"required,min=2,max=100"`
	SystemType        string                 `json:"system_type" validate:"required,oneof=web mobile api service"`
	IntegrationType   string                 `json:"integration_type" validate:"required,oneof=SSO API DATABASE WEBHOOK"`
	BaseURL           *string                `json:"base_url"`
	APIEndpoint       *string                `json:"api_endpoint"`
	AuthEndpoint      *string                `json:"auth_endpoint"`
	CallbackURL       *string                `json:"callback_url"`
	ConfigData        map[string]interface{} `json:"config_data" validate:"required"`
	AuthConfig        map[string]interface{} `json:"auth_config"`
	PermissionMapping map[string]interface{} `json:"permission_mapping"`
	Version           *string                `json:"version"`
	Vendor            *string                `json:"vendor"`
	Description       *string                `json:"description"`
	IsSSOEnabled      bool                   `json:"is_sso_enabled"`
	IsAutoProvision   bool                   `json:"is_auto_provision"`
	SyncFrequency     string                 `json:"sync_frequency" validate:"oneof=manual hourly daily"`
	CreatedBy         uuid.UUID              `json:"created_by" validate:"required"`
}

// UpdateSystemIntegrationRequest 更新系统集成请求
type UpdateSystemIntegrationRequest struct {
	SystemName        *string                `json:"system_name" validate:"omitempty,min=2,max=100"`
	BaseURL           *string                `json:"base_url"`
	APIEndpoint       *string                `json:"api_endpoint"`
	AuthEndpoint      *string                `json:"auth_endpoint"`
	CallbackURL       *string                `json:"callback_url"`
	ConfigData        map[string]interface{} `json:"config_data"`
	AuthConfig        map[string]interface{} `json:"auth_config"`
	PermissionMapping map[string]interface{} `json:"permission_mapping"`
	Version           *string                `json:"version"`
	Vendor            *string                `json:"vendor"`
	Description       *string                `json:"description"`
	Status            *string                `json:"status" validate:"omitempty,oneof=active inactive maintenance"`
	IsSSOEnabled      *bool                  `json:"is_sso_enabled"`
	IsAutoProvision   *bool                  `json:"is_auto_provision"`
	SyncFrequency     *string                `json:"sync_frequency" validate:"omitempty,oneof=manual hourly daily"`
	UpdatedBy         uuid.UUID              `json:"updated_by" validate:"required"`
}

// SystemIntegrationFilter 系统集成查询过滤器
type SystemIntegrationFilter struct {
	SystemType      []string `json:"system_type"`
	IntegrationType []string `json:"integration_type"`
	Status          []string `json:"status"`
	IsSSOEnabled    *bool    `json:"is_sso_enabled"`
	Keyword         *string  `json:"keyword"`
	Offset          *int     `json:"offset"`
	Limit           *int     `json:"limit"`
	SortBy          *string  `json:"sort_by"`
	SortOrder       *string  `json:"sort_order"`
}

// CreateUserSystemProfileRequest 创建用户系统配置文件请求
type CreateUserSystemProfileRequest struct {
	UserID              uuid.UUID              `json:"user_id" validate:"required"`
	SystemCode          string                 `json:"system_code" validate:"required"`
	ExternalUserID      *string                `json:"external_user_id"`
	ExternalUsername    *string                `json:"external_username"`
	ExternalEmail       *string                `json:"external_email"`
	ProfileData         map[string]interface{} `json:"profile_data"`
	PermissionOverrides map[string]interface{} `json:"permission_overrides"`
	Preferences         map[string]interface{} `json:"preferences"`
	CreatedBy           uuid.UUID              `json:"created_by" validate:"required"`
}

// UpdateUserSystemProfileRequest 更新用户系统配置文件请求
type UpdateUserSystemProfileRequest struct {
	ExternalUserID      *string                `json:"external_user_id"`
	ExternalUsername    *string                `json:"external_username"`
	ExternalEmail       *string                `json:"external_email"`
	ProfileData         map[string]interface{} `json:"profile_data"`
	PermissionOverrides map[string]interface{} `json:"permission_overrides"`
	Preferences         map[string]interface{} `json:"preferences"`
	Status              *string                `json:"status" validate:"omitempty,oneof=active inactive suspended"`
	UpdatedBy           uuid.UUID              `json:"updated_by" validate:"required"`
}



// CreatePermissionMappingRequest 创建权限映射请求
type CreatePermissionMappingRequest struct {
	SourceSystem        string                 `json:"source_system" validate:"required"`
	TargetSystem        string                 `json:"target_system" validate:"required"`
	SourcePermission    string                 `json:"source_permission" validate:"required"`
	TargetPermission    string                 `json:"target_permission" validate:"required"`
	MappingType         string                 `json:"mapping_type" validate:"required,oneof=direct transformed conditional"`
	TransformationRules map[string]interface{} `json:"transformation_rules"`
	Conditions          map[string]interface{} `json:"conditions"`
	Priority            int                    `json:"priority"`
	Description         *string                `json:"description"`
	IsBidirectional     bool                   `json:"is_bidirectional"`
	CreatedBy           uuid.UUID              `json:"created_by" validate:"required"`
}

// SyncUserDataRequest 同步用户数据请求
type SyncUserDataRequest struct {
	UserID       uuid.UUID `json:"user_id" validate:"required"`
	SourceSystem string    `json:"source_system" validate:"required"`
	TargetSystem string    `json:"target_system" validate:"required"`
	SyncType     string    `json:"sync_type" validate:"required,oneof=full incremental"`
	ForceSync    bool      `json:"force_sync"`
}

// SyncPermissionsRequest 同步权限请求
type SyncPermissionsRequest struct {
	UserID       *uuid.UUID `json:"user_id"`
	SourceSystem string     `json:"source_system" validate:"required"`
	TargetSystem string     `json:"target_system" validate:"required"`
	SyncType     string     `json:"sync_type" validate:"required,oneof=full incremental"`
	ForceSync    bool       `json:"force_sync"`
}

// SyncResult 同步结果
type SyncResult struct {
	SyncID           uuid.UUID `json:"sync_id"`
	Result           string    `json:"result"`
	RecordsProcessed int       `json:"records_processed"`
	RecordsSuccess   int       `json:"records_success"`
	RecordsFailed    int       `json:"records_failed"`
	DurationMs       int64     `json:"duration_ms"`
	ErrorMessage     *string   `json:"error_message"`
	Details          map[string]interface{} `json:"details"`
}

// SyncLogFilter 同步日志查询过滤器
type SyncLogFilter struct {
	SyncType     []string    `json:"sync_type"`
	SourceSystem []string    `json:"source_system"`
	TargetSystem []string    `json:"target_system"`
	SyncResult   []string    `json:"sync_result"`
	EntityType   []string    `json:"entity_type"`
	UserID       *uuid.UUID  `json:"user_id"`
	StartDate    *string     `json:"start_date"`
	EndDate      *string     `json:"end_date"`
	Offset       *int        `json:"offset"`
	Limit        *int        `json:"limit"`
	SortBy       *string     `json:"sort_by"`
	SortOrder    *string     `json:"sort_order"`
}

// SystemPermission 系统权限
type SystemPermission struct {
	PermissionCode string                 `json:"permission_code"`
	PermissionName string                 `json:"permission_name"`
	ResourceType   string                 `json:"resource_type"`
	Actions        []string               `json:"actions"`
	Scope          map[string]interface{} `json:"scope"`
	Source         string                 `json:"source"` // direct, mapped, inherited
}
