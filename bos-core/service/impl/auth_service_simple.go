package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/config"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/errors"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// simpleAuthService 简化的认证服务实现
type simpleAuthService struct {
	repos      repository.RepositoryManager
	jwtManager *utils.JWTManager
	config     *config.Config
	logger     logger.Logger
}

// NewSimpleAuthService 创建简化认证服务实例
func NewSimpleAuthService(
	repos repository.RepositoryManager,
	jwtManager *utils.JWTManager,
	config *config.Config,
	logger logger.Logger,
) service.AuthService {
	return &simpleAuthService{
		repos:      repos,
		jwtManager: jwtManager,
		config:     config,
		logger:     logger,
	}
}

// Login 用户登录
func (s *simpleAuthService) Login(ctx context.Context, username, password, system, ipAddress, userAgent string) (*service.LoginResult, error) {
	// 获取用户 - 支持用户名或邮箱登录
	var user *domain.User
	var err error
	if utils.IsEmailAddress(username) {
		// 如果输入的是邮箱地址，通过邮箱查找用户
		user, err = s.repos.User().GetByEmail(ctx, utils.NormalizeEmail(username))
	} else {
		// 否则通过用户名查找用户
		user, err = s.repos.User().GetByUsername(ctx, username)
	}
	
	if err != nil {
		s.createLoginLog(ctx, nil, username, ipAddress, userAgent, domain.LogResultFailure, "用户不存在")
		return nil, errors.ErrInvalidCredentialsError
	}

	// 检查用户状态
	if user.Status != domain.UserStatusActive {
		s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "用户已被禁用")
		return nil, errors.ErrUserDisabledError
	}

	// 验证密码
	if !utils.VerifyPassword(password, user.PasswordHash) {
		s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "密码错误")
		return nil, errors.ErrInvalidCredentialsError
	}

	// 创建会话
	session := &domain.Session{
		ID:        uuid.New(),
		SessionID: uuid.New().String(), // 生成唯一的SessionID
		UserID:    user.ID,
		IPAddress: ipAddress,
		UserAgent: &userAgent, // 修复：使用指针类型
		Status:    domain.SessionStatusActive,
		ExpiresAt: time.Now().Add(s.config.JWT.ExpiresIn),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.repos.Session().Create(ctx, session); err != nil {
		s.logger.Error("创建会话失败")
		return nil, fmt.Errorf("创建会话失败: %w", err)
	}

	// 生成JWT令牌
	accessToken, err := s.jwtManager.GenerateAccessToken(
		user.ID.String(),
		user.Username,
		user.Email,
		[]string{}, // 暂时空角色
		session.SessionID,
		system,     // 传入的系统参数
		"",         // 简单服务不设置访问级别
		[]int64{},  // 简单服务不设置站点ID
	)
	if err != nil {
		s.logger.Error("生成访问令牌失败")
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}

	refreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID.String(), session.SessionID)
	if err != nil {
		s.logger.Error("生成刷新令牌失败")
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}

	// 更新用户登录信息
	now := time.Now()
	user.LastLoginAt = &now

	user.UpdatedAt = time.Now()
	if err := s.repos.User().Update(ctx, user); err != nil {
		s.logger.Error("更新用户登录信息失败")
	}

	// 加载用户的站点关联信息
	userWithStations, err := s.repos.User().GetUserWithStationRoles(ctx, user.ID)
	if err != nil {
		s.logger.Error("获取用户站点关联信息失败")
		// 不影响登录流程，使用原用户信息
		userWithStations = user
	}

	// 获取用户关联的站点ID列表
	var stationIDs []int64
	if userWithStations != nil && len(userWithStations.StationRoles) > 0 {
		// 获取用户管理的站点ID列表（包含manager和supervisor角色）
		managedStationIDs := userWithStations.GetStationIDsWithManagementAccess()
		
		// 验证站点是否真实有效
		if len(managedStationIDs) > 0 {
			validStationIDs := make([]int64, 0, len(managedStationIDs))
			for _, stationID := range managedStationIDs {
				// 检查站点是否存在
				station, err := s.repos.Station().GetByID(ctx, stationID)
				if err != nil {
					// 特别处理记录不存在的情况
					if err == gorm.ErrRecordNotFound {
						s.logger.Warn("站点不存在，跳过返回", 
							zap.Int64("station_id", stationID), 
							zap.String("user_id", user.ID.String()),
							zap.String("reason", "station_not_found"))
					} else {
						s.logger.Error("查询站点信息失败", 
							zap.Int64("station_id", stationID), 
							zap.String("user_id", user.ID.String()),
							zap.Error(err))
					}
					continue
				}
				
				// 确保站点对象不为空
				if station == nil {
					s.logger.Warn("站点查询返回空对象，跳过返回", 
						zap.Int64("station_id", stationID),
						zap.String("user_id", user.ID.String()))
					continue
				}
				
				// 检查站点是否为活跃状态
				if !station.IsActive() {
					s.logger.Warn("站点状态非活跃，跳过返回", 
						zap.Int64("station_id", stationID), 
						zap.String("user_id", user.ID.String()),
						zap.String("status", string(station.BusinessStatus)),
						zap.String("site_code", station.SiteCode),
						zap.String("site_name", station.SiteName))
					continue
				}
				
				// 验证站点基本信息完整性
				if station.SiteCode == "" || station.SiteName == "" {
					s.logger.Warn("站点基本信息不完整，跳过返回", 
						zap.Int64("station_id", stationID),
						zap.String("user_id", user.ID.String()),
						zap.String("site_code", station.SiteCode),
						zap.String("site_name", station.SiteName))
					continue
				}
				
				// 站点验证通过，添加到有效列表
				validStationIDs = append(validStationIDs, stationID)
				s.logger.Info("站点验证通过", 
					zap.Int64("station_id", stationID),
					zap.String("user_id", user.ID.String()),
					zap.String("site_code", station.SiteCode),
					zap.String("site_name", station.SiteName),
					zap.String("status", string(station.BusinessStatus)))
			}
			stationIDs = validStationIDs
			
			// 记录最终结果
			s.logger.Info("站点权限验证完成", 
				zap.String("user_id", user.ID.String()),
				zap.Int("total_managed_stations", len(managedStationIDs)),
				zap.Int("valid_stations", len(validStationIDs)),
				zap.Int64s("valid_station_ids", validStationIDs))
		}
	}

	// 记录成功登录日志
	s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultSuccess, "登录成功")

	return &service.LoginResult{
		User:         userWithStations,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    session.ExpiresAt,
		SessionID:    session.ID,
		StationIDs:   stationIDs,
	}, nil
}

// Logout 用户登出
func (s *simpleAuthService) Logout(ctx context.Context, token string) error {
	// 验证令牌并获取会话信息
	claims, err := s.jwtManager.ValidateToken(token)
	if err != nil {
		return errors.ErrInvalidTokenError
	}

	// 获取会话并更新状态 - 直接使用SessionID字符串
	session, err := s.repos.Session().GetBySessionID(ctx, claims.SessionID)
	if err != nil {
		return errors.ErrSessionNotFoundError
	}

	session.Status = domain.SessionStatusTerminated
	session.UpdatedAt = time.Now()
	if err := s.repos.Session().Update(ctx, session); err != nil {
		s.logger.Error("终止会话失败")
		return fmt.Errorf("终止会话失败: %w", err)
	}

	return nil
}

// RefreshToken 刷新令牌
func (s *simpleAuthService) RefreshToken(ctx context.Context, refreshToken string) (*service.TokenResult, error) {
	// 验证刷新令牌
	claims, err := s.jwtManager.ValidateToken(refreshToken)
	if err != nil {
		return nil, errors.ErrInvalidTokenError
	}

	if claims.TokenType != "refresh" {
		return nil, errors.ErrInvalidTokenError
	}

	// 检查会话是否有效 - 直接使用SessionID字符串
	session, err := s.repos.Session().GetBySessionID(ctx, claims.SessionID)
	if err != nil {
		return nil, errors.ErrSessionNotFoundError
	}

	if session.Status != domain.SessionStatusActive {
		return nil, errors.ErrSessionExpiredError
	}

	// 解析用户ID
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, errors.ErrInvalidTokenError
	}

	// 检查用户是否仍然有效
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return nil, errors.ErrUserNotFoundError
	}

	if user.Status != domain.UserStatusActive {
		return nil, errors.ErrUserDisabledError
	}

	// 生成新的令牌
	newAccessToken, err := s.jwtManager.GenerateAccessToken(
		user.ID.String(),
		user.Username,
		user.Email,
		[]string{}, // 暂时空角色
		session.SessionID,
		"",         // 简单服务不设置系统
		"",         // 简单服务不设置访问级别
		[]int64{},  // 简单服务不设置站点ID
	)
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}

	newRefreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID.String(), session.SessionID)
	if err != nil {
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}

	// 延长会话有效期
	session.ExpiresAt = time.Now().Add(s.config.JWT.ExpiresIn)
	session.UpdatedAt = time.Now()
	if err := s.repos.Session().Update(ctx, session); err != nil {
		s.logger.Error("更新会话失败")
	}

	return &service.TokenResult{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    session.ExpiresAt,
	}, nil
}

// ValidateToken 验证令牌
func (s *simpleAuthService) ValidateToken(ctx context.Context, token string) (*domain.User, error) {
	// 验证令牌
	claims, err := s.jwtManager.ValidateToken(token)
	if err != nil {
		return nil, errors.ErrInvalidTokenError
	}

	// 检查会话是否有效 - 直接使用SessionID字符串
	session, err := s.repos.Session().GetBySessionID(ctx, claims.SessionID)
	if err != nil {
		return nil, errors.ErrSessionNotFoundError
	}

	if session.Status != domain.SessionStatusActive {
		return nil, errors.ErrSessionExpiredError
	}

	// 解析用户ID
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, errors.ErrInvalidTokenError
	}

	// 获取用户信息
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return nil, errors.ErrUserNotFoundError
	}

	if user.Status != domain.UserStatusActive {
		return nil, errors.ErrUserDisabledError
	}

	return user, nil
}

// ChangePassword 修改密码
func (s *simpleAuthService) ChangePassword(ctx context.Context, userID uuid.UUID, oldPassword, newPassword string) error {
	// 获取用户
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if !utils.VerifyPassword(oldPassword, user.PasswordHash) {
		return errors.ErrInvalidCredentialsError
	}

	// 验证新密码强度
	if !utils.IsPasswordStrong(newPassword) {
		return errors.ErrWeakPasswordError
	}

	// 生成新密码哈希
	newPasswordHash, err := utils.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 生成新的盐值（虽然bcrypt不需要，但数据库设计有这个字段）
	newSalt, err := utils.GenerateSalt(24)
	if err != nil {
		return fmt.Errorf("生成盐值失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = newPasswordHash
	user.Salt = newSalt
	user.UpdatedAt = time.Now()

	if err := s.repos.User().Update(ctx, user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}

// ResetPassword 重置密码
func (s *simpleAuthService) ResetPassword(ctx context.Context, userID uuid.UUID, newPassword string) error {
	// 获取用户
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证新密码强度
	if !utils.IsPasswordStrong(newPassword) {
		return errors.ErrWeakPasswordError
	}

	// 生成新密码哈希
	newPasswordHash, err := utils.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 生成新的盐值（虽然bcrypt不需要，但数据库设计有这个字段）
	newSalt, err := utils.GenerateSalt(24)
	if err != nil {
		return fmt.Errorf("生成盐值失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = newPasswordHash
	user.Salt = newSalt
	user.UpdatedAt = time.Now()

	if err := s.repos.User().Update(ctx, user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}

// GetUserSessions 获取用户会话
func (s *simpleAuthService) GetUserSessions(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error) {
	return s.repos.Session().GetActiveSessions(ctx, userID)
}

// TerminateSession 终止会话
func (s *simpleAuthService) TerminateSession(ctx context.Context, sessionID uuid.UUID) error {
	session, err := s.repos.Session().GetByID(ctx, sessionID)
	if err != nil {
		return errors.ErrSessionNotFoundError
	}

	session.Status = domain.SessionStatusTerminated
	session.UpdatedAt = time.Now()
	return s.repos.Session().Update(ctx, session)
}

// TerminateAllSessions 终止所有会话
func (s *simpleAuthService) TerminateAllSessions(ctx context.Context, userID uuid.UUID, excludeSessionID *uuid.UUID) error {
	sessions, err := s.repos.Session().GetActiveSessions(ctx, userID)
	if err != nil {
		return err
	}

	for _, session := range sessions {
		if excludeSessionID != nil && session.ID == *excludeSessionID {
			continue
		}
		session.Status = domain.SessionStatusTerminated
		session.UpdatedAt = time.Now()
		if err := s.repos.Session().Update(ctx, session); err != nil {
			s.logger.Error("终止会话失败")
		}
	}

	return nil
}

// createLoginLog 创建登录日志
func (s *simpleAuthService) createLoginLog(ctx context.Context, userID *uuid.UUID, username, ipAddress, userAgent string, result domain.LogResult, message string) {
	var userAgentPtr *string
	if userAgent != "" {
		userAgentPtr = &userAgent
	}

	var failureReasonPtr *string
	if message != "" {
		failureReasonPtr = &message
	}

	log := &domain.LoginLog{
		ID:            uuid.New(),
		UserID:        userID,
		Username:      username,
		IPAddress:     ipAddress,
		UserAgent:     userAgentPtr,
		LoginResult:   result,
		FailureReason: failureReasonPtr,
		LoginAt:       time.Now(),
		CreatedAt:     time.Now(),
	}

	if err := s.repos.LoginLog().Create(ctx, log); err != nil {
		s.logger.Error("创建登录日志失败")
	}
}

// ValidateTokenWithContext 验证令牌并获取上下文
func (s *simpleAuthService) ValidateTokenWithContext(ctx context.Context, token string) (*service.AuthContext, error) {
	return nil, fmt.Errorf("功能未实现")
}

// ValidateOrganizationAccess 验证组织访问权限
func (s *simpleAuthService) ValidateOrganizationAccess(ctx context.Context, userID uuid.UUID, organizationID int64, requiredRole string) (bool, error) {
	return false, fmt.Errorf("功能未实现")
}

// GetUserOrganizationContext 获取用户组织上下文
func (s *simpleAuthService) GetUserOrganizationContext(ctx context.Context, userID uuid.UUID) (*service.OrganizationContext, error) {
	return nil, fmt.Errorf("功能未实现")
}

// SwitchOrganizationContext 切换组织上下文
func (s *simpleAuthService) SwitchOrganizationContext(ctx context.Context, userID uuid.UUID, organizationID int64) (*service.OrganizationContext, error) {
	return nil, fmt.Errorf("功能未实现")
}

// LoginWithOrganization 组织登录
func (s *simpleAuthService) LoginWithOrganization(ctx context.Context, req *service.OrganizationLoginRequest) (*service.LoginResult, error) {
	return nil, fmt.Errorf("功能未实现")
}

// InitiateSSO 发起SSO认证
func (s *simpleAuthService) InitiateSSO(ctx context.Context, req *service.InitiateSSORequest) (*service.SSOInitiationResponse, error) {
	return nil, fmt.Errorf("功能未实现")
}

// ValidateSSO 验证SSO认证
func (s *simpleAuthService) ValidateSSO(ctx context.Context, req *service.ValidateSSORequest) (*service.SSOValidationResponse, error) {
	return nil, fmt.Errorf("功能未实现")
}

// GetSSOToken 获取SSO令牌
func (s *simpleAuthService) GetSSOToken(ctx context.Context, userID uuid.UUID, targetSystem string) (*service.SSOTokenInfo, error) {
	return nil, fmt.Errorf("功能未实现")
}
