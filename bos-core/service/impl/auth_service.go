package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/config"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/errors"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
	"go.uber.org/zap"
)

// authService 认证服务实现
type authService struct {
	repos             repository.RepositoryManager
	jwtManager        *utils.JWTManager
	config            *config.Config
	logger            logger.Logger
	systemAccessSvc   service.SystemAccessService
}

// NewAuthService 创建认证服务实例
func NewAuthService(
	repos repository.RepositoryManager,
	jwtManager *utils.JWTManager,
	config *config.Config,
	logger logger.Logger,
	systemAccessSvc service.SystemAccessService,
) service.AuthService {
	return &authService{
		repos:           repos,
		jwtManager:      jwtManager,
		config:          config,
		logger:          logger,
		systemAccessSvc: systemAccessSvc,
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, username, password, system, ipAddress, userAgent string) (*service.LoginResult, error) {
	// 账户锁定功能已禁用 - 不再检查登录失败次数
	// failedAttempts, err := s.repos.LoginLog().GetFailedAttemptsCount(ctx, username, time.Now().Add(-s.config.Security.LoginPolicy.LockoutDuration))
	// if err != nil {
	//	s.logger.Error("获取登录失败次数失败")
	// }

	// 账户锁定功能已禁用 - 不再检查是否超过最大失败次数
	// if failedAttempts >= int64(s.config.Security.LoginPolicy.MaxFailedAttempts) {
	//	s.createLoginLog(ctx, nil, username, ipAddress, userAgent, domain.LogResultFailure, "账户已被锁定")
	//	return nil, errors.ErrAccountLockedError
	// }

	// 获取用户 - 支持用户名或邮箱登录
	var user *domain.User
	var err error
	if utils.IsEmailAddress(username) {
		// 如果输入的是邮箱地址，通过邮箱查找用户
		user, err = s.repos.User().GetByEmail(ctx, utils.NormalizeEmail(username))
	} else {
		// 否则通过用户名查找用户
		user, err = s.repos.User().GetByUsername(ctx, username)
	}
	
	if err != nil {
		s.createLoginLog(ctx, nil, username, ipAddress, userAgent, domain.LogResultFailure, "用户不存在")
		return nil, errors.ErrInvalidCredentialsError
	}

	// 检查用户状态
	if !user.IsActive() {
		s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "用户已被禁用")
		return nil, errors.ErrUserDisabledError
	}

	// 账户锁定功能已禁用 - 不再检查用户锁定状态
	// if user.IsLocked() {
	//	s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "用户已被锁定")
	//	return nil, errors.ErrAccountLockedError
	// }

	// 验证密码
	if !utils.VerifyPassword(password, user.PasswordHash) {
		s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "密码错误")
		return nil, errors.ErrInvalidCredentialsError
	}

	// 根据系统类型进行不同的权限验证
	var systemAccessInfo *service.SystemAccessInfo
	var accessLevel string
	var systemStationIDs []int64

	switch system {
	case "HOS":
		// HOS系统使用新的系统权限模型
		// 检查用户是否有HOS系统访问权限
		hasAccess, err := s.repos.UserSystemAccess().HasSystemAccess(ctx, user.ID, domain.SystemCodeHOS)
		if err != nil {
			s.logger.Error("检查HOS系统访问权限失败", zap.Error(err))
			s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "检查HOS系统权限失败")
			return nil, fmt.Errorf("failed to check HOS system access: %w", err)
		}

		if !hasAccess {
			s.logger.Warn("用户没有HOS系统访问权限",
				zap.String("user_id", user.ID.String()),
				zap.String("username", username))
			s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "Insufficient permissions for HOS system")
			return nil, fmt.Errorf("insufficient permissions for HOS system")
		}

		// 获取HOS系统访问权限详情
		hosAccess, err := s.repos.UserSystemAccess().GetByUserAndSystem(ctx, user.ID, domain.SystemCodeHOS)
		if err != nil {
			s.logger.Error("获取HOS系统访问权限详情失败", zap.Error(err))
			s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "获取HOS系统权限详情失败")
			return nil, fmt.Errorf("failed to get HOS system access details: %w", err)
		}

		accessLevel = string(hosAccess.AccessLevel)
		systemStationIDs = []int64{} // HOS系统使用全局权限，不需要站点ID

		systemAccessInfo = &service.SystemAccessInfo{
			System:       system,
			AccessLevel:  accessLevel,
			StationIDs:   systemStationIDs,
			StationCount: 0, // 全局权限
		}

	default:
		// BOS和EDC系统使用现有的站点权限模型
		userWithStations, err := s.repos.User().GetUserWithStationRoles(ctx, user.ID)
		if err != nil {
			s.logger.Error("获取用户站点关联信息失败", zap.Error(err))
			s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, "获取用户权限失败")
			return nil, fmt.Errorf("failed to get user station roles: %w", err)
		}

		// 验证系统访问权限
		if err := s.systemAccessSvc.ValidateSystemAccessRequest(userWithStations.StationRoles, system); err != nil {
			s.logger.Warn("系统访问权限验证失败",
				zap.String("user_id", user.ID.String()),
				zap.String("system", system),
				zap.Error(err))
			s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultFailure, err.Error())
			return nil, err
		}

		// 获取系统访问信息
		accessLevel = s.systemAccessSvc.GetSystemAccessLevel(userWithStations.StationRoles, system)
		systemStationIDs = s.systemAccessSvc.GetSystemStationIDs(userWithStations.StationRoles, system)

		systemAccessInfo = &service.SystemAccessInfo{
			System:       system,
			AccessLevel:  accessLevel,
			StationIDs:   systemStationIDs,
			StationCount: len(systemStationIDs),
		}
	}

	// 创建会话
	sessionID := uuid.New().String()
	now := time.Now()
	session := &domain.Session{
		ID:             uuid.New(),
		SessionID:      sessionID,
		UserID:         user.ID,
		IPAddress:      ipAddress,
		UserAgent:      &userAgent,
		LoginAt:        &now,
		LastActivityAt: &now,
		Status:         domain.SessionStatusActive,
		ExpiresAt:      time.Now().Add(s.config.JWT.ExpiresIn),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err := s.repos.Session().Create(ctx, session); err != nil {
		s.logger.Error("创建会话失败")
		return nil, fmt.Errorf("创建会话失败: %w", err)
	}

	// 获取用户角色
	roles, err := s.repos.User().GetUserRoles(ctx, user.ID)
	if err != nil {
		s.logger.Error("获取用户角色失败")
		roles = []*domain.Role{} // 默认空角色
	}

	roleNames := make([]string, len(roles))
	for i, role := range roles {
		roleNames[i] = role.Name
	}

	// 生成JWT令牌
	accessToken, err := s.jwtManager.GenerateAccessToken(
		user.ID.String(),
		user.Username,
		user.Email,
		roleNames,
		session.SessionID,
		system,
		accessLevel,
		systemStationIDs,
	)
	if err != nil {
		s.logger.Error("生成访问令牌失败")
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}

	refreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID.String(), session.SessionID)
	if err != nil {
		s.logger.Error("生成刷新令牌失败")
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}

	// 更新用户登录信息
	user.UpdateLastLogin()
	user.UpdatedAt = time.Now()
	if err := s.repos.User().Update(ctx, user); err != nil {
		s.logger.Error("更新用户登录信息失败", zap.Error(err))
	}

	// 记录成功登录日志
	s.createLoginLog(ctx, &user.ID, username, ipAddress, userAgent, domain.LogResultSuccess, "登录成功")

	s.logger.Info("用户登录成功",
		zap.String("user_id", user.ID.String()),
		zap.String("username", username),
		zap.String("system", system),
		zap.String("access_level", systemAccessInfo.AccessLevel),
		zap.Int("station_count", systemAccessInfo.StationCount),
		zap.Int64s("station_ids", systemAccessInfo.StationIDs))

	return &service.LoginResult{
		User:         user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    session.ExpiresAt,
		SessionID:    session.ID,
		StationIDs:   systemStationIDs, // 保持向后兼容
		SystemAccess: systemAccessInfo,
	}, nil
}

// Logout 用户登出
func (s *authService) Logout(ctx context.Context, token string) error {
	// 验证令牌并获取会话信息
	claims, err := s.jwtManager.ValidateToken(token)
	if err != nil {
		return errors.ErrInvalidTokenError
	}

	// 终止会话 - 先通过SessionID字符串获取session，然后使用主键ID终止
	session, err := s.repos.Session().GetBySessionID(ctx, claims.SessionID)
	if err != nil {
		s.logger.Error("获取会话失败", zap.Error(err))
		return errors.ErrSessionNotFoundError
	}
	if err := s.repos.Session().TerminateSession(ctx, session.ID); err != nil {
		s.logger.Error("终止会话失败", zap.Error(err))
		return fmt.Errorf("终止会话失败: %w", err)
	}

	return nil
}

// RefreshToken 刷新令牌
func (s *authService) RefreshToken(ctx context.Context, refreshToken string) (*service.TokenResult, error) {
	// 验证刷新令牌
	claims, err := s.jwtManager.ValidateToken(refreshToken)
	if err != nil {
		return nil, errors.ErrInvalidTokenError
	}
	if claims.TokenType != "refresh" {
		return nil, errors.ErrInvalidTokenError
	}

	// 查找会话
	session, err := s.repos.Session().GetByRefreshToken(ctx, refreshToken)
	if err != nil {
		return nil, errors.ErrSessionNotFoundError
	}
	if !session.IsActive() {
		return nil, errors.ErrSessionExpiredError
	}

	// 查找用户
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, errors.ErrUserNotFoundError
	}
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return nil, errors.ErrUserNotFoundError
	}
	if !user.IsActive() {
		return nil, errors.ErrUserDisabledError
	}

	// 生成新令牌
	roleNames := user.GetRoleNames()
	// 从原始token中获取系统信息
	system := claims.System
	accessLevel := claims.AccessLevel
	stationIDs := claims.StationIDs

	newAccessToken, err := s.jwtManager.GenerateAccessToken(user.ID.String(), user.Username, user.Email, roleNames, session.SessionID, system, accessLevel, stationIDs)
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}
	newRefreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID.String(), session.SessionID)
	if err != nil {
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}

	// 延长会话有效期
	session.Extend(s.config.JWT.ExpiresIn)
	if err := s.repos.Session().Update(ctx, session); err != nil {
		s.logger.Error("更新会话失败", zap.Error(err))
	}

	return &service.TokenResult{
		AccessToken:  newAccessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    session.ExpiresAt,
	}, nil
}

// ValidateToken 验证令牌
func (s *authService) ValidateToken(ctx context.Context, token string) (*domain.User, error) {
	// 验证令牌
	claims, err := s.jwtManager.ValidateToken(token)
	if err != nil {
		return nil, errors.ErrInvalidTokenError
	}

	// 检查会话是否有效 - 使用SessionID字符串查找
	session, err := s.repos.Session().GetBySessionID(ctx, claims.SessionID)
	if err != nil {
		return nil, errors.ErrSessionNotFoundError
	}

	if !session.IsActive() {
		return nil, errors.ErrSessionExpiredError
	}

	// 获取用户信息
	userUUID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, errors.ErrUserNotFoundError
	}
	user, err := s.repos.User().GetByID(ctx, userUUID)
	if err != nil {
		return nil, errors.ErrUserNotFoundError
	}

	if !user.IsActive() {
		return nil, errors.ErrUserDisabledError
	}

	return user, nil
}

// ChangePassword 修改密码
func (s *authService) ChangePassword(ctx context.Context, userID uuid.UUID, oldPassword, newPassword string) error {
	// 获取用户
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if !utils.VerifyPassword(oldPassword, user.PasswordHash) {
		return errors.ErrInvalidCredentialsError
	}

	// 验证新密码强度
	if !utils.IsPasswordStrong(newPassword) {
		return errors.ErrWeakPasswordError
	}

	// 生成新密码哈希
	newPasswordHash, err := utils.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 生成新的盐值（虽然bcrypt不需要，但数据库设计有这个字段）
	newSalt, err := utils.GenerateSalt(24)
	if err != nil {
		return fmt.Errorf("生成盐值失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = newPasswordHash
	user.Salt = newSalt
	user.UpdatedAt = time.Now()

	if err := s.repos.User().Update(ctx, user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	// 终止其他会话（可选）
	if s.config.Security.LogoutOnPasswordChange {
		if err := s.repos.Session().TerminateUserSessions(ctx, userID, nil); err != nil {
			s.logger.Error("终止用户会话失败", zap.Error(err))
		}
	}

	return nil
}

// ResetPassword 重置密码
func (s *authService) ResetPassword(ctx context.Context, userID uuid.UUID, newPassword string) error {
	// 获取用户
	user, err := s.repos.User().GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证新密码强度
	if !utils.IsPasswordStrong(newPassword) {
		return errors.ErrWeakPasswordError
	}

	// 生成新密码哈希
	newPasswordHash, err := utils.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("密码哈希失败: %w", err)
	}

	// 生成新的盐值（虽然bcrypt不需要，但数据库设计有这个字段）
	newSalt, err := utils.GenerateSalt(24)
	if err != nil {
		return fmt.Errorf("生成盐值失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = newPasswordHash
	user.Salt = newSalt
	user.UpdatedAt = time.Now()

	if err := s.repos.User().Update(ctx, user); err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	// 终止所有会话
	if err := s.repos.Session().TerminateUserSessions(ctx, userID, nil); err != nil {
		s.logger.Error("终止用户会话失败", zap.Error(err))
	}

	return nil
}

// GetUserSessions 获取用户会话
func (s *authService) GetUserSessions(ctx context.Context, userID uuid.UUID) ([]*domain.Session, error) {
	return s.repos.Session().GetUserSessions(ctx, userID)
}

// TerminateSession 终止会话
func (s *authService) TerminateSession(ctx context.Context, sessionID uuid.UUID) error {
	return s.repos.Session().TerminateSession(ctx, sessionID)
}

// TerminateAllSessions 终止所有会话
func (s *authService) TerminateAllSessions(ctx context.Context, userID uuid.UUID, excludeSessionID *uuid.UUID) error {
	return s.repos.Session().TerminateUserSessions(ctx, userID, excludeSessionID)
}

// createLoginLog 创建登录日志
func (s *authService) createLoginLog(ctx context.Context, userID *uuid.UUID, username, ipAddress, userAgent string, result domain.LogResult, message string) {
	var userAgentPtr *string
	if userAgent != "" {
		userAgentPtr = &userAgent
	}

	var failureReasonPtr *string
	if message != "" {
		failureReasonPtr = &message
	}

	log := &domain.LoginLog{
		ID:            uuid.New(),
		UserID:        userID,
		Username:      username,
		IPAddress:     ipAddress,
		UserAgent:     userAgentPtr,
		LoginResult:   result,
		FailureReason: failureReasonPtr,
		LoginAt:       time.Now(),
		CreatedAt:     time.Now(),
	}

	if err := s.repos.LoginLog().Create(ctx, log); err != nil {
		s.logger.Error("创建登录日志失败", zap.Error(err))
	}
}

// ValidateTokenWithContext 验证令牌并获取上下文
func (s *authService) ValidateTokenWithContext(ctx context.Context, token string) (*service.AuthContext, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}

// ValidateOrganizationAccess 验证组织访问权限
func (s *authService) ValidateOrganizationAccess(ctx context.Context, userID uuid.UUID, organizationID int64, requiredRole string) (bool, error) {
	// 暂时返回未实现
	return false, fmt.Errorf("功能未实现")
}

// GetUserOrganizationContext 获取用户组织上下文
func (s *authService) GetUserOrganizationContext(ctx context.Context, userID uuid.UUID) (*service.OrganizationContext, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}

// SwitchOrganizationContext 切换组织上下文
func (s *authService) SwitchOrganizationContext(ctx context.Context, userID uuid.UUID, organizationID int64) (*service.OrganizationContext, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}

// LoginWithOrganization 组织登录
func (s *authService) LoginWithOrganization(ctx context.Context, req *service.OrganizationLoginRequest) (*service.LoginResult, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}

// InitiateSSO 发起SSO认证
func (s *authService) InitiateSSO(ctx context.Context, req *service.InitiateSSORequest) (*service.SSOInitiationResponse, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}

// ValidateSSO 验证SSO认证
func (s *authService) ValidateSSO(ctx context.Context, req *service.ValidateSSORequest) (*service.SSOValidationResponse, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}

// GetSSOToken 获取SSO令牌
func (s *authService) GetSSOToken(ctx context.Context, userID uuid.UUID, targetSystem string) (*service.SSOTokenInfo, error) {
	// 暂时返回未实现
	return nil, fmt.Errorf("功能未实现")
}
