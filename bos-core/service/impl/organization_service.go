package impl

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/errors"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
	"go.uber.org/zap"
)

// organizationService 组织管理服务实现
type organizationService struct {
	repos  repository.RepositoryManager
	logger logger.Logger
}

// NewOrganizationService 创建组织管理服务实例
func NewOrganizationService(repos repository.RepositoryManager, logger logger.Logger) service.OrganizationService {
	return &organizationService{
		repos:  repos,
		logger: logger,
	}
}

// CreateOrganization 创建组织
func (s *organizationService) CreateOrganization(ctx context.Context, req *service.CreateOrganizationRequest) (*domain.Organization, error) {
	// 验证组织编码唯一性
	exists, err := s.repos.Organization().ExistsByCode(ctx, req.OrgCode)
	if err != nil {
		return nil, fmt.Errorf("failed to check organization code existence: %w", err)
	}
	if exists {
		return nil, errors.ErrOrganizationCodeExists
	}

	// 验证组织类型存在性
	if _, err := s.repos.OrganizationType().GetByID(ctx, req.OrgTypeID); err != nil {
		return nil, fmt.Errorf("failed to get organization type: %w", err)
	}

	// 验证父组织（如果指定）
	if req.ParentID != nil {
		parentExists, err := s.repos.Organization().ExistsByID(ctx, *req.ParentID)
		if err != nil {
			return nil, fmt.Errorf("failed to check parent organization existence: %w", err)
		}
		if !parentExists {
			return nil, errors.ErrParentOrganizationNotFound
		}
	}

	// 构建组织实体
	org := &domain.Organization{
		OrgCode:             req.OrgCode,
		OrgName:             req.OrgName,
		OrgTypeID:           req.OrgTypeID,
		ParentID:            req.ParentID,
		SortOrder:           0,
		Status:              string(domain.OrganizationStatusActive),
		BusinessLicense:     req.BusinessLicense,
		TaxNumber:           req.TaxNumber,
		LegalRepresentative: req.LegalRepresentative,
		ExternalSystemID:    req.ExternalSystemID,
		SyncStatus:          "pending",
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
		CreatedBy:           &req.CreatedBy,
	}

	if req.SortOrder != nil {
		org.SortOrder = *req.SortOrder
	}

	// 处理联系信息和地址
	if req.ContactInfo != nil {
		contactInfoJSON, err := json.Marshal(req.ContactInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal contact info: %w", err)
		}
		contactInfoStr := string(contactInfoJSON)
		org.ContactInfo = &contactInfoStr
	}

	if req.Address != nil {
		addressJSON, err := json.Marshal(req.Address)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal address: %w", err)
		}
		addressStr := string(addressJSON)
		org.Address = &addressStr
	}

	// 创建组织
	if err := s.repos.Organization().Create(ctx, org); err != nil {
		return nil, fmt.Errorf("failed to create organization: %w", err)
	}

	s.logger.Info("Organization created successfully",
		zap.Int64("org_id", org.ID),
		zap.String("org_code", org.OrgCode),
		zap.String("org_name", org.OrgName),
		zap.String("created_by", req.CreatedBy.String()))

	// 重新获取完整的组织信息（包含关联数据）
	return s.repos.Organization().GetByID(ctx, org.ID)
}

// GetOrganization 获取组织
func (s *organizationService) GetOrganization(ctx context.Context, id int64) (*domain.Organization, error) {
	org, err := s.repos.Organization().GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get organization: %w", err)
	}
	return org, nil
}

// GetOrganizationByCode 根据编码获取组织
func (s *organizationService) GetOrganizationByCode(ctx context.Context, orgCode string) (*domain.Organization, error) {
	org, err := s.repos.Organization().GetByCode(ctx, orgCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get organization by code: %w", err)
	}
	return org, nil
}

// UpdateOrganization 更新组织
func (s *organizationService) UpdateOrganization(ctx context.Context, id int64, req *service.UpdateOrganizationRequest) (*domain.Organization, error) {
	// 获取现有组织
	org, err := s.repos.Organization().GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get organization: %w", err)
	}

	// 验证父组织变更（如果指定）
	if req.ParentID != nil && (org.ParentID == nil || *org.ParentID != *req.ParentID) {
		if err := s.repos.Organization().ValidateHierarchy(ctx, id, *req.ParentID); err != nil {
			return nil, fmt.Errorf("invalid hierarchy change: %w", err)
		}
	}

	// 更新字段
	if req.OrgName != nil {
		org.OrgName = *req.OrgName
	}
	if req.ParentID != nil {
		org.ParentID = req.ParentID
	}
	if req.SortOrder != nil {
		org.SortOrder = *req.SortOrder
	}
	if req.Status != nil {
		org.Status = *req.Status
	}
	if req.BusinessLicense != nil {
		org.BusinessLicense = req.BusinessLicense
	}
	if req.TaxNumber != nil {
		org.TaxNumber = req.TaxNumber
	}
	if req.LegalRepresentative != nil {
		org.LegalRepresentative = req.LegalRepresentative
	}
	if req.ExternalSystemID != nil {
		org.ExternalSystemID = req.ExternalSystemID
	}

	// 处理联系信息和地址
	if req.ContactInfo != nil {
		contactInfoJSON, err := json.Marshal(req.ContactInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal contact info: %w", err)
		}
		contactInfoStr := string(contactInfoJSON)
		org.ContactInfo = &contactInfoStr
	}

	if req.Address != nil {
		addressJSON, err := json.Marshal(req.Address)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal address: %w", err)
		}
		addressStr := string(addressJSON)
		org.Address = &addressStr
	}

	org.UpdatedBy = &req.UpdatedBy
	org.UpdatedAt = time.Now()

	// 更新组织
	if err := s.repos.Organization().Update(ctx, org); err != nil {
		return nil, fmt.Errorf("failed to update organization: %w", err)
	}

	s.logger.Info("Organization updated successfully",
		zap.Int64("org_id", id),
		zap.String("updated_by", req.UpdatedBy.String()))

	return org, nil
}

// DeleteOrganization 删除组织
func (s *organizationService) DeleteOrganization(ctx context.Context, id int64) error {
	// 检查是否有子组织
	children, err := s.repos.Organization().GetChildren(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check child organizations: %w", err)
	}
	if len(children) > 0 {
		return errors.ErrOrganizationHasChildren
	}

	// 检查是否有关联用户
	users, err := s.repos.UserOrganization().GetOrganizationUsers(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check organization users: %w", err)
	}
	if len(users) > 0 {
		return errors.ErrOrganizationHasUsers
	}

	// 删除组织
	if err := s.repos.Organization().Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete organization: %w", err)
	}

	s.logger.Info("Organization deleted successfully", zap.Int64("org_id", id))
	return nil
}

// GetOrganizationHierarchy 获取组织层级结构
func (s *organizationService) GetOrganizationHierarchy(ctx context.Context, rootID *int64) ([]*repository.OrganizationHierarchyNode, error) {
	var rootOrgs []*domain.Organization
	var err error

	if rootID != nil {
		// 获取指定根组织
		org, err := s.repos.Organization().GetByID(ctx, *rootID)
		if err != nil {
			return nil, fmt.Errorf("failed to get root organization: %w", err)
		}
		rootOrgs = []*domain.Organization{org}
	} else {
		// 获取所有根组织
		rootOrgs, err = s.repos.Organization().GetRootOrganizations(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get root organizations: %w", err)
		}
	}

	// 构建层级结构
	var nodes []*repository.OrganizationHierarchyNode
	for _, rootOrg := range rootOrgs {
		node, err := s.buildHierarchyNode(ctx, rootOrg, 1)
		if err != nil {
			return nil, err
		}
		nodes = append(nodes, node)
	}

	return nodes, nil
}

// buildHierarchyNode 构建层级节点
func (s *organizationService) buildHierarchyNode(ctx context.Context, org *domain.Organization, level int) (*repository.OrganizationHierarchyNode, error) {
	// 获取子组织
	children, err := s.repos.Organization().GetChildren(ctx, org.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get children for organization %d: %w", org.ID, err)
	}

	node := &repository.OrganizationHierarchyNode{
		Organization: org,
		Level:        level,
		HasChildren:  len(children) > 0,
		Children:     make([]*repository.OrganizationHierarchyNode, 0, len(children)),
	}

	// 递归构建子节点
	for _, child := range children {
		childNode, err := s.buildHierarchyNode(ctx, child, level+1)
		if err != nil {
			return nil, err
		}
		node.Children = append(node.Children, childNode)
	}

	return node, nil
}

// GetOrganizationChildren 获取组织的直接子组织
func (s *organizationService) GetOrganizationChildren(ctx context.Context, parentID int64) ([]*domain.Organization, error) {
	return s.repos.Organization().GetChildren(ctx, parentID)
}

// GetOrganizationDescendants 获取组织的所有后代组织
func (s *organizationService) GetOrganizationDescendants(ctx context.Context, parentID int64) ([]*domain.Organization, error) {
	return s.repos.Organization().GetDescendants(ctx, parentID)
}

// GetOrganizationAncestors 获取组织的所有祖先组织
func (s *organizationService) GetOrganizationAncestors(ctx context.Context, orgID int64) ([]*domain.Organization, error) {
	return s.repos.Organization().GetAncestors(ctx, orgID)
}

// MoveOrganization 移动组织到新的父组织下
func (s *organizationService) MoveOrganization(ctx context.Context, orgID int64, newParentID *int64) error {
	// 验证层级关系
	if newParentID != nil {
		if err := s.repos.Organization().ValidateHierarchy(ctx, orgID, *newParentID); err != nil {
			return fmt.Errorf("invalid hierarchy move: %w", err)
		}
	}

	// 获取组织并更新父组织
	org, err := s.repos.Organization().GetByID(ctx, orgID)
	if err != nil {
		return fmt.Errorf("failed to get organization: %w", err)
	}

	org.ParentID = newParentID
	org.UpdatedAt = time.Now()

	if err := s.repos.Organization().Update(ctx, org); err != nil {
		return fmt.Errorf("failed to move organization: %w", err)
	}

	s.logger.Info("Organization moved successfully",
		zap.Int64("org_id", orgID),
		zap.Any("new_parent_id", newParentID))

	return nil
}

// ListOrganizations 列出组织
func (s *organizationService) ListOrganizations(ctx context.Context, filter *repository.OrganizationFilter) ([]*domain.Organization, error) {
	return s.repos.Organization().List(ctx, filter)
}

// SearchOrganizations 搜索组织
func (s *organizationService) SearchOrganizations(ctx context.Context, keyword string, limit int) ([]*domain.Organization, error) {
	return s.repos.Organization().Search(ctx, keyword, limit)
}

// GetOrganizationsByType 根据类型获取组织
func (s *organizationService) GetOrganizationsByType(ctx context.Context, orgType string) ([]*domain.Organization, error) {
	return s.repos.Organization().GetByType(ctx, orgType)
}

// CreateOrganizationType 创建组织类型
func (s *organizationService) CreateOrganizationType(ctx context.Context, req *service.CreateOrganizationTypeRequest) (*domain.OrganizationTypeEntity, error) {
	// 验证类型编码唯一性
	exists, err := s.repos.OrganizationType().ExistsByCode(ctx, req.TypeCode)
	if err != nil {
		return nil, fmt.Errorf("failed to check organization type code existence: %w", err)
	}
	if exists {
		return nil, errors.ErrOrganizationTypeCodeExists
	}

	orgType := &domain.OrganizationTypeEntity{
		TypeCode:    req.TypeCode,
		TypeName:    req.TypeName,
		LevelOrder:  req.LevelOrder,
		Description: req.Description,
		IsSystem:    false,
		Status:      "active",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   &req.CreatedBy,
	}

	if err := s.repos.OrganizationType().Create(ctx, orgType); err != nil {
		return nil, fmt.Errorf("failed to create organization type: %w", err)
	}

	s.logger.Info("Organization type created successfully",
		zap.Int64("type_id", orgType.ID),
		zap.String("type_code", orgType.TypeCode),
		zap.String("created_by", req.CreatedBy.String()))

	return orgType, nil
}

// GetOrganizationTypes 获取所有组织类型
func (s *organizationService) GetOrganizationTypes(ctx context.Context) ([]*domain.OrganizationTypeEntity, error) {
	return s.repos.OrganizationType().List(ctx)
}

// GetOrganizationType 获取组织类型
func (s *organizationService) GetOrganizationType(ctx context.Context, id int64) (*domain.OrganizationTypeEntity, error) {
	return s.repos.OrganizationType().GetByID(ctx, id)
}

// UpdateOrganizationType 更新组织类型
func (s *organizationService) UpdateOrganizationType(ctx context.Context, id int64, req *service.UpdateOrganizationTypeRequest) (*domain.OrganizationTypeEntity, error) {
	orgType, err := s.repos.OrganizationType().GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get organization type: %w", err)
	}

	// 系统类型不允许修改
	if orgType.IsSystem {
		return nil, errors.ErrSystemOrganizationTypeNotModifiable
	}

	// 更新字段
	if req.TypeName != nil {
		orgType.TypeName = *req.TypeName
	}
	if req.LevelOrder != nil {
		orgType.LevelOrder = *req.LevelOrder
	}
	if req.Description != nil {
		orgType.Description = req.Description
	}
	if req.Status != nil {
		orgType.Status = *req.Status
	}

	orgType.UpdatedBy = &req.UpdatedBy
	orgType.UpdatedAt = time.Now()

	if err := s.repos.OrganizationType().Update(ctx, orgType); err != nil {
		return nil, fmt.Errorf("failed to update organization type: %w", err)
	}

	s.logger.Info("Organization type updated successfully",
		zap.Int64("type_id", id),
		zap.String("updated_by", req.UpdatedBy.String()))

	return orgType, nil
}

// DeleteOrganizationType 删除组织类型
func (s *organizationService) DeleteOrganizationType(ctx context.Context, id int64) error {
	orgType, err := s.repos.OrganizationType().GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get organization type: %w", err)
	}

	// 系统类型不允许删除
	if orgType.IsSystem {
		return errors.ErrSystemOrganizationTypeNotDeletable
	}

	// 检查是否有组织使用此类型
	orgs, err := s.repos.Organization().GetByType(ctx, orgType.TypeCode)
	if err != nil {
		return fmt.Errorf("failed to check organizations using this type: %w", err)
	}
	if len(orgs) > 0 {
		return errors.ErrOrganizationTypeInUse
	}

	if err := s.repos.OrganizationType().Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete organization type: %w", err)
	}

	s.logger.Info("Organization type deleted successfully", zap.Int64("type_id", id))
	return nil
}

// AssignUserToOrganization 分配用户到组织
func (s *organizationService) AssignUserToOrganization(ctx context.Context, req *service.AssignUserToOrganizationRequest) error {
	// 验证用户存在性
	if _, err := s.repos.User().GetByID(ctx, req.UserID); err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 验证组织存在性
	if _, err := s.repos.Organization().GetByID(ctx, req.OrganizationID); err != nil {
		return fmt.Errorf("failed to get organization: %w", err)
	}

	// 检查是否已存在关联
	exists, err := s.repos.UserOrganization().ExistsByUserAndOrganization(ctx, req.UserID, req.OrganizationID)
	if err != nil {
		return fmt.Errorf("failed to check existing association: %w", err)
	}
	if exists {
		return errors.ErrUserOrganizationAssociationExists
	}

	// 解析过期时间
	var expiresAt *time.Time
	if req.ExpiresAt != nil {
		parsedTime, err := time.Parse(time.RFC3339, *req.ExpiresAt)
		if err != nil {
			return fmt.Errorf("invalid expires_at format: %w", err)
		}
		expiresAt = &parsedTime
	}

	// 创建用户组织关联
	userOrg := &domain.UserOrganization{
		ID:                 uuid.New(),
		UserID:             req.UserID,
		OrganizationID:     req.OrganizationID,
		IsPrimary:          req.IsPrimary,
		RoleInOrg:          &req.RoleInOrg,
		ResponsibilityArea: req.ResponsibilityArea,
		GrantedBy:          &req.GrantedBy,
		GrantedAt:          time.Now(),
		ExpiresAt:          expiresAt,
		Status:             string(domain.UserOrganizationStatusActive),
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// 如果设置为主要组织，需要先清除其他主要组织标记
	if req.IsPrimary {
		if err := s.repos.UserOrganization().SetPrimaryOrganization(ctx, req.UserID, req.OrganizationID); err != nil {
			return fmt.Errorf("failed to set primary organization: %w", err)
		}
	} else {
		if err := s.repos.UserOrganization().Create(ctx, userOrg); err != nil {
			return fmt.Errorf("failed to create user organization association: %w", err)
		}
	}

	s.logger.Info("User assigned to organization successfully",
		zap.String("user_id", req.UserID.String()),
		zap.Int64("org_id", req.OrganizationID),
		zap.String("role", req.RoleInOrg),
		zap.Bool("is_primary", req.IsPrimary),
		zap.String("granted_by", req.GrantedBy.String()))

	return nil
}

// RemoveUserFromOrganization 从组织中移除用户
func (s *organizationService) RemoveUserFromOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error {
	// 检查关联是否存在
	exists, err := s.repos.UserOrganization().ExistsByUserAndOrganization(ctx, userID, orgID)
	if err != nil {
		return fmt.Errorf("failed to check association existence: %w", err)
	}
	if !exists {
		return errors.ErrUserOrganizationAssociationNotFound
	}

	if err := s.repos.UserOrganization().RemoveUserFromOrganization(ctx, userID, orgID); err != nil {
		return fmt.Errorf("failed to remove user from organization: %w", err)
	}

	s.logger.Info("User removed from organization successfully",
		zap.String("user_id", userID.String()),
		zap.Int64("org_id", orgID))

	return nil
}

// SetUserPrimaryOrganization 设置用户的主要组织
func (s *organizationService) SetUserPrimaryOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error {
	// 检查关联是否存在
	exists, err := s.repos.UserOrganization().ExistsByUserAndOrganization(ctx, userID, orgID)
	if err != nil {
		return fmt.Errorf("failed to check association existence: %w", err)
	}
	if !exists {
		return errors.ErrUserOrganizationAssociationNotFound
	}

	if err := s.repos.UserOrganization().SetPrimaryOrganization(ctx, userID, orgID); err != nil {
		return fmt.Errorf("failed to set primary organization: %w", err)
	}

	s.logger.Info("User primary organization set successfully",
		zap.String("user_id", userID.String()),
		zap.Int64("org_id", orgID))

	return nil
}

// GetUserOrganizations 获取用户的组织关联
func (s *organizationService) GetUserOrganizations(ctx context.Context, userID uuid.UUID) ([]*domain.UserOrganization, error) {
	return s.repos.UserOrganization().GetUserOrganizations(ctx, userID)
}

// GetOrganizationUsers 获取组织的用户列表
func (s *organizationService) GetOrganizationUsers(ctx context.Context, orgID int64, includeDescendants bool) ([]*domain.User, error) {
	return s.repos.UserOrganization().GetUsersByOrganization(ctx, orgID, includeDescendants)
}

// BatchAssignUsersToOrganization 批量分配用户到组织
func (s *organizationService) BatchAssignUsersToOrganization(ctx context.Context, req *service.BatchAssignUsersRequest) error {
	// 验证组织存在性
	_, err := s.repos.Organization().GetByID(ctx, req.OrganizationID)
	if err != nil {
		return fmt.Errorf("failed to get organization: %w", err)
	}

	if err := s.repos.UserOrganization().BatchAssignUsers(ctx, req.UserIDs, req.OrganizationID, req.RoleInOrg, &req.GrantedBy); err != nil {
		return fmt.Errorf("failed to batch assign users: %w", err)
	}

	s.logger.Info("Users batch assigned to organization successfully",
		zap.Int("user_count", len(req.UserIDs)),
		zap.Int64("org_id", req.OrganizationID),
		zap.String("role", req.RoleInOrg),
		zap.String("granted_by", req.GrantedBy.String()))

	return nil
}

// BatchRemoveUsersFromOrganization 批量从组织中移除用户
func (s *organizationService) BatchRemoveUsersFromOrganization(ctx context.Context, userIDs []uuid.UUID, orgID int64) error {
	if err := s.repos.UserOrganization().BatchRemoveUsers(ctx, userIDs, orgID); err != nil {
		return fmt.Errorf("failed to batch remove users: %w", err)
	}

	s.logger.Info("Users batch removed from organization successfully",
		zap.Int("user_count", len(userIDs)),
		zap.Int64("org_id", orgID))

	return nil
}

// GetOrganizationStatistics 获取组织统计信息
func (s *organizationService) GetOrganizationStatistics(ctx context.Context, orgID *int64) (*repository.OrganizationStatistics, error) {
	// 这里需要实现统计逻辑，暂时返回基础统计
	stats := &repository.OrganizationStatistics{}

	// 获取组织总数
	filter := &repository.OrganizationFilter{}
	if orgID != nil {
		// 如果指定了组织ID，统计该组织及其后代
		org, err := s.repos.Organization().GetByID(ctx, *orgID)
		if err != nil {
			return nil, fmt.Errorf("failed to get organization: %w", err)
		}
		if org.LevelPath != nil {
			levelPathLike := *org.LevelPath + "/%"
			filter.LevelPathLike = &levelPathLike
		}
	}

	totalOrgs, err := s.repos.Organization().Count(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to count organizations: %w", err)
	}
	stats.TotalOrganizations = totalOrgs

	// 获取活跃组织数
	activeFilter := *filter
	activeFilter.Status = []string{string(domain.OrganizationStatusActive)}
	activeOrgs, err := s.repos.Organization().Count(ctx, &activeFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to count active organizations: %w", err)
	}
	stats.ActiveOrganizations = activeOrgs

	// 按类型统计
	hqOrgs, _ := s.repos.Organization().GetByType(ctx, string(domain.OrganizationTypeHQ))
	regionOrgs, _ := s.repos.Organization().GetByType(ctx, string(domain.OrganizationTypeRegion))
	stationOrgs, _ := s.repos.Organization().GetByType(ctx, string(domain.OrganizationTypeStation))

	stats.HeadquartersCount = int64(len(hqOrgs))
	stats.RegionsCount = int64(len(regionOrgs))
	stats.StationsCount = int64(len(stationOrgs))

	return stats, nil
}

// GetUserOrganizationStatistics 获取用户组织关联统计信息
func (s *organizationService) GetUserOrganizationStatistics(ctx context.Context, userID *uuid.UUID) (*repository.UserOrganizationStatistics, error) {
	// 这里需要实现用户组织关联统计逻辑，暂时返回基础统计
	stats := &repository.UserOrganizationStatistics{}

	// 如果指定了用户ID，获取该用户的统计信息
	if userID != nil {
		userOrgs, err := s.repos.UserOrganization().GetUserOrganizations(ctx, *userID)
		if err != nil {
			return nil, fmt.Errorf("failed to get user organizations: %w", err)
		}

		stats.TotalAssociations = int64(len(userOrgs))

		var activeCount, adminCount, managerCount, memberCount int64
		for _, userOrg := range userOrgs {
			if userOrg.IsActive() {
				activeCount++
			}

			if userOrg.RoleInOrg != nil {
				switch *userOrg.RoleInOrg {
				case string(domain.UserOrganizationRoleAdmin):
					adminCount++
				case string(domain.UserOrganizationRoleManager):
					managerCount++
				case string(domain.UserOrganizationRoleMember):
					memberCount++
				}
			}
		}

		stats.ActiveAssociations = activeCount
		stats.AdminCount = adminCount
		stats.ManagerCount = managerCount
		stats.MemberCount = memberCount

		// 检查是否有主要组织
		hasPrimary, _ := s.repos.UserOrganization().HasPrimaryOrganization(ctx, *userID)
		if hasPrimary {
			stats.UsersWithPrimary = 1
		} else {
			stats.UsersWithoutPrimary = 1
		}
	}

	return stats, nil
}

// ValidateOrganizationHierarchy 验证组织层级关系
func (s *organizationService) ValidateOrganizationHierarchy(ctx context.Context, orgID, parentID int64) error {
	return s.repos.Organization().ValidateHierarchy(ctx, orgID, parentID)
}

// ValidateUserOrganizationAccess 验证用户组织访问权限
func (s *organizationService) ValidateUserOrganizationAccess(ctx context.Context, userID uuid.UUID, orgID int64, requiredRole string) (bool, error) {
	// 获取用户在该组织的关联信息
	userOrgs, err := s.repos.UserOrganization().GetUserOrganizations(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user organizations: %w", err)
	}

	// 检查用户是否在该组织或其祖先组织中有足够的权限
	for _, userOrg := range userOrgs {
		if !userOrg.IsActive() {
			continue
		}

		// 直接匹配组织
		if userOrg.OrganizationID == orgID {
			return s.checkRolePermission(userOrg.RoleInOrg, requiredRole), nil
		}

		// 检查是否为祖先组织（上级组织的用户可以访问下级组织）
		descendants, err := s.repos.Organization().GetDescendants(ctx, userOrg.OrganizationID)
		if err != nil {
			continue
		}

		for _, descendant := range descendants {
			if descendant.ID == orgID {
				return s.checkRolePermission(userOrg.RoleInOrg, requiredRole), nil
			}
		}
	}

	return false, nil
}

// checkRolePermission 检查角色权限
func (s *organizationService) checkRolePermission(userRole *string, requiredRole string) bool {
	if userRole == nil {
		return false
	}

	// 定义角色权限级别
	roleLevel := map[string]int{
		string(domain.UserOrganizationRoleMember):  1,
		string(domain.UserOrganizationRoleManager): 2,
		string(domain.UserOrganizationRoleAdmin):   3,
	}

	userLevel, userExists := roleLevel[*userRole]
	requiredLevel, requiredExists := roleLevel[requiredRole]

	if !userExists || !requiredExists {
		return false
	}

	return userLevel >= requiredLevel
}
