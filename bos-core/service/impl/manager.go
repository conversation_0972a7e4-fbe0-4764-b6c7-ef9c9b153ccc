package impl

import (
	"gitlab4.weicheche.cn/indo-bp/bos-core/config"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

// serviceManager 服务管理器实现
type serviceManager struct {
	authService          service.AuthService
	userService          service.UserService
	roleService          service.RoleService
	permissionService    service.PermissionService
	sessionService       service.SessionService
	logService           service.LogService
	operationLogsService service.OperationLogsService
	stationService       service.StationService
	equipmentService     service.EquipmentService
	configService        service.ConfigService
	userStationService     service.UserStationService
	systemAccessService    service.SystemAccessService
	userSystemAccessService service.UserSystemAccessService

	// 数据字典服务
	dictCategoryService     service.DictCategoryService
	dictItemService         service.DictItemService
	dictValueService        service.DictValueService
	dictQueryService        service.DictQueryService
	dictSyncService         service.DictSyncService
	dictCacheService        service.DictCacheService
	dictStatsService        service.DictStatsService
	dictImportExportService service.DictImportExportService

	// 标签系统服务
	tagGroupService      service.TagGroupService
	tagService           service.TagService
	entityTagService     service.EntityTagService
	tagStatisticsService service.TagStatisticsService

	// 组织管理服务
	organizationService service.OrganizationService
}

// NewServiceManager 创建服务管理器实例
func NewServiceManager(
	repos repository.RepositoryManager,
	jwtManager *utils.JWTManager,
	config *config.Config,
	logger logger.Logger,
) service.ServiceManager {
	systemAccessService := NewSystemAccessService()

	return &serviceManager{
		authService:          NewAuthService(repos, jwtManager, config, logger, systemAccessService),
		userService:          NewUserService(repos, config, logger),
		roleService:          NewRoleService(repos, config, logger),
		permissionService:    NewPermissionService(repos, logger),
		sessionService:       NewSessionService(repos, config, logger),
		logService:           NewLogService(repos, config, logger),
		operationLogsService: NewOperationLogsService(repos, config, logger),
		stationService:       NewStationService(repos, config, logger),
		equipmentService:     NewEquipmentService(repos, config, logger),
		configService:        NewConfigService(repos, config, logger),
		userStationService:     NewUserStationService(repos, config, logger),
		systemAccessService:    systemAccessService,
		userSystemAccessService: NewUserSystemAccessService(repos, logger),

		// 数据字典服务
		dictCategoryService:     NewDictCategoryService(repos, config, logger),
		dictItemService:         NewDictItemService(repos, config, logger),
		dictValueService:        NewDictValueService(repos, config, logger),
		dictQueryService:        NewDictQueryService(repos, config, logger),
		dictSyncService:         NewDictSyncService(repos, config, logger),
		dictCacheService:        nil, // TODO: 实现 DictCacheService
		dictStatsService:        nil, // TODO: 实现 DictStatsService
		dictImportExportService: NewDictImportExportService(repos, config, logger),

		// 标签系统服务
		tagGroupService:      NewTagGroupService(repos.TagGroup(), repos.Tag()),
		tagService:           NewTagService(repos.Tag(), repos.TagGroup()),
		entityTagService:     NewEntityTagService(repos.EntityTag(), repos.Tag()),
		tagStatisticsService: NewTagStatisticsService(repos.TagStatistics(), repos.Tag(), repos.EntityTag(), repos.TagGroup()),

		// 组织管理服务
		organizationService: NewOrganizationService(repos, logger),
	}
}

// Auth 获取认证服务
func (m *serviceManager) Auth() service.AuthService {
	return m.authService
}

// User 获取用户服务
func (m *serviceManager) User() service.UserService {
	return m.userService
}

// Role 获取角色服务
func (m *serviceManager) Role() service.RoleService {
	return m.roleService
}

// Permission 获取权限服务
func (m *serviceManager) Permission() service.PermissionService {
	return m.permissionService
}

// Session 获取会话服务
func (m *serviceManager) Session() service.SessionService {
	return m.sessionService
}

// Log 获取日志服务
func (m *serviceManager) Log() service.LogService {
	return m.logService
}

// Station 获取站点服务
func (m *serviceManager) Station() service.StationService {
	return m.stationService
}

// Equipment 获取设备服务
func (m *serviceManager) Equipment() service.EquipmentService {
	return m.equipmentService
}

// Config 获取配置服务
func (m *serviceManager) Config() service.ConfigService {
	return m.configService
}

// UserStation 获取用户站点服务
func (m *serviceManager) UserStation() service.UserStationService {
	return m.userStationService
}

// SystemAccess 获取系统访问控制服务
func (m *serviceManager) SystemAccess() service.SystemAccessService {
	return m.systemAccessService
}

// UserSystemAccess 获取用户系统访问权限服务
func (m *serviceManager) UserSystemAccess() service.UserSystemAccessService {
	return m.userSystemAccessService
}

// 数据字典服务方法
func (m *serviceManager) DictCategory() service.DictCategoryService {
	return m.dictCategoryService
}

func (m *serviceManager) DictItem() service.DictItemService {
	return m.dictItemService
}

func (m *serviceManager) DictValue() service.DictValueService {
	return m.dictValueService
}

func (m *serviceManager) DictQuery() service.DictQueryService {
	return m.dictQueryService
}

func (m *serviceManager) DictSync() service.DictSyncService {
	return m.dictSyncService
}

func (m *serviceManager) DictCache() service.DictCacheService {
	return m.dictCacheService
}

func (m *serviceManager) DictStats() service.DictStatsService {
	return m.dictStatsService
}

func (m *serviceManager) DictImportExport() service.DictImportExportService {
	return m.dictImportExportService
}

// OperationLogs 获取操作日志服务
func (m *serviceManager) OperationLogs() service.OperationLogsService {
	return m.operationLogsService
}

// 标签系统服务方法
func (m *serviceManager) TagGroup() service.TagGroupService {
	return m.tagGroupService
}

func (m *serviceManager) Tag() service.TagService {
	return m.tagService
}

func (m *serviceManager) EntityTag() service.EntityTagService {
	return m.entityTagService
}

func (m *serviceManager) TagStatistics() service.TagStatisticsService {
	return m.tagStatisticsService
}

// Organization 获取组织管理服务
func (m *serviceManager) Organization() service.OrganizationService {
	return m.organizationService
}
