package impl

import (
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

// systemAccessService 系统访问控制服务实现
type systemAccessService struct{}

// NewSystemAccessService 创建系统访问控制服务实例
func NewSystemAccessService() service.SystemAccessService {
	return &systemAccessService{}
}

// System constants
const (
	SystemBOS = "BOS" // Back Office System
	SystemEDC = "EDC" // Electronic Data Capture
	SystemHOS = "HOS" // Head Office System
)

// Access level constants
const (
	AccessLevelManager          = "manager"
	AccessLevelAssistantManager = "assistant_manager"
	AccessLevelSupervisor       = "supervisor"
	AccessLevelOperator         = "operator"
)

// CanAccessSystem 验证用户是否可以访问指定系统
func (s *systemAccessService) CanAccessSystem(userRoles []domain.UserStationRole, system string) bool {
	if !s.IsValidSystem(system) {
		return false
	}

	// 如果用户没有任何站点角色，拒绝访问
	if len(userRoles) == 0 {
		return false
	}

	// 获取用户的最高权限级别
	highestRole := s.getHighestRole(userRoles)
	if highestRole == nil {
		return false
	}

	switch system {
	case SystemBOS:
		// BOS系统需要管理或监督权限
		return s.canAccessBOS(highestRole.RoleType)
	case SystemEDC:
		// EDC系统所有角色都可访问
		return true
	case SystemHOS:
		// HOS系统需要全局权限，暂时通过站点角色验证
		return s.canAccessHOS(highestRole.RoleType)
	default:
		return false
	}
}

// GetSystemAccessLevel 获取用户在指定系统的最高权限级别
func (s *systemAccessService) GetSystemAccessLevel(userRoles []domain.UserStationRole, system string) string {
	if !s.CanAccessSystem(userRoles, system) {
		return ""
	}

	highestRole := s.getHighestRole(userRoles)
	if highestRole == nil {
		return ""
	}

	return string(highestRole.RoleType)
}

// GetSystemStationIDs 获取用户在指定系统下可管理的站点ID
func (s *systemAccessService) GetSystemStationIDs(userRoles []domain.UserStationRole, system string) []int64 {
	if !s.CanAccessSystem(userRoles, system) {
		return []int64{}
	}

	var stationIDs []int64
	stationMap := make(map[int64]bool) // 用于去重

	for _, role := range userRoles {
		if !role.IsActive() {
			continue
		}

		// 根据系统类型和角色权限决定是否包含该站点
		switch system {
		case SystemBOS:
			// BOS系统只包含有管理权限的站点
			if role.CanManage() || role.IsSupervisor() {
				if !stationMap[role.StationID] {
					stationIDs = append(stationIDs, role.StationID)
					stationMap[role.StationID] = true
				}
			}
		case SystemEDC:
			// EDC系统包含所有有权限的站点
			if role.CanOperate() {
				if !stationMap[role.StationID] {
					stationIDs = append(stationIDs, role.StationID)
					stationMap[role.StationID] = true
				}
			}
		}
	}

	return stationIDs
}

// GetAccessibleSystems 获取用户可访问的系统列表
func (s *systemAccessService) GetAccessibleSystems(userRoles []domain.UserStationRole) []string {
	var systems []string

	if s.CanAccessSystem(userRoles, SystemBOS) {
		systems = append(systems, SystemBOS)
	}

	if s.CanAccessSystem(userRoles, SystemEDC) {
		systems = append(systems, SystemEDC)
	}

	if s.CanAccessSystem(userRoles, SystemHOS) {
		systems = append(systems, SystemHOS)
	}

	return systems
}

// IsValidSystem 验证系统标识是否有效
func (s *systemAccessService) IsValidSystem(system string) bool {
	return system == SystemBOS || system == SystemEDC || system == SystemHOS
}

// canAccessBOS 检查角色是否可以访问BOS系统
func (s *systemAccessService) canAccessBOS(roleType domain.UserStationRoleType) bool {
	return roleType == domain.UserStationRoleManager ||
		roleType == domain.UserStationRoleAssistantManager ||
		roleType == domain.UserStationRoleSupervisor
}

// canAccessHOS 检查角色是否可以访问HOS系统
func (s *systemAccessService) canAccessHOS(roleType domain.UserStationRoleType) bool {
	// HOS系统需要管理员级别权限
	return roleType == domain.UserStationRoleManager ||
		roleType == domain.UserStationRoleAssistantManager
}

// getHighestRole 获取用户的最高权限角色
func (s *systemAccessService) getHighestRole(userRoles []domain.UserStationRole) *domain.UserStationRole {
	var highestRole *domain.UserStationRole
	highestLevel := 0

	for _, role := range userRoles {
		if !role.IsActive() {
			continue
		}

		level := role.GetRoleLevel()
		if level > highestLevel {
			highestLevel = level
			highestRole = &role
		}
	}

	return highestRole
}

// GetSystemDisplayName 获取系统显示名称
func (s *systemAccessService) GetSystemDisplayName(system string) string {
	switch system {
	case SystemBOS:
		return "Back Office System"
	case SystemEDC:
		return "Electronic Data Capture"
	default:
		return system
	}
}

// GetAccessLevelDisplayName 获取权限级别显示名称
func (s *systemAccessService) GetAccessLevelDisplayName(accessLevel string) string {
	switch accessLevel {
	case AccessLevelManager:
		return "Station Manager"
	case AccessLevelAssistantManager:
		return "Assistant Manager"
	case AccessLevelSupervisor:
		return "Supervisor"
	case AccessLevelOperator:
		return "Operator"
	default:
		return accessLevel
	}
}

// ValidateSystemAccessRequest 验证系统访问请求
func (s *systemAccessService) ValidateSystemAccessRequest(userRoles []domain.UserStationRole, system string) error {
	if system == "" {
		return &SystemAccessError{
			Code:    "INVALID_SYSTEM",
			Message: "System parameter is required",
		}
	}

	if !s.IsValidSystem(system) {
		return &SystemAccessError{
			Code:    "UNKNOWN_SYSTEM",
			Message: "Unknown system type: " + system,
		}
	}

	if len(userRoles) == 0 {
		return &SystemAccessError{
			Code:    "NO_STATION_ASSOCIATION",
			Message: "User has no station associations",
		}
	}

	if !s.CanAccessSystem(userRoles, system) {
		var message string
		switch system {
		case SystemBOS:
			message = "Insufficient permissions for BOS system. Only manager, assistant manager, and supervisor roles can access BOS. Please contact administrator or use EDC system."
		case SystemEDC:
			message = "Insufficient permissions for EDC system"
		case SystemHOS:
			message = "Insufficient permissions for HOS system. Only manager and assistant manager roles can access HOS. Please contact administrator."
		default:
			message = "Insufficient permissions for " + system + " system"
		}

		return &SystemAccessError{
			Code:    "INSUFFICIENT_PERMISSIONS",
			Message: message,
		}
	}

	return nil
}

// SystemAccessError 系统访问错误
type SystemAccessError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *SystemAccessError) Error() string {
	return e.Message
}
