package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
)

type userSystemAccessService struct {
	repos  repository.RepositoryManager
	logger *zap.Logger
}

// NewUserSystemAccessService 创建用户系统访问权限服务实例
func NewUserSystemAccessService(repos repository.RepositoryManager, logger logger.Logger) service.UserSystemAccessService {
	// 创建一个默认的zap logger
	zapLogger, _ := zap.NewProduction()

	return &userSystemAccessService{
		repos:  repos,
		logger: zapLogger,
	}
}

// GrantSystemAccess 授予用户系统访问权限
func (s *userSystemAccessService) GrantSystemAccess(ctx context.Context, req *service.GrantSystemAccessRequest) error {
	// 检查用户是否存在
	user, err := s.repos.User().GetByID(ctx, req.UserID)
	if err != nil {
		s.logger.Error("Failed to get user", zap.String("user_id", req.UserID.String()), zap.Error(err))
		return fmt.Errorf("user not found: %w", err)
	}

	// 检查是否已有该系统的访问权限
	exists, err := s.repos.UserSystemAccess().ExistsByUserAndSystem(ctx, req.UserID, req.SystemCode)
	if err != nil {
		s.logger.Error("Failed to check existing access", zap.Error(err))
		return fmt.Errorf("failed to check existing access: %w", err)
	}
	if exists {
		return fmt.Errorf("user already has access to system %s", req.SystemCode)
	}

	// 创建系统访问权限
	access := &domain.UserSystemAccess{
		ID:          uuid.New(),
		UserID:      req.UserID,
		SystemCode:  req.SystemCode,
		AccessLevel: req.AccessLevel,
		ScopeType:   req.ScopeType,
		ScopeIDs:    req.ScopeIDs,
		Status:      domain.UserSystemAccessStatusActive,
		GrantedBy:   req.GrantedBy,
		GrantedAt:   time.Now(),
		ExpiresAt:   req.ExpiresAt,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.repos.UserSystemAccess().Create(ctx, access); err != nil {
		s.logger.Error("Failed to create system access", zap.Error(err))
		return fmt.Errorf("failed to create system access: %w", err)
	}

	s.logger.Info("System access granted successfully",
		zap.String("user_id", req.UserID.String()),
		zap.String("username", user.Username),
		zap.String("system_code", string(req.SystemCode)),
		zap.String("access_level", string(req.AccessLevel)),
		zap.String("scope_type", string(req.ScopeType)))

	return nil
}

// RevokeSystemAccess 撤销用户系统访问权限
func (s *userSystemAccessService) RevokeSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) error {
	if err := s.repos.UserSystemAccess().RevokeSystemAccess(ctx, userID, systemCode); err != nil {
		s.logger.Error("Failed to revoke system access", 
			zap.String("user_id", userID.String()),
			zap.String("system_code", string(systemCode)),
			zap.Error(err))
		return fmt.Errorf("failed to revoke system access: %w", err)
	}

	s.logger.Info("System access revoked successfully",
		zap.String("user_id", userID.String()),
		zap.String("system_code", string(systemCode)))

	return nil
}

// GetUserSystemAccess 获取用户系统访问权限
func (s *userSystemAccessService) GetUserSystemAccess(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error) {
	return s.repos.UserSystemAccess().GetByUserID(ctx, userID)
}

// GetActiveUserSystemAccess 获取用户活跃的系统访问权限
func (s *userSystemAccessService) GetActiveUserSystemAccess(ctx context.Context, userID uuid.UUID) ([]*domain.UserSystemAccess, error) {
	return s.repos.UserSystemAccess().GetActiveByUserID(ctx, userID)
}

// GetUserSystemAccessBySystem 获取用户在指定系统的访问权限
func (s *userSystemAccessService) GetUserSystemAccessBySystem(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*domain.UserSystemAccess, error) {
	return s.repos.UserSystemAccess().GetByUserAndSystem(ctx, userID, systemCode)
}

// HasSystemAccess 检查用户是否有系统访问权限
func (s *userSystemAccessService) HasSystemAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (bool, error) {
	return s.repos.UserSystemAccess().HasSystemAccess(ctx, userID, systemCode)
}

// HasStationAccess 检查用户是否有站点访问权限
func (s *userSystemAccessService) HasStationAccess(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, stationID int64) (bool, error) {
	return s.repos.UserSystemAccess().HasStationAccess(ctx, userID, systemCode, stationID)
}

// UpdateAccessLevel 更新用户系统访问级别
func (s *userSystemAccessService) UpdateAccessLevel(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, accessLevel domain.AccessLevel) error {
	if err := s.repos.UserSystemAccess().UpdateAccessLevel(ctx, userID, systemCode, accessLevel); err != nil {
		s.logger.Error("Failed to update access level", 
			zap.String("user_id", userID.String()),
			zap.String("system_code", string(systemCode)),
			zap.String("access_level", string(accessLevel)),
			zap.Error(err))
		return fmt.Errorf("failed to update access level: %w", err)
	}

	s.logger.Info("Access level updated successfully",
		zap.String("user_id", userID.String()),
		zap.String("system_code", string(systemCode)),
		zap.String("access_level", string(accessLevel)))

	return nil
}

// UpdateScopeIDs 更新用户系统访问范围
func (s *userSystemAccessService) UpdateScopeIDs(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode, scopeIDs []int64) error {
	if err := s.repos.UserSystemAccess().UpdateScopeIDs(ctx, userID, systemCode, scopeIDs); err != nil {
		s.logger.Error("Failed to update scope IDs", 
			zap.String("user_id", userID.String()),
			zap.String("system_code", string(systemCode)),
			zap.Int64s("scope_ids", scopeIDs),
			zap.Error(err))
		return fmt.Errorf("failed to update scope IDs: %w", err)
	}

	s.logger.Info("Scope IDs updated successfully",
		zap.String("user_id", userID.String()),
		zap.String("system_code", string(systemCode)),
		zap.Int64s("scope_ids", scopeIDs))

	return nil
}

// GetSystemAccessInfo 获取用户系统访问信息（用于JWT）
func (s *userSystemAccessService) GetSystemAccessInfo(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*domain.SystemAccessInfo, error) {
	access, err := s.repos.UserSystemAccess().GetByUserAndSystem(ctx, userID, systemCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get system access: %w", err)
	}

	if !access.IsActive() {
		return nil, fmt.Errorf("system access is not active")
	}

	// TODO: 获取用户的具体权限列表
	permissions := []string{}

	return &domain.SystemAccessInfo{
		System:       string(access.SystemCode),
		AccessLevel:  string(access.AccessLevel),
		ScopeType:    string(access.ScopeType),
		ScopeIDs:     access.ScopeIDs,
		StationCount: len(access.ScopeIDs),
		Permissions:  permissions,
	}, nil
}

// ValidateSystemLogin 验证用户系统登录权限
func (s *userSystemAccessService) ValidateSystemLogin(ctx context.Context, userID uuid.UUID, systemCode domain.SystemCode) (*service.SystemLoginValidationResult, error) {
	access, err := s.repos.UserSystemAccess().GetByUserAndSystem(ctx, userID, systemCode)
	if err != nil {
		if err.Error() == "record not found" {
			return &service.SystemLoginValidationResult{
				HasAccess:    false,
				ErrorMessage: fmt.Sprintf("No access to system %s", systemCode),
			}, nil
		}
		return nil, fmt.Errorf("failed to get system access: %w", err)
	}

	if !access.IsActive() {
		return &service.SystemLoginValidationResult{
			HasAccess:    false,
			ErrorMessage: "System access is inactive or expired",
		}, nil
	}

	// TODO: 获取用户的具体权限列表
	permissions := []string{}

	return &service.SystemLoginValidationResult{
		HasAccess:    true,
		AccessLevel:  access.AccessLevel,
		ScopeType:    access.ScopeType,
		ScopeIDs:     access.ScopeIDs,
		StationCount: len(access.ScopeIDs),
		Permissions:  permissions,
	}, nil
}

// BatchGrantSystemAccess 批量授予用户系统访问权限
func (s *userSystemAccessService) BatchGrantSystemAccess(ctx context.Context, req *service.BatchGrantSystemAccessRequest) error {
	if len(req.UserIDs) == 0 {
		return fmt.Errorf("no user IDs provided")
	}

	if err := s.repos.UserSystemAccess().BatchGrantSystemAccess(ctx, req.UserIDs, req.SystemCode, req.AccessLevel, req.ScopeType, req.ScopeIDs, req.GrantedBy); err != nil {
		s.logger.Error("Failed to batch grant system access", zap.Error(err))
		return fmt.Errorf("failed to batch grant system access: %w", err)
	}

	s.logger.Info("Batch system access granted successfully",
		zap.Int("user_count", len(req.UserIDs)),
		zap.String("system_code", string(req.SystemCode)),
		zap.String("access_level", string(req.AccessLevel)))

	return nil
}

// BatchRevokeSystemAccess 批量撤销用户系统访问权限
func (s *userSystemAccessService) BatchRevokeSystemAccess(ctx context.Context, userIDs []uuid.UUID, systemCode domain.SystemCode) error {
	if len(userIDs) == 0 {
		return fmt.Errorf("no user IDs provided")
	}

	if err := s.repos.UserSystemAccess().BatchRevokeSystemAccess(ctx, userIDs, systemCode); err != nil {
		s.logger.Error("Failed to batch revoke system access", zap.Error(err))
		return fmt.Errorf("failed to batch revoke system access: %w", err)
	}

	s.logger.Info("Batch system access revoked successfully",
		zap.Int("user_count", len(userIDs)),
		zap.String("system_code", string(systemCode)))

	return nil
}

// GetUsersBySystemAccess 根据系统访问权限获取用户列表
func (s *userSystemAccessService) GetUsersBySystemAccess(ctx context.Context, systemCode domain.SystemCode, accessLevel domain.AccessLevel, scopeType domain.ScopeType) ([]*domain.User, error) {
	return s.repos.UserSystemAccess().GetUsersBySystemAccess(ctx, systemCode, accessLevel, scopeType)
}

// CleanupExpiredAccess 清理过期的访问权限
func (s *userSystemAccessService) CleanupExpiredAccess(ctx context.Context) error {
	if err := s.repos.UserSystemAccess().CleanupExpiredAccess(ctx); err != nil {
		s.logger.Error("Failed to cleanup expired access", zap.Error(err))
		return fmt.Errorf("failed to cleanup expired access: %w", err)
	}

	s.logger.Info("Expired access cleaned up successfully")
	return nil
}
