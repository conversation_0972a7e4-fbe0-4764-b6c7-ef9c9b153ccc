package service

import (
	"context"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/domain"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository"
)

// OrganizationService 组织管理服务接口
type OrganizationService interface {
	// Organization management
	CreateOrganization(ctx context.Context, req *CreateOrganizationRequest) (*domain.Organization, error)
	GetOrganization(ctx context.Context, id int64) (*domain.Organization, error)
	GetOrganizationByCode(ctx context.Context, orgCode string) (*domain.Organization, error)
	UpdateOrganization(ctx context.Context, id int64, req *UpdateOrganizationRequest) (*domain.Organization, error)
	DeleteOrganization(ctx context.Context, id int64) error
	
	// Organization hierarchy
	GetOrganizationHierarchy(ctx context.Context, rootID *int64) ([]*repository.OrganizationHierarchyNode, error)
	GetOrganizationChildren(ctx context.Context, parentID int64) ([]*domain.Organization, error)
	GetOrganizationDescendants(ctx context.Context, parentID int64) ([]*domain.Organization, error)
	GetOrganizationAncestors(ctx context.Context, orgID int64) ([]*domain.Organization, error)
	MoveOrganization(ctx context.Context, orgID int64, newParentID *int64) error
	
	// Organization listing and search
	ListOrganizations(ctx context.Context, filter *repository.OrganizationFilter) ([]*domain.Organization, error)
	SearchOrganizations(ctx context.Context, keyword string, limit int) ([]*domain.Organization, error)
	GetOrganizationsByType(ctx context.Context, orgType string) ([]*domain.Organization, error)
	
	// Organization types
	CreateOrganizationType(ctx context.Context, req *CreateOrganizationTypeRequest) (*domain.OrganizationTypeEntity, error)
	GetOrganizationTypes(ctx context.Context) ([]*domain.OrganizationTypeEntity, error)
	GetOrganizationType(ctx context.Context, id int64) (*domain.OrganizationTypeEntity, error)
	UpdateOrganizationType(ctx context.Context, id int64, req *UpdateOrganizationTypeRequest) (*domain.OrganizationTypeEntity, error)
	DeleteOrganizationType(ctx context.Context, id int64) error
	
	// User-Organization associations
	AssignUserToOrganization(ctx context.Context, req *AssignUserToOrganizationRequest) error
	RemoveUserFromOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error
	SetUserPrimaryOrganization(ctx context.Context, userID uuid.UUID, orgID int64) error
	GetUserOrganizations(ctx context.Context, userID uuid.UUID) ([]*domain.UserOrganization, error)
	GetOrganizationUsers(ctx context.Context, orgID int64, includeDescendants bool) ([]*domain.User, error)
	
	// Batch operations
	BatchAssignUsersToOrganization(ctx context.Context, req *BatchAssignUsersRequest) error
	BatchRemoveUsersFromOrganization(ctx context.Context, userIDs []uuid.UUID, orgID int64) error
	
	// Statistics and reporting
	GetOrganizationStatistics(ctx context.Context, orgID *int64) (*repository.OrganizationStatistics, error)
	GetUserOrganizationStatistics(ctx context.Context, userID *uuid.UUID) (*repository.UserOrganizationStatistics, error)
	
	// Validation
	ValidateOrganizationHierarchy(ctx context.Context, orgID, parentID int64) error
	ValidateUserOrganizationAccess(ctx context.Context, userID uuid.UUID, orgID int64, requiredRole string) (bool, error)
}

// CreateOrganizationRequest 创建组织请求
type CreateOrganizationRequest struct {
	OrgCode             string                 `json:"org_code" validate:"required,min=2,max=50"`
	OrgName             string                 `json:"org_name" validate:"required,min=2,max=200"`
	OrgTypeID           int64                  `json:"org_type_id" validate:"required"`
	ParentID            *int64                 `json:"parent_id"`
	SortOrder           *int                   `json:"sort_order"`
	ContactInfo         map[string]interface{} `json:"contact_info"`
	Address             map[string]interface{} `json:"address"`
	BusinessLicense     *string                `json:"business_license"`
	TaxNumber           *string                `json:"tax_number"`
	LegalRepresentative *string                `json:"legal_representative"`
	ExternalSystemID    *string                `json:"external_system_id"`
	CreatedBy           uuid.UUID              `json:"created_by"`
}

// UpdateOrganizationRequest 更新组织请求
type UpdateOrganizationRequest struct {
	OrgName             *string                `json:"org_name" validate:"omitempty,min=2,max=200"`
	ParentID            *int64                 `json:"parent_id"`
	SortOrder           *int                   `json:"sort_order"`
	Status              *string                `json:"status" validate:"omitempty,oneof=active inactive suspended"`
	ContactInfo         map[string]interface{} `json:"contact_info"`
	Address             map[string]interface{} `json:"address"`
	BusinessLicense     *string                `json:"business_license"`
	TaxNumber           *string                `json:"tax_number"`
	LegalRepresentative *string                `json:"legal_representative"`
	ExternalSystemID    *string                `json:"external_system_id"`
	UpdatedBy           uuid.UUID              `json:"updated_by"`
}

// CreateOrganizationTypeRequest 创建组织类型请求
type CreateOrganizationTypeRequest struct {
	TypeCode    string     `json:"type_code" validate:"required,min=2,max=50"`
	TypeName    string     `json:"type_name" validate:"required,min=2,max=100"`
	LevelOrder  int        `json:"level_order" validate:"required,min=1"`
	Description *string    `json:"description"`
	CreatedBy   uuid.UUID  `json:"created_by"`
}

// UpdateOrganizationTypeRequest 更新组织类型请求
type UpdateOrganizationTypeRequest struct {
	TypeName    *string    `json:"type_name" validate:"omitempty,min=2,max=100"`
	LevelOrder  *int       `json:"level_order" validate:"omitempty,min=1"`
	Description *string    `json:"description"`
	Status      *string    `json:"status" validate:"omitempty,oneof=active inactive"`
	UpdatedBy   uuid.UUID  `json:"updated_by"`
}

// AssignUserToOrganizationRequest 分配用户到组织请求
type AssignUserToOrganizationRequest struct {
	UserID             uuid.UUID  `json:"user_id" validate:"required"`
	OrganizationID     int64      `json:"organization_id" validate:"required"`
	RoleInOrg          string     `json:"role_in_org" validate:"required,oneof=admin manager member"`
	IsPrimary          bool       `json:"is_primary"`
	ResponsibilityArea *string    `json:"responsibility_area"`
	ExpiresAt          *string    `json:"expires_at"` // ISO 8601 format
	GrantedBy          uuid.UUID  `json:"granted_by" validate:"required"`
}

// BatchAssignUsersRequest 批量分配用户请求
type BatchAssignUsersRequest struct {
	UserIDs        []uuid.UUID `json:"user_ids" validate:"required,min=1"`
	OrganizationID int64       `json:"organization_id" validate:"required"`
	RoleInOrg      string      `json:"role_in_org" validate:"required,oneof=admin manager member"`
	GrantedBy      uuid.UUID   `json:"granted_by" validate:"required"`
}

// OrganizationResponse 组织响应
type OrganizationResponse struct {
	ID                  int64                        `json:"id"`
	OrgCode             string                       `json:"org_code"`
	OrgName             string                       `json:"org_name"`
	OrgType             *domain.OrganizationTypeEntity `json:"org_type"`
	Parent              *OrganizationResponse        `json:"parent"`
	LevelPath           *string                      `json:"level_path"`
	LevelDepth          int                          `json:"level_depth"`
	SortOrder           int                          `json:"sort_order"`
	Status              string                       `json:"status"`
	ContactInfo         map[string]interface{}       `json:"contact_info"`
	Address             map[string]interface{}       `json:"address"`
	BusinessLicense     *string                      `json:"business_license"`
	TaxNumber           *string                      `json:"tax_number"`
	LegalRepresentative *string                      `json:"legal_representative"`
	ExternalSystemID    *string                      `json:"external_system_id"`
	SyncStatus          string                       `json:"sync_status"`
	LastSyncAt          *string                      `json:"last_sync_at"`
	CreatedAt           string                       `json:"created_at"`
	UpdatedAt           string                       `json:"updated_at"`
	
	// Additional fields for hierarchy display
	Children    []*OrganizationResponse `json:"children,omitempty"`
	UserCount   int                     `json:"user_count,omitempty"`
	StationCount int                    `json:"station_count,omitempty"`
}

// UserOrganizationResponse 用户组织关联响应
type UserOrganizationResponse struct {
	ID                 string                       `json:"id"`
	User               *UserResponse                `json:"user"`
	Organization       *OrganizationResponse        `json:"organization"`
	IsPrimary          bool                         `json:"is_primary"`
	RoleInOrg          *string                      `json:"role_in_org"`
	RoleDisplayName    string                       `json:"role_display_name"`
	ResponsibilityArea *string                      `json:"responsibility_area"`
	Status             string                       `json:"status"`
	GrantedBy          *UserResponse                `json:"granted_by"`
	GrantedAt          string                       `json:"granted_at"`
	ExpiresAt          *string                      `json:"expires_at"`
	CreatedAt          string                       `json:"created_at"`
	UpdatedAt          string                       `json:"updated_at"`
}

// UserResponse 用户响应（简化版）
type UserResponse struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	FullName string `json:"full_name"`
	Email    string `json:"email"`
	Status   string `json:"status"`
}
