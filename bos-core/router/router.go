package router

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab4.weicheche.cn/indo-bp/bos-core/config"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/handler"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/logger"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/middleware"
	gormRepo "gitlab4.weicheche.cn/indo-bp/bos-core/internal/repository/gorm"
	"gitlab4.weicheche.cn/indo-bp/bos-core/internal/utils"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service"
	"gitlab4.weicheche.cn/indo-bp/bos-core/service/impl"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// API版本常量 - 统一维护API路径前缀
const (
	// 统一的API版本前缀，所有路由都使用此前缀
	APIVersionPrefix = "/api/v1"
)

// ModuleOptions 定义模块依赖结构体
// 主程序只需要传入基础依赖，模块内部负责创建具体的服务实例
type ModuleOptions struct {
	DB     *gorm.DB       // 数据库连接（使用GORM）
	Logger *zap.Logger    // 日志实例（使用zap.Logger，因为handler需要）
	Config *config.Config // 配置信息
}

// Exports 定义模块导出的服务
type Exports struct {
	Services service.ServiceManager // 导出服务管理器供其他模块使用
}

// zapLoggerAdapter zap.Logger到内部Logger接口的适配器
type zapLoggerAdapter struct {
	logger *zap.Logger
}

func (z *zapLoggerAdapter) Debug(msg string, fields ...zap.Field) {
	z.logger.Debug(msg, fields...)
}

func (z *zapLoggerAdapter) Info(msg string, fields ...zap.Field) {
	z.logger.Info(msg, fields...)
}

func (z *zapLoggerAdapter) Warn(msg string, fields ...zap.Field) {
	z.logger.Warn(msg, fields...)
}

func (z *zapLoggerAdapter) Error(msg string, fields ...zap.Field) {
	z.logger.Error(msg, fields...)
}

func (z *zapLoggerAdapter) Fatal(msg string, fields ...zap.Field) {
	z.logger.Fatal(msg, fields...)
}

func (z *zapLoggerAdapter) With(fields ...zap.Field) logger.Logger {
	return &zapLoggerAdapter{logger: z.logger.With(fields...)}
}

func (z *zapLoggerAdapter) Sync() error {
	return z.logger.Sync()
}

// newZapLoggerAdapter 创建zap.Logger适配器
func newZapLoggerAdapter(zapLogger *zap.Logger) logger.Logger {
	return &zapLoggerAdapter{logger: zapLogger}
}

// RegisterRoutes 注册bos-core模块的路由
// 这是模块的统一入口函数，符合模块化开发规范
func RegisterRoutes(e *echo.Echo, opts ModuleOptions) Exports {
	// 1. 创建Repository管理器
	repoManager := gormRepo.NewRepositoryManager(opts.DB)

	// 2. 创建Logger适配器（将zap.Logger适配为内部logger.Logger接口）
	loggerAdapter := newZapLoggerAdapter(opts.Logger)

	// 3. 创建JWT管理器
	jwtManager := utils.NewJWTManager(
		opts.Config.JWT.SecretKey,
		opts.Config.JWT.ExpiresIn,
		opts.Config.JWT.RefreshExpiresIn,
		opts.Config.JWT.Issuer,
	)

	// 4. 创建Service管理器
	serviceManager := impl.NewServiceManager(repoManager, jwtManager, opts.Config, loggerAdapter)

	// 5. 创建Handler实例
	h := handler.NewHandler(serviceManager, opts.Logger)
	sessionHandler := handler.NewSessionHandler(serviceManager, opts.Logger)
	logHandler := handler.NewLogHandler(serviceManager, opts.Logger)
	operationLogsHandler := handler.NewOperationLogsHandler(serviceManager, opts.Logger)
	equipmentHandler := handler.NewEquipmentHandler(serviceManager.Equipment())
	userStationHandler := handler.NewUserStationHandler(serviceManager.UserStation(), loggerAdapter)

	// 数据字典处理器
	dictCategoryHandler := handler.NewDictCategoryHandler(h)
	dictItemHandler := handler.NewDictItemHandler(h, serviceManager, opts.Logger)
	dictValueHandler := handler.NewDictValueHandler(h, serviceManager, opts.Logger)
	dictSyncHandler := handler.NewDictSyncHandler(h, serviceManager, opts.Logger)

	// 6. 创建JWT认证中间件
	authMiddleware := middleware.AuthMiddleware(middleware.AuthConfig{
		Skipper:    middleware.DefaultSkipper,
		JWTManager: jwtManager,
		SessionSvc: serviceManager.Session(),
		Logger:     opts.Logger,
	})

	// === 所有路由都使用统一的API版本前缀 ===
	apiV1 := e.Group(APIVersionPrefix)

	// 7. 注册认证路由组（无需认证）
	authGroup := apiV1.Group("/auth")
	authGroup.GET("/health", h.HealthCheck)
	authGroup.POST("/login", h.Login)
	authGroup.POST("/logout", h.Logout)
	authGroup.POST("/refresh", h.RefreshToken)

	// 8. 注册需要认证的认证路由
	authProtectedGroup := apiV1.Group("/auth", authMiddleware)
	authProtectedGroup.GET("/me", h.GetCurrentUser)        // 获取当前用户信息
	authProtectedGroup.POST("/verify", h.VerifyPermission) // 权限验证接口需要认证
	authProtectedGroup.PUT("/password", h.ChangePassword)  // 修改密码需要认证

	// 9. 注册用户管理路由组（需要认证）
	usersGroup := apiV1.Group("/users", authMiddleware)
	usersGroup.GET("", h.ListUsers)
	usersGroup.POST("", h.CreateUser)
	usersGroup.GET("/:userId", h.GetUser)
	usersGroup.PUT("/:userId", h.UpdateUser)
	usersGroup.DELETE("/:userId", h.DeleteUser)
	usersGroup.PUT("/:userId/status", h.UpdateUserStatus)
	usersGroup.PUT("/:userId/roles", h.AssignUserRoles)
	usersGroup.GET("/:userId/permissions", h.GetUserPermissions)

	// 10. 注册角色管理路由组（需要认证）
	rolesGroup := apiV1.Group("/roles", authMiddleware)
	rolesGroup.GET("", h.ListRoles)
	rolesGroup.POST("", h.CreateRole)
	rolesGroup.GET("/:roleId", h.GetRole)
	rolesGroup.PUT("/:roleId", h.UpdateRole)
	rolesGroup.DELETE("/:roleId", h.DeleteRole)
	rolesGroup.PUT("/:roleId/permissions", h.AssignRolePermissions)

	// 11. 注册权限管理路由组（需要认证）
	permissionsGroup := apiV1.Group("/permissions", authMiddleware)
	permissionsGroup.GET("", h.ListPermissions)
	permissionsGroup.POST("", h.CreatePermission)
	permissionsGroup.GET("/:permissionId", h.GetPermission)
	permissionsGroup.PUT("/:permissionId", h.UpdatePermission)
	permissionsGroup.DELETE("/:permissionId", h.DeletePermission)

	// 11.5. 注册系统权限管理路由组（需要认证）
	// 创建系统权限管理处理器
	systemAccessHandler := handler.NewSystemAccessSimpleHandler(h, serviceManager.UserSystemAccess())

	systemAccessGroup := apiV1.Group("/system-access", authMiddleware)
	// HOS系统权限管理
	systemAccessGroup.POST("/hos/grant", systemAccessHandler.GrantHOSAccess)        // 授予HOS访问权限
	systemAccessGroup.DELETE("/hos/:user_id", systemAccessHandler.RevokeHOSAccess)  // 撤销HOS访问权限
	systemAccessGroup.GET("/hos/:user_id", systemAccessHandler.GetUserHOSAccess)    // 获取用户HOS权限
	systemAccessGroup.GET("/hos/users", systemAccessHandler.ListUsersWithHOSAccess) // 列出拥有HOS权限的用户

	// 12. 注册会话管理路由组（需要认证）
	sessionsGroup := apiV1.Group("/sessions", authMiddleware)
	sessionsGroup.GET("", sessionHandler.GetSessions)                      // 获取当前用户的活跃会话列表
	sessionsGroup.DELETE("/:sessionId", sessionHandler.TerminateSession)   // 终止指定会话
	sessionsGroup.DELETE("/others", sessionHandler.TerminateOtherSessions) // 终止除当前会话外的所有其他会话

	// 13. 注册日志查询路由组（需要认证）
	logsGroup := apiV1.Group("/logs", authMiddleware)
	logsGroup.GET("/login", logHandler.GetLoginLogs)           // 获取登录日志
	logsGroup.GET("/permission", logHandler.GetPermissionLogs) // 获取权限验证日志

	// 操作日志接口（按照API文档要求）
	logsGroup.POST("/operations", operationLogsHandler.RecordLog)             // 记录单条操作日志
	logsGroup.POST("/operations/async", operationLogsHandler.RecordLogAsync)  // 异步记录操作日志
	logsGroup.POST("/operations/batch", operationLogsHandler.RecordBatchLogs) // 批量记录操作日志
	logsGroup.GET("/operations", operationLogsHandler.QueryLogs)              // 查询操作日志列表
	logsGroup.GET("/operations/:logId", operationLogsHandler.GetLogByID)      // 查询单条日志详情
	logsGroup.GET("/statistics", operationLogsHandler.GetStatistics)          // 获取日志统计信息
	logsGroup.POST("/export", operationLogsHandler.ExportLogs)                // 导出日志数据
	logsGroup.GET("/export/:taskId", operationLogsHandler.GetExportStatus)    // 查询导出任务状态
	logsGroup.DELETE("/cleanup", operationLogsHandler.CleanupLogs)            // 清理过期日志

	// 14. 注册站点管理路由组（需要认证）
	stationsGroup := apiV1.Group("/stations", authMiddleware)
	// 站点基础管理
	stationsGroup.POST("", h.CreateStation)                      // 创建站点
	stationsGroup.GET("", h.ListStations)                        // 获取站点列表
	stationsGroup.GET("/:id", h.GetStation)                      // 根据ID获取站点
	stationsGroup.GET("/code/:siteCode", h.GetStationBySiteCode) // 根据站点编码获取站点
	stationsGroup.PUT("/:id", h.UpdateStation)                   // 更新站点
	stationsGroup.DELETE("/:id", h.DeleteStation)                // 删除站点
	stationsGroup.PATCH("/:id/status", h.UpdateStationStatus)    // 更新站点状态

	// 站点设备管理（嵌套在站点路由下）
	stationsGroup.POST("/:stid/equipments", equipmentHandler.CreateStationEquipment) // 添加设备到站点
	stationsGroup.GET("/:stid/equipments", equipmentHandler.ListStationEquipments)   // 获取站点设备列表

	// 站点运营参数管理（嵌套在站点路由下）
	stationsGroup.POST("/:stid/params", equipmentHandler.CreateOperationParam)             // 创建运营参数
	stationsGroup.GET("/:stid/params", equipmentHandler.ListOperationParams)               // 获取站点运营参数列表

	// 15. 注册用户站点管理路由组（需要认证）
	userStationsGroup := apiV1.Group("/user-stations", authMiddleware)
	
	// 用户站点分配管理
	userStationsGroup.POST("/assign", userStationHandler.AssignUserToStation)                                            // 分配用户到站点
	userStationsGroup.DELETE("/:user_id/stations/:station_id", userStationHandler.RemoveUserFromStation)                // 从站点移除用户
	userStationsGroup.GET("/:user_id/stations", userStationHandler.GetUserStations)                                     // 获取用户管理的站点列表
	userStationsGroup.GET("/stations/:station_id/managers", userStationHandler.GetStationManagers)                      // 获取站点管理员列表
	userStationsGroup.GET("/stations/:station_id/users", userStationHandler.GetStationUsers)                            // 获取站点用户列表
	userStationsGroup.GET("/stations/:station_id/users-detail", userStationHandler.GetStationUsersDetail)               // 获取站点用户详细信息
	userStationsGroup.GET("/:user_id/stations/:station_id/role", userStationHandler.GetUserStationRole)                 // 获取用户在站点的角色
	userStationsGroup.GET("/:user_id/stations/:station_id/permission", userStationHandler.CheckUserStationPermission)   // 检查用户站点权限
	
	// 批量操作
	userStationsGroup.POST("/:user_id/batch-assign", userStationHandler.BatchAssignStations)                            // 批量分配站点给用户
	userStationsGroup.POST("/:user_id/batch-remove", userStationHandler.BatchRemoveStations)                            // 批量移除用户的站点
	
	// 角色管理
	userStationsGroup.PUT("/:user_id/stations/:station_id/role", userStationHandler.UpdateUserStationRole)              // 更新用户站点角色
	userStationsGroup.POST("/stations/:station_id/transfer", userStationHandler.TransferStationManagement)              // 转移站点管理权
	userStationsGroup.PUT("/:user_id/stations/:station_id/expiration", userStationHandler.SetRoleExpiration)            // 设置角色过期时间
	
	// 列表和统计
	userStationsGroup.GET("/roles", userStationHandler.ListUserStationRoles)                                            // 获取用户站点角色列表
	userStationsGroup.GET("/stats", userStationHandler.GetUserStationStats)                                             // 获取用户站点统计
	stationsGroup.GET("/:stid/params/:id", equipmentHandler.GetOperationParam)             // 获取运营参数详情
	stationsGroup.PUT("/:stid/params/:id", equipmentHandler.UpdateOperationParam)          // 更新运营参数
	stationsGroup.DELETE("/:stid/params/:id", equipmentHandler.DeleteOperationParam)       // 删除运营参数
	stationsGroup.GET("/:stid/params/:id/history", equipmentHandler.GetParamChangeHistory) // 获取参数变更历史

	// 15. 注册设备管理路由组（需要认证）
	equipmentsGroup := apiV1.Group("/equipments", authMiddleware)
	equipmentsGroup.GET("/:id", equipmentHandler.GetStationEquipment)            // 获取设备详情
	equipmentsGroup.PUT("/:id", equipmentHandler.UpdateStationEquipment)         // 更新设备
	equipmentsGroup.DELETE("/:id", equipmentHandler.DeleteStationEquipment)      // 删除设备
	equipmentsGroup.PATCH("/:id/status", equipmentHandler.UpdateEquipmentStatus) // 更新设备状态

	// 设备维护记录管理（嵌套在设备路由下）
	equipmentsGroup.POST("/:id/maintenance", equipmentHandler.CreateMaintenanceRecord) // 添加维护记录到设备
	equipmentsGroup.GET("/:id/maintenance", equipmentHandler.ListMaintenanceRecords)   // 获取设备维护记录列表

	// 16. 注册维护记录管理路由组（需要认证）- 独立访问方式
	maintenanceGroup := apiV1.Group("/maintenance-records", authMiddleware)
	maintenanceGroup.POST("", equipmentHandler.CreateMaintenanceRecord)    // 创建维护记录
	maintenanceGroup.GET("", equipmentHandler.ListMaintenanceRecords)      // 获取维护记录列表
	maintenanceGroup.GET("/:id", equipmentHandler.GetMaintenanceRecord)    // 获取维护记录详情
	maintenanceGroup.PUT("/:id", equipmentHandler.UpdateMaintenanceRecord) // 更新维护记录

	// 17. 注册运营参数管理路由组（需要认证）- 独立访问方式
	paramsGroup := apiV1.Group("/operation-params", authMiddleware)
	paramsGroup.POST("", equipmentHandler.CreateOperationParam)             // 创建运营参数
	paramsGroup.GET("", equipmentHandler.ListOperationParams)               // 获取运营参数列表
	paramsGroup.GET("/:id", equipmentHandler.GetOperationParam)             // 获取运营参数详情
	paramsGroup.GET("/key/:key", equipmentHandler.GetOperationParamByKey)   // 根据key获取运营参数
	paramsGroup.PUT("/:id", equipmentHandler.UpdateOperationParam)          // 更新运营参数
	paramsGroup.DELETE("/:id", equipmentHandler.DeleteOperationParam)       // 删除运营参数
	paramsGroup.GET("/:id/history", equipmentHandler.GetParamChangeHistory) // 获取参数变更历史

	// 18. 注册配置管理路由组（需要认证）
	configsGroup := apiV1.Group("/configs", authMiddleware)

	// 配置项基础管理
	configsGroup.POST("", h.CreateConfigItem)             // 创建配置项
	configsGroup.GET("", h.ListConfigItems)               // 获取配置项列表
	configsGroup.GET("/search", h.SearchConfigItems)      // 搜索配置项
	configsGroup.GET("/:id", h.GetConfigItem)             // 根据ID获取配置项
	configsGroup.GET("/value/:key", h.GetConfigItemByKey) // 根据键名获取配置值
	configsGroup.PUT("/:id", h.UpdateConfigItem)          // 更新配置项
	configsGroup.DELETE("/:id", h.DeleteConfigItem)       // 删除配置项

	// 配置项分类管理
	configsGroup.GET("/category/:category", h.GetConfigItemsByCategory) // 根据分类获取配置项
	configsGroup.GET("/categories", h.GetCategories)                    // 获取配置分类列表

	// 批量操作
	configsGroup.POST("/batch", h.BatchCreateConfigItems)   // 批量创建配置项
	configsGroup.PUT("/batch", h.BatchUpdateConfigItems)    // 批量更新配置项
	configsGroup.DELETE("/batch", h.BatchDeleteConfigItems) // 批量删除配置项
	configsGroup.POST("/values", h.BatchGetConfigValues)    // 批量获取配置值

	// 统计信息
	configsGroup.GET("/stats/categories", h.GetCategoryStats)  // 获取分类统计
	configsGroup.GET("/stats/data-types", h.GetDataTypeStats)  // 获取数据类型统计
	configsGroup.GET("/stats/changes", h.GetConfigChangeStats) // 获取配置变更统计
	configsGroup.GET("/stats/approvals", h.GetApprovalStats)   // 获取审批统计

	// 配置日志管理
	configsGroup.POST("/logs", h.CreateConfigLog)      // 创建配置日志
	configsGroup.GET("/logs", h.ListConfigLogs)        // 获取配置日志列表
	configsGroup.GET("/logs/:id", h.GetConfigLog)      // 获取配置日志详情
	configsGroup.GET("/:id/logs", h.GetConfigItemLogs) // 获取指定配置项的日志

	// 审批管理
	configsGroup.POST("/logs/:id/submit", h.SubmitConfigLogForApproval) // 提交配置日志审批
	configsGroup.POST("/logs/:id/approve", h.ApproveConfigLog)          // 审批通过配置日志
	configsGroup.POST("/logs/:id/reject", h.RejectConfigLog)            // 审批拒绝配置日志

	// 19. 注册数据字典管理路由组（需要认证）
	dictGroup := apiV1.Group("/dict", authMiddleware)

	// 创建字典查询处理器
	dictQueryHandler := handler.NewDictQueryHandler(h)

	// 数据字典查询接口 - F128基础数据服务
	dictGroup.GET("/query", dictQueryHandler.QueryDict)                                                     // 通用字典查询
	dictGroup.POST("/query/batch", dictQueryHandler.BatchQueryDict)                                         // 批量字典查询
	dictGroup.GET("/tree", dictQueryHandler.QueryDictTree)                                                  // 查询字典树形结构
	dictGroup.POST("/validate", dictQueryHandler.ValidateValues)                                            // 验证字典值
	dictGroup.GET("/full", dictQueryHandler.GetFullDictData)                                                // 获取完整字典数据
	dictGroup.GET("/categories/:category_code/items", dictQueryHandler.GetCategoryWithItems)                // 获取分类及其项目
	dictGroup.GET("/categories/:category_code/items/:item_code/values", dictQueryHandler.GetItemWithValues) // 获取项目及其值

	// 数据字典导入导出接口
	dictImportExportHandler := handler.NewDictImportExportHandler(h, serviceManager.DictImportExport())
	dictGroup.GET("/export", dictImportExportHandler.ExportDict)                             // 导出字典数据
	dictGroup.GET("/export/download/:file_name", dictImportExportHandler.DownloadExportFile) // 下载导出文件
	dictGroup.POST("/import", dictImportExportHandler.ImportDict)                            // 导入字典数据
	dictGroup.GET("/import/status/:import_id", dictImportExportHandler.GetImportStatus)      // 获取导入状态

	// 数据字典分类管理
	dictCategoriesGroup := dictGroup.Group("/categories")
	dictCategoriesGroup.POST("", dictCategoryHandler.CreateCategory)              // 创建分类
	dictCategoriesGroup.GET("", dictCategoryHandler.ListCategories)               // 获取分类列表
	dictCategoriesGroup.GET("/search", dictCategoryHandler.SearchCategories)      // 搜索分类
	dictCategoriesGroup.GET("/stats", dictCategoryHandler.GetCategoryStats)       // 获取分类统计
	dictCategoriesGroup.GET("/:id", dictCategoryHandler.GetCategory)              // 根据ID获取分类
	dictCategoriesGroup.GET("/code/:code", dictCategoryHandler.GetCategoryByCode) // 根据代码获取分类
	dictCategoriesGroup.PUT("/:id", dictCategoryHandler.UpdateCategory)           // 更新分类
	dictCategoriesGroup.DELETE("/:id", dictCategoryHandler.DeleteCategory)        // 删除分类

	// 数据字典项目管理
	dictItemsGroup := dictGroup.Group("/items")
	dictItemsGroup.POST("", dictItemHandler.CreateItem)               // 创建项目
	dictItemsGroup.GET("", dictItemHandler.ListItems)                 // 获取项目列表
	dictItemsGroup.GET("/search", dictItemHandler.SearchItems)        // 搜索项目
	dictItemsGroup.GET("/:id", dictItemHandler.GetItem)               // 根据ID获取项目
	dictItemsGroup.PUT("/:id", dictItemHandler.UpdateItem)            // 更新项目
	dictItemsGroup.DELETE("/:id", dictItemHandler.DeleteItem)         // 删除项目
	dictItemsGroup.POST("/batch", dictItemHandler.BatchCreateItems)   // 批量创建项目
	dictItemsGroup.PUT("/batch", dictItemHandler.BatchUpdateItems)    // 批量更新项目
	dictItemsGroup.DELETE("/batch", dictItemHandler.BatchDeleteItems) // 批量删除项目

	// 数据字典值管理
	dictValuesGroup := dictGroup.Group("/values")
	dictValuesGroup.POST("", dictValueHandler.CreateValue)               // 创建字典值
	dictValuesGroup.GET("", dictValueHandler.ListValues)                 // 获取字典值列表
	dictValuesGroup.GET("/search", dictValueHandler.SearchValues)        // 搜索字典值
	dictValuesGroup.GET("/:id", dictValueHandler.GetValue)               // 根据ID获取字典值
	dictValuesGroup.PUT("/:id", dictValueHandler.UpdateValue)            // 更新字典值
	dictValuesGroup.DELETE("/:id", dictValueHandler.DeleteValue)         // 删除字典值
	dictValuesGroup.POST("/batch", dictValueHandler.BatchCreateValues)   // 批量创建字典值
	dictValuesGroup.PUT("/batch", dictValueHandler.BatchUpdateValues)    // 批量更新字典值
	dictValuesGroup.DELETE("/batch", dictValueHandler.BatchDeleteValues) // 批量删除字典值

	// 数据字典同步管理
	dictSyncGroup := dictGroup.Group("/sync")
	dictSyncGroup.POST("/hos", dictSyncHandler.SyncHOSData)              // 同步HOS数据
	dictSyncGroup.GET("/status/:sync_id", dictSyncHandler.GetSyncStatus) // 获取同步状态
	dictSyncGroup.GET("/history", dictSyncHandler.GetSyncHistory)        // 获取同步历史
	dictSyncGroup.POST("/schemas", dictSyncHandler.SyncHOSSchemas)       // 同步HOS表结构
	dictSyncGroup.DELETE("/records", dictSyncHandler.CleanupSyncRecords) // 清理同步记录

	// 20. 注册通用标签系统路由组（需要认证）
	// 标签分组管理路由
	tagGroupsGroup := apiV1.Group("/tag-groups", authMiddleware)
	tagGroupsGroup.GET("", h.ListTagGroups)                         // 获取标签分组列表
	tagGroupsGroup.POST("", h.CreateTagGroup)                       // 创建标签分组
	tagGroupsGroup.GET("/:groupId", h.GetTagGroup)                  // 获取标签分组详情
	tagGroupsGroup.PUT("/:groupId", h.UpdateTagGroup)               // 更新标签分组
	tagGroupsGroup.DELETE("/:groupId", h.DeleteTagGroup)            // 删除标签分组
	tagGroupsGroup.PUT("/:groupId/status", h.UpdateTagGroupStatus)  // 更新标签分组状态
	tagGroupsGroup.GET("/tree", h.GetTagGroupTree)                  // 获取标签分组树
	tagGroupsGroup.GET("/:groupId/children", h.GetTagGroupChildren) // 获取子分组
	tagGroupsGroup.GET("/:groupId/stats", h.GetTagGroupStats)       // 获取分组统计

	// 标签管理路由
	tagsGroup := apiV1.Group("/tags", authMiddleware)
	tagsGroup.GET("", h.ListTags)                      // 获取标签列表
	tagsGroup.POST("", h.CreateTag)                    // 创建标签
	tagsGroup.GET("/:tagId", h.GetTag)                 // 获取标签详情
	tagsGroup.PUT("/:tagId", h.UpdateTag)              // 更新标签
	tagsGroup.DELETE("/:tagId", h.DeleteTag)           // 删除标签
	tagsGroup.PUT("/:tagId/status", h.UpdateTagStatus) // 更新标签状态
	tagsGroup.GET("/group/:groupId", h.GetTagsByGroup) // 获取分组下的标签
	tagsGroup.GET("/search", h.SearchTags)             // 搜索标签
	tagsGroup.POST("/batch", h.BatchCreateTags)        // 批量创建标签
	tagsGroup.PUT("/batch", h.BatchUpdateTags)         // 批量更新标签
	tagsGroup.DELETE("/batch", h.BatchDeleteTags)      // 批量删除标签
	tagsGroup.GET("/popular", h.GetPopularTags)        // 获取热门标签

	// 实体标签关联路由
	entitiesGroup := apiV1.Group("/entities", authMiddleware)
	entitiesGroup.GET("/:entityType/:entityId/tags", h.GetEntityTags)             // 获取实体标签
	entitiesGroup.POST("/:entityType/:entityId/tags", h.AddEntityTag)             // 为实体添加标签
	entitiesGroup.DELETE("/:entityType/:entityId/tags/:tagId", h.RemoveEntityTag) // 移除实体标签
	entitiesGroup.POST("/batch-tags", h.BatchAddEntityTags)                       // 批量添加实体标签
	entitiesGroup.DELETE("/batch-tags", h.BatchRemoveEntityTags)                  // 批量移除实体标签
	entitiesGroup.PUT("/:entityType/:entityId/tags", h.ReplaceEntityTags)         // 替换实体标签
	entitiesGroup.GET("/expiring-tags", h.GetExpiringTags)                        // 获取即将过期的标签
	entitiesGroup.DELETE("/expired-tags", h.CleanupExpiredTags)                   // 清理过期标签

	// 标签下的实体查询路由
	tagsGroup.GET("/:tagId/entities/:entityType", h.GetTagEntities) // 获取标签下的实体

	// 用户标签专用路由
	usersTagGroup := usersGroup.Group("/:userId/tags")
	usersTagGroup.GET("", h.GetEntityTags) // 获取用户标签（复用通用接口）
	usersTagGroup.POST("", h.AddEntityTag) // 为用户添加标签（复用通用接口）

	// 油站标签专用路由
	stationsTagGroup := stationsGroup.Group("/:stationId/tags")
	stationsTagGroup.GET("", h.GetEntityTags) // 获取油站标签（复用通用接口）
	stationsTagGroup.POST("", h.AddEntityTag) // 为油站添加标签（复用通用接口）

	// 标签统计分析路由
	tagStatsGroup := apiV1.Group("/tag-statistics", authMiddleware)
	tagStatsGroup.GET("", h.GetTagStatistics)                                  // 获取标签统计信息
	tagStatsGroup.GET("/distribution/:entityType", h.GetEntityTagDistribution) // 获取实体标签分布

	tagStatsGroup.GET("/trends", h.GetTrendAnalysis)                  // 获取趋势分析
	tagStatsGroup.GET("/hot-tags", h.GetHotTags)                      // 获取热门标签
	tagStatsGroup.GET("/untagged/:entityType", h.GetUntaggedEntities) // 获取未标记实体

	// 系统管理路由（标签系统相关）
	systemGroup := apiV1.Group("/system", authMiddleware)
	systemGroup.GET("/entity-types", h.GetEntityTypes)        // 获取实体类型配置（需要实现）
	systemGroup.DELETE("/expired-tags", h.CleanupExpiredTags) // 清理过期标签（复用）

	// 21. 返回导出的服务实例，供其他模块使用
	return Exports{
		Services: serviceManager,
	}
}

// AuthModuleHandler 认证模块的HTTP处理器（保留用于健康检查等独立功能）
type AuthModuleHandler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// healthHandler 模块健康检查接口
func (h *AuthModuleHandler) healthHandler(c echo.Context) error {
	// 检查数据库连接
	sqlDB, err := h.db.DB()
	if err != nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]string{
			"status": "unhealthy",
			"module": "bos-core",
			"error":  "database connection error: " + err.Error(),
		})
	}

	if err := sqlDB.Ping(); err != nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]string{
			"status": "unhealthy",
			"module": "bos-core",
			"error":  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"status":    "healthy",
		"module":    "bos-core",
		"version":   "1.0.0",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
