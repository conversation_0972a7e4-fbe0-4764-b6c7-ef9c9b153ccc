# BOS 系统 API 接口总览文档

## 概述

本文档提供BOS（Business Operation System）加油站管理系统的完整API接口总览，包括主要的bos项目和bos-core核心认证模块的所有API接口。

**基础信息**:
- **Base URL**: `/api/v1`
- **认证方式**: Bearer <PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 项目架构

### BOS 主项目
提供加油站业务核心功能，包括燃油交易、订单管理、报表分析、员工管理、班次管理、促销管理、会员管理、油品管理、设备管理、客户管理、库存管理和支付管理等。

### BOS-Core 核心模块
提供系统的认证、授权、用户管理、角色管理、权限管理、配置管理、数据字典、标签系统等基础服务。

## API 接口分类总览

### 一、BOS 主项目 API 接口 (157+ 接口)

#### 0. 员工认证管理 (7个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/employee/login` | 员工登录认证 |
| GET | `/employee/profile` | 获取当前员工信息 |
| POST | `/employee` | 创建员工 |
| GET | `/employees` | 获取员工列表 |
| GET | `/employee/{id}` | 获取员工详情 |
| PUT | `/employee/{id}` | 更新员工信息 |
| DELETE | `/employee/{id}` | 删除员工 |

#### 1. 燃油交易管理 (12个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/fuel-transactions` | 获取燃油交易列表 |
| POST | `/fuel-transactions` | 创建燃油交易 |
| GET | `/fuel-transactions/{id}` | 获取燃油交易详情 |
| PATCH | `/fuel-transactions/{id}/pump-readings` | 更新燃油交易泵码数 |
| POST | `/fuel-transactions/{id}/link` | 关联燃油交易到订单 |
| DELETE | `/fuel-transactions/{id}/unlink/{order_id}` | 解除燃油交易与订单关联 |
| GET | `/fuel-transactions/{id}/orders` | 获取燃油交易关联的订单 |
| PUT | `/fuel-transactions/{id}/link/{order_id}` | 更新关联分配金额 |
| POST | `/fuel-transactions/{id}/confirm/{order_id}` | 确认燃油交易关联 |
| POST | `/fuel-transactions/{id}/cancel/{order_id}` | 取消燃油交易关联 |
| POST | `/fuel-transactions/{id}/cleanup` | 清理燃油交易关联 |
| POST | `/fuel-transactions/{id}/process` | 处理燃油交易状态 |

#### 2. 订单管理 (10个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/orders` | 获取订单列表 |
| POST | `/orders` | 创建订单 |
| GET | `/orders/{id}` | 获取订单详情 |
| POST | `/orders/{id}/items` | 添加订单项 |
| DELETE | `/orders/{id}/items/{item_id}` | 移除订单项 |
| POST | `/orders/{id}/promotions` | 应用促销 |
| POST | `/orders/{id}/complete` | 完成订单 |
| POST | `/orders/{id}/cancel` | 取消订单 |
| GET | `/orders/{id}/payment-status` | 获取订单支付状态 |
| GET | `/orders/{order_id}/fuel-transactions` | 获取订单关联的燃油交易 |

#### 3. 报表管理 (7个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/reports/payment-methods` | 获取支付方式汇总 |
| GET | `/reports/revenue` | 获取聚合收入报表 |
| GET | `/reports/receivable` | 获取应收账款报表 |
| GET | `/reports/nozzle-sales` | 获取油枪销售汇总 |
| GET | `/reports/transactions` | 获取交易报表数据 |
| GET | `/reports/sales-by-category` | 获取按分类销售汇总 |
| GET | `/reports/shift-eod` | 获取班次结算报表 |

#### 4. 增强报表管理 (6个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/enhanced-reports/total-revenue` | 获取总收入汇总 |
| GET | `/enhanced-reports/fuel-receivable` | 获取燃油应收汇总 |
| GET | `/enhanced-reports/sales-by-payment-method` | 按支付方式销售明细 |
| GET | `/enhanced-reports/sales-by-product` | 按产品销售明细 |
| GET | `/enhanced-reports/nozzle-sales` | 获取油枪详细销售 |
| GET | `/enhanced-reports/query-performance` | 获取查询性能 |

#### 5. 员工管理 (6个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/employee/login` | 员工登录 |
| POST | `/employee` | 创建员工 |
| GET | `/employee/{id}` | 获取员工信息 |
| PUT | `/employee/{id}` | 更新员工信息 |
| DELETE | `/employee/{id}` | 删除员工 |
| GET | `/employees` | 获取员工列表 |

#### 6. 班次管理 (10个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/shifts` | 获取班次列表 |
| POST | `/shifts/start` | 开始新班次 |
| POST | `/shifts/{station_id}/end` | 结束当前班次 |
| GET | `/shifts/current/{station_id}` | 获取当前活跃班次 |
| POST | `/shifts/{station_id}/ensure` | 确保班次已开始 |
| GET | `/shifts/{id}` | 获取班次详情 |
| GET | `/shifts/number/{number}` | 根据编号获取班次 |
| GET | `/shifts/report/{id}` | 获取班次报表 |
| DELETE | `/shifts/{id}` | 软删除班次 |
| POST | `/shifts/{id}/restore` | 恢复已删除班次 |

#### 7. 促销管理 (8个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/promotions` | 获取促销列表 |
| GET | `/promotions/{id}` | 查看促销详情 |
| GET | `/promotions/new` | 显示新建促销表单 |
| GET | `/promotions/edit/{id}` | 显示编辑促销表单 |
| POST | `/promotions/save` | 保存促销 |
| POST | `/promotions/save/{id}` | 更新促销 |
| DELETE | `/promotions/{id}` | 删除促销 |
| POST | `/promotions/{id}/status` | 更新促销状态 |

#### 8. 促销计算 (4个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/calculator` | 促销计算首页 |
| GET | `/calculator/status` | 获取计算状态 |
| POST | `/calculator/process` | 处理订单 |
| POST | `/calculator/calculate` | 计算促销 |

#### 9. 会员管理 (8个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/members` | 创建会员 |
| GET | `/members` | 获取会员列表 |
| GET | `/members/{id}` | 获取会员信息 |
| GET | `/members/phone/{phone}` | 根据手机号获取会员 |
| POST | `/members/verify` | 验证会员 |
| PUT | `/members/{id}` | 更新会员信息 |
| PATCH | `/members/{id}/status` | 更新会员状态 |
| GET | `/members/statistics` | 获取会员统计 |

#### 10. 油品管理 (15个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/oil/sales-price` | 获取油品销售价格 |
| GET | `/oil/products` | 获取油品列表 |
| POST | `/oil/products` | 创建新油品 |
| GET | `/oil/products/{id}` | 获取单个油品详情 |
| PUT | `/oil/products/{id}` | 更新油品信息 |
| POST | `/oil/products/{id}/price` | 设置油品基础价格 |
| GET | `/oil/strategies` | 获取定价策略列表 |
| POST | `/oil/strategies` | 创建定价策略 |
| POST | `/oil/strategies/{id}/activate` | 激活策略 |
| POST | `/oil/strategies/{id}/deactivate` | 停用策略 |
| GET | `/oil/adjustments` | 获取价格调整流程列表 |
| POST | `/oil/adjustments` | 发起价格调整 |
| GET | `/oil/adjustments/{id}` | 获取调整流程详情 |
| POST | `/oil/adjustments/{id}/approve` | 审批价格调整 |
| POST | `/oil/adjustments/{id}/submit` | 提交调整审批 |

#### 11. PTS2设备管理 (6个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/pts2/devices` | 获取设备列表 |
| GET | `/pts2/devices/{deviceId}` | 获取设备详情 |
| GET | `/pts2/devices/{deviceId}/status` | 获取设备状态 |
| POST | `/pts2/devices/{deviceId}/connect` | 连接设备 |
| POST | `/pts2/devices/{deviceId}/disconnect` | 断开设备 |
| GET | `/pts2/devices/{deviceId}/info` | 获取设备信息 |

#### 12. PTS2油泵管理 (5个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/pts2/devices/{deviceId}/pumps/{pumpId}` | 获取油泵状态 |
| POST | `/pts2/devices/{deviceId}/pumps/{pumpId}/authorize` | 油泵授权 |
| POST | `/pts2/devices/{deviceId}/pumps/{pumpId}/preset` | 油泵预设 |
| POST | `/pts2/devices/{deviceId}/pumps/{pumpId}/prices` | 设置油泵价格 |
| POST | `/pts2/devices/{deviceId}/pumps/{pumpId}/nozzles/{nozzleId}/price` | 设置油枪价格 |

#### 13. 客户管理 (9个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/customers` | 创建客户 |
| GET | `/customers` | 获取客户列表 |
| GET | `/customers/count` | 统计客户数量 |
| GET | `/customers/{id}` | 获取客户详情 |
| PUT | `/customers/{id}` | 更新客户信息 |
| GET | `/customers/phone/{phone}` | 根据手机号获取客户 |
| GET | `/customers/{customer_id}/vehicles` | 获取客户车辆列表 |
| GET | `/customers/{customer_id}/labels` | 获取客户标签 |
| POST | `/customer-labels` | 给客户分配标签 |

#### 14. 车辆管理 (4个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/vehicles` | 创建新车辆 |
| GET | `/vehicles/{id}` | 获取车辆信息 |
| PUT | `/vehicles/{id}` | 更新车辆信息 |
| GET | `/vehicles/number/{number}` | 根据车牌号获取车辆 |

#### 15. 标签管理 (2个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/labels` | 创建新标签 |
| GET | `/labels` | 获取标签列表 |

#### 16. 库存管理 (18个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/inventory/products` | 创建商品 |
| GET | `/inventory/products/{id}` | 获取商品信息 |
| GET | `/inventory/products` | 获取商品列表 |
| DELETE | `/inventory/products/{id}` | 删除商品 |
| GET | `/inventory/stocks` | 获取库存列表 |
| GET | `/inventory/stocks/{id}` | 获取库存详情 |
| POST | `/inventory/inbound` | 入库操作 |
| POST | `/inventory/outbound` | 出库操作 |
| POST | `/inventory/reservations` | 创建库存预留 |
| POST | `/inventory/reservations/{id}/release` | 释放库存预留 |
| GET | `/inventory/stocktakes` | 获取盘点列表 |
| GET | `/inventory/stocktakes/{id}` | 获取盘点详情 |
| POST | `/inventory/stocktakes` | 开始盘点 |
| POST | `/inventory/stocktakes/items/record` | 记录盘点项 |
| POST | `/inventory/stocktakes/{id}/complete` | 完成盘点 |
| GET | `/inventory/warnings` | 获取库存预警 |
| POST | `/inventory/categories` | 创建分类 |
| GET | `/inventory/categories` | 获取分类列表 |

#### 17. 支付管理 (12个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/payments` | 处理支付 |
| GET | `/payments/{id}` | 获取支付记录详情 |
| GET | `/payments/{id}/status` | 查询支付状态 |
| POST | `/payments/{id}/cancel` | 取消支付 |
| GET | `/payments` | 获取支付记录列表 |
| POST | `/payments/refunds` | 处理退款 |
| GET | `/payments/refunds/{id}/status` | 查询退款状态 |
| GET | `/payment-methods` | 获取支付方式列表 |
| POST | `/payment-methods` | 创建支付方式 |
| PUT | `/payment-methods/{id}` | 更新支付方式 |
| POST | `/payment-methods/{id}/enable` | 启用支付方式 |
| POST | `/payment-methods/{id}/disable` | 禁用支付方式 |

### 二、BOS-Core 核心模块 API 接口 (130+ 接口)

#### 1. 认证管理 (6个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/auth/health` | 健康检查 |
| POST | `/auth/login` | 用户登录 (支持local/ldap/ad认证) |
| POST | `/auth/logout` | 用户登出 |
| POST | `/auth/refresh` | 刷新令牌 |
| POST | `/auth/verify` | 权限验证 |
| PUT | `/auth/password` | 修改密码 |

#### 2. 用户管理 (8个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/users` | 获取用户列表 |
| POST | `/users` | 创建用户 |
| GET | `/users/{userId}` | 获取用户详情 |
| PUT | `/users/{userId}` | 更新用户 |
| DELETE | `/users/{userId}` | 删除用户 |
| PUT | `/users/{userId}/status` | 更新用户状态 |
| PUT | `/users/{userId}/roles` | 分配用户角色 |
| GET | `/users/{userId}/permissions` | 获取用户权限 |

#### 2.1 用户站点关联管理 (10个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/user-stations/assign` | 分配用户到站点 |
| DELETE | `/user-stations/{user_id}/stations/{station_id}` | 从站点移除用户 |
| GET | `/user-stations/{user_id}/stations` | 获取用户管理的站点列表 |
| GET | `/user-stations/stations/{station_id}/managers` | 获取站点管理员列表 |
| GET | `/user-stations/stations/{station_id}/users` | 获取站点用户列表 |
| GET | `/user-stations/stations/{station_id}/users-detail` | 获取站点用户详细信息 |
| GET | `/user-stations/{user_id}/stations/{station_id}/role` | 获取用户在站点的角色 |
| GET | `/user-stations/{user_id}/stations/{station_id}/permission` | 检查用户站点权限 |
| POST | `/user-stations/{user_id}/batch-assign` | 批量分配站点给用户 |
| PUT | `/user-stations/{user_id}/stations/{station_id}/role` | 更新用户站点角色 |
| GET | `/user-stations/roles` | 获取用户站点角色列表 |

#### 3. 角色管理 (6个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/roles` | 获取角色列表 |
| POST | `/roles` | 创建角色 |
| GET | `/roles/{roleId}` | 获取角色详情 |
| PUT | `/roles/{roleId}` | 更新角色 |
| DELETE | `/roles/{roleId}` | 删除角色 |
| PUT | `/roles/{roleId}/permissions` | 分配角色权限 |

#### 4. 权限管理 (5个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/permissions` | 获取权限列表 |
| POST | `/permissions` | 创建权限 |
| GET | `/permissions/{permissionId}` | 获取权限详情 |
| PUT | `/permissions/{permissionId}` | 更新权限 |
| DELETE | `/permissions/{permissionId}` | 删除权限 |

#### 5. 会话管理 (3个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/sessions` | 获取当前用户会话列表 |
| DELETE | `/sessions/{sessionId}` | 终止指定会话 |
| DELETE | `/sessions/others` | 终止其他会话 |

#### 6. 日志管理 (11个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/logs/login` | 获取登录日志 |
| GET | `/logs/permission` | 获取权限验证日志 |
| POST | `/logs/operations` | 记录操作日志 |
| POST | `/logs/operations/async` | 异步记录操作日志 |
| POST | `/logs/operations/batch` | 批量记录操作日志 |
| GET | `/logs/operations` | 查询操作日志列表 |
| GET | `/logs/operations/{logId}` | 查询单条日志详情 |
| GET | `/logs/statistics` | 获取日志统计信息 |
| POST | `/logs/export` | 导出日志数据 |
| GET | `/logs/export/{taskId}` | 查询导出任务状态 |
| DELETE | `/logs/cleanup` | 清理过期日志 |

#### 7. 站点管理 (8个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/stations` | 创建站点 |
| GET | `/stations` | 获取站点列表 |
| GET | `/stations/{id}` | 根据ID获取站点 |
| GET | `/stations/code/{siteCode}` | 根据站点编码获取站点 |
| PUT | `/stations/{id}` | 更新站点 |
| DELETE | `/stations/{id}` | 删除站点 |
| PATCH | `/stations/{id}/status` | 更新站点状态 |
| POST | `/stations/{stid}/equipments` | 添加设备到站点 |

#### 8. 设备管理 (7个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/equipments/{id}` | 获取设备详情 |
| PUT | `/equipments/{id}` | 更新设备 |
| DELETE | `/equipments/{id}` | 删除设备 |
| PATCH | `/equipments/{id}/status` | 更新设备状态 |
| POST | `/equipments/{id}/maintenance` | 添加维护记录 |
| GET | `/equipments/{id}/maintenance` | 获取维护记录列表 |
| GET | `/stations/{stid}/equipments` | 获取站点设备列表 |

#### 9. 运营参数管理 (7个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/operation-params` | 创建运营参数 |
| GET | `/operation-params` | 获取运营参数列表 |
| GET | `/operation-params/{id}` | 获取运营参数详情 |
| GET | `/operation-params/key/{key}` | 根据key获取运营参数 |
| PUT | `/operation-params/{id}` | 更新运营参数 |
| DELETE | `/operation-params/{id}` | 删除运营参数 |
| GET | `/operation-params/{id}/history` | 获取参数变更历史 |

#### 10. 配置管理 (18个接口)
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/configs` | 创建配置项 |
| GET | `/configs` | 获取配置项列表 |
| GET | `/configs/search` | 搜索配置项 |
| GET | `/configs/{id}` | 根据ID获取配置项 |
| GET | `/configs/value/{key}` | 根据键名获取配置值 |
| PUT | `/configs/{id}` | 更新配置项 |
| DELETE | `/configs/{id}` | 删除配置项 |
| GET | `/configs/category/{category}` | 根据分类获取配置项 |
| GET | `/configs/categories` | 获取配置分类列表 |
| POST | `/configs/batch` | 批量创建配置项 |
| PUT | `/configs/batch` | 批量更新配置项 |
| DELETE | `/configs/batch` | 批量删除配置项 |
| POST | `/configs/values` | 批量获取配置值 |
| GET | `/configs/stats/categories` | 获取分类统计 |
| GET | `/configs/stats/data-types` | 获取数据类型统计 |
| GET | `/configs/stats/changes` | 获取配置变更统计 |
| POST | `/configs/logs` | 创建配置日志 |
| GET | `/configs/logs` | 获取配置日志列表 |

#### 11. 数据字典管理 (32个接口)

**字典查询接口 (7个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/dict/query` | 查询字典 |
| POST | `/dict/query/batch` | 批量字典查询 |
| GET | `/dict/tree` | 查询字典树形结构 |
| POST | `/dict/validate` | 验证字典值 |
| GET | `/dict/full` | 获取完整字典数据 |
| GET | `/dict/categories/{category_code}/items` | 获取分类及其项目 |
| GET | `/dict/categories/{category_code}/items/{item_code}/values` | 获取项目及其值 |

**字典导入导出接口 (4个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/dict/export` | 导出字典数据 |
| GET | `/dict/export/download/{file_name}` | 下载导出文件 |
| POST | `/dict/import` | 导入字典数据 |
| GET | `/dict/import/status/{import_id}` | 获取导入状态 |

**分类管理接口 (8个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/dict/categories` | 创建分类 |
| GET | `/dict/categories` | 获取分类列表 |
| GET | `/dict/categories/search` | 搜索分类 |
| GET | `/dict/categories/stats` | 获取分类统计 |
| GET | `/dict/categories/{id}` | 获取分类详情 |
| GET | `/dict/categories/code/{code}` | 根据代码获取分类 |
| PUT | `/dict/categories/{id}` | 更新分类 |
| DELETE | `/dict/categories/{id}` | 删除分类 |

**项目管理接口 (9个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/dict/items` | 创建项目 |
| GET | `/dict/items` | 获取项目列表 |
| GET | `/dict/items/search` | 搜索项目 |
| GET | `/dict/items/{id}` | 根据ID获取项目 |
| PUT | `/dict/items/{id}` | 更新项目 |
| DELETE | `/dict/items/{id}` | 删除项目 |
| POST | `/dict/items/batch` | 批量创建项目 |
| PUT | `/dict/items/batch` | 批量更新项目 |
| DELETE | `/dict/items/batch` | 批量删除项目 |

**值管理接口 (9个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| POST | `/dict/values` | 创建字典值 |
| GET | `/dict/values` | 获取字典值列表 |
| GET | `/dict/values/search` | 搜索字典值 |
| GET | `/dict/values/{id}` | 根据ID获取字典值 |
| PUT | `/dict/values/{id}` | 更新字典值 |
| DELETE | `/dict/values/{id}` | 删除字典值 |
| POST | `/dict/values/batch` | 批量创建字典值 |
| PUT | `/dict/values/batch` | 批量更新字典值 |
| DELETE | `/dict/values/batch` | 批量删除字典值 |

#### 12. 标签系统管理 (25个接口)

**标签分组管理 (9个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/tag-groups` | 获取标签分组列表 |
| POST | `/tag-groups` | 创建标签分组 |
| GET | `/tag-groups/{groupId}` | 获取标签分组详情 |
| PUT | `/tag-groups/{groupId}` | 更新标签分组 |
| DELETE | `/tag-groups/{groupId}` | 删除标签分组 |
| PUT | `/tag-groups/{groupId}/status` | 更新标签分组状态 |
| GET | `/tag-groups/tree` | 获取标签分组树 |
| GET | `/tag-groups/{groupId}/children` | 获取子分组 |
| GET | `/tag-groups/{groupId}/stats` | 获取分组统计 |

**标签管理 (12个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/tags` | 获取标签列表 |
| POST | `/tags` | 创建标签 |
| GET | `/tags/{tagId}` | 获取标签详情 |
| PUT | `/tags/{tagId}` | 更新标签 |
| DELETE | `/tags/{tagId}` | 删除标签 |
| PUT | `/tags/{tagId}/status` | 更新标签状态 |
| GET | `/tags/group/{groupId}` | 获取分组下的标签 |
| GET | `/tags/search` | 搜索标签 |
| POST | `/tags/batch` | 批量创建标签 |
| PUT | `/tags/batch` | 批量更新标签 |
| DELETE | `/tags/batch` | 批量删除标签 |
| GET | `/tags/popular` | 获取热门标签 |

**实体标签关联 (9个)**
| 方法 | 接口 | 描述 |
|------|------|------|
| GET | `/entities/{entityType}/{entityId}/tags` | 获取实体标签 |
| POST | `/entities/{entityType}/{entityId}/tags` | 为实体添加标签 |
| DELETE | `/entities/{entityType}/{entityId}/tags/{tagId}` | 移除实体标签 |
| POST | `/entities/batch-tags` | 批量添加实体标签 |
| DELETE | `/entities/batch-tags` | 批量移除实体标签 |
| PUT | `/entities/{entityType}/{entityId}/tags` | 替换实体标签 |
| GET | `/entities/expiring-tags` | 获取即将过期的标签 |
| DELETE | `/entities/expired-tags` | 清理过期标签 |
| GET | `/tags/{tagId}/entities/{entityType}` | 获取标签下的实体 |

## 详细文档链接

### BOS 主项目文档
- [员工认证API文档](./bos/docs/BOS-员工认证API文档.md)
- [燃油交易管理API文档](./bos/docs/BOS-燃油交易管理API文档.md)
- [订单管理API文档](./bos/docs/BOS-订单管理API文档.md)
- [报表管理API文档](./bos/docs/BOS-报表管理API文档.md)
- [员工与班次管理API文档](./bos/docs/BOS-员工与班次管理API文档.md)
- [促销管理API文档](./bos/docs/BOS-促销管理API文档.md)
- [会员管理API文档](./bos/docs/BOS-会员管理API文档.md)
- [油品管理API文档](./bos/docs/BOS-油品管理API文档.md)
- [设备管理API文档](./bos/docs/BOS-设备管理API文档.md)
- [客户管理API文档](./bos/docs/BOS-客户管理API文档.md)
- [库存管理API文档](./bos/docs/BOS-库存管理API文档.md)
- [支付管理API文档](./bos/docs/BOS-支付管理API文档.md)

### BOS-Core 核心模块文档
- [认证与用户管理API文档](./bos-core/docs/BOS-Core-认证与用户管理API文档.md)
- [角色与权限管理API文档](./bos-core/docs/BOS-Core-角色与权限管理API文档.md)
- [配置管理API文档](./bos-core/docs/BOS-Core-配置管理API文档.md)
- [数据字典管理API文档](./bos-core/docs/BOS-Core-数据字典管理API文档.md)
- [标签系统管理API文档](./bos-core/docs/BOS-Core-标签系统管理API文档.md)
- [日志管理API文档](./bos-core/docs/BOS-Core-日志管理API文档.md)
- [设备与站点管理API文档](./bos-core/docs/BOS-Core-设备与站点管理API文档.md)

## 通用数据类型

### 分页请求参数
```json
{
  "page": "int (页码，默认1)",
  "page_size": "int (每页数量，默认20)",
  "sort_by": "string (排序字段)",
  "sort_dir": "string (排序方向：asc/desc)"
}
```

### 分页响应格式
```json
{
  "items": "array (数据列表)",
  "pagination": {
    "page": "int (当前页码)",
    "page_size": "int (每页数量)",
    "total": "int (总记录数)",
    "total_pages": "int (总页数)",
    "has_next": "boolean (是否有下一页)",
    "has_prev": "boolean (是否有上一页)"
  }
}
```

### 时间格式
- **日期时间**: `2024-01-15T10:30:00Z` (ISO 8601格式)
- **日期**: `2024-01-15` (YYYY-MM-DD格式)
- **时间**: `10:30:00` (HH:MM:SS格式)

## 状态码总览

### HTTP状态码
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `500` - 服务器内部错误

### 业务状态码 (BOS项目)
- `200` - 操作成功
- `ERROR_CODE` - 具体错误码（详见各模块文档）

### 业务状态码 (BOS-Core项目)
- `0` - 操作成功
- `1001-9999` - 各种业务错误码

## 认证说明

### 认证方式

系统提供多种认证方式：

#### BOS-Core 用户认证
通过 `POST /auth/login` 接口登录，支持用户名/邮箱地址登录：

**支持的认证类型**:
- `local`: 本地用户认证（默认）
- `ldap`: LDAP域认证
- `ad`: Active Directory认证

**用户名登录示例**:
```json
{
  "username": "admin",
  "password": "password123",
  "authType": "local",
  "rememberMe": true
}
```

**邮箱登录示例**:
```json
{
  "username": "<EMAIL>", 
  "password": "password123",
  "authType": "local"
}
```

#### BOS 员工认证
通过 `POST /employee/login` 接口登录：
```json
{
  "employee_no": "EMP001",
  "password": "password123"
}
```

### JWT Token 格式
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token 生命周期
- **Access Token**: 3600秒 (1小时)
- **Refresh Token**: 604800秒 (7天)

### 登录特性

#### 智能登录识别
- **BOS-Core**: 自动识别用户名或邮箱地址，无需指定登录类型
- **BOS员工**: 使用员工编号进行登录认证
- **安全控制**: 支持登录失败次数限制和账户锁定机制

#### 会话管理
- **JWT令牌**: 使用访问令牌和刷新令牌机制
- **会话跟踪**: 记录用户登录状态和活动
- **多设备支持**: 支持同一用户多设备同时登录

### 用户站点权限
BOS-Core支持用户站点关联管理，用户可以被分配到不同站点并拥有不同角色：
- `manager`: 站长 - 拥有站点完全管理权限
- `assistant_manager`: 副站长 - 协助管理权限
- `supervisor`: 主管 - 监督和指导权限
- `operator`: 操作员 - 基本操作权限

### 权限格式
- **格式**: `资源:操作` (例如: `user:read`, `order:write`)
- **通配符**: `*` 表示所有权限

## 接口调用示例

### 获取认证Token
```bash
curl -X POST /api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123"
  }'
```

### 使用Token调用API
```bash
curl -X GET /api/v1/orders \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json"
```

### 分页查询示例
```bash
curl -G /api/v1/users \
  -H "Authorization: Bearer <access_token>" \
  -d page=1 \
  -d page_size=20 \
  -d keyword=admin \
  -d sort_by=created_at \
  -d sort_dir=desc
```

## 系统架构说明

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   BOS Main      │    │   BOS Core      │
│   Applications  │◄──►│   Application   │◄──►│   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Third Party   │    │   Database      │
                       │   Services      │    │   PostgreSQL    │
                       └─────────────────┘    └─────────────────┘
```

### 核心组件
- **BOS Frontend**: Web前端应用
- **BOS Main**: 主业务应用（燃油交易、订单等）
- **BOS Core**: 核心认证授权服务
- **Third Party**: 第三方服务（支付、促销等）
- **Database**: PostgreSQL数据库

## 版本信息

- **API版本**: v1
- **文档版本**: 1.0
- **最后更新**: 2024-01-15
- **维护团队**: BOS开发团队

## 联系信息

- **技术支持**: <EMAIL>
- **API文档**: https://api.bos.com/docs
- **问题反馈**: https://github.com/bos/issues

---

**注意**: 本文档是基于代码分析生成的API接口总览，实际使用时请参考各模块的详细API文档。所有接口都需要适当的认证和授权。 