# 权限管理 API

## 权限评估

### 评估权限

**POST** `/api/v1/permissions/evaluate`

评估用户对特定资源的权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "permission_code": "station:read:all",
  "resource_type": "station",
  "resource_id": "123",
  "context": {
    "organization_id": 10,
    "system": "BOS"
  }
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "权限评估成功",
  "data": {
    "is_granted": true,
    "permission_code": "station:read:all",
    "resource_type": "station",
    "resource_id": "123",
    "effective_scope": {
      "scope_type": "organization",
      "scope_value": [10, 11, 12]
    },
    "source": "direct",
    "expires_at": "2024-12-31T23:59:59Z"
  }
}
```

### 评估数据权限

**POST** `/api/v1/permissions/evaluate/data`

评估用户对数据资源的访问权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "resource_type": "station",
  "action": "read",
  "resource_ids": ["123", "124", "125"],
  "context": {
    "organization_id": 10
  }
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "数据权限评估成功",
  "data": {
    "is_granted": true,
    "effective_scope": {
      "scope_type": "organization",
      "scope_value": [10, 11, 12]
    },
    "accessible_resources": ["123", "124"],
    "denied_resources": ["125"]
  }
}
```

### 批量评估权限

**POST** `/api/v1/permissions/evaluate/batch`

批量评估多个权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "permissions": [
    {
      "permission_code": "station:read:all",
      "resource_type": "station",
      "resource_id": "123"
    },
    {
      "permission_code": "user:write:organization",
      "resource_type": "user",
      "resource_id": "456"
    }
  ]
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "批量权限评估成功",
  "data": [
    {
      "is_granted": true,
      "permission_code": "station:read:all",
      "resource_type": "station",
      "resource_id": "123",
      "source": "direct"
    },
    {
      "is_granted": false,
      "permission_code": "user:write:organization",
      "resource_type": "user",
      "resource_id": "456",
      "source": "denied"
    }
  ]
}
```

## 数据权限管理

### 创建数据权限

**POST** `/api/v1/permissions/data`

创建新的数据权限定义。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "permission_code": "report:read:department",
  "permission_name": "部门报表读取权限",
  "resource_type": "report",
  "scope_type": "department",
  "description": "允许读取部门级别的报表数据"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "数据权限创建成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440020",
    "permission_code": "report:read:department",
    "permission_name": "部门报表读取权限",
    "resource_type": "report",
    "scope_type": "department",
    "description": "允许读取部门级别的报表数据",
    "is_system": false,
    "status": "active",
    "created_at": "2024-01-19T10:00:00Z",
    "created_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 获取数据权限列表

**GET** `/api/v1/permissions/data`

获取数据权限列表。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| resource_type | string | 资源类型过滤 | "station" |
| scope_type | string | 范围类型过滤 | "organization" |
| status | string | 状态过滤 | "active" |
| is_system | bool | 系统权限过滤 | false |
| keyword | string | 关键词搜索 | "读取" |
| offset | int | 偏移量 | 0 |
| limit | int | 限制数量 | 20 |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取数据权限列表成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440020",
      "permission_code": "station:read:all",
      "permission_name": "站点全部读取权限",
      "resource_type": "station",
      "scope_type": "all",
      "description": "允许读取所有站点数据",
      "is_system": true,
      "status": "active",
      "user_count": 5,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 更新数据权限

**PUT** `/api/v1/permissions/data/{permissionId}`

更新数据权限信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "permission_name": "部门报表读取权限（更新）",
  "description": "允许读取部门级别的报表数据 - 已更新",
  "status": "active"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "数据权限更新成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440020",
    "permission_name": "部门报表读取权限（更新）",
    "description": "允许读取部门级别的报表数据 - 已更新",
    "updated_at": "2024-01-19T11:00:00Z",
    "updated_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 删除数据权限

**DELETE** `/api/v1/permissions/data/{permissionId}`

删除数据权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "数据权限删除成功"
}
```

## 用户权限管理

### 授予用户数据权限

**POST** `/api/v1/permissions/users/grant`

授予用户特定的数据权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "permission_code": "station:read:organization",
  "scope_value": {
    "organization_ids": [10, 11, 12]
  },
  "scope_conditions": {
    "time_range": {
      "start": "09:00",
      "end": "18:00"
    }
  },
  "expires_at": "2024-12-31T23:59:59Z"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "用户数据权限授予成功"
}
```

### 撤销用户数据权限

**DELETE** `/api/v1/permissions/users/{userId}/permissions/{permissionId}`

撤销用户的特定数据权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "用户数据权限撤销成功"
}
```

### 获取用户数据权限

**GET** `/api/v1/permissions/users/{userId}/permissions`

获取用户的所有数据权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取用户数据权限成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440030",
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "data_permission": {
        "id": "550e8400-e29b-41d4-a716-446655440020",
        "permission_code": "station:read:organization",
        "permission_name": "组织站点读取权限",
        "resource_type": "station",
        "scope_type": "organization"
      },
      "scope_value": {
        "organization_ids": [10, 11, 12]
      },
      "scope_conditions": {
        "time_range": {
          "start": "09:00",
          "end": "18:00"
        }
      },
      "granted_at": "2024-01-19T10:00:00Z",
      "expires_at": "2024-12-31T23:59:59Z",
      "granted_by": "550e8400-e29b-41d4-a716-446655440001"
    }
  ]
}
```

### 获取用户有效权限

**GET** `/api/v1/permissions/users/{userId}/effective-permissions`

获取用户的有效权限（包括直接权限和继承权限）。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| resource_type | string | 资源类型过滤 | "station" |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取用户有效权限成功",
  "data": [
    {
      "permission_code": "station:read:organization",
      "permission_name": "组织站点读取权限",
      "resource_type": "station",
      "scope_type": "organization",
      "scope_value": {
        "organization_ids": [10, 11, 12]
      },
      "scope_conditions": {
        "time_range": {
          "start": "09:00",
          "end": "18:00"
        }
      },
      "source": "direct",
      "granted_at": "2024-01-19T10:00:00Z",
      "expires_at": "2024-12-31T23:59:59Z"
    },
    {
      "permission_code": "user:read:department",
      "permission_name": "部门用户读取权限",
      "resource_type": "user",
      "scope_type": "department",
      "scope_value": {
        "department_ids": [20, 21]
      },
      "source": "inherited",
      "granted_at": "2024-01-19T10:00:00Z"
    }
  ]
}
```

## 权限模板管理

### 创建权限模板

**POST** `/api/v1/permissions/templates`

创建组织权限模板。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "organization_id": 10,
  "role_type": "manager",
  "template_name": "分公司经理权限模板",
  "description": "分公司经理的标准权限配置",
  "permissions": [
    "org:read:all",
    "org:write:own",
    "user:read:organization",
    "user:write:department"
  ],
  "data_permissions": {
    "station:read:organization": {
      "scope_type": "organization",
      "scope_value": {
        "organization_ids": [10]
      }
    },
    "report:read:organization": {
      "scope_type": "organization",
      "scope_value": {
        "organization_ids": [10]
      }
    }
  },
  "is_default": true
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "权限模板创建成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440040",
    "organization_id": 10,
    "role_type": "manager",
    "template_name": "分公司经理权限模板",
    "description": "分公司经理的标准权限配置",
    "permissions": [
      "org:read:all",
      "org:write:own",
      "user:read:organization",
      "user:write:department"
    ],
    "data_permissions": { /* 数据权限配置 */ },
    "is_default": true,
    "is_system": false,
    "priority": 0,
    "status": "active",
    "created_at": "2024-01-19T10:00:00Z",
    "created_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 获取权限模板详情

**GET** `/api/v1/permissions/templates/{templateId}`

获取权限模板的详细信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取权限模板成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440040",
    "organization": {
      "id": 10,
      "org_name": "北京分公司",
      "org_code": "BJ_BRANCH"
    },
    "role_type": "manager",
    "template_name": "分公司经理权限模板",
    "description": "分公司经理的标准权限配置",
    "permissions": [ /* 权限列表 */ ],
    "data_permissions": { /* 数据权限配置 */ },
    "is_default": true,
    "is_system": false,
    "applied_user_count": 5,
    "created_at": "2024-01-19T10:00:00Z"
  }
}
```

### 更新权限模板

**PUT** `/api/v1/permissions/templates/{templateId}`

更新权限模板。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "template_name": "分公司经理权限模板（更新）",
  "description": "分公司经理的标准权限配置 - 已更新",
  "permissions": [
    "org:read:all",
    "org:write:own",
    "user:read:organization",
    "user:write:department",
    "report:read:organization"
  ],
  "is_default": true,
  "status": "active"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "权限模板更新成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440040",
    "template_name": "分公司经理权限模板（更新）",
    "description": "分公司经理的标准权限配置 - 已更新",
    "updated_at": "2024-01-19T11:00:00Z",
    "updated_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 删除权限模板

**DELETE** `/api/v1/permissions/templates/{templateId}`

删除权限模板。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "权限模板删除成功"
}
```

### 应用权限模板

**POST** `/api/v1/permissions/templates/{templateId}/apply`

将权限模板应用到指定用户。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_ids": [
    "550e8400-e29b-41d4-a716-446655440000",
    "550e8400-e29b-41d4-a716-446655440001"
  ]
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "权限模板应用成功"
}
```
