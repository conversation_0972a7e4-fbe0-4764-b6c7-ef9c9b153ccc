# 认证管理 API

## 基础认证

### 用户登录

**POST** `/api/v1/auth/login`

用户基础登录接口。

#### 请求参数

```json
{
  "username": "admin",           // 用户名或邮箱
  "password": "Admin123",        // 密码
  "system": "BOS",              // 目标系统 (BOS/EDC)
  "authType": "local",          // 认证类型 (local/ldap/ad)
  "rememberMe": false,          // 是否记住登录
  "captcha": "",                // 验证码
  "captchaId": ""               // 验证码ID
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "系统管理员",
      "status": "active"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-19T10:00:00Z",
    "session_id": "550e8400-e29b-41d4-a716-446655440001",
    "station_ids": [1, 2, 3],
    "system_access": {
      "system": "BOS",
      "access_level": "admin",
      "station_ids": [1, 2, 3],
      "station_count": 3
    }
  }
}
```

### 组织登录

**POST** `/api/v1/auth/login/organization`

支持组织上下文的登录接口。

#### 请求参数

```json
{
  "username": "admin",
  "password": "Admin123",
  "organization_id": 1,         // 可选，指定登录的组织
  "system": "BOS",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0..."
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织登录成功",
  "data": {
    "user": { /* 用户信息 */ },
    "access_token": "...",
    "refresh_token": "...",
    "expires_at": "2024-01-19T10:00:00Z",
    "session_id": "...",
    "organization_context": {
      "current_organization": {
        "id": 1,
        "org_name": "总部",
        "org_code": "HQ",
        "org_type": "headquarters"
      },
      "user_role": "manager",
      "permissions": ["org:read", "org:write"],
      "accessible_systems": ["BOS", "EDC"]
    }
  }
}
```

### 登出

**POST** `/api/v1/auth/logout`

用户登出接口。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "登出成功"
}
```

### 刷新令牌

**POST** `/api/v1/auth/refresh`

刷新访问令牌。

#### 请求参数

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "令牌刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-19T11:00:00Z"
  }
}
```

### 验证令牌

**POST** `/api/v1/auth/validate`

验证访问令牌的有效性。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "令牌验证成功",
  "data": {
    "user": { /* 用户信息 */ },
    "expires_at": "2024-01-19T10:00:00Z",
    "is_valid": true
  }
}
```

### 获取认证上下文

**GET** `/api/v1/auth/context`

获取当前用户的完整认证上下文。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取认证上下文成功",
  "data": {
    "user": { /* 用户信息 */ },
    "primary_organization": { /* 主要组织信息 */ },
    "user_organizations": [ /* 用户组织关联列表 */ ],
    "permissions": [ /* 权限列表 */ ],
    "data_permissions": [ /* 数据权限列表 */ ],
    "session_id": "...",
    "system": "BOS",
    "access_level": "admin"
  }
}
```

## 密码管理

### 修改密码

**POST** `/api/v1/auth/password/change`

用户修改密码。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "current_password": "Admin123",
  "new_password": "NewPassword123",
  "confirm_password": "NewPassword123"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "密码修改成功"
}
```

### 重置密码

**POST** `/api/v1/auth/password/reset`

管理员重置用户密码。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "new_password": "ResetPassword123"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "密码重置成功"
}
```

## 会话管理

### 获取用户会话

**GET** `/api/v1/auth/sessions`

获取当前用户的所有活跃会话。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取用户会话成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440001",
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "ip_address": "*************",
      "user_agent": "Mozilla/5.0...",
      "created_at": "2024-01-19T09:00:00Z",
      "last_accessed_at": "2024-01-19T09:30:00Z",
      "expires_at": "2024-01-19T10:00:00Z",
      "is_current": true
    }
  ]
}
```

### 终止会话

**DELETE** `/api/v1/auth/sessions/{sessionId}`

终止指定会话。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "会话终止成功"
}
```

### 终止所有会话

**DELETE** `/api/v1/auth/sessions/all`

终止用户的所有会话（除当前会话外）。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "所有会话终止成功"
}
```

## 组织认证

### 验证组织访问权限

**POST** `/api/v1/auth/organization/validate-access`

验证用户对特定组织的访问权限。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "organization_id": 1,
  "required_role": "manager"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织访问权限验证成功",
  "data": {
    "is_allowed": true
  }
}
```

### 获取用户组织上下文

**GET** `/api/v1/auth/organization/context`

获取当前用户的组织上下文信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取用户组织上下文成功",
  "data": {
    "current_organization": { /* 当前组织信息 */ },
    "user_role": "manager",
    "permissions": ["org:read", "org:write"],
    "accessible_systems": ["BOS", "EDC"],
    "child_organizations": [ /* 子组织列表 */ ],
    "parent_organizations": [ /* 父组织列表 */ ]
  }
}
```

### 切换组织上下文

**POST** `/api/v1/auth/organization/switch/{orgId}`

切换到指定组织的上下文。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织上下文切换成功",
  "data": {
    "current_organization": { /* 新的当前组织信息 */ },
    "user_role": "operator",
    "permissions": ["org:read"],
    "accessible_systems": ["EDC"]
  }
}
```
