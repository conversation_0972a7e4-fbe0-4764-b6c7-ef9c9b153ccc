# 统一身份认证API文档

## 概述

本文档描述了支持HOS（总部管理系统）、BOS（站点后台系统）、EDC（手持设备系统）三个系统的统一身份认证API。

### 版本信息
- **API版本**: v1
- **文档版本**: 1.0.0
- **更新日期**: 2025-01-20

### 基础URL
```
http://localhost:8080/api/v1
```

## 认证流程

### 系统权限模型

| 系统 | 权限范围 | 访问级别 | 说明 |
|------|----------|----------|------|
| **HOS** | global（全局） | admin, manager | 总部管理系统，具有跨站点的全局权限 |
| **BOS** | station（站点） | manager, supervisor | 站点后台系统，权限限制在特定站点 |
| **EDC** | operation（操作） | supervisor, operator | 手持设备系统，主要用于交易操作 |

### 权限级别说明

- **admin**: 管理员级别，拥有最高权限
- **manager**: 经理级别，拥有管理权限
- **supervisor**: 主管级别，拥有监督权限
- **operator**: 操作员级别，拥有基础操作权限

## API接口

### 1. 用户登录

#### 接口信息
- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录接口，支持HOS、BOS、EDC三个系统

#### 请求参数

```json
{
  "username": "string",      // 用户名或邮箱地址 (必填)
  "password": "string",      // 密码 (必填)
  "system": "string",        // 目标系统: HOS|BOS|EDC (必填)
  "authType": "string",      // 认证类型: local|ldap|ad (可选，默认local)
  "rememberMe": "boolean",   // 是否记住登录状态 (可选)
  "captcha": "string",       // 验证码 (可选)
  "captchaId": "string"      // 验证码ID (可选)
}
```

#### 请求示例

**HOS系统登录**:
```json
{
  "username": "admin",
  "password": "Admin123",
  "system": "HOS",
  "authType": "local",
  "rememberMe": true
}
```

**BOS系统登录**:
```json
{
  "username": "station_manager",
  "password": "Manager123",
  "system": "BOS",
  "authType": "local"
}
```

**EDC系统登录**:
```json
{
  "username": "operator",
  "password": "Operator123",
  "system": "EDC",
  "authType": "local"
}
```

#### 响应格式

**成功响应** (HTTP 200):
```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "user": {
      "id": "uuid",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "phone": "string",
      "status": "string",
      "roles": ["string"],
      "permissions": ["string"],
      "lastLoginAt": "2025-01-20T10:00:00Z",
      "language": "en",
      "sites": [
        {
          "siteId": "string",
          "siteName": "string",
          "role": "string",
          "isDefault": true
        }
      ],
      "stations": [
        {
          "id": 1,
          "siteCode": "string",
          "siteName": "string",
          "address": {},
          "status": "string"
        }
      ]
    },
    "systemAccess": {
      "system": "string",        // HOS|BOS|EDC
      "accessLevel": "string",   // admin|manager|supervisor|operator
      "scopeType": "string",     // global|station|operation
      "scopeIds": [1, 2, 3],     // 权限范围ID列表
      "stationIds": [1, 2, 3],   // 可访问站点ID列表（向后兼容）
      "stationCount": 3,         // 可访问站点数量
      "permissions": ["string"]  // 用户权限列表
    }
  },
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

#### 不同系统的响应差异

**HOS系统响应特点**:
- `scopeType`: "global"
- `scopeIds`: [] (空数组，表示全局权限)
- `stationCount`: 0
- `permissions`: 包含全局管理权限

**BOS系统响应特点**:
- `scopeType`: "station"
- `scopeIds`: [1, 2, 3] (用户可管理的站点ID)
- `stationCount`: 实际可管理站点数量
- `permissions`: 包含站点管理权限

**EDC系统响应特点**:
- `scopeType`: "operation"
- `scopeIds`: [1, 2, 3] (用户可操作的站点ID)
- `stationCount`: 实际可操作站点数量
- `permissions`: 包含基础操作权限

#### 错误响应

**认证失败** (HTTP 401):
```json
{
  "code": 1001,
  "message": "Invalid username or password",
  "data": null,
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

**系统访问权限不足** (HTTP 403):
```json
{
  "code": 4002,
  "message": "Insufficient permissions for HOS system. Only manager and assistant manager roles can access HOS. Please contact administrator.",
  "data": null,
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

**无站点关联** (HTTP 403):
```json
{
  "code": 4001,
  "message": "User has no station associations",
  "data": null,
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

**未知系统** (HTTP 400):
```json
{
  "code": 4003,
  "message": "Unknown system type: INVALID_SYSTEM",
  "data": null,
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

### 2. 获取当前用户信息

#### 接口信息
- **URL**: `/auth/me`
- **方法**: `GET`
- **描述**: 获取当前登录用户的详细信息
- **认证**: 需要Bearer Token

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应格式

**成功响应** (HTTP 200):
```json
{
  "code": 200,
  "message": "User information retrieved successfully",
  "data": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "fullName": "string",
    "phone": "string",
    "status": "string",
    "roles": ["string"],
    "permissions": ["string"],
    "lastLoginAt": "2025-01-20T10:00:00Z",
    "language": "en",
    "sites": [],
    "stations": [],
    "systemAccess": {
      "system": "string",
      "accessLevel": "string",
      "scopeType": "string",
      "scopeIds": [],
      "stationIds": [],
      "stationCount": 0,
      "permissions": []
    }
  },
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

### 3. 刷新访问令牌

#### 接口信息
- **URL**: `/auth/refresh`
- **方法**: `POST`
- **描述**: 使用刷新令牌获取新的访问令牌

#### 请求参数
```json
{
  "refreshToken": "string"  // 刷新令牌 (必填)
}
```

#### 响应格式

**成功响应** (HTTP 200):
```json
{
  "code": 200,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "string",
    "refreshToken": "string",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

### 4. 用户登出

#### 接口信息
- **URL**: `/auth/logout`
- **方法**: `POST`
- **描述**: 用户登出，使当前会话失效
- **认证**: 需要Bearer Token

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应格式

**成功响应** (HTTP 200):
```json
{
  "code": 200,
  "message": "Logout successful",
  "data": null,
  "timestamp": "2025-01-20T10:00:00Z",
  "requestId": "uuid"
}
```

## JWT Token结构

### Access Token Claims

```json
{
  "user_id": "string",       // 用户ID
  "username": "string",      // 用户名
  "email": "string",         // 邮箱
  "roles": ["string"],       // 角色列表
  "session_id": "string",    // 会话ID
  "token_type": "access",    // 令牌类型
  "system": "string",        // 系统代码 (HOS|BOS|EDC)
  "access_level": "string",  // 访问级别 (admin|manager|supervisor|operator)
  "scope_type": "string",    // 权限范围类型 (global|station|operation)
  "scope_ids": [1, 2, 3],    // 权限范围ID列表
  "station_ids": [1, 2, 3],  // 站点ID列表（向后兼容）
  "permissions": ["string"], // 权限列表
  "iss": "bos-core",        // 签发者
  "sub": "string",          // 主题（用户ID）
  "aud": ["bos-core-auth"], // 受众
  "exp": 1642694400,        // 过期时间
  "nbf": 1642690800,        // 生效时间
  "iat": 1642690800,        // 签发时间
  "jti": "string"           // JWT ID
}
```

## 错误码说明

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 200 | 200 | 成功 |
| 1001 | 401 | 用户名或密码错误 |
| 4001 | 403 | 用户无站点关联 |
| 4002 | 403 | 系统访问权限不足 |
| 4003 | 400 | 未知系统类型 |
| 4004 | 403 | 其他系统访问错误 |
| 5001 | 500 | 内部服务器错误 |

## 使用示例

### JavaScript/TypeScript示例

```typescript
// 登录函数
async function login(username: string, password: string, system: 'HOS' | 'BOS' | 'EDC') {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      username,
      password,
      system,
      authType: 'local'
    })
  });

  if (!response.ok) {
    throw new Error('Login failed');
  }

  const result = await response.json();
  
  // 存储访问令牌
  localStorage.setItem('access_token', result.data.accessToken);
  localStorage.setItem('refresh_token', result.data.refreshToken);
  
  return result.data;
}

// 使用访问令牌调用API
async function callAPI(url: string, options: RequestInit = {}) {
  const token = localStorage.getItem('access_token');
  
  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    }
  });
}

// 获取当前用户信息
async function getCurrentUser() {
  const response = await callAPI('/api/v1/auth/me');
  return response.json();
}
```

### cURL示例

```bash
# HOS系统登录
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123",
    "system": "HOS"
  }'

# 使用访问令牌获取用户信息
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "Authorization: Bearer <access_token>"

# 刷新令牌
curl -X POST http://localhost:8080/api/v1/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "<refresh_token>"
  }'

# 登出
curl -X POST http://localhost:8080/api/v1/auth/logout \
  -H "Authorization: Bearer <access_token>"
```

## 注意事项

1. **系统兼容性**: 现有的BOS和EDC系统登录功能完全兼容，不会受到影响
2. **权限验证**: HOS系统使用全局权限模型，BOS和EDC系统继续使用站点权限模型
3. **令牌安全**: 访问令牌应安全存储，避免在URL或日志中暴露
4. **会话管理**: 系统支持会话管理，可以终止特定会话或所有会话
5. **权限升级**: 用户可以同时拥有多个系统的访问权限，但每次登录只能访问一个系统

## 更新日志

### v1.0.0 (2025-01-20)
- 新增HOS系统支持
- 扩展JWT token结构
- 实现统一身份认证
- 支持全局权限模型
- 保持BOS/EDC系统向后兼容
