# SSO和系统集成 API

## SSO认证流程

### 发起SSO认证

**POST** `/api/v1/sso/initiate`

发起SSO单点登录认证流程。

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "source_system": "BOS",
  "target_system": "EDC",
  "client_id": "bos-client",
  "redirect_uri": "https://edc.company.com/auth/callback",
  "scopes": ["read", "write"],
  "device_info": {
    "device_type": "mobile",
    "device_id": "device-123",
    "app_version": "1.0.0"
  }
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "SSO认证发起成功",
  "data": {
    "authorization_url": "https://sso.company.com/auth?code=abc123&state=xyz789",
    "state": "xyz789",
    "code_challenge": "E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM",
    "expires_in": 600
  }
}
```

### 验证SSO认证

**POST** `/api/v1/sso/validate`

验证SSO认证授权码并获取访问令牌。

#### 请求参数

```json
{
  "authorization_code": "abc123",
  "state": "xyz789",
  "client_id": "bos-client",
  "code_verifier": "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "SSO认证验证成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "id_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "scope": "read write",
    "user_info": {
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "系统管理员"
    }
  }
}
```

### 刷新SSO令牌

**POST** `/api/v1/sso/refresh`

刷新SSO访问令牌。

#### 请求参数

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "client_id": "bos-client",
  "scopes": ["read", "write"]
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "SSO令牌刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "scope": "read write"
  }
}
```

### 撤销SSO令牌

**POST** `/api/v1/sso/revoke`

撤销SSO令牌。

#### 请求参数

```json
{
  "token_hash": "a1b2c3d4e5f6..."
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "SSO令牌撤销成功"
}
```

## 令牌管理

### 创建SSO令牌

**POST** `/api/v1/sso/tokens`

创建新的SSO令牌。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "token_type": "access_token",
  "source_system": "BOS",
  "target_systems": ["EDC", "HOS"],
  "scopes": ["read", "write"],
  "expires_in": 3600,
  "client_id": "bos-client",
  "session_id": "session-123",
  "device_info": {
    "device_type": "web",
    "browser": "Chrome"
  }
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "SSO令牌创建成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440050",
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "token_type": "access_token",
    "token_value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_hash": "a1b2c3d4e5f6...",
    "source_system": "BOS",
    "target_systems": ["EDC", "HOS"],
    "scopes": ["read", "write"],
    "issued_at": "2024-01-19T10:00:00Z",
    "expires_at": "2024-01-19T11:00:00Z",
    "status": "active"
  }
}
```

### 获取SSO令牌

**GET** `/api/v1/sso/tokens/{tokenHash}`

获取SSO令牌信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取SSO令牌成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440050",
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "token_type": "access_token",
    "source_system": "BOS",
    "target_systems": ["EDC", "HOS"],
    "scopes": ["read", "write"],
    "issued_at": "2024-01-19T10:00:00Z",
    "expires_at": "2024-01-19T11:00:00Z",
    "used_at": "2024-01-19T10:30:00Z",
    "status": "active",
    "user": {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "admin",
      "full_name": "系统管理员"
    }
  }
}
```

### 验证令牌

**POST** `/api/v1/sso/tokens/{tokenHash}/validate`

验证SSO令牌的有效性。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| targetSystem | string | 目标系统 | "EDC" |

#### 响应示例

```json
{
  "code": 0,
  "message": "令牌验证成功",
  "data": {
    "is_valid": true,
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "token_type": "access_token",
    "source_system": "BOS",
    "scopes": ["read", "write"],
    "expires_at": "2024-01-19T11:00:00Z",
    "user_info": {
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "系统管理员"
    },
    "permissions": [
      "station:read:organization",
      "user:read:department"
    ]
  }
}
```

### 撤销用户令牌

**DELETE** `/api/v1/sso/tokens/users/{userId}`

撤销指定用户的所有SSO令牌。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| sourceSystem | string | 源系统过滤 | "BOS" |

#### 响应示例

```json
{
  "code": 0,
  "message": "用户令牌撤销成功"
}
```

### 清理过期令牌

**DELETE** `/api/v1/sso/tokens/cleanup`

清理所有过期的SSO令牌。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "过期令牌清理成功",
  "data": {
    "cleaned_count": 150
  }
}

## 系统集成管理

### 注册系统

**POST** `/api/v1/sso/systems`

注册新的系统集成。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "system_code": "CRM",
  "system_name": "客户关系管理系统",
  "system_type": "web",
  "integration_type": "SSO",
  "base_url": "https://crm.company.com",
  "api_endpoint": "https://crm.company.com/api",
  "auth_endpoint": "https://crm.company.com/auth",
  "callback_url": "https://crm.company.com/auth/callback",
  "config_data": {
    "features": ["customer_management", "sales_tracking"],
    "ui_theme": "modern",
    "max_sessions": 100
  },
  "auth_config": {
    "auth_method": "jwt",
    "token_expiry": 3600,
    "refresh_enabled": true,
    "encryption_key": "secret-key"
  },
  "permission_mapping": {
    "user:read": "crm:user:view",
    "customer:write": "crm:customer:edit"
  },
  "version": "2.1.0",
  "vendor": "Company Tech",
  "description": "企业客户关系管理系统",
  "is_sso_enabled": true,
  "is_auto_provision": false,
  "sync_frequency": "daily"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "系统注册成功",
  "data": {
    "id": 4,
    "system_code": "CRM",
    "system_name": "客户关系管理系统",
    "system_type": "web",
    "integration_type": "SSO",
    "base_url": "https://crm.company.com",
    "config_data": { /* 配置数据 */ },
    "auth_config": { /* 认证配置 */ },
    "permission_mapping": { /* 权限映射 */ },
    "version": "2.1.0",
    "vendor": "Company Tech",
    "description": "企业客户关系管理系统",
    "status": "active",
    "is_sso_enabled": true,
    "is_auto_provision": false,
    "sync_frequency": "daily",
    "created_at": "2024-01-19T10:00:00Z",
    "created_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 系统集成列表

**GET** `/api/v1/sso/systems`

获取系统集成列表。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| system_type | string | 系统类型过滤 | "web" |
| integration_type | string | 集成类型过滤 | "SSO" |
| status | string | 状态过滤 | "active" |
| is_sso_enabled | bool | SSO启用过滤 | true |
| keyword | string | 关键词搜索 | "CRM" |
| offset | int | 偏移量 | 0 |
| limit | int | 限制数量 | 20 |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取系统集成列表成功",
  "data": [
    {
      "id": 1,
      "system_code": "BOS",
      "system_name": "Business Operations System",
      "system_type": "web",
      "integration_type": "SSO",
      "status": "active",
      "is_sso_enabled": true,
      "base_url": "https://bos.company.com",
      "user_count": 50,
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "system_code": "EDC",
      "system_name": "Electronic Data Capture",
      "system_type": "mobile",
      "integration_type": "SSO",
      "status": "active",
      "is_sso_enabled": true,
      "base_url": null,
      "user_count": 25,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取系统集成详情

**GET** `/api/v1/sso/systems/{systemCode}`

获取指定系统的集成详情。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取系统集成详情成功",
  "data": {
    "id": 4,
    "system_code": "CRM",
    "system_name": "客户关系管理系统",
    "system_type": "web",
    "integration_type": "SSO",
    "base_url": "https://crm.company.com",
    "api_endpoint": "https://crm.company.com/api",
    "auth_endpoint": "https://crm.company.com/auth",
    "callback_url": "https://crm.company.com/auth/callback",
    "config_data": {
      "features": ["customer_management", "sales_tracking"],
      "ui_theme": "modern",
      "max_sessions": 100
    },
    "auth_config": {
      "auth_method": "jwt",
      "token_expiry": 3600,
      "refresh_enabled": true
    },
    "permission_mapping": {
      "user:read": "crm:user:view",
      "customer:write": "crm:customer:edit"
    },
    "version": "2.1.0",
    "vendor": "Company Tech",
    "description": "企业客户关系管理系统",
    "status": "active",
    "is_sso_enabled": true,
    "is_auto_provision": false,
    "sync_frequency": "daily",
    "user_profiles_count": 15,
    "last_sync_at": "2024-01-19T09:00:00Z",
    "created_at": "2024-01-19T10:00:00Z"
  }
}
```

### 更新系统集成

**PUT** `/api/v1/sso/systems/{systemCode}`

更新系统集成配置。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "system_name": "客户关系管理系统（更新）",
  "base_url": "https://crm-new.company.com",
  "config_data": {
    "features": ["customer_management", "sales_tracking", "analytics"],
    "ui_theme": "modern",
    "max_sessions": 200
  },
  "version": "2.2.0",
  "description": "企业客户关系管理系统 - 已升级",
  "status": "active",
  "is_sso_enabled": true,
  "sync_frequency": "hourly"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "系统集成更新成功",
  "data": {
    "id": 4,
    "system_code": "CRM",
    "system_name": "客户关系管理系统（更新）",
    "base_url": "https://crm-new.company.com",
    "version": "2.2.0",
    "description": "企业客户关系管理系统 - 已升级",
    "sync_frequency": "hourly",
    "updated_at": "2024-01-19T11:00:00Z",
    "updated_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```
```
