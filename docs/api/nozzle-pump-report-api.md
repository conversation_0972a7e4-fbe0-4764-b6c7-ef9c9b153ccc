# 油枪泵码报表 API 文档

## 概述

油枪泵码报表API用于获取指定站点和日期的油枪泵码读数报表，主要用于检测油枪异常。通过比较机械读数差异与实际销售体积的差异来判断油枪状态。

## API 端点

### 获取油枪泵码报表

**请求方式**: `GET`  
**请求路径**: `/api/v1/reports/nozzle-pump-readings`  
**认证**: 需要Bearer Token认证

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| site_id | integer | 是 | 站点ID | 1 |
| report_date | string | 是 | 报表日期，格式: YYYY-MM-DD | 2024-01-04 |
| shift_id | integer | 否 | 班次ID，不提供则显示全天数据 | 1 |

#### 请求示例

```bash
GET /api/v1/reports/nozzle-pump-readings?site_id=1&report_date=2024-01-04&shift_id=1
Authorization: Bearer your_token_here
```

#### 响应格式

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "report_header": {
      "site_id": 1,
      "site_name": "Meruya Ilir Station",
      "report_date": "2024-01-04",
      "report_time": "2024-01-04T10:30:00Z",
      "shift_id": 1,
      "shift_name": "Shift 1 (00:00-08:00)"
    },
    "nozzle_readings": [
      {
        "nozzle_id": "N001",
        "pump_id": "P001",
        "fuel_type": "Gasoline",
        "fuel_grade": "92",
        "fuel_name": "BP 92",
        "opening_reading": 10000.500,
        "closing_reading": 10850.750,
        "meter_difference": 850.250,
        "sales_volume": 850.250,
        "sales_amount": 12679500,
        "variance": 0.000,
        "variance_percentage": 0.000,
        "status": "normal",
        "last_updated": "2024-01-04T08:00:00Z"
      },
      {
        "nozzle_id": "N002",
        "pump_id": "P001",
        "fuel_type": "Gasoline",
        "fuel_grade": "95",
        "fuel_name": "BP Ultimate",
        "opening_reading": 8500.250,
        "closing_reading": 9200.800,
        "meter_difference": 700.550,
        "sales_volume": 680.500,
        "sales_amount": 11288000,
        "variance": 20.050,
        "variance_percentage": 2.860,
        "status": "abnormal",
        "last_updated": "2024-01-04T08:00:00Z"
      }
    ],
    "summary": {
      "total_nozzles": 2,
      "normal_count": 1,
      "abnormal_count": 1,
      "total_variance": 20.050,
      "total_sales_volume": 1530.750,
      "total_sales_amount": 23967500
    }
  }
}
```

#### 数据字段说明

##### report_header (报表头信息)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| site_id | integer | 站点ID |
| site_name | string | 站点名称 |
| report_date | string | 报表日期 |
| report_time | string | 报表生成时间 (ISO 8601格式) |
| shift_id | integer | 班次ID (可选) |
| shift_name | string | 班次名称 (可选) |

##### nozzle_readings (油枪读数列表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| nozzle_id | string | 油枪ID |
| pump_id | string | 油泵ID |
| fuel_type | string | 燃油类型 (如: Gasoline, Diesel) |
| fuel_grade | string | 燃油标号 (如: 92, 95, Diesel) |
| fuel_name | string | 燃油名称 (如: BP 92, BP Ultimate) |
| opening_reading | number | 开始读数 (升) |
| closing_reading | number | 结束读数 (升) |
| meter_difference | number | 机械读数差异 (升) |
| sales_volume | number | 实际销售体积 (升) |
| sales_amount | number | 销售金额 (印尼盾) |
| variance | number | 差异量 (升) |
| variance_percentage | number | 差异百分比 |
| status | string | 状态: "normal" 或 "abnormal" |
| last_updated | string | 最后更新时间 (ISO 8601格式) |

##### summary (汇总信息)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_nozzles | integer | 油枪总数 |
| normal_count | integer | 正常油枪数量 |
| abnormal_count | integer | 异常油枪数量 |
| total_variance | number | 总差异量 (升) |
| total_sales_volume | number | 总销售体积 (升) |
| total_sales_amount | number | 总销售金额 (印尼盾) |

#### 状态判断逻辑

- **normal**: variance <= 0 (机械读数差异小于等于实际销售体积)
- **abnormal**: variance > 0 (机械读数差异大于实际销售体积，可能存在异常)

#### 错误响应

**400 Bad Request** - 参数错误:

```json
{
  "code": "MISSING_SITE_ID",
  "message": "站点ID是必填参数",
  "detail": "site_id parameter is required"
}
```

**400 Bad Request** - 日期格式错误:

```json
{
  "code": "INVALID_DATE_FORMAT",
  "message": "无效的日期格式，应为YYYY-MM-DD",
  "detail": "parsing time \"2024-1-4\" as \"2006-01-02\": cannot parse \"1-4\" as \"01\""
}
```

**500 Internal Server Error** - 服务器错误:

```json
{
  "code": "INTERNAL_ERROR",
  "message": "获取油枪泵码报表失败",
  "detail": "database connection failed"
}
```

## 业务逻辑说明

### 数据计算逻辑

1. **meter_difference** = closing_reading - opening_reading
2. **variance** = meter_difference - sales_volume  
3. **variance_percentage** = (variance / meter_difference) × 100
4. **status** = variance > 0 ? "abnormal" : "normal"

### 数据来源

- 基础数据来源于 `fuel_transactions` 表
- 按 `nozzle_id`, `fuel_type`, `fuel_grade` 分组聚合
- 使用 `start_totalizer` 和 `end_totalizer` 计算机械读数
- 使用 `volume` 和 `amount` 字段计算销售数据

### 时区处理

- 所有时间相关的查询和响应都使用雅加达时区 (Asia/Jakarta)
- 日期参数按雅加达时区的日期范围进行查询

## 前端集成

### 移除Mock数据

原有的前端代码在API调用失败时会fallback到mock数据，现已移除此逻辑：

```typescript
// 旧代码 (已移除)
catch (error) {
  // 使用模拟数据进行演示
  const mockData = { ... };
  setReportData(processReportData(mockData));
}

// 新代码
catch (error) {
  // 显示真实错误信息
  toast({
    title: "Error",
    description: `${errorMessage}: ${errorDetail}`,
    variant: "destructive",
  });
  setReportData(null);
}
```

### API调用示例

```typescript
import { getNozzlePumpReport } from "@/api/reports";

const fetchReport = async () => {
  try {
    const response = await getNozzlePumpReport({
      site_id: currentSite.id,
      report_date: selectedDate,
      shift_id: selectedShift !== 'all' ? parseInt(selectedShift) : undefined
    });
    
    if (response.code === 200 && response.data) {
      setReportData(processReportData(response.data));
    }
  } catch (error) {
    // 处理错误
    console.error("Error fetching report:", error);
  }
};
```

## 测试建议

### 单元测试

1. 测试参数验证逻辑
2. 测试数据计算逻辑 (variance, percentage等)
3. 测试状态判断逻辑
4. 测试时区处理

### 集成测试

1. 测试完整的API调用流程
2. 测试不同参数组合的响应
3. 测试错误场景的处理

### 性能测试

1. 测试大量数据的查询性能
2. 测试并发请求的处理能力

## 部署注意事项

1. 确保数据库连接配置正确
2. 确保时区设置为雅加达时区
3. 确保相关数据表存在且有数据
4. 确保API路由正确注册
5. 确保认证中间件正常工作
