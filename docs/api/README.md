# 组织架构和权限管理系统 API 文档

## 概述

本文档描述了组织架构和权限管理系统的完整API接口，包括组织管理、权限控制、SSO认证和系统集成等功能。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API Version**: `v1`
- **Content-Type**: `application/json`
- **Authentication**: Bearer Token

## 通用响应格式

所有API响应都遵循统一的格式：

```json
{
  "code": 0,           // 状态码，0表示成功
  "message": "成功",    // 响应消息
  "data": {},          // 响应数据（可选）
  "error": ""          // 错误信息（可选）
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 未授权访问 |
| 1002 | 参数错误 |
| 1003 | 请求参数错误 |
| 5001 | 服务器内部错误 |
| 5002 | 功能未实现 |

## API 接口分类

### 1. 认证管理 (Authentication)
- [基础认证](#基础认证)
- [组织认证](#组织认证)
- [SSO认证](#sso认证)

### 2. 组织管理 (Organization)
- [组织CRUD](#组织crud)
- [组织层级](#组织层级)
- [用户组织关联](#用户组织关联)
- [组织类型管理](#组织类型管理)

### 3. 权限管理 (Permission)
- [权限评估](#权限评估)
- [数据权限](#数据权限)
- [权限模板](#权限模板)
- [权限验证](#权限验证)

### 4. SSO和系统集成 (SSO)
- [SSO认证流程](#sso认证流程)
- [系统集成管理](#系统集成管理)
- [用户系统配置](#用户系统配置)
- [权限映射](#权限映射)

## 认证说明

大部分API需要在请求头中包含认证令牌：

```
Authorization: Bearer <access_token>
```

## 分页参数

支持分页的接口使用以下参数：

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| offset | int | 偏移量 | 0 |
| limit | int | 限制数量 | 20 |
| sortBy | string | 排序字段 | created_at |
| sortOrder | string | 排序方向 (asc/desc) | desc |

## 过滤参数

支持过滤的接口使用以下通用参数：

| 参数 | 类型 | 说明 |
|------|------|------|
| keyword | string | 关键词搜索 |
| status | string | 状态过滤 |
| startDate | string | 开始日期 (ISO 8601) |
| endDate | string | 结束日期 (ISO 8601) |

---

## 详细接口文档

请参考以下具体的API文档：

1. [认证管理 API](./auth.md)
2. [组织管理 API](./organization.md)
3. [权限管理 API](./permission.md)
4. [SSO和系统集成 API](./sso.md)

## 错误处理

### 常见错误响应

#### 401 未授权
```json
{
  "code": 1001,
  "message": "未授权访问",
  "error": "Invalid or expired token"
}
```

#### 400 请求参数错误
```json
{
  "code": 1003,
  "message": "请求参数错误",
  "error": "Field 'username' is required"
}
```

#### 500 服务器错误
```json
{
  "code": 5001,
  "message": "服务器内部错误",
  "error": "Database connection failed"
}
```

## 测试环境

### 测试账号
- **用户名**: admin
- **密码**: Admin123
- **系统**: BOS

### 测试数据
系统已预置以下测试数据：
- 3个组织类型（总部、分公司、部门）
- 基础权限配置
- 系统集成配置（BOS、EDC、HOS）

## SDK和工具

### cURL 示例
```bash
# 登录获取令牌
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123",
    "system": "BOS"
  }'

# 使用令牌访问API
curl -X GET http://localhost:8080/api/v1/organizations \
  -H "Authorization: Bearer <access_token>"
```

### Postman Collection
我们提供了完整的Postman集合，包含所有API接口的示例请求。

## 版本历史

| 版本 | 日期 | 变更说明 |
|------|------|----------|
| v1.0 | 2024-01-19 | 初始版本，包含完整的组织架构和权限管理功能 |

## 联系方式

如有问题或建议，请联系开发团队。
