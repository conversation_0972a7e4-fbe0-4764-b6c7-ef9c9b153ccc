# 组织管理 API

## 组织CRUD操作

### 创建组织

**POST** `/api/v1/organizations`

创建新的组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "org_name": "北京分公司",
  "org_code": "BJ_BRANCH",
  "org_type_id": 2,
  "parent_id": 1,
  "description": "北京地区分公司",
  "address": {
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "street": "建国路88号",
    "postal_code": "100020"
  },
  "contact_info": {
    "phone": "010-12345678",
    "email": "<EMAIL>",
    "fax": "010-12345679"
  },
  "business_scope": ["销售", "服务"],
  "status": "active"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织创建成功",
  "data": {
    "id": 10,
    "org_name": "北京分公司",
    "org_code": "BJ_BRANCH",
    "org_type_id": 2,
    "parent_id": 1,
    "level": 2,
    "path": "1.10",
    "description": "北京地区分公司",
    "address": { /* 地址信息 */ },
    "contact_info": { /* 联系信息 */ },
    "business_scope": ["销售", "服务"],
    "status": "active",
    "created_at": "2024-01-19T10:00:00Z",
    "updated_at": "2024-01-19T10:00:00Z",
    "created_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 获取组织详情

**GET** `/api/v1/organizations/{orgId}`

获取指定组织的详细信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取组织详情成功",
  "data": {
    "id": 10,
    "org_name": "北京分公司",
    "org_code": "BJ_BRANCH",
    "org_type": {
      "id": 2,
      "type_name": "分公司",
      "type_code": "BRANCH"
    },
    "parent_organization": {
      "id": 1,
      "org_name": "总部",
      "org_code": "HQ"
    },
    "level": 2,
    "path": "1.10",
    "description": "北京地区分公司",
    "address": { /* 地址信息 */ },
    "contact_info": { /* 联系信息 */ },
    "business_scope": ["销售", "服务"],
    "status": "active",
    "user_count": 25,
    "child_count": 3,
    "created_at": "2024-01-19T10:00:00Z",
    "updated_at": "2024-01-19T10:00:00Z"
  }
}
```

### 更新组织

**PUT** `/api/v1/organizations/{orgId}`

更新组织信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "org_name": "北京分公司（更新）",
  "description": "北京地区分公司 - 已更新",
  "contact_info": {
    "phone": "010-87654321",
    "email": "<EMAIL>"
  },
  "status": "active"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织更新成功",
  "data": {
    "id": 10,
    "org_name": "北京分公司（更新）",
    "description": "北京地区分公司 - 已更新",
    /* 其他字段 */
    "updated_at": "2024-01-19T11:00:00Z",
    "updated_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 删除组织

**DELETE** `/api/v1/organizations/{orgId}`

删除指定组织（软删除）。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织删除成功"
}
```

### 组织列表

**GET** `/api/v1/organizations`

获取组织列表，支持分页和过滤。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| offset | int | 偏移量 | 0 |
| limit | int | 限制数量 | 20 |
| keyword | string | 关键词搜索 | "北京" |
| org_type_id | int | 组织类型ID | 2 |
| parent_id | int | 父组织ID | 1 |
| status | string | 状态过滤 | "active" |
| level | int | 层级过滤 | 2 |
| sortBy | string | 排序字段 | "org_name" |
| sortOrder | string | 排序方向 | "asc" |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取组织列表成功",
  "data": {
    "organizations": [
      {
        "id": 1,
        "org_name": "总部",
        "org_code": "HQ",
        "org_type": { /* 组织类型信息 */ },
        "level": 1,
        "status": "active",
        "user_count": 50,
        "child_count": 5,
        "created_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": 10,
        "org_name": "北京分公司",
        "org_code": "BJ_BRANCH",
        "org_type": { /* 组织类型信息 */ },
        "parent_id": 1,
        "level": 2,
        "status": "active",
        "user_count": 25,
        "child_count": 3,
        "created_at": "2024-01-19T10:00:00Z"
      }
    ],
    "total": 15,
    "offset": 0,
    "limit": 20
  }
}
```

## 组织层级结构

### 获取组织层级结构

**GET** `/api/v1/organizations/hierarchy`

获取完整的组织层级结构树。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| root_id | int | 根组织ID | 1 |
| max_depth | int | 最大深度 | 5 |
| include_inactive | bool | 包含非活跃组织 | false |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取组织层级结构成功",
  "data": {
    "id": 1,
    "org_name": "总部",
    "org_code": "HQ",
    "level": 1,
    "status": "active",
    "user_count": 50,
    "children": [
      {
        "id": 10,
        "org_name": "北京分公司",
        "org_code": "BJ_BRANCH",
        "level": 2,
        "status": "active",
        "user_count": 25,
        "children": [
          {
            "id": 20,
            "org_name": "北京销售部",
            "org_code": "BJ_SALES",
            "level": 3,
            "status": "active",
            "user_count": 10,
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 获取子组织

**GET** `/api/v1/organizations/{orgId}/children`

获取指定组织的直接子组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取子组织成功",
  "data": [
    {
      "id": 20,
      "org_name": "北京销售部",
      "org_code": "BJ_SALES",
      "parent_id": 10,
      "level": 3,
      "status": "active",
      "user_count": 10,
      "child_count": 0
    },
    {
      "id": 21,
      "org_name": "北京技术部",
      "org_code": "BJ_TECH",
      "parent_id": 10,
      "level": 3,
      "status": "active",
      "user_count": 15,
      "child_count": 2
    }
  ]
}
```

### 获取所有后代组织

**GET** `/api/v1/organizations/{orgId}/descendants`

获取指定组织的所有后代组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| max_depth | int | 最大深度 | 3 |
| include_self | bool | 包含自身 | false |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取后代组织成功",
  "data": [
    {
      "id": 20,
      "org_name": "北京销售部",
      "level": 3,
      "path": "1.10.20"
    },
    {
      "id": 21,
      "org_name": "北京技术部",
      "level": 3,
      "path": "1.10.21"
    },
    {
      "id": 30,
      "org_name": "前端开发组",
      "level": 4,
      "path": "1.10.21.30"
    }
  ]
}
```

### 获取祖先组织

**GET** `/api/v1/organizations/{orgId}/ancestors`

获取指定组织的所有祖先组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取祖先组织成功",
  "data": [
    {
      "id": 1,
      "org_name": "总部",
      "level": 1,
      "path": "1"
    },
    {
      "id": 10,
      "org_name": "北京分公司",
      "level": 2,
      "path": "1.10"
    }
  ]
}
```

### 移动组织

**PUT** `/api/v1/organizations/{orgId}/move`

将组织移动到新的父组织下。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "new_parent_id": 5,
  "reason": "组织架构调整"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织移动成功",
  "data": {
    "id": 20,
    "org_name": "北京销售部",
    "old_parent_id": 10,
    "new_parent_id": 5,
    "old_path": "1.10.20",
    "new_path": "1.5.20",
    "old_level": 3,
    "new_level": 3,
    "updated_at": "2024-01-19T12:00:00Z"
  }
}

## 用户组织关联

### 分配用户到组织

**POST** `/api/v1/organizations/users/assign`

将用户分配到指定组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "organization_id": 10,
  "role_in_org": "manager",
  "is_primary": false,
  "effective_date": "2024-01-19T00:00:00Z",
  "expiry_date": "2024-12-31T23:59:59Z",
  "notes": "调任北京分公司经理"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "用户组织分配成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440010",
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "organization_id": 10,
    "role_in_org": "manager",
    "is_primary": false,
    "status": "active",
    "effective_date": "2024-01-19T00:00:00Z",
    "expiry_date": "2024-12-31T23:59:59Z",
    "notes": "调任北京分公司经理",
    "created_at": "2024-01-19T10:00:00Z",
    "created_by": "550e8400-e29b-41d4-a716-446655440001"
  }
}
```

### 移除用户组织关联

**DELETE** `/api/v1/organizations/users/{userId}/organizations/{orgId}`

移除用户与组织的关联关系。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "用户组织关联移除成功"
}
```

### 设置用户主要组织

**PUT** `/api/v1/organizations/users/{userId}/primary-organization`

设置用户的主要组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "organization_id": 10
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "主要组织设置成功",
  "data": {
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "old_primary_org_id": 1,
    "new_primary_org_id": 10,
    "updated_at": "2024-01-19T10:00:00Z"
  }
}
```

### 获取用户组织关联

**GET** `/api/v1/organizations/users/{userId}/organizations`

获取用户的所有组织关联。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| status | string | 状态过滤 | "active" |
| include_expired | bool | 包含过期关联 | false |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取用户组织关联成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440010",
      "organization": {
        "id": 10,
        "org_name": "北京分公司",
        "org_code": "BJ_BRANCH",
        "org_type": { /* 组织类型信息 */ }
      },
      "role_in_org": "manager",
      "is_primary": true,
      "status": "active",
      "effective_date": "2024-01-19T00:00:00Z",
      "expiry_date": "2024-12-31T23:59:59Z",
      "created_at": "2024-01-19T10:00:00Z"
    }
  ]
}
```

### 获取组织用户列表

**GET** `/api/v1/organizations/{orgId}/users`

获取指定组织的用户列表。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| offset | int | 偏移量 | 0 |
| limit | int | 限制数量 | 20 |
| role_in_org | string | 角色过滤 | "manager" |
| status | string | 状态过滤 | "active" |
| keyword | string | 关键词搜索 | "张三" |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取组织用户列表成功",
  "data": {
    "users": [
      {
        "user": {
          "id": "550e8400-e29b-41d4-a716-446655440000",
          "username": "zhangsan",
          "full_name": "张三",
          "email": "<EMAIL>",
          "status": "active"
        },
        "role_in_org": "manager",
        "is_primary": true,
        "status": "active",
        "effective_date": "2024-01-19T00:00:00Z",
        "joined_at": "2024-01-19T10:00:00Z"
      }
    ],
    "total": 25,
    "offset": 0,
    "limit": 20
  }
}
```

### 批量分配用户

**POST** `/api/v1/organizations/users/batch-assign`

批量将用户分配到组织。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "organization_id": 10,
  "assignments": [
    {
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "role_in_org": "manager",
      "is_primary": false
    },
    {
      "user_id": "550e8400-e29b-41d4-a716-446655440001",
      "role_in_org": "operator",
      "is_primary": false
    }
  ],
  "effective_date": "2024-01-19T00:00:00Z",
  "notes": "批量调整组织架构"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "批量用户分配成功",
  "data": {
    "organization_id": 10,
    "total_assignments": 2,
    "successful_assignments": 2,
    "failed_assignments": 0,
    "results": [
      {
        "user_id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "success",
        "assignment_id": "550e8400-e29b-41d4-a716-446655440010"
      },
      {
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "status": "success",
        "assignment_id": "550e8400-e29b-41d4-a716-446655440011"
      }
    ]
  }
}
```

### 批量移除用户

**POST** `/api/v1/organizations/users/batch-remove`

批量移除用户的组织关联。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "organization_id": 10,
  "user_ids": [
    "550e8400-e29b-41d4-a716-446655440000",
    "550e8400-e29b-41d4-a716-446655440001"
  ],
  "reason": "组织架构调整"
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "批量用户移除成功",
  "data": {
    "organization_id": 10,
    "total_removals": 2,
    "successful_removals": 2,
    "failed_removals": 0,
    "results": [
      {
        "user_id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "success"
      },
      {
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "status": "success"
      }
    ]
  }
}
```

## 组织统计

### 获取组织统计信息

**GET** `/api/v1/organizations/statistics`

获取组织的统计信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| org_id | int | 组织ID | 10 |
| include_children | bool | 包含子组织 | true |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取组织统计信息成功",
  "data": {
    "organization_id": 10,
    "organization_name": "北京分公司",
    "total_users": 25,
    "active_users": 23,
    "inactive_users": 2,
    "total_child_organizations": 3,
    "active_child_organizations": 3,
    "total_descendants": 8,
    "user_by_role": {
      "manager": 2,
      "operator": 20,
      "viewer": 3
    },
    "created_at": "2024-01-19T10:00:00Z",
    "last_updated": "2024-01-19T12:00:00Z"
  }
}
```

### 获取用户组织统计

**GET** `/api/v1/organizations/users/{userId}/statistics`

获取用户的组织统计信息。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 响应示例

```json
{
  "code": 0,
  "message": "获取用户组织统计成功",
  "data": {
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "total_organizations": 3,
    "active_organizations": 2,
    "primary_organization": {
      "id": 10,
      "org_name": "北京分公司",
      "role_in_org": "manager"
    },
    "organizations_by_role": {
      "manager": 1,
      "operator": 1,
      "viewer": 1
    },
    "organizations_by_type": {
      "headquarters": 1,
      "branch": 1,
      "department": 1
    }
  }
}
```

## 组织类型管理

### 创建组织类型

**POST** `/api/v1/organization-types`

创建新的组织类型。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 请求参数

```json
{
  "type_name": "区域中心",
  "type_code": "REGIONAL_CENTER",
  "description": "区域管理中心",
  "level_range": {
    "min_level": 2,
    "max_level": 3
  },
  "allowed_parent_types": ["headquarters", "branch"],
  "allowed_child_types": ["department", "team"],
  "default_permissions": ["org:read", "user:read"],
  "is_system": false,
  "sort_order": 3
}
```

#### 响应示例

```json
{
  "code": 0,
  "message": "组织类型创建成功",
  "data": {
    "id": 4,
    "type_name": "区域中心",
    "type_code": "REGIONAL_CENTER",
    "description": "区域管理中心",
    "level_range": {
      "min_level": 2,
      "max_level": 3
    },
    "allowed_parent_types": ["headquarters", "branch"],
    "allowed_child_types": ["department", "team"],
    "default_permissions": ["org:read", "user:read"],
    "is_system": false,
    "sort_order": 3,
    "status": "active",
    "created_at": "2024-01-19T10:00:00Z",
    "created_by": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### 获取组织类型列表

**GET** `/api/v1/organization-types`

获取所有组织类型。

#### 请求头
```
Authorization: Bearer <access_token>
```

#### 查询参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| status | string | 状态过滤 | "active" |
| is_system | bool | 系统类型过滤 | false |

#### 响应示例

```json
{
  "code": 0,
  "message": "获取组织类型列表成功",
  "data": [
    {
      "id": 1,
      "type_name": "总部",
      "type_code": "headquarters",
      "description": "公司总部",
      "is_system": true,
      "sort_order": 1,
      "status": "active",
      "organization_count": 1
    },
    {
      "id": 2,
      "type_name": "分公司",
      "type_code": "branch",
      "description": "地区分公司",
      "is_system": true,
      "sort_order": 2,
      "status": "active",
      "organization_count": 5
    }
  ]
}
```
```
