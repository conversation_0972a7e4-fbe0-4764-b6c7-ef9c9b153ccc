# 燃油交易泵码数更新接口

## 概述
本接口用于更新燃油交易记录中的起始泵码数(start pump reading)和截止泵码数(end pump reading)。支持部分更新，可以只更新其中一个字段。

## 接口信息
- **URL**: `/api/v1/fuel-transactions/{id}/pump-readings`
- **方法**: `PATCH`
- **描述**: 更新指定燃油交易的泵码数信息
- **权限**: 需要认证

## 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `id` | integer | 是 | 燃油交易ID |

## 请求体参数
| 参数名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| `start_totalizer` | float64 | 否 | 起始泵码数 | 必须 >= 0 |
| `end_totalizer` | float64 | 否 | 截止泵码数 | 必须 >= 0，且 >= start_totalizer |

**注意**: 至少需要提供一个字段进行更新。

## 请求示例

### 更新起始泵码数
```bash
curl -X PATCH /api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "start_totalizer": 12345.67
  }'
```

### 更新截止泵码数
```bash
curl -X PATCH /api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "end_totalizer": 12395.67
  }'
```

### 同时更新两个字段
```bash
curl -X PATCH /api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "start_totalizer": 12345.67,
    "end_totalizer": 12395.67
  }'
```

## 响应格式

### 成功响应 (200 OK)
```json
{
  "success": true,
  "message": "Pump readings updated successfully",
  "data": {
    "id": 123,
    "transaction_number": "TXN202401150001",
    "station_id": 1,
    "pump_id": "PUMP01",
    "nozzle_id": "NOZZLE01",
    "fuel_type": "GASOLINE",
    "fuel_grade": "95",
    "unit_price": 7850.0,
    "volume": 50.0,
    "amount": 392500.0,
    "start_totalizer": 12345.67,
    "end_totalizer": 12395.67,
    "totalizer_continuity_status": "normal",
    "status": "pending",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### 错误响应

#### 400 Bad Request - 参数错误
```json
{
  "success": false,
  "error": "validation_error",
  "message": "At least one pump reading field must be provided"
}
```

#### 400 Bad Request - 泵码数验证失败
```json
{
  "success": false,
  "error": "validation_error", 
  "message": "End totalizer cannot be less than start totalizer"
}
```

#### 404 Not Found - 交易不存在
```json
{
  "success": false,
  "error": "not_found",
  "message": "Fuel transaction not found"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "error": "internal_error",
  "message": "Failed to update pump readings: database error"
}
```

## 业务规则

### 1. 状态限制
- 只能修改状态为 `pending`(待处理) 或 `processed`(已处理) 的交易
- 已取消的交易不允许修改泵码数

### 2. 数据验证
- 泵码数必须为非负数
- 如果同时提供起始和截止泵码数，截止泵码数必须大于等于起始泵码数
- 至少需要提供一个字段进行更新

### 3. 自动计算
- 更新泵码数后，系统会自动重新计算 `totalizer_continuity_status`
- 系统会检查与同一油枪上一笔交易的连续性

### 4. 审计日志
- 所有泵码数修改操作都会记录在系统日志中
- 包含操作人员、操作时间、修改前后的值

## 使用场景

1. **设备故障修正**: 当加油设备出现故障导致泵码数记录错误时
2. **数据校正**: 发现历史数据中的泵码数错误需要修正
3. **系统迁移**: 从其他系统迁移数据时需要补充泵码数信息
4. **审计要求**: 根据审计要求调整泵码数以保证数据准确性

## 注意事项

1. **权限控制**: 建议只允许站点管理员或特定角色执行此操作
2. **数据一致性**: 修改泵码数可能影响库存计算和连续性检查
3. **操作记录**: 建议在前端界面显示修改历史记录
4. **批量操作**: 如需批量修改，建议分批处理避免长时间锁定

## 相关接口
- [获取燃油交易详情](./fuel-transactions-get.md)
- [燃油交易列表查询](./fuel-transactions-list.md)
- [燃油交易完整信息查询](./fuel-transactions-full.md)
