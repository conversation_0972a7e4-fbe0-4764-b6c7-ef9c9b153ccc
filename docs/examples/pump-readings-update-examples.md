# 燃油交易泵码数更新接口使用示例

## 概述
本文档提供燃油交易泵码数更新接口的实际使用示例，包括各种场景下的请求和响应。

## 基本使用

### 1. 更新起始泵码数
当需要修正加油开始时的泵码数时：

```bash
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "start_totalizer": 12345.67
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "Pump readings updated successfully",
  "data": {
    "id": 123,
    "transaction_number": "TXN202401150001",
    "start_totalizer": 12345.67,
    "end_totalizer": 12395.67,
    "totalizer_continuity_status": "normal",
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### 2. 更新截止泵码数
当需要修正加油结束时的泵码数时：

```bash
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "end_totalizer": 12395.67
  }'
```

### 3. 同时更新两个字段
当需要同时修正起始和截止泵码数时：

```bash
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "start_totalizer": 12300.00,
    "end_totalizer": 12350.00
  }'
```

## 实际业务场景

### 场景1: 设备故障修正
**背景**: 加油机显示屏故障，导致记录的泵码数不准确

```bash
# 步骤1: 查询当前交易信息
curl -X GET http://localhost:8080/api/v1/fuel-transactions/456

# 步骤2: 根据实际情况修正泵码数
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/456/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "start_totalizer": 15678.90,
    "end_totalizer": 15728.90
  }'
```

### 场景2: 数据迁移补充
**背景**: 从旧系统迁移数据，需要补充缺失的泵码数信息

```bash
# 批量更新多个交易的泵码数
for id in 100 101 102; do
  curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/$id/pump-readings \
    -H "Content-Type: application/json" \
    -d "{
      \"start_totalizer\": $((10000 + id * 50)).00,
      \"end_totalizer\": $((10000 + id * 50 + 45)).00
    }"
done
```

### 场景3: 审计数据修正
**背景**: 审计发现某些交易的泵码数与实际不符，需要修正

```bash
# 修正特定交易的起始泵码数
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/789/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "start_totalizer": 20123.45
  }'
```

## 错误处理示例

### 1. 参数验证错误
```bash
# 请求: 空的请求体
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -d '{}'

# 响应:
{
  "success": false,
  "error": "validation_error",
  "message": "At least one pump reading field must be provided"
}
```

### 2. 泵码数范围错误
```bash
# 请求: 截止泵码数小于起始泵码数
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/123/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "start_totalizer": 12400.00,
    "end_totalizer": 12300.00
  }'

# 响应:
{
  "success": false,
  "error": "validation_error",
  "message": "End totalizer cannot be less than start totalizer"
}
```

### 3. 交易不存在
```bash
# 请求: 不存在的交易ID
curl -X PATCH http://localhost:8080/api/v1/fuel-transactions/999999/pump-readings \
  -H "Content-Type: application/json" \
  -d '{
    "start_totalizer": 12345.67
  }'

# 响应:
{
  "success": false,
  "error": "not_found",
  "message": "Fuel transaction not found"
}
```

## JavaScript/前端集成示例

### 使用 Fetch API
```javascript
async function updatePumpReadings(transactionId, startTotalizer, endTotalizer) {
  try {
    const response = await fetch(`/api/v1/fuel-transactions/${transactionId}/pump-readings`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        start_totalizer: startTotalizer,
        end_totalizer: endTotalizer
      })
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('泵码数更新成功:', result.data);
      return result.data;
    } else {
      console.error('更新失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

// 使用示例
updatePumpReadings(123, 12345.67, 12395.67)
  .then(updatedTransaction => {
    console.log('更新后的交易:', updatedTransaction);
  })
  .catch(error => {
    console.error('更新失败:', error);
  });
```

### 使用 Axios
```javascript
import axios from 'axios';

const updatePumpReadings = async (transactionId, pumpReadings) => {
  try {
    const response = await axios.patch(
      `/api/v1/fuel-transactions/${transactionId}/pump-readings`,
      pumpReadings,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      }
    );
    
    return response.data.data;
  } catch (error) {
    if (error.response) {
      throw new Error(error.response.data.message);
    }
    throw error;
  }
};

// 使用示例
updatePumpReadings(123, {
  start_totalizer: 12345.67,
  end_totalizer: 12395.67
});
```

## 最佳实践

### 1. 数据验证
- 在前端进行基本验证，减少无效请求
- 确保泵码数的合理性（非负数、范围合理）

### 2. 错误处理
- 实现完整的错误处理逻辑
- 为用户提供友好的错误提示

### 3. 操作记录
- 记录所有修改操作的日志
- 提供修改历史查询功能

### 4. 权限控制
- 只允许有权限的用户执行修改操作
- 实现操作审批流程（如需要）

## 测试建议

1. **功能测试**: 使用提供的测试脚本验证基本功能
2. **边界测试**: 测试极值和边界条件
3. **并发测试**: 测试多用户同时修改的情况
4. **性能测试**: 测试大量数据下的响应时间
