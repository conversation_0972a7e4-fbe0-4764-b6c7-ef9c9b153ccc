-- Organization Data Migration Script
-- 组织数据迁移脚本 - 将现有站点数据迁移到新的组织架构中

-- =====================================================
-- 1. Create Default Headquarters Organization
-- 创建默认总部组织
-- =====================================================

-- 获取总部组织类型ID
DO $$
DECLARE
    hq_type_id bigint;
    station_type_id bigint;
    default_hq_id bigint;
BEGIN
    -- 获取组织类型ID
    SELECT id INTO hq_type_id FROM core_schema.organization_types WHERE type_code = 'HQ';
    SELECT id INTO station_type_id FROM core_schema.organization_types WHERE type_code = 'STATION';
    
    -- 创建默认总部组织（如果不存在）
    INSERT INTO core_schema.organizations (
        org_code, 
        org_name, 
        org_type_id, 
        parent_id, 
        sort_order, 
        status,
        created_at,
        updated_at
    ) VALUES (
        'HQ001',
        'Indo BP Headquarters',
        hq_type_id,
        NULL,
        1,
        'active',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (org_code) DO NOTHING;
    
    -- 获取总部组织ID
    SELECT id INTO default_hq_id FROM core_schema.organizations WHERE org_code = 'HQ001';
    
    -- 更新所有现有站点，关联到总部组织
    UPDATE core_schema.stations 
    SET organization_id = default_hq_id
    WHERE organization_id IS NULL;
    
    RAISE NOTICE 'Default headquarters organization created and stations updated';
END $$;

-- =====================================================
-- 2. Create Organizations for Existing Stations
-- 为现有站点创建对应的组织记录
-- =====================================================

DO $$
DECLARE
    station_type_id bigint;
    hq_id bigint;
    station_record RECORD;
BEGIN
    -- 获取站点组织类型ID和总部ID
    SELECT id INTO station_type_id FROM core_schema.organization_types WHERE type_code = 'STATION';
    SELECT id INTO hq_id FROM core_schema.organizations WHERE org_code = 'HQ001';
    
    -- 为每个站点创建对应的组织记录
    FOR station_record IN 
        SELECT id, site_code, site_name, created_at, created_by 
        FROM core_schema.stations 
        WHERE organization_id = hq_id
    LOOP
        -- 为站点创建组织记录
        INSERT INTO core_schema.organizations (
            org_code,
            org_name,
            org_type_id,
            parent_id,
            sort_order,
            status,
            external_system_id,
            created_at,
            updated_at,
            created_by
        ) VALUES (
            'ST_' || station_record.site_code,
            station_record.site_name,
            station_type_id,
            hq_id,
            0,
            'active',
            station_record.id::text,
            station_record.created_at,
            CURRENT_TIMESTAMP,
            station_record.created_by
        ) ON CONFLICT (org_code) DO NOTHING;
        
        -- 更新站点的organization_id为新创建的组织ID
        UPDATE core_schema.stations 
        SET organization_id = (
            SELECT id FROM core_schema.organizations 
            WHERE org_code = 'ST_' || station_record.site_code
        )
        WHERE id = station_record.id;
        
    END LOOP;
    
    RAISE NOTICE 'Station organizations created and linked';
END $$;

-- =====================================================
-- 3. Migrate User Station Roles to User Organizations
-- 将用户站点角色迁移到用户组织关联
-- =====================================================

DO $$
DECLARE
    user_station_record RECORD;
    org_id bigint;
    role_mapping text;
BEGIN
    -- 遍历所有活跃的用户站点角色
    FOR user_station_record IN 
        SELECT usr.user_id, usr.station_id, usr.role_type, usr.status, 
               usr.granted_by, usr.granted_at, usr.expires_at,
               usr.created_at, usr.created_by
        FROM core_schema.user_station_roles usr
        WHERE usr.status = 'active' AND usr.deleted_at IS NULL
    LOOP
        -- 获取站点对应的组织ID
        SELECT organization_id INTO org_id 
        FROM core_schema.stations 
        WHERE id = user_station_record.station_id;
        
        -- 角色映射：站点角色 -> 组织角色
        CASE user_station_record.role_type
            WHEN 'manager' THEN role_mapping := 'admin';
            WHEN 'assistant_manager' THEN role_mapping := 'manager';
            WHEN 'supervisor' THEN role_mapping := 'manager';
            WHEN 'operator' THEN role_mapping := 'member';
            ELSE role_mapping := 'member';
        END CASE;
        
        -- 创建用户组织关联（如果不存在）
        INSERT INTO core_schema.user_organizations (
            user_id,
            organization_id,
            is_primary,
            role_in_org,
            granted_by,
            granted_at,
            expires_at,
            status,
            created_at,
            updated_at,
            created_by
        ) VALUES (
            user_station_record.user_id,
            org_id,
            false, -- 暂时都设为非主要组织，后续可以手动调整
            role_mapping,
            user_station_record.granted_by,
            user_station_record.granted_at,
            user_station_record.expires_at,
            'active',
            user_station_record.created_at,
            CURRENT_TIMESTAMP,
            user_station_record.created_by
        ) ON CONFLICT (user_id, organization_id) DO NOTHING;
        
    END LOOP;
    
    RAISE NOTICE 'User station roles migrated to user organizations';
END $$;

-- =====================================================
-- 4. Set Primary Organizations for Users
-- 为用户设置主要组织
-- =====================================================

DO $$
DECLARE
    user_record RECORD;
    primary_org_id bigint;
BEGIN
    -- 为每个用户设置一个主要组织（选择第一个关联的组织）
    FOR user_record IN 
        SELECT DISTINCT user_id 
        FROM core_schema.user_organizations 
        WHERE is_primary = false
    LOOP
        -- 获取用户的第一个组织作为主要组织
        SELECT organization_id INTO primary_org_id
        FROM core_schema.user_organizations
        WHERE user_id = user_record.user_id
        ORDER BY granted_at ASC
        LIMIT 1;
        
        -- 设置为主要组织
        UPDATE core_schema.user_organizations
        SET is_primary = true, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = user_record.user_id 
        AND organization_id = primary_org_id;
        
        -- 更新用户表的主要组织ID
        UPDATE core_schema.users
        SET primary_organization_id = primary_org_id
        WHERE id = user_record.user_id;
        
    END LOOP;
    
    RAISE NOTICE 'Primary organizations set for users';
END $$;

-- =====================================================
-- 5. Create Sample Regional Organizations (Optional)
-- 创建示例区域组织（可选）
-- =====================================================

DO $$
DECLARE
    hq_id bigint;
    region_type_id bigint;
    region_id bigint;
BEGIN
    -- 获取总部ID和区域类型ID
    SELECT id INTO hq_id FROM core_schema.organizations WHERE org_code = 'HQ001';
    SELECT id INTO region_type_id FROM core_schema.organization_types WHERE type_code = 'REGION';
    
    -- 创建示例区域组织
    INSERT INTO core_schema.organizations (
        org_code,
        org_name,
        org_type_id,
        parent_id,
        sort_order,
        status,
        created_at,
        updated_at
    ) VALUES 
    ('RG001', 'Jakarta Region', region_type_id, hq_id, 1, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('RG002', 'Surabaya Region', region_type_id, hq_id, 2, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('RG003', 'Medan Region', region_type_id, hq_id, 3, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (org_code) DO NOTHING;
    
    RAISE NOTICE 'Sample regional organizations created';
END $$;

-- =====================================================
-- 6. Verification Queries
-- 验证查询
-- =====================================================

-- 显示组织层级结构
SELECT 
    o.id,
    o.org_code,
    o.org_name,
    ot.type_name,
    o.level_path,
    o.level_depth,
    p.org_name as parent_name
FROM core_schema.organizations o
JOIN core_schema.organization_types ot ON o.org_type_id = ot.id
LEFT JOIN core_schema.organizations p ON o.parent_id = p.id
ORDER BY o.level_path;

-- 显示用户组织关联统计
SELECT 
    ot.type_name,
    COUNT(*) as user_count,
    COUNT(CASE WHEN uo.is_primary THEN 1 END) as primary_count
FROM core_schema.user_organizations uo
JOIN core_schema.organizations o ON uo.organization_id = o.id
JOIN core_schema.organization_types ot ON o.org_type_id = ot.id
WHERE uo.status = 'active'
GROUP BY ot.type_name
ORDER BY ot.level_order;
