-- Organization Schema Enhancement for HOS-BOS Management Architecture
-- 组织架构增强方案，支持HOS-BOS管理架构

-- =====================================================
-- 1. Organization Type Definition Table
-- 组织类型定义表
-- =====================================================
CREATE TABLE core_schema.organization_types (
    id bigserial PRIMARY KEY,
    type_code varchar(50) NOT NULL UNIQUE, -- 类型编码：HQ, REGION, STATION
    type_name varchar(100) NOT NULL, -- 类型名称：总部、区域、站点
    level_order int NOT NULL, -- 层级顺序：1-总部，2-区域，3-站点
    description text, -- 类型描述
    is_system boolean DEFAULT false, -- 是否系统预定义类型
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id)
);

-- Create indexes for organization_types
CREATE INDEX idx_organization_types_level_order ON core_schema.organization_types(level_order);
CREATE INDEX idx_organization_types_status ON core_schema.organization_types(status);

-- =====================================================
-- 2. Organizations Table
-- 组织架构表
-- =====================================================
CREATE TABLE core_schema.organizations (
    id bigserial PRIMARY KEY,
    org_code varchar(50) NOT NULL UNIQUE, -- 组织编码，全局唯一
    org_name varchar(200) NOT NULL, -- 组织名称
    org_type_id bigint NOT NULL REFERENCES core_schema.organization_types(id),
    parent_id bigint REFERENCES core_schema.organizations(id), -- 父组织ID
    level_path varchar(500), -- 层级路径，如：/1/2/3，便于查询子组织
    level_depth int DEFAULT 1, -- 层级深度
    sort_order int DEFAULT 0, -- 同级排序
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    
    -- Contact and Address Information
    contact_info jsonb, -- 联系方式信息
    address jsonb, -- 地址信息
    
    -- Business Information
    business_license varchar(100), -- 营业执照号
    tax_number varchar(100), -- 税号
    legal_representative varchar(100), -- 法定代表人
    
    -- System Integration
    external_system_id varchar(100), -- 外部系统ID（如HOS系统中的ID）
    sync_status varchar(20) DEFAULT 'pending', -- 同步状态
    last_sync_at timestamp, -- 最后同步时间
    
    -- Audit Fields
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for organizations
CREATE INDEX idx_organizations_org_type ON core_schema.organizations(org_type_id);
CREATE INDEX idx_organizations_parent ON core_schema.organizations(parent_id);
CREATE INDEX idx_organizations_level_path ON core_schema.organizations USING gin(level_path gin_trgm_ops);
CREATE INDEX idx_organizations_status ON core_schema.organizations(status);
CREATE INDEX idx_organizations_external_id ON core_schema.organizations(external_system_id);

-- =====================================================
-- 3. User Organizations Association Table
-- 用户组织归属表
-- =====================================================
CREATE TABLE core_schema.user_organizations (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES core_schema.users(id) ON DELETE CASCADE,
    organization_id bigint NOT NULL REFERENCES core_schema.organizations(id) ON DELETE CASCADE,
    
    -- Role and Status
    is_primary boolean DEFAULT false, -- 是否主要归属组织
    role_in_org varchar(50), -- 在组织中的角色：admin, manager, member
    responsibility_area varchar(200), -- 负责区域/职责范围
    
    -- Authorization
    granted_by uuid REFERENCES core_schema.users(id), -- 授权人
    granted_at timestamp DEFAULT CURRENT_TIMESTAMP, -- 授权时间
    expires_at timestamp, -- 过期时间，NULL表示永久有效
    
    -- Status Management
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    
    -- Audit Fields
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for user_organizations
CREATE INDEX idx_user_organizations_user ON core_schema.user_organizations(user_id);
CREATE INDEX idx_user_organizations_org ON core_schema.user_organizations(organization_id);
CREATE INDEX idx_user_organizations_primary ON core_schema.user_organizations(user_id, is_primary);
CREATE INDEX idx_user_organizations_status ON core_schema.user_organizations(status);
CREATE UNIQUE INDEX idx_user_organizations_primary_unique ON core_schema.user_organizations(user_id) 
    WHERE is_primary = true AND deleted_at IS NULL;

-- =====================================================
-- 4. Organization Hierarchy Functions
-- 组织层级管理函数
-- =====================================================

-- Function to update level_path when organization hierarchy changes
CREATE OR REPLACE FUNCTION core_schema.update_organization_level_path()
RETURNS TRIGGER AS $$
DECLARE
    parent_path varchar(500);
    parent_depth int;
BEGIN
    -- If this is a root organization (no parent)
    IF NEW.parent_id IS NULL THEN
        NEW.level_path := '/' || NEW.id::text;
        NEW.level_depth := 1;
    ELSE
        -- Get parent's path and depth
        SELECT level_path, level_depth 
        INTO parent_path, parent_depth
        FROM core_schema.organizations 
        WHERE id = NEW.parent_id;
        
        -- Build new path and depth
        NEW.level_path := parent_path || '/' || NEW.id::text;
        NEW.level_depth := parent_depth + 1;
    END IF;
    
    NEW.updated_at := CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic level_path update
CREATE TRIGGER trigger_update_organization_level_path
    BEFORE INSERT OR UPDATE OF parent_id ON core_schema.organizations
    FOR EACH ROW
    EXECUTE FUNCTION core_schema.update_organization_level_path();

-- =====================================================
-- 5. Data Migration and Initial Setup
-- 数据迁移和初始化设置
-- =====================================================

-- Insert default organization types
INSERT INTO core_schema.organization_types (type_code, type_name, level_order, description, is_system) VALUES
('HQ', 'Headquarters', 1, 'Company headquarters', true),
('REGION', 'Regional Office', 2, 'Regional management office', true),
('STATION', 'Gas Station', 3, 'Individual gas station', true);

-- =====================================================
-- 6. Modify Existing Tables
-- 修改现有表结构
-- =====================================================

-- Add organization reference to users table
ALTER TABLE core_schema.users 
ADD COLUMN primary_organization_id bigint REFERENCES core_schema.organizations(id);

-- Add organization reference to stations table  
ALTER TABLE core_schema.stations 
ADD COLUMN organization_id bigint REFERENCES core_schema.organizations(id);

-- Create indexes for new foreign keys
CREATE INDEX idx_users_primary_org ON core_schema.users(primary_organization_id);
CREATE INDEX idx_stations_organization ON core_schema.stations(organization_id);

-- =====================================================
-- 7. Views for Easy Querying
-- 便于查询的视图
-- =====================================================

-- View for organization hierarchy with type information
CREATE VIEW core_schema.v_organization_hierarchy AS
SELECT 
    o.id,
    o.org_code,
    o.org_name,
    o.parent_id,
    o.level_path,
    o.level_depth,
    o.status,
    ot.type_code,
    ot.type_name,
    ot.level_order,
    p.org_name as parent_name,
    p.org_code as parent_code
FROM core_schema.organizations o
JOIN core_schema.organization_types ot ON o.org_type_id = ot.id
LEFT JOIN core_schema.organizations p ON o.parent_id = p.id
WHERE o.deleted_at IS NULL
ORDER BY o.level_path;

-- View for user organization associations with details
CREATE VIEW core_schema.v_user_organization_details AS
SELECT 
    uo.id,
    uo.user_id,
    u.username,
    u.full_name,
    uo.organization_id,
    o.org_code,
    o.org_name,
    ot.type_name as org_type,
    uo.is_primary,
    uo.role_in_org,
    uo.status,
    uo.granted_at,
    uo.expires_at
FROM core_schema.user_organizations uo
JOIN core_schema.users u ON uo.user_id = u.id
JOIN core_schema.organizations o ON uo.organization_id = o.id
JOIN core_schema.organization_types ot ON o.org_type_id = ot.id
WHERE uo.deleted_at IS NULL AND u.deleted_at IS NULL AND o.deleted_at IS NULL;
