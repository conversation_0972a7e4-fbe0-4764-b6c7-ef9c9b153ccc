-- System Integration Schema for SSO and Multi-System Support
-- 系统集成架构，支持SSO和多系统权限映射

-- =====================================================
-- 1. System Integrations Table
-- 系统集成配置表
-- =====================================================
CREATE TABLE core_schema.system_integrations (
    id bigserial PRIMARY KEY,
    system_code varchar(50) NOT NULL UNIQUE, -- 系统代码：BOS, EDC, HOS等
    system_name varchar(100) NOT NULL, -- 系统名称
    system_type varchar(50) NOT NULL, -- 系统类型：web, mobile, api, service
    integration_type varchar(50) NOT NULL, -- 集成类型：SSO, API, DATABASE, WEBHOOK
    
    -- System configuration
    base_url varchar(500), -- 系统基础URL
    api_endpoint varchar(500), -- API端点
    auth_endpoint varchar(500), -- 认证端点
    callback_url varchar(500), -- 回调URL
    
    -- Integration configuration
    config_data jsonb NOT NULL, -- 集成配置数据
    auth_config jsonb, -- 认证配置
    permission_mapping jsonb, -- 权限映射配置
    
    -- System metadata
    version varchar(50), -- 系统版本
    vendor varchar(100), -- 供应商
    description text, -- 系统描述
    
    -- Status and settings
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    is_sso_enabled boolean DEFAULT false, -- 是否启用SSO
    is_auto_provision boolean DEFAULT false, -- 是否自动创建用户
    sync_frequency varchar(50) DEFAULT 'manual', -- 同步频率：manual, hourly, daily
    
    -- Audit fields
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for system_integrations
CREATE INDEX idx_system_integrations_type ON core_schema.system_integrations(system_type);
CREATE INDEX idx_system_integrations_integration_type ON core_schema.system_integrations(integration_type);
CREATE INDEX idx_system_integrations_status ON core_schema.system_integrations(status);
CREATE INDEX idx_system_integrations_sso ON core_schema.system_integrations(is_sso_enabled);

-- =====================================================
-- 2. User System Profiles Table
-- 用户系统配置文件表
-- =====================================================
CREATE TABLE core_schema.user_system_profiles (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES core_schema.users(id) ON DELETE CASCADE,
    system_code varchar(50) NOT NULL, -- 关联到system_integrations.system_code
    
    -- User profile in target system
    external_user_id varchar(100), -- 外部系统用户ID
    external_username varchar(100), -- 外部系统用户名
    external_email varchar(255), -- 外部系统邮箱
    
    -- Profile configuration
    profile_data jsonb, -- 用户在该系统的配置信息
    permission_overrides jsonb, -- 权限覆盖配置
    preferences jsonb, -- 用户偏好设置
    
    -- Synchronization
    last_sync_at timestamp, -- 最后同步时间
    sync_status varchar(20) DEFAULT 'pending', -- 同步状态：pending, synced, failed
    sync_error text, -- 同步错误信息
    
    -- Status and audit
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for user_system_profiles
CREATE INDEX idx_user_system_profiles_user ON core_schema.user_system_profiles(user_id);
CREATE INDEX idx_user_system_profiles_system ON core_schema.user_system_profiles(system_code);
CREATE INDEX idx_user_system_profiles_external_user ON core_schema.user_system_profiles(external_user_id);
CREATE INDEX idx_user_system_profiles_sync_status ON core_schema.user_system_profiles(sync_status);
CREATE UNIQUE INDEX idx_user_system_profiles_unique ON core_schema.user_system_profiles(user_id, system_code) 
    WHERE deleted_at IS NULL;

-- =====================================================
-- 3. SSO Tokens Table
-- SSO令牌管理表
-- =====================================================
CREATE TABLE core_schema.sso_tokens (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES core_schema.users(id) ON DELETE CASCADE,
    
    -- Token information
    token_type varchar(50) NOT NULL, -- access_token, refresh_token, id_token
    token_value varchar(1000) NOT NULL, -- 令牌值（加密存储）
    token_hash varchar(255) NOT NULL UNIQUE, -- 令牌哈希值，用于快速查找
    
    -- Token scope and systems
    source_system varchar(50) NOT NULL, -- 源系统
    target_systems varchar(200)[], -- 可访问的目标系统列表
    scopes varchar(500)[], -- 权限范围
    
    -- Token lifecycle
    issued_at timestamp DEFAULT CURRENT_TIMESTAMP, -- 签发时间
    expires_at timestamp NOT NULL, -- 过期时间
    used_at timestamp, -- 最后使用时间
    revoked_at timestamp, -- 撤销时间
    
    -- Token metadata
    client_id varchar(100), -- 客户端ID
    session_id varchar(255), -- 会话ID
    device_info jsonb, -- 设备信息
    ip_address inet, -- IP地址
    user_agent text, -- 用户代理
    
    -- Status
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked', 'used')),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for sso_tokens
CREATE INDEX idx_sso_tokens_user ON core_schema.sso_tokens(user_id);
CREATE INDEX idx_sso_tokens_type ON core_schema.sso_tokens(token_type);
CREATE INDEX idx_sso_tokens_source_system ON core_schema.sso_tokens(source_system);
CREATE INDEX idx_sso_tokens_expires ON core_schema.sso_tokens(expires_at);
CREATE INDEX idx_sso_tokens_status ON core_schema.sso_tokens(status);
CREATE INDEX idx_sso_tokens_session ON core_schema.sso_tokens(session_id);

-- =====================================================
-- 4. System Permission Mappings Table
-- 系统权限映射表
-- =====================================================
CREATE TABLE core_schema.system_permission_mappings (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    source_system varchar(50) NOT NULL, -- 源系统
    target_system varchar(50) NOT NULL, -- 目标系统
    
    -- Permission mapping
    source_permission varchar(200) NOT NULL, -- 源权限代码
    target_permission varchar(200) NOT NULL, -- 目标权限代码
    mapping_type varchar(50) NOT NULL, -- 映射类型：direct, transformed, conditional
    
    -- Mapping configuration
    transformation_rules jsonb, -- 转换规则
    conditions jsonb, -- 映射条件
    priority int DEFAULT 0, -- 优先级
    
    -- Mapping metadata
    description text, -- 映射描述
    is_bidirectional boolean DEFAULT false, -- 是否双向映射
    is_active boolean DEFAULT true, -- 是否启用
    
    -- Audit fields
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id)
);

-- Create indexes for system_permission_mappings
CREATE INDEX idx_system_perm_mappings_source ON core_schema.system_permission_mappings(source_system, source_permission);
CREATE INDEX idx_system_perm_mappings_target ON core_schema.system_permission_mappings(target_system, target_permission);
CREATE INDEX idx_system_perm_mappings_type ON core_schema.system_permission_mappings(mapping_type);
CREATE INDEX idx_system_perm_mappings_active ON core_schema.system_permission_mappings(is_active);

-- =====================================================
-- 5. System Sync Logs Table
-- 系统同步日志表
-- =====================================================
CREATE TABLE core_schema.system_sync_logs (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    sync_type varchar(50) NOT NULL, -- 同步类型：user, permission, data
    source_system varchar(50) NOT NULL, -- 源系统
    target_system varchar(50) NOT NULL, -- 目标系统
    
    -- Sync operation
    operation varchar(50) NOT NULL, -- 操作类型：create, update, delete, sync
    entity_type varchar(50) NOT NULL, -- 实体类型：user, role, permission
    entity_id varchar(100), -- 实体ID
    
    -- Sync details
    sync_data jsonb, -- 同步数据
    sync_result varchar(20) NOT NULL, -- 同步结果：success, failed, partial
    error_message text, -- 错误信息
    error_details jsonb, -- 错误详情
    
    -- Sync metadata
    sync_batch_id uuid, -- 批次ID
    sync_duration_ms bigint, -- 同步耗时（毫秒）
    records_processed int DEFAULT 0, -- 处理记录数
    records_success int DEFAULT 0, -- 成功记录数
    records_failed int DEFAULT 0, -- 失败记录数
    
    -- Audit fields
    started_at timestamp DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp,
    created_by uuid REFERENCES core_schema.users(id)
);

-- Create indexes for system_sync_logs
CREATE INDEX idx_system_sync_logs_type ON core_schema.system_sync_logs(sync_type);
CREATE INDEX idx_system_sync_logs_source ON core_schema.system_sync_logs(source_system);
CREATE INDEX idx_system_sync_logs_target ON core_schema.system_sync_logs(target_system);
CREATE INDEX idx_system_sync_logs_result ON core_schema.system_sync_logs(sync_result);
CREATE INDEX idx_system_sync_logs_batch ON core_schema.system_sync_logs(sync_batch_id);
CREATE INDEX idx_system_sync_logs_started ON core_schema.system_sync_logs(started_at);

-- =====================================================
-- 6. Insert Default System Integrations
-- 插入默认系统集成配置
-- =====================================================

INSERT INTO core_schema.system_integrations (
    system_code, 
    system_name, 
    system_type, 
    integration_type, 
    config_data, 
    auth_config,
    description,
    is_sso_enabled
) VALUES 
(
    'BOS',
    'Business Operations System',
    'web',
    'SSO',
    '{"features": ["user_management", "station_management", "reporting"], "ui_theme": "admin"}',
    '{"auth_method": "jwt", "token_expiry": 3600, "refresh_enabled": true}',
    'Web-based business operations and management system',
    true
),
(
    'EDC',
    'Electronic Data Capture',
    'mobile',
    'SSO',
    '{"features": ["data_collection", "offline_sync"], "platform": "android"}',
    '{"auth_method": "jwt", "token_expiry": 7200, "refresh_enabled": true}',
    'Mobile data collection system for field operations',
    true
),
(
    'HOS',
    'Head Office System',
    'web',
    'API',
    '{"features": ["financial_reporting", "analytics", "dashboard"], "integration_mode": "api"}',
    '{"auth_method": "api_key", "rate_limit": 1000}',
    'Head office management and reporting system',
    false
);

-- =====================================================
-- 7. Insert Default Permission Mappings
-- 插入默认权限映射
-- =====================================================

INSERT INTO core_schema.system_permission_mappings (
    source_system,
    target_system,
    source_permission,
    target_permission,
    mapping_type,
    description,
    is_active
) VALUES 
-- BOS to EDC mappings
('BOS', 'EDC', 'station:read:all', 'station:access', 'direct', 'Station access permission mapping', true),
('BOS', 'EDC', 'station:write:all', 'station:manage', 'direct', 'Station management permission mapping', true),
('BOS', 'EDC', 'user:read:organization', 'user:view', 'direct', 'User view permission mapping', true),

-- EDC to BOS mappings
('EDC', 'BOS', 'station:access', 'station:read:own', 'transformed', 'Limited station read access from EDC', true),
('EDC', 'BOS', 'data:submit', 'transaction:create', 'transformed', 'Data submission to transaction creation', true),

-- HOS integration mappings
('BOS', 'HOS', 'report:read:all', 'analytics:view', 'direct', 'Reporting access for HOS integration', true),
('BOS', 'HOS', 'station:read:all', 'location:view', 'transformed', 'Station data for location analytics', true);

-- =====================================================
-- 8. Create Views for System Integration
-- 创建系统集成相关视图
-- =====================================================

-- View for active system integrations
CREATE VIEW core_schema.v_active_system_integrations AS
SELECT 
    si.id,
    si.system_code,
    si.system_name,
    si.system_type,
    si.integration_type,
    si.status,
    si.is_sso_enabled,
    si.base_url,
    COUNT(usp.id) as user_count,
    si.created_at,
    si.updated_at
FROM core_schema.system_integrations si
LEFT JOIN core_schema.user_system_profiles usp ON si.system_code = usp.system_code AND usp.deleted_at IS NULL
WHERE si.status = 'active' AND si.deleted_at IS NULL
GROUP BY si.id, si.system_code, si.system_name, si.system_type, si.integration_type, 
         si.status, si.is_sso_enabled, si.base_url, si.created_at, si.updated_at
ORDER BY si.system_name;

-- View for user system access summary
CREATE VIEW core_schema.v_user_system_access AS
SELECT 
    u.id as user_id,
    u.username,
    u.full_name,
    u.email,
    array_agg(DISTINCT usp.system_code) as accessible_systems,
    array_agg(DISTINCT si.system_name) as system_names,
    COUNT(DISTINCT usp.system_code) as system_count,
    MAX(usp.last_sync_at) as last_sync_at
FROM core_schema.users u
LEFT JOIN core_schema.user_system_profiles usp ON u.id = usp.user_id AND usp.status = 'active' AND usp.deleted_at IS NULL
LEFT JOIN core_schema.system_integrations si ON usp.system_code = si.system_code AND si.status = 'active'
WHERE u.deleted_at IS NULL
GROUP BY u.id, u.username, u.full_name, u.email
ORDER BY u.username;
