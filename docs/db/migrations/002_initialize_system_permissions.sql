-- Migration: Initialize System Permissions Data
-- Version: 002
-- Description: 初始化HOS、BOS、EDC三个系统的基础权限数据
-- Date: 2025-01-20

-- =====================================================
-- Migration Up
-- =====================================================

-- 清理可能存在的旧数据
DELETE FROM core_schema.system_permissions WHERE is_system = true;

-- =====================================================
-- HOS系统权限定义
-- =====================================================

-- 用户管理权限
INSERT INTO core_schema.system_permissions (system_code, permission_code, permission_name, scope_type, resource, action, description, is_system, status) VALUES
('HOS', 'user:management:create', '创建用户', 'global', 'user', 'create', 'HOS系统创建用户权限', true, 'active'),
('HOS', 'user:management:read', '查看用户', 'global', 'user', 'read', 'HOS系统查看用户权限', true, 'active'),
('HOS', 'user:management:update', '更新用户', 'global', 'user', 'update', 'HOS系统更新用户权限', true, 'active'),
('HOS', 'user:management:delete', '删除用户', 'global', 'user', 'delete', 'HOS系统删除用户权限', true, 'active'),

-- 站点管理权限
('HOS', 'station:management:create', '创建站点', 'global', 'station', 'create', 'HOS系统创建站点权限', true, 'active'),
('HOS', 'station:management:read', '查看站点', 'global', 'station', 'read', 'HOS系统查看所有站点权限', true, 'active'),
('HOS', 'station:management:update', '更新站点', 'global', 'station', 'update', 'HOS系统更新站点权限', true, 'active'),
('HOS', 'station:management:delete', '删除站点', 'global', 'station', 'delete', 'HOS系统删除站点权限', true, 'active'),

-- 报表分析权限
('HOS', 'report:analytics:view', '查看分析报表', 'global', 'report', 'view', 'HOS系统查看全局分析报表权限', true, 'active'),
('HOS', 'report:analytics:export', '导出分析报表', 'global', 'report', 'export', 'HOS系统导出分析报表权限', true, 'active'),
('HOS', 'report:financial:view', '查看财务报表', 'global', 'report', 'view', 'HOS系统查看财务报表权限', true, 'active'),
('HOS', 'report:financial:export', '导出财务报表', 'global', 'report', 'export', 'HOS系统导出财务报表权限', true, 'active'),

-- 系统配置权限
('HOS', 'system:config:read', '查看系统配置', 'global', 'system', 'read', 'HOS系统查看配置权限', true, 'active'),
('HOS', 'system:config:update', '更新系统配置', 'global', 'system', 'update', 'HOS系统更新配置权限', true, 'active'),
('HOS', 'system:config:manage', '管理系统配置', 'global', 'system', 'manage', 'HOS系统管理配置权限', true, 'active'),

-- 权限管理权限
('HOS', 'permission:management:read', '查看权限', 'global', 'permission', 'read', 'HOS系统查看权限权限', true, 'active'),
('HOS', 'permission:management:update', '分配权限', 'global', 'permission', 'update', 'HOS系统分配权限权限', true, 'active'),
('HOS', 'permission:management:manage', '管理权限', 'global', 'permission', 'manage', 'HOS系统管理权限权限', true, 'active'),

-- 审计日志权限
('HOS', 'audit:log:view', '查看审计日志', 'global', 'audit', 'view', 'HOS系统查看审计日志权限', true, 'active'),
('HOS', 'audit:log:export', '导出审计日志', 'global', 'audit', 'export', 'HOS系统导出审计日志权限', true, 'active');

-- =====================================================
-- BOS系统权限定义
-- =====================================================

-- 站点管理权限
INSERT INTO core_schema.system_permissions (system_code, permission_code, permission_name, scope_type, resource, action, description, is_system, status) VALUES
('BOS', 'station:operation:read', '查看站点运营', 'station', 'station', 'read', 'BOS系统查看站点运营权限', true, 'active'),
('BOS', 'station:operation:update', '更新站点运营', 'station', 'station', 'update', 'BOS系统更新站点运营权限', true, 'active'),
('BOS', 'station:operation:manage', '管理站点运营', 'station', 'station', 'manage', 'BOS系统管理站点运营权限', true, 'active'),

-- 员工管理权限
('BOS', 'employee:management:create', '创建员工', 'station', 'employee', 'create', 'BOS系统创建员工权限', true, 'active'),
('BOS', 'employee:management:read', '查看员工', 'station', 'employee', 'read', 'BOS系统查看员工权限', true, 'active'),
('BOS', 'employee:management:update', '更新员工', 'station', 'employee', 'update', 'BOS系统更新员工权限', true, 'active'),
('BOS', 'employee:management:delete', '删除员工', 'station', 'employee', 'delete', 'BOS系统删除员工权限', true, 'active'),

-- 库存管理权限
('BOS', 'inventory:management:read', '查看库存', 'station', 'inventory', 'read', 'BOS系统查看库存权限', true, 'active'),
('BOS', 'inventory:management:update', '更新库存', 'station', 'inventory', 'update', 'BOS系统更新库存权限', true, 'active'),
('BOS', 'inventory:management:manage', '管理库存', 'station', 'inventory', 'manage', 'BOS系统管理库存权限', true, 'active'),

-- 交易管理权限
('BOS', 'transaction:management:read', '查看交易', 'station', 'transaction', 'read', 'BOS系统查看交易权限', true, 'active'),
('BOS', 'transaction:management:update', '处理交易', 'station', 'transaction', 'update', 'BOS系统处理交易权限', true, 'active'),

-- 报表权限
('BOS', 'report:station:view', '查看站点报表', 'station', 'report', 'view', 'BOS系统查看站点报表权限', true, 'active'),
('BOS', 'report:station:export', '导出站点报表', 'station', 'report', 'export', 'BOS系统导出站点报表权限', true, 'active');

-- =====================================================
-- EDC系统权限定义
-- =====================================================

-- 交易操作权限
INSERT INTO core_schema.system_permissions (system_code, permission_code, permission_name, scope_type, resource, action, description, is_system, status) VALUES
('EDC', 'transaction:operation:create', '创建交易', 'operation', 'transaction', 'create', 'EDC系统创建交易权限', true, 'active'),
('EDC', 'transaction:operation:read', '查看交易', 'operation', 'transaction', 'read', 'EDC系统查看交易权限', true, 'active'),
('EDC', 'transaction:operation:update', '更新交易', 'operation', 'transaction', 'update', 'EDC系统更新交易权限', true, 'active'),

-- 数据查询权限
('EDC', 'data:query:read', '查询数据', 'operation', 'data', 'read', 'EDC系统查询数据权限', true, 'active'),

-- 设备操作权限
('EDC', 'device:operation:execute', '设备操作', 'operation', 'device', 'execute', 'EDC系统设备操作权限', true, 'active'),

-- 基础功能权限
('EDC', 'basic:function:execute', '基础功能', 'operation', 'basic', 'execute', 'EDC系统基础功能权限', true, 'active');

-- =====================================================
-- 创建默认的HOS管理员用户系统访问权限
-- =====================================================

-- 为admin用户创建HOS系统访问权限（如果admin用户存在）
INSERT INTO core_schema.user_system_access (user_id, system_code, access_level, scope_type, scope_ids, status, granted_at)
SELECT 
    u.id,
    'HOS',
    'admin',
    'global',
    NULL,
    'active',
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'admin'
ON CONFLICT (user_id, system_code) DO NOTHING;

-- =====================================================
-- Migration Verification
-- =====================================================

-- 验证权限数据是否插入成功
DO $$
DECLARE
    hos_count INTEGER;
    bos_count INTEGER;
    edc_count INTEGER;
BEGIN
    -- 检查HOS权限数量
    SELECT COUNT(*) INTO hos_count FROM core_schema.system_permissions WHERE system_code = 'HOS' AND is_system = true;
    IF hos_count < 10 THEN
        RAISE EXCEPTION 'HOS system permissions not initialized properly. Expected at least 10, got %', hos_count;
    END IF;
    
    -- 检查BOS权限数量
    SELECT COUNT(*) INTO bos_count FROM core_schema.system_permissions WHERE system_code = 'BOS' AND is_system = true;
    IF bos_count < 10 THEN
        RAISE EXCEPTION 'BOS system permissions not initialized properly. Expected at least 10, got %', bos_count;
    END IF;
    
    -- 检查EDC权限数量
    SELECT COUNT(*) INTO edc_count FROM core_schema.system_permissions WHERE system_code = 'EDC' AND is_system = true;
    IF edc_count < 5 THEN
        RAISE EXCEPTION 'EDC system permissions not initialized properly. Expected at least 5, got %', edc_count;
    END IF;
    
    RAISE NOTICE 'Migration 002_initialize_system_permissions completed successfully';
    RAISE NOTICE 'HOS permissions: %, BOS permissions: %, EDC permissions: %', hos_count, bos_count, edc_count;
END $$;
