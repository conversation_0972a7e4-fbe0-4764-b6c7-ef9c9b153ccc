-- Migration: Add Unified Authentication Tables
-- Version: 001
-- Description: 添加统一身份认证表结构，支持HOS、BOS、EDC三个系统
-- Date: 2025-01-20

-- =====================================================
-- Migration Up
-- =====================================================

-- 1. 创建用户系统访问权限表
CREATE TABLE IF NOT EXISTS core_schema.user_system_access (
    id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    system_code varchar(20) NOT NULL,
    access_level varchar(50) NOT NULL,
    scope_type varchar(20) NOT NULL,
    scope_ids jsonb,
    status varchar(20) DEFAULT 'active'::character varying NOT NULL,
    granted_by uuid NULL,
    granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    expires_at timestamptz NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    created_by uuid NULL,
    updated_by uuid NULL,
    deleted_at timestamptz NULL,
    
    CONSTRAINT user_system_access_pkey PRIMARY KEY (id),
    CONSTRAINT uk_user_system_active UNIQUE (user_id, system_code) DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT chk_system_code CHECK (system_code IN ('HOS', 'BOS', 'EDC')),
    CONSTRAINT chk_access_level CHECK (access_level IN ('admin', 'manager', 'supervisor', 'operator')),
    CONSTRAINT chk_scope_type CHECK (scope_type IN ('global', 'station', 'operation')),
    CONSTRAINT chk_status CHECK (status IN ('active', 'inactive', 'suspended')),
    CONSTRAINT user_system_access_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id) ON DELETE CASCADE,
    CONSTRAINT user_system_access_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES core_schema.users(id),
    CONSTRAINT user_system_access_created_by_fkey FOREIGN KEY (created_by) REFERENCES core_schema.users(id),
    CONSTRAINT user_system_access_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES core_schema.users(id)
);

-- 2. 创建系统权限定义表
CREATE TABLE IF NOT EXISTS core_schema.system_permissions (
    id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
    system_code varchar(20) NOT NULL,
    permission_code varchar(100) NOT NULL,
    permission_name varchar(200) NOT NULL,
    scope_type varchar(20) NOT NULL,
    resource varchar(100) NOT NULL,
    action varchar(50) NOT NULL,
    description text NULL,
    is_system boolean DEFAULT false NOT NULL,
    status varchar(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    created_by uuid NULL,
    updated_by uuid NULL,
    deleted_at timestamptz NULL,
    
    CONSTRAINT system_permissions_pkey PRIMARY KEY (id),
    CONSTRAINT uk_system_permission UNIQUE (system_code, permission_code),
    CONSTRAINT chk_system_code_sp CHECK (system_code IN ('HOS', 'BOS', 'EDC')),
    CONSTRAINT chk_scope_type_sp CHECK (scope_type IN ('global', 'station', 'operation')),
    CONSTRAINT chk_action CHECK (action IN ('create', 'read', 'update', 'delete', 'execute', 'manage', 'view', 'export', 'import')),
    CONSTRAINT chk_status_sp CHECK (status IN ('active', 'inactive')),
    CONSTRAINT system_permissions_created_by_fkey FOREIGN KEY (created_by) REFERENCES core_schema.users(id),
    CONSTRAINT system_permissions_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES core_schema.users(id)
);

-- 3. 创建用户系统权限分配表
CREATE TABLE IF NOT EXISTS core_schema.user_system_permissions (
    id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    system_permission_id uuid NOT NULL,
    granted_by uuid NULL,
    granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    expires_at timestamptz NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    
    CONSTRAINT user_system_permissions_pkey PRIMARY KEY (id),
    CONSTRAINT uk_user_system_permission UNIQUE (user_id, system_permission_id),
    CONSTRAINT user_system_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id) ON DELETE CASCADE,
    CONSTRAINT user_system_permissions_permission_id_fkey FOREIGN KEY (system_permission_id) REFERENCES core_schema.system_permissions(id) ON DELETE CASCADE,
    CONSTRAINT user_system_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES core_schema.users(id)
);

-- 4. 创建索引
CREATE INDEX IF NOT EXISTS idx_user_system_access_user_id ON core_schema.user_system_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_system_access_system_code ON core_schema.user_system_access(system_code);
CREATE INDEX IF NOT EXISTS idx_user_system_access_status ON core_schema.user_system_access(status);
CREATE INDEX IF NOT EXISTS idx_user_system_access_scope_type ON core_schema.user_system_access(scope_type);

CREATE INDEX IF NOT EXISTS idx_system_permissions_system_code ON core_schema.system_permissions(system_code);
CREATE INDEX IF NOT EXISTS idx_system_permissions_permission_code ON core_schema.system_permissions(permission_code);
CREATE INDEX IF NOT EXISTS idx_system_permissions_scope_type ON core_schema.system_permissions(scope_type);
CREATE INDEX IF NOT EXISTS idx_system_permissions_resource ON core_schema.system_permissions(resource);
CREATE INDEX IF NOT EXISTS idx_system_permissions_status ON core_schema.system_permissions(status);

CREATE INDEX IF NOT EXISTS idx_user_system_permissions_user_id ON core_schema.user_system_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_system_permissions_permission_id ON core_schema.user_system_permissions(system_permission_id);

-- 5. 添加表注释
COMMENT ON TABLE core_schema.user_system_access IS '用户系统访问权限表，支持HOS全局权限、BOS站点权限、EDC操作权限';
COMMENT ON TABLE core_schema.system_permissions IS '系统权限定义表，为不同系统定义专用权限';
COMMENT ON TABLE core_schema.user_system_permissions IS '用户系统权限分配表，记录用户在各系统中的具体权限';

-- =====================================================
-- Migration Down (Rollback)
-- =====================================================

-- 如果需要回滚，执行以下语句：
-- DROP TABLE IF EXISTS core_schema.user_system_permissions;
-- DROP TABLE IF EXISTS core_schema.system_permissions;
-- DROP TABLE IF EXISTS core_schema.user_system_access;

-- =====================================================
-- Migration Verification
-- =====================================================

-- 验证表是否创建成功
DO $$
BEGIN
    -- 检查表是否存在
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'core_schema' AND table_name = 'user_system_access') THEN
        RAISE EXCEPTION 'Table user_system_access was not created successfully';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'core_schema' AND table_name = 'system_permissions') THEN
        RAISE EXCEPTION 'Table system_permissions was not created successfully';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'core_schema' AND table_name = 'user_system_permissions') THEN
        RAISE EXCEPTION 'Table user_system_permissions was not created successfully';
    END IF;
    
    RAISE NOTICE 'Migration 001_add_unified_auth_tables completed successfully';
END $$;
