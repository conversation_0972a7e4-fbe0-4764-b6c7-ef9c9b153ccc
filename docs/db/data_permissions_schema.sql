-- Data Permissions Schema Enhancement
-- 数据权限体系增强方案

-- =====================================================
-- 1. Data Permissions Definition Table
-- 数据权限定义表
-- =====================================================
CREATE TABLE core_schema.data_permissions (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    permission_code varchar(100) NOT NULL UNIQUE, -- 权限代码，如：org:read:own, org:read:department, org:read:all
    permission_name varchar(200) NOT NULL, -- 权限名称
    resource_type varchar(50) NOT NULL, -- 资源类型：organization, station, user, order等
    scope_type varchar(50) NOT NULL, -- 权限范围：own, department, organization, all
    description text, -- 权限描述
    is_system boolean DEFAULT false, -- 是否系统预定义权限
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for data_permissions
CREATE INDEX idx_data_permissions_resource_type ON core_schema.data_permissions(resource_type);
CREATE INDEX idx_data_permissions_scope_type ON core_schema.data_permissions(scope_type);
CREATE INDEX idx_data_permissions_status ON core_schema.data_permissions(status);

-- =====================================================
-- 2. User Data Permissions Association Table
-- 用户数据权限关联表
-- =====================================================
CREATE TABLE core_schema.user_data_permissions (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES core_schema.users(id) ON DELETE CASCADE,
    data_permission_id uuid NOT NULL REFERENCES core_schema.data_permissions(id) ON DELETE CASCADE,
    
    -- Scope configuration
    scope_value jsonb, -- 权限范围值，如具体的组织ID列表、站点ID列表等
    scope_conditions jsonb, -- 权限条件，如时间范围、状态条件等
    
    -- Authorization info
    granted_by uuid REFERENCES core_schema.users(id), -- 授权人
    granted_at timestamp DEFAULT CURRENT_TIMESTAMP, -- 授权时间
    expires_at timestamp, -- 过期时间，NULL表示永久有效
    
    -- Status and audit
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for user_data_permissions
CREATE INDEX idx_user_data_permissions_user ON core_schema.user_data_permissions(user_id);
CREATE INDEX idx_user_data_permissions_permission ON core_schema.user_data_permissions(data_permission_id);
CREATE INDEX idx_user_data_permissions_status ON core_schema.user_data_permissions(status);
CREATE INDEX idx_user_data_permissions_expires ON core_schema.user_data_permissions(expires_at);
CREATE UNIQUE INDEX idx_user_data_permissions_unique ON core_schema.user_data_permissions(user_id, data_permission_id) 
    WHERE deleted_at IS NULL;

-- =====================================================
-- 3. Organization Permission Templates Table
-- 组织权限模板表
-- =====================================================
CREATE TABLE core_schema.organization_permission_templates (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    organization_id bigint NOT NULL REFERENCES core_schema.organizations(id) ON DELETE CASCADE,
    role_type varchar(50) NOT NULL, -- 角色类型：admin, manager, member
    template_name varchar(100) NOT NULL, -- 模板名称
    description text, -- 模板描述
    
    -- Permission configuration
    permissions jsonb NOT NULL, -- 权限列表，包含权限代码和配置
    data_permissions jsonb, -- 数据权限列表
    
    -- Template settings
    is_default boolean DEFAULT false, -- 是否默认模板
    is_system boolean DEFAULT false, -- 是否系统模板
    priority int DEFAULT 0, -- 优先级，数字越大优先级越高
    
    -- Status and audit
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id),
    deleted_at timestamp -- 软删除
);

-- Create indexes for organization_permission_templates
CREATE INDEX idx_org_perm_templates_org ON core_schema.organization_permission_templates(organization_id);
CREATE INDEX idx_org_perm_templates_role ON core_schema.organization_permission_templates(role_type);
CREATE INDEX idx_org_perm_templates_default ON core_schema.organization_permission_templates(is_default);
CREATE INDEX idx_org_perm_templates_status ON core_schema.organization_permission_templates(status);

-- =====================================================
-- 4. Permission Inheritance Rules Table
-- 权限继承规则表
-- =====================================================
CREATE TABLE core_schema.permission_inheritance_rules (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    rule_name varchar(100) NOT NULL, -- 规则名称
    rule_code varchar(50) NOT NULL UNIQUE, -- 规则代码
    description text, -- 规则描述
    
    -- Inheritance configuration
    source_type varchar(50) NOT NULL, -- 源类型：organization, role, user
    target_type varchar(50) NOT NULL, -- 目标类型：organization, role, user
    inheritance_type varchar(50) NOT NULL, -- 继承类型：full, partial, conditional
    
    -- Rule conditions
    conditions jsonb, -- 继承条件
    transformations jsonb, -- 权限转换规则
    
    -- Rule settings
    is_active boolean DEFAULT true, -- 是否启用
    priority int DEFAULT 0, -- 优先级
    
    -- Status and audit
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP,
    created_by uuid REFERENCES core_schema.users(id),
    updated_by uuid REFERENCES core_schema.users(id)
);

-- Create indexes for permission_inheritance_rules
CREATE INDEX idx_perm_inheritance_source ON core_schema.permission_inheritance_rules(source_type);
CREATE INDEX idx_perm_inheritance_target ON core_schema.permission_inheritance_rules(target_type);
CREATE INDEX idx_perm_inheritance_active ON core_schema.permission_inheritance_rules(is_active);

-- =====================================================
-- 5. Permission Evaluation Cache Table
-- 权限评估缓存表
-- =====================================================
CREATE TABLE core_schema.permission_evaluation_cache (
    id uuid DEFAULT core_schema.gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES core_schema.users(id) ON DELETE CASCADE,
    resource_type varchar(50) NOT NULL, -- 资源类型
    resource_id varchar(100), -- 资源ID（可选）
    permission_code varchar(100) NOT NULL, -- 权限代码
    
    -- Evaluation result
    is_granted boolean NOT NULL, -- 是否授权
    evaluation_context jsonb, -- 评估上下文
    computed_scope jsonb, -- 计算出的权限范围
    
    -- Cache metadata
    cache_key varchar(255) NOT NULL UNIQUE, -- 缓存键
    expires_at timestamp NOT NULL, -- 缓存过期时间
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for permission_evaluation_cache
CREATE INDEX idx_perm_cache_user ON core_schema.permission_evaluation_cache(user_id);
CREATE INDEX idx_perm_cache_resource ON core_schema.permission_evaluation_cache(resource_type, resource_id);
CREATE INDEX idx_perm_cache_permission ON core_schema.permission_evaluation_cache(permission_code);
CREATE INDEX idx_perm_cache_expires ON core_schema.permission_evaluation_cache(expires_at);

-- =====================================================
-- 6. Insert Default Data Permissions
-- 插入默认数据权限
-- =====================================================

-- Organization related data permissions
INSERT INTO core_schema.data_permissions (permission_code, permission_name, resource_type, scope_type, description, is_system) VALUES
('org:read:own', 'Read Own Organization', 'organization', 'own', 'Can read own organization information', true),
('org:read:department', 'Read Department Organizations', 'organization', 'department', 'Can read organizations in same department', true),
('org:read:all', 'Read All Organizations', 'organization', 'all', 'Can read all organizations', true),
('org:write:own', 'Write Own Organization', 'organization', 'own', 'Can modify own organization information', true),
('org:write:department', 'Write Department Organizations', 'organization', 'department', 'Can modify organizations in same department', true),
('org:write:all', 'Write All Organizations', 'organization', 'all', 'Can modify all organizations', true),

-- User related data permissions
('user:read:own', 'Read Own User Info', 'user', 'own', 'Can read own user information', true),
('user:read:department', 'Read Department Users', 'user', 'department', 'Can read users in same department', true),
('user:read:organization', 'Read Organization Users', 'user', 'organization', 'Can read users in same organization', true),
('user:read:all', 'Read All Users', 'user', 'all', 'Can read all users', true),
('user:write:own', 'Write Own User Info', 'user', 'own', 'Can modify own user information', true),
('user:write:department', 'Write Department Users', 'user', 'department', 'Can modify users in same department', true),
('user:write:organization', 'Write Organization Users', 'user', 'organization', 'Can modify users in same organization', true),
('user:write:all', 'Write All Users', 'user', 'all', 'Can modify all users', true),

-- Station related data permissions
('station:read:own', 'Read Own Stations', 'station', 'own', 'Can read own managed stations', true),
('station:read:organization', 'Read Organization Stations', 'station', 'organization', 'Can read stations in same organization', true),
('station:read:all', 'Read All Stations', 'station', 'all', 'Can read all stations', true),
('station:write:own', 'Write Own Stations', 'station', 'own', 'Can modify own managed stations', true),
('station:write:organization', 'Write Organization Stations', 'station', 'organization', 'Can modify stations in same organization', true),
('station:write:all', 'Write All Stations', 'station', 'all', 'Can modify all stations', true);

-- =====================================================
-- 7. Insert Default Permission Inheritance Rules
-- 插入默认权限继承规则
-- =====================================================

INSERT INTO core_schema.permission_inheritance_rules (rule_name, rule_code, description, source_type, target_type, inheritance_type, conditions, is_active) VALUES
('Organization Admin Inheritance', 'org_admin_inherit', 'Organization admins inherit permissions to child organizations', 'organization', 'organization', 'conditional', '{"role": "admin", "inherit_to_children": true}', true),
('Department Manager Inheritance', 'dept_manager_inherit', 'Department managers inherit limited permissions to child departments', 'organization', 'organization', 'partial', '{"role": "manager", "inherit_to_children": true, "permission_filter": ["read", "limited_write"]}', true),
('Role Permission Inheritance', 'role_perm_inherit', 'Users inherit permissions from their roles', 'role', 'user', 'full', '{"inherit_all_permissions": true}', true);

-- =====================================================
-- 8. Create Views for Easy Querying
-- 创建便于查询的视图
-- =====================================================

-- View for user effective permissions
CREATE VIEW core_schema.v_user_effective_permissions AS
SELECT DISTINCT
    u.id as user_id,
    u.username,
    u.full_name,
    dp.permission_code,
    dp.permission_name,
    dp.resource_type,
    dp.scope_type,
    udp.scope_value,
    udp.expires_at,
    'direct' as permission_source
FROM core_schema.users u
JOIN core_schema.user_data_permissions udp ON u.id = udp.user_id
JOIN core_schema.data_permissions dp ON udp.data_permission_id = dp.id
WHERE udp.status = 'active' 
  AND dp.status = 'active'
  AND (udp.expires_at IS NULL OR udp.expires_at > CURRENT_TIMESTAMP)
  AND u.deleted_at IS NULL
  AND udp.deleted_at IS NULL
  AND dp.deleted_at IS NULL;

-- View for organization permission summary
CREATE VIEW core_schema.v_organization_permission_summary AS
SELECT 
    o.id as organization_id,
    o.org_code,
    o.org_name,
    ot.type_name as org_type,
    COUNT(DISTINCT uo.user_id) as user_count,
    COUNT(DISTINCT CASE WHEN uo.role_in_org = 'admin' THEN uo.user_id END) as admin_count,
    COUNT(DISTINCT CASE WHEN uo.role_in_org = 'manager' THEN uo.user_id END) as manager_count,
    COUNT(DISTINCT CASE WHEN uo.role_in_org = 'member' THEN uo.user_id END) as member_count
FROM core_schema.organizations o
JOIN core_schema.organization_types ot ON o.org_type_id = ot.id
LEFT JOIN core_schema.user_organizations uo ON o.id = uo.organization_id AND uo.status = 'active'
WHERE o.deleted_at IS NULL
GROUP BY o.id, o.org_code, o.org_name, ot.type_name
ORDER BY o.level_path;
