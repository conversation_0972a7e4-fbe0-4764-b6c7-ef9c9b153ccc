# 统一身份认证系统使用指南

## 概述

本指南介绍如何使用新的统一身份认证系统，该系统支持HOS（总部管理系统）、BOS（站点后台系统）、EDC（手持设备系统）三个系统的统一登录和权限管理。

## 系统架构

### 权限模型

```
┌─────────────────────────────────────────────────────────────┐
│                    统一身份认证系统                          │
├─────────────────────────────────────────────────────────────┤
│  HOS (总部管理系统)     │  BOS (站点后台系统)  │  EDC (手持设备系统) │
│  - 权限范围: global     │  - 权限范围: station │  - 权限范围: operation │
│  - 访问级别: admin/mgr  │  - 访问级别: mgr/sup │  - 访问级别: sup/op   │
│  - 跨站点全局权限       │  - 站点级别权限      │  - 操作级别权限        │
└─────────────────────────────────────────────────────────────┘
```

### 数据库表结构

1. **user_system_access**: 用户系统访问权限表
2. **system_permissions**: 系统权限定义表
3. **user_system_permissions**: 用户系统权限分配表

## 部署和配置

### 1. 数据库迁移

运行数据库迁移脚本来创建新的表结构：

```bash
# 应用统一认证表结构
psql -h localhost -U postgres -d bos_core -f scripts/apply_unified_auth_migration.sql
```

### 2. 服务启动

确保bos-core服务正常启动：

```bash
cd bos-core
go run main.go
```

### 3. 验证部署

运行测试脚本验证系统是否正常工作：

```bash
chmod +x scripts/test_unified_auth.sh
./scripts/test_unified_auth.sh
```

## API使用示例

### 1. HOS系统登录

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "Admin123",
    "system": "HOS"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "systemAccess": {
      "system": "HOS",
      "accessLevel": "admin",
      "scopeType": "global",
      "scopeIds": [],
      "stationCount": 0,
      "permissions": ["user:management:create", "station:management:read", ...]
    }
  }
}
```

### 2. BOS系统登录

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "station_manager",
    "password": "Manager123",
    "system": "BOS"
  }'
```

### 3. EDC系统登录

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "operator",
    "password": "Operator123",
    "system": "EDC"
  }'
```

## 权限管理

### 1. 授予HOS系统访问权限

```sql
-- 为用户授予HOS系统管理员权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status
) VALUES (
    'user-uuid', 'HOS', 'admin', 'global', NULL, 'active'
);
```

### 2. 授予BOS系统访问权限

```sql
-- 为用户授予BOS系统站点管理权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status
) VALUES (
    'user-uuid', 'BOS', 'manager', 'station', '[1, 2, 3]', 'active'
);
```

### 3. 授予EDC系统访问权限

```sql
-- 为用户授予EDC系统操作权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status
) VALUES (
    'user-uuid', 'EDC', 'operator', 'operation', '[1, 2]', 'active'
);
```

## 前端集成

### 1. 登录组件更新

```typescript
// 更新登录表单，添加系统选择
interface LoginForm {
  username: string;
  password: string;
  system: 'HOS' | 'BOS' | 'EDC';
}

// 登录函数
async function login(form: LoginForm) {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(form)
  });
  
  const result = await response.json();
  
  if (result.code === 200) {
    // 存储token和系统访问信息
    localStorage.setItem('access_token', result.data.accessToken);
    localStorage.setItem('system_access', JSON.stringify(result.data.systemAccess));
    
    // 根据系统类型跳转到对应页面
    switch (result.data.systemAccess.system) {
      case 'HOS':
        router.push('/hos/dashboard');
        break;
      case 'BOS':
        router.push('/bos/dashboard');
        break;
      case 'EDC':
        router.push('/edc/dashboard');
        break;
    }
  }
}
```

### 2. 权限检查

```typescript
// 权限检查函数
function hasPermission(permission: string): boolean {
  const systemAccess = JSON.parse(localStorage.getItem('system_access') || '{}');
  return systemAccess.permissions?.includes(permission) || false;
}

// 在组件中使用
function UserManagementComponent() {
  const canCreateUser = hasPermission('user:management:create');
  const canDeleteUser = hasPermission('user:management:delete');
  
  return (
    <div>
      {canCreateUser && <button>创建用户</button>}
      {canDeleteUser && <button>删除用户</button>}
    </div>
  );
}
```

## 故障排除

### 1. 常见错误

**错误**: "Insufficient permissions for HOS system"
**解决**: 确保用户有HOS系统访问权限，或者用户具有manager/assistant_manager角色

**错误**: "User has no station associations"
**解决**: 为用户分配站点角色，或者使用HOS系统登录

**错误**: "Unknown system type"
**解决**: 确保system参数为HOS、BOS或EDC之一

### 2. 调试步骤

1. 检查数据库表是否正确创建：
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'core_schema' 
AND table_name IN ('user_system_access', 'system_permissions', 'user_system_permissions');
```

2. 检查系统权限是否正确初始化：
```sql
SELECT system_code, COUNT(*) as permission_count 
FROM core_schema.system_permissions 
WHERE is_system = true 
GROUP BY system_code;
```

3. 检查用户系统访问权限：
```sql
SELECT u.username, usa.system_code, usa.access_level, usa.scope_type, usa.status
FROM core_schema.user_system_access usa
JOIN core_schema.users u ON usa.user_id = u.id
WHERE u.username = 'admin';
```

### 3. 日志分析

查看应用日志中的认证相关信息：

```bash
# 查看登录日志
grep "登录" logs/app.log

# 查看权限验证日志
grep "权限验证" logs/app.log

# 查看系统访问日志
grep "系统访问" logs/app.log
```

## 最佳实践

### 1. 权限设计

- **最小权限原则**: 只授予用户完成工作所需的最小权限
- **权限分离**: 不同系统的权限相互独立
- **定期审查**: 定期检查和清理不必要的权限

### 2. 安全考虑

- **Token安全**: 访问令牌应安全存储，避免在URL中传递
- **会话管理**: 实现会话超时和强制登出机制
- **审计日志**: 记录所有权限变更和敏感操作

### 3. 性能优化

- **权限缓存**: 在JWT token中包含权限信息，减少数据库查询
- **索引优化**: 确保权限相关表有适当的索引
- **批量操作**: 使用批量API进行权限分配

## 迁移指南

### 从旧系统迁移

1. **备份现有数据**
2. **运行迁移脚本**
3. **验证权限映射**
4. **更新前端代码**
5. **测试所有功能**

### 兼容性说明

- 现有的BOS和EDC登录功能完全兼容
- JWT token结构向后兼容
- 现有的权限验证逻辑继续工作

## 支持和维护

### 监控指标

- 登录成功率
- 权限验证失败次数
- 系统访问分布
- Token刷新频率

### 定期维护

- 清理过期的访问权限
- 更新系统权限定义
- 优化权限查询性能
- 审查用户权限分配

## 联系支持

如有问题或需要支持，请联系开发团队或查看相关文档：

- API文档: `docs/api/unified_authentication_api.md`
- 数据库设计: `docs/db/unified_auth_schema.sql`
- 测试脚本: `scripts/test_unified_auth.sh`
