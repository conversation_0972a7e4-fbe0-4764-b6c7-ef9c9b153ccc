# Refresh按钮文本修改为Query

## 🎯 修改概览

将Nozzle Pump Report页面上的"Refresh"按钮文本修改为"Query"，以更准确地反映按钮的功能。

## ✅ 已完成的修改

### 修改内容

**英文翻译文件**:
- 文件: `bos-frontend/messages/en/reports.json`
- 修改: `"refreshReport": "Refresh"` → `"refreshReport": "Query"`

**印尼语翻译文件**:
- 文件: `bos-frontend/messages/id/reports.json`  
- 修改: `"refreshReport": "Refresh"` → `"refreshReport": "Query"`

### 按钮实现

页面中的按钮实现保持不变，使用翻译键：
```typescript
<Button variant="outline" onClick={fetchReport} disabled={isLoading || !currentSite}>
  <Search className="mr-2 w-4 h-4" />
  {isLoading ? t('nozzlePumpReport.loading') : t('nozzlePumpReport.refreshReport')}
</Button>
```

## 🔧 技术细节

### 修改原因
- "Query"更准确地描述了按钮的功能：根据选择的条件查询报表数据
- 与搜索图标(Search)更匹配
- 更符合用户的操作预期

### 影响范围
- 只影响按钮显示文本
- 不影响按钮功能逻辑
- 支持多语言显示

### 兼容性
- 完全向后兼容
- 不需要重新编译后端
- 前端刷新后即可生效

## 📊 验证结果

修改后的按钮文本：
- **英文**: "Query"
- **印尼语**: "Query" (保持英文，符合技术术语习惯)
- **加载状态**: 仍显示"Loading..."/"Memuat..."

## 🎉 总结

成功将refresh按钮文本修改为"Query"，更准确地反映了按钮的查询功能。修改简单高效，支持多语言环境，用户体验更加直观。
