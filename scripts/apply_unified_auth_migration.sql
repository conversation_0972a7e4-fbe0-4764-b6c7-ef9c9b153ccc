-- Apply Unified Authentication Migration
-- This script applies the unified authentication tables and initializes system permissions
-- Run this script against your PostgreSQL database

\echo 'Starting unified authentication migration...'

-- Set search path
SET search_path TO core_schema, public;

-- Apply migration 001: Create unified auth tables
\echo 'Creating unified authentication tables...'

-- 1. 创建用户系统访问权限表
CREATE TABLE IF NOT EXISTS core_schema.user_system_access (
    id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    system_code varchar(20) NOT NULL,
    access_level varchar(50) NOT NULL,
    scope_type varchar(20) NOT NULL,
    scope_ids jsonb,
    status varchar(20) DEFAULT 'active'::character varying NOT NULL,
    granted_by uuid NULL,
    granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    expires_at timestamptz NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    created_by uuid NULL,
    updated_by uuid NULL,
    deleted_at timestamptz NULL,
    
    CONSTRAINT user_system_access_pkey PRIMARY KEY (id),
    CONSTRAINT uk_user_system_active UNIQUE (user_id, system_code) DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT chk_system_code CHECK (system_code IN ('HOS', 'BOS', 'EDC')),
    CONSTRAINT chk_access_level CHECK (access_level IN ('admin', 'manager', 'supervisor', 'operator')),
    CONSTRAINT chk_scope_type CHECK (scope_type IN ('global', 'station', 'operation')),
    CONSTRAINT chk_status CHECK (status IN ('active', 'inactive', 'suspended')),
    CONSTRAINT user_system_access_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id) ON DELETE CASCADE,
    CONSTRAINT user_system_access_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES core_schema.users(id),
    CONSTRAINT user_system_access_created_by_fkey FOREIGN KEY (created_by) REFERENCES core_schema.users(id),
    CONSTRAINT user_system_access_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES core_schema.users(id)
);

-- 2. 创建系统权限定义表
CREATE TABLE IF NOT EXISTS core_schema.system_permissions (
    id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
    system_code varchar(20) NOT NULL,
    permission_code varchar(100) NOT NULL,
    permission_name varchar(200) NOT NULL,
    scope_type varchar(20) NOT NULL,
    resource varchar(100) NOT NULL,
    action varchar(50) NOT NULL,
    description text NULL,
    is_system boolean DEFAULT false NOT NULL,
    status varchar(20) DEFAULT 'active'::character varying NOT NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    created_by uuid NULL,
    updated_by uuid NULL,
    deleted_at timestamptz NULL,
    
    CONSTRAINT system_permissions_pkey PRIMARY KEY (id),
    CONSTRAINT uk_system_permission UNIQUE (system_code, permission_code),
    CONSTRAINT chk_system_code_sp CHECK (system_code IN ('HOS', 'BOS', 'EDC')),
    CONSTRAINT chk_scope_type_sp CHECK (scope_type IN ('global', 'station', 'operation')),
    CONSTRAINT chk_action CHECK (action IN ('create', 'read', 'update', 'delete', 'execute', 'manage', 'view', 'export', 'import')),
    CONSTRAINT chk_status_sp CHECK (status IN ('active', 'inactive')),
    CONSTRAINT system_permissions_created_by_fkey FOREIGN KEY (created_by) REFERENCES core_schema.users(id),
    CONSTRAINT system_permissions_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES core_schema.users(id)
);

-- 3. 创建用户系统权限分配表
CREATE TABLE IF NOT EXISTS core_schema.user_system_permissions (
    id uuid DEFAULT core_schema.gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    system_permission_id uuid NOT NULL,
    granted_by uuid NULL,
    granted_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    expires_at timestamptz NULL,
    created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
    
    CONSTRAINT user_system_permissions_pkey PRIMARY KEY (id),
    CONSTRAINT uk_user_system_permission UNIQUE (user_id, system_permission_id),
    CONSTRAINT user_system_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES core_schema.users(id) ON DELETE CASCADE,
    CONSTRAINT user_system_permissions_permission_id_fkey FOREIGN KEY (system_permission_id) REFERENCES core_schema.system_permissions(id) ON DELETE CASCADE,
    CONSTRAINT user_system_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES core_schema.users(id)
);

-- 4. 创建索引
\echo 'Creating indexes...'

CREATE INDEX IF NOT EXISTS idx_user_system_access_user_id ON core_schema.user_system_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_system_access_system_code ON core_schema.user_system_access(system_code);
CREATE INDEX IF NOT EXISTS idx_user_system_access_status ON core_schema.user_system_access(status);
CREATE INDEX IF NOT EXISTS idx_user_system_access_scope_type ON core_schema.user_system_access(scope_type);

CREATE INDEX IF NOT EXISTS idx_system_permissions_system_code ON core_schema.system_permissions(system_code);
CREATE INDEX IF NOT EXISTS idx_system_permissions_permission_code ON core_schema.system_permissions(permission_code);
CREATE INDEX IF NOT EXISTS idx_system_permissions_scope_type ON core_schema.system_permissions(scope_type);
CREATE INDEX IF NOT EXISTS idx_system_permissions_resource ON core_schema.system_permissions(resource);
CREATE INDEX IF NOT EXISTS idx_system_permissions_status ON core_schema.system_permissions(status);

CREATE INDEX IF NOT EXISTS idx_user_system_permissions_user_id ON core_schema.user_system_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_system_permissions_permission_id ON core_schema.user_system_permissions(system_permission_id);

-- 5. 添加表注释
\echo 'Adding table comments...'

COMMENT ON TABLE core_schema.user_system_access IS '用户系统访问权限表，支持HOS全局权限、BOS站点权限、EDC操作权限';
COMMENT ON TABLE core_schema.system_permissions IS '系统权限定义表，为不同系统定义专用权限';
COMMENT ON TABLE core_schema.user_system_permissions IS '用户系统权限分配表，记录用户在各系统中的具体权限';

-- Apply migration 002: Initialize system permissions
\echo 'Initializing system permissions...'

-- 清理可能存在的旧数据
DELETE FROM core_schema.system_permissions WHERE is_system = true;

-- HOS系统权限定义
\echo 'Inserting HOS system permissions...'

INSERT INTO core_schema.system_permissions (system_code, permission_code, permission_name, scope_type, resource, action, description, is_system, status) VALUES
-- 用户管理权限
('HOS', 'user:management:create', '创建用户', 'global', 'user', 'create', 'HOS系统创建用户权限', true, 'active'),
('HOS', 'user:management:read', '查看用户', 'global', 'user', 'read', 'HOS系统查看用户权限', true, 'active'),
('HOS', 'user:management:update', '更新用户', 'global', 'user', 'update', 'HOS系统更新用户权限', true, 'active'),
('HOS', 'user:management:delete', '删除用户', 'global', 'user', 'delete', 'HOS系统删除用户权限', true, 'active'),

-- 站点管理权限
('HOS', 'station:management:create', '创建站点', 'global', 'station', 'create', 'HOS系统创建站点权限', true, 'active'),
('HOS', 'station:management:read', '查看站点', 'global', 'station', 'read', 'HOS系统查看所有站点权限', true, 'active'),
('HOS', 'station:management:update', '更新站点', 'global', 'station', 'update', 'HOS系统更新站点权限', true, 'active'),
('HOS', 'station:management:delete', '删除站点', 'global', 'station', 'delete', 'HOS系统删除站点权限', true, 'active'),

-- 报表分析权限
('HOS', 'report:analytics:view', '查看分析报表', 'global', 'report', 'view', 'HOS系统查看全局分析报表权限', true, 'active'),
('HOS', 'report:analytics:export', '导出分析报表', 'global', 'report', 'export', 'HOS系统导出分析报表权限', true, 'active'),
('HOS', 'report:financial:view', '查看财务报表', 'global', 'report', 'view', 'HOS系统查看财务报表权限', true, 'active'),
('HOS', 'report:financial:export', '导出财务报表', 'global', 'report', 'export', 'HOS系统导出财务报表权限', true, 'active'),

-- 系统配置权限
('HOS', 'system:config:read', '查看系统配置', 'global', 'system', 'read', 'HOS系统查看配置权限', true, 'active'),
('HOS', 'system:config:update', '更新系统配置', 'global', 'system', 'update', 'HOS系统更新配置权限', true, 'active'),
('HOS', 'system:config:manage', '管理系统配置', 'global', 'system', 'manage', 'HOS系统管理配置权限', true, 'active'),

-- 权限管理权限
('HOS', 'permission:management:read', '查看权限', 'global', 'permission', 'read', 'HOS系统查看权限权限', true, 'active'),
('HOS', 'permission:management:update', '分配权限', 'global', 'permission', 'update', 'HOS系统分配权限权限', true, 'active'),
('HOS', 'permission:management:manage', '管理权限', 'global', 'permission', 'manage', 'HOS系统管理权限权限', true, 'active'),

-- 审计日志权限
('HOS', 'audit:log:view', '查看审计日志', 'global', 'audit', 'view', 'HOS系统查看审计日志权限', true, 'active'),
('HOS', 'audit:log:export', '导出审计日志', 'global', 'audit', 'export', 'HOS系统导出审计日志权限', true, 'active');

-- BOS系统权限定义
\echo 'Inserting BOS system permissions...'

INSERT INTO core_schema.system_permissions (system_code, permission_code, permission_name, scope_type, resource, action, description, is_system, status) VALUES
-- 站点管理权限
('BOS', 'station:operation:read', '查看站点运营', 'station', 'station', 'read', 'BOS系统查看站点运营权限', true, 'active'),
('BOS', 'station:operation:update', '更新站点运营', 'station', 'station', 'update', 'BOS系统更新站点运营权限', true, 'active'),
('BOS', 'station:operation:manage', '管理站点运营', 'station', 'station', 'manage', 'BOS系统管理站点运营权限', true, 'active'),

-- 员工管理权限
('BOS', 'employee:management:create', '创建员工', 'station', 'employee', 'create', 'BOS系统创建员工权限', true, 'active'),
('BOS', 'employee:management:read', '查看员工', 'station', 'employee', 'read', 'BOS系统查看员工权限', true, 'active'),
('BOS', 'employee:management:update', '更新员工', 'station', 'employee', 'update', 'BOS系统更新员工权限', true, 'active'),
('BOS', 'employee:management:delete', '删除员工', 'station', 'employee', 'delete', 'BOS系统删除员工权限', true, 'active'),

-- 库存管理权限
('BOS', 'inventory:management:read', '查看库存', 'station', 'inventory', 'read', 'BOS系统查看库存权限', true, 'active'),
('BOS', 'inventory:management:update', '更新库存', 'station', 'inventory', 'update', 'BOS系统更新库存权限', true, 'active'),
('BOS', 'inventory:management:manage', '管理库存', 'station', 'inventory', 'manage', 'BOS系统管理库存权限', true, 'active'),

-- 交易管理权限
('BOS', 'transaction:management:read', '查看交易', 'station', 'transaction', 'read', 'BOS系统查看交易权限', true, 'active'),
('BOS', 'transaction:management:update', '处理交易', 'station', 'transaction', 'update', 'BOS系统处理交易权限', true, 'active'),

-- 报表权限
('BOS', 'report:station:view', '查看站点报表', 'station', 'report', 'view', 'BOS系统查看站点报表权限', true, 'active'),
('BOS', 'report:station:export', '导出站点报表', 'station', 'report', 'export', 'BOS系统导出站点报表权限', true, 'active');

-- EDC系统权限定义
\echo 'Inserting EDC system permissions...'

INSERT INTO core_schema.system_permissions (system_code, permission_code, permission_name, scope_type, resource, action, description, is_system, status) VALUES
-- 交易操作权限
('EDC', 'transaction:operation:create', '创建交易', 'operation', 'transaction', 'create', 'EDC系统创建交易权限', true, 'active'),
('EDC', 'transaction:operation:read', '查看交易', 'operation', 'transaction', 'read', 'EDC系统查看交易权限', true, 'active'),
('EDC', 'transaction:operation:update', '更新交易', 'operation', 'transaction', 'update', 'EDC系统更新交易权限', true, 'active'),

-- 数据查询权限
('EDC', 'data:query:read', '查询数据', 'operation', 'data', 'read', 'EDC系统查询数据权限', true, 'active'),

-- 设备操作权限
('EDC', 'device:operation:execute', '设备操作', 'operation', 'device', 'execute', 'EDC系统设备操作权限', true, 'active'),

-- 基础功能权限
('EDC', 'basic:function:execute', '基础功能', 'operation', 'basic', 'execute', 'EDC系统基础功能权限', true, 'active');

-- 为admin用户创建HOS系统访问权限（如果admin用户存在）
\echo 'Granting HOS access to admin user...'

INSERT INTO core_schema.user_system_access (user_id, system_code, access_level, scope_type, scope_ids, status, granted_at)
SELECT 
    u.id,
    'HOS',
    'admin',
    'global',
    NULL,
    'active',
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'admin'
ON CONFLICT (user_id, system_code) DO NOTHING;

-- 验证迁移结果
\echo 'Verifying migration results...'

DO $$
DECLARE
    hos_count INTEGER;
    bos_count INTEGER;
    edc_count INTEGER;
    admin_access_count INTEGER;
BEGIN
    -- 检查HOS权限数量
    SELECT COUNT(*) INTO hos_count FROM core_schema.system_permissions WHERE system_code = 'HOS' AND is_system = true;
    RAISE NOTICE 'HOS system permissions created: %', hos_count;
    
    -- 检查BOS权限数量
    SELECT COUNT(*) INTO bos_count FROM core_schema.system_permissions WHERE system_code = 'BOS' AND is_system = true;
    RAISE NOTICE 'BOS system permissions created: %', bos_count;
    
    -- 检查EDC权限数量
    SELECT COUNT(*) INTO edc_count FROM core_schema.system_permissions WHERE system_code = 'EDC' AND is_system = true;
    RAISE NOTICE 'EDC system permissions created: %', edc_count;
    
    -- 检查admin用户HOS访问权限
    SELECT COUNT(*) INTO admin_access_count FROM core_schema.user_system_access usa 
    JOIN core_schema.users u ON usa.user_id = u.id 
    WHERE u.username = 'admin' AND usa.system_code = 'HOS';
    RAISE NOTICE 'Admin HOS access granted: %', admin_access_count;
    
    IF hos_count >= 10 AND bos_count >= 10 AND edc_count >= 5 THEN
        RAISE NOTICE 'Unified authentication migration completed successfully!';
    ELSE
        RAISE WARNING 'Migration may be incomplete. Please check the results.';
    END IF;
END $$;

\echo 'Unified authentication migration completed!'
