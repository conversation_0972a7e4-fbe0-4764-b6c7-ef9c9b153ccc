-- Deploy Complete Test Data for Unified Authentication System
-- 部署统一身份认证系统完整测试数据
-- Date: 2025-01-20

\echo '=========================================='
\echo '  Unified Authentication Test Data Setup'
\echo '=========================================='
\echo ''

-- Set search path
SET search_path TO core_schema, public;

-- Start transaction
BEGIN;

\echo 'Step 1: Creating test users...'
\i scripts/create_test_users.sql

\echo ''
\echo 'Step 2: Creating test stations...'
\i scripts/create_test_stations.sql

\echo ''
\echo 'Step 3: Assigning test permissions...'
\i scripts/assign_test_permissions.sql

-- =====================================================
-- Final Verification and Summary
-- =====================================================

\echo ''
\echo '=========================================='
\echo '  Test Data Summary'
\echo '=========================================='

-- 用户账号汇总
\echo ''
\echo 'TEST USERS SUMMARY:'
\echo '==================='
SELECT 
    u.username,
    u.full_name,
    u.email,
    CASE 
        WHEN u.username LIKE 'hos_%' THEN 'HOS System User'
        WHEN u.username LIKE 'bos_%' THEN 'BOS System User'
        WHEN u.username LIKE 'edc_%' THEN 'EDC System User'
        WHEN u.username = 'multi_user' THEN 'Multi-System User'
        ELSE 'Other'
    END as user_category,
    u.status
FROM core_schema.users u
WHERE u.username IN (
    'hos_admin', 'hos_manager', 'bos_manager', 'bos_supervisor', 
    'edc_supervisor', 'edc_operator', 'multi_user'
)
ORDER BY 
    CASE 
        WHEN u.username LIKE 'hos_%' THEN 1
        WHEN u.username LIKE 'bos_%' THEN 2
        WHEN u.username LIKE 'edc_%' THEN 3
        WHEN u.username = 'multi_user' THEN 4
        ELSE 5
    END,
    u.username;

-- 站点信息汇总
\echo ''
\echo 'TEST STATIONS SUMMARY:'
\echo '======================'
SELECT 
    s.id,
    s.site_code,
    s.site_name,
    sa.city || ', ' || sa.state_province as location,
    s.status,
    COUNT(se.id) as equipment_count
FROM core_schema.stations s
LEFT JOIN core_schema.station_addresses sa ON s.id = sa.station_id AND sa.address_type = 'primary'
LEFT JOIN core_schema.station_equipment se ON s.id = se.station_id
WHERE s.site_code IN ('TEST001', 'TEST002')
GROUP BY s.id, s.site_code, s.site_name, sa.city, sa.state_province, s.status
ORDER BY s.site_code;

-- HOS权限汇总
\echo ''
\echo 'HOS SYSTEM PERMISSIONS:'
\echo '======================='
SELECT 
    u.username,
    u.full_name,
    usa.access_level,
    usa.scope_type,
    usa.status as permission_status
FROM core_schema.user_system_access usa
JOIN core_schema.users u ON usa.user_id = u.id
WHERE usa.system_code = 'HOS'
ORDER BY 
    CASE usa.access_level 
        WHEN 'admin' THEN 1 
        WHEN 'manager' THEN 2 
        ELSE 3 
    END,
    u.username;

-- BOS/EDC站点角色汇总
\echo ''
\echo 'BOS/EDC STATION ROLES:'
\echo '======================'
SELECT 
    u.username,
    u.full_name,
    s.site_code,
    s.site_name,
    usr.role_type,
    CASE 
        WHEN u.username LIKE 'bos_%' THEN 'BOS System'
        WHEN u.username LIKE 'edc_%' THEN 'EDC System'
        WHEN u.username = 'multi_user' THEN 'Multi-System'
        ELSE 'Other'
    END as intended_system,
    usr.status as role_status
FROM core_schema.user_station_roles usr
JOIN core_schema.users u ON usr.user_id = u.id
JOIN core_schema.stations s ON usr.station_id = s.id
WHERE s.site_code IN ('TEST001', 'TEST002')
ORDER BY 
    CASE 
        WHEN u.username LIKE 'bos_%' THEN 1
        WHEN u.username LIKE 'edc_%' THEN 2
        WHEN u.username = 'multi_user' THEN 3
        ELSE 4
    END,
    u.username,
    s.site_code;

-- 最终验证
\echo ''
\echo 'FINAL VERIFICATION:'
\echo '==================='

DO $$
DECLARE
    user_count INTEGER;
    station_count INTEGER;
    hos_permission_count INTEGER;
    station_role_count INTEGER;
    all_good BOOLEAN := true;
BEGIN
    -- 检查用户数量
    SELECT COUNT(*) INTO user_count 
    FROM core_schema.users 
    WHERE username IN ('hos_admin', 'hos_manager', 'bos_manager', 'bos_supervisor', 'edc_supervisor', 'edc_operator', 'multi_user');
    
    -- 检查站点数量
    SELECT COUNT(*) INTO station_count 
    FROM core_schema.stations 
    WHERE site_code IN ('TEST001', 'TEST002');
    
    -- 检查HOS权限数量
    SELECT COUNT(*) INTO hos_permission_count 
    FROM core_schema.user_system_access usa
    JOIN core_schema.users u ON usa.user_id = u.id
    WHERE u.username IN ('hos_admin', 'hos_manager', 'multi_user') AND usa.system_code = 'HOS';
    
    -- 检查站点角色数量
    SELECT COUNT(*) INTO station_role_count 
    FROM core_schema.user_station_roles usr
    JOIN core_schema.users u ON usr.user_id = u.id
    JOIN core_schema.stations s ON usr.station_id = s.id
    WHERE u.username IN ('bos_manager', 'bos_supervisor', 'edc_supervisor', 'edc_operator', 'multi_user')
    AND s.site_code IN ('TEST001', 'TEST002');
    
    -- 验证结果
    RAISE NOTICE 'Users created: % (expected: 7)', user_count;
    RAISE NOTICE 'Stations created: % (expected: 2)', station_count;
    RAISE NOTICE 'HOS permissions assigned: % (expected: 3)', hos_permission_count;
    RAISE NOTICE 'Station roles assigned: % (expected: 6)', station_role_count;
    
    IF user_count != 7 THEN all_good := false; END IF;
    IF station_count != 2 THEN all_good := false; END IF;
    IF hos_permission_count != 3 THEN all_good := false; END IF;
    IF station_role_count != 6 THEN all_good := false; END IF;
    
    IF all_good THEN
        RAISE NOTICE '';
        RAISE NOTICE '✅ ALL TEST DATA DEPLOYED SUCCESSFULLY!';
        RAISE NOTICE '';
        RAISE NOTICE 'Test credentials for all users:';
        RAISE NOTICE '  Username: [hos_admin|hos_manager|bos_manager|bos_supervisor|edc_supervisor|edc_operator|multi_user]';
        RAISE NOTICE '  Password: password';
        RAISE NOTICE '';
        RAISE NOTICE 'Ready for login testing!';
    ELSE
        RAISE WARNING '';
        RAISE WARNING '❌ SOME TEST DATA MAY BE INCOMPLETE!';
        RAISE WARNING 'Please check the verification results above.';
    END IF;
END $$;

-- Commit transaction
COMMIT;

\echo ''
\echo '=========================================='
\echo '  Test Data Deployment Completed'
\echo '=========================================='
