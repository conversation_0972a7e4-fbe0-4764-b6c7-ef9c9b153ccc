-- Create Test Stations for Unified Authentication System
-- 为统一身份认证系统创建测试站点
-- Date: 2025-01-20

\echo 'Creating test stations for unified authentication system...'

-- Set search path
SET search_path TO core_schema, public;

-- =====================================================
-- 1. Create Test Stations
-- =====================================================

-- 清理可能存在的测试站点
DELETE FROM core_schema.stations WHERE site_code IN ('TEST001', 'TEST002');

-- 1.1 测试站点1 - 雅加达中央站
INSERT INTO core_schema.stations (
    id, site_code, site_name, status, created_at, updated_at
) VALUES (
    1001,
    'TEST001',
    'Jakarta Central Test Station',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 1.2 测试站点2 - 万隆北站
INSERT INTO core_schema.stations (
    id, site_code, site_name, status, created_at, updated_at
) VALUES (
    1002,
    'TEST002',
    'Bandung North Test Station',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. Create Station Addresses
-- =====================================================

-- 为测试站点创建地址信息
INSERT INTO core_schema.station_addresses (
    id, station_id, address_type, street_address, city, state_province, 
    postal_code, country, latitude, longitude, created_at, updated_at
) VALUES 
-- 雅加达中央站地址
(
    gen_random_uuid(),
    1001,
    'primary',
    'Jl. Sudirman No. 123',
    'Jakarta',
    'DKI Jakarta',
    '10220',
    'Indonesia',
    -6.2088,
    106.8456,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
-- 万隆北站地址
(
    gen_random_uuid(),
    1002,
    'primary',
    'Jl. Asia Afrika No. 456',
    'Bandung',
    'West Java',
    '40111',
    'Indonesia',
    -6.9175,
    107.6191,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. Create Station Equipment (Optional)
-- =====================================================

-- 为测试站点创建基础设备信息
INSERT INTO core_schema.station_equipment (
    id, station_id, equipment_type, equipment_name, model, serial_number, 
    status, installation_date, created_at, updated_at
) VALUES 
-- 雅加达中央站设备
(
    gen_random_uuid(),
    1001,
    'fuel_dispenser',
    'Fuel Dispenser 1',
    'FD-2000',
    'FD001-TEST-001',
    'active',
    CURRENT_DATE - INTERVAL '30 days',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),
    1001,
    'pos_terminal',
    'POS Terminal 1',
    'PT-500',
    'PT001-TEST-001',
    'active',
    CURRENT_DATE - INTERVAL '30 days',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
-- 万隆北站设备
(
    gen_random_uuid(),
    1002,
    'fuel_dispenser',
    'Fuel Dispenser 1',
    'FD-2000',
    'FD002-TEST-001',
    'active',
    CURRENT_DATE - INTERVAL '25 days',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
),
(
    gen_random_uuid(),
    1002,
    'pos_terminal',
    'POS Terminal 1',
    'PT-500',
    'PT002-TEST-001',
    'active',
    CURRENT_DATE - INTERVAL '25 days',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. Verify Station Creation
-- =====================================================

-- 验证站点创建结果
DO $$
DECLARE
    station_count INTEGER;
    address_count INTEGER;
    equipment_count INTEGER;
BEGIN
    -- 检查站点数量
    SELECT COUNT(*) INTO station_count 
    FROM core_schema.stations 
    WHERE site_code IN ('TEST001', 'TEST002');
    
    -- 检查地址数量
    SELECT COUNT(*) INTO address_count 
    FROM core_schema.station_addresses sa
    JOIN core_schema.stations s ON sa.station_id = s.id
    WHERE s.site_code IN ('TEST001', 'TEST002');
    
    -- 检查设备数量
    SELECT COUNT(*) INTO equipment_count 
    FROM core_schema.station_equipment se
    JOIN core_schema.stations s ON se.station_id = s.id
    WHERE s.site_code IN ('TEST001', 'TEST002');
    
    RAISE NOTICE 'Created % test stations', station_count;
    RAISE NOTICE 'Created % station addresses', address_count;
    RAISE NOTICE 'Created % station equipment records', equipment_count;
    
    IF station_count = 2 AND address_count = 2 AND equipment_count = 4 THEN
        RAISE NOTICE 'All test station data created successfully!';
    ELSE
        RAISE WARNING 'Some test station data may be missing';
    END IF;
END $$;

-- 显示创建的站点列表
\echo 'Test stations created:'
SELECT 
    s.id,
    s.site_code,
    s.site_name,
    s.status,
    sa.city,
    sa.state_province,
    COUNT(se.id) as equipment_count
FROM core_schema.stations s
LEFT JOIN core_schema.station_addresses sa ON s.id = sa.station_id
LEFT JOIN core_schema.station_equipment se ON s.id = se.station_id
WHERE s.site_code IN ('TEST001', 'TEST002')
GROUP BY s.id, s.site_code, s.site_name, s.status, sa.city, sa.state_province
ORDER BY s.site_code;

\echo 'Test stations creation completed!'
