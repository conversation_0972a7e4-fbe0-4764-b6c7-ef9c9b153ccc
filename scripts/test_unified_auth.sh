#!/bin/bash

# Test Unified Authentication System
# This script tests the unified authentication API for HOS, BOS, and EDC systems

set -e

# Configuration
BASE_URL="http://localhost:8080/api/v1"
ADMIN_USERNAME="admin"
ADMIN_PASSWORD="Admin123"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local token=$4
    
    local headers="Content-Type: application/json"
    if [ ! -z "$token" ]; then
        headers="$headers -H Authorization: Bearer $token"
    fi
    
    curl -s -X "$method" \
        -H "$headers" \
        ${data:+-d "$data"} \
        "$BASE_URL$endpoint"
}

# Function to extract field from JSON response
extract_field() {
    local json=$1
    local field=$2
    echo "$json" | grep -o "\"$field\":\"[^\"]*\"" | cut -d'"' -f4
}

# Function to test login for a specific system
test_login() {
    local system=$1
    local username=$2
    local password=$3
    
    print_status "Testing $system system login..."
    
    local login_data="{
        \"username\": \"$username\",
        \"password\": \"$password\",
        \"system\": \"$system\",
        \"authType\": \"local\"
    }"
    
    local response=$(api_call "POST" "/auth/login" "$login_data")
    local code=$(echo "$response" | grep -o '"code":[0-9]*' | cut -d':' -f2)
    
    if [ "$code" = "200" ]; then
        local access_token=$(extract_field "$response" "accessToken")
        local system_access=$(echo "$response" | grep -o '"system":"[^"]*"' | cut -d'"' -f4)
        local access_level=$(echo "$response" | grep -o '"accessLevel":"[^"]*"' | cut -d'"' -f4)
        local scope_type=$(echo "$response" | grep -o '"scopeType":"[^"]*"' | cut -d'"' -f4)
        
        print_success "$system login successful"
        print_status "  System: $system_access"
        print_status "  Access Level: $access_level"
        print_status "  Scope Type: $scope_type"
        
        # Test getting current user info
        print_status "Testing /auth/me endpoint..."
        local me_response=$(api_call "GET" "/auth/me" "" "$access_token")
        local me_code=$(echo "$me_response" | grep -o '"code":[0-9]*' | cut -d':' -f2)
        
        if [ "$me_code" = "200" ]; then
            print_success "/auth/me endpoint working"
        else
            print_error "/auth/me endpoint failed"
        fi
        
        # Test logout
        print_status "Testing logout..."
        local logout_response=$(api_call "POST" "/auth/logout" "" "$access_token")
        local logout_code=$(echo "$logout_response" | grep -o '"code":[0-9]*' | cut -d':' -f2)
        
        if [ "$logout_code" = "200" ]; then
            print_success "Logout successful"
        else
            print_error "Logout failed"
        fi
        
        echo ""
        return 0
    else
        local message=$(extract_field "$response" "message")
        print_error "$system login failed: $message"
        echo ""
        return 1
    fi
}

# Function to test invalid login
test_invalid_login() {
    print_status "Testing invalid login scenarios..."
    
    # Test with invalid system
    local invalid_system_data="{
        \"username\": \"$ADMIN_USERNAME\",
        \"password\": \"$ADMIN_PASSWORD\",
        \"system\": \"INVALID\",
        \"authType\": \"local\"
    }"
    
    local response=$(api_call "POST" "/auth/login" "$invalid_system_data")
    local code=$(echo "$response" | grep -o '"code":[0-9]*' | cut -d':' -f2)
    
    if [ "$code" != "200" ]; then
        print_success "Invalid system rejection working"
    else
        print_error "Invalid system should be rejected"
    fi
    
    # Test with wrong password
    local wrong_password_data="{
        \"username\": \"$ADMIN_USERNAME\",
        \"password\": \"WrongPassword\",
        \"system\": \"HOS\",
        \"authType\": \"local\"
    }"
    
    local response=$(api_call "POST" "/auth/login" "$wrong_password_data")
    local code=$(echo "$response" | grep -o '"code":[0-9]*' | cut -d':' -f2)
    
    if [ "$code" != "200" ]; then
        print_success "Wrong password rejection working"
    else
        print_error "Wrong password should be rejected"
    fi
    
    echo ""
}

# Function to check API health
check_api_health() {
    print_status "Checking API health..."
    
    local health_response=$(curl -s "$BASE_URL/../health" || echo "")
    
    if [ ! -z "$health_response" ]; then
        print_success "API is responding"
    else
        print_error "API is not responding"
        exit 1
    fi
    
    echo ""
}

# Main test execution
main() {
    echo "=========================================="
    echo "  Unified Authentication System Test"
    echo "=========================================="
    echo ""
    
    # Check if API is running
    check_api_health
    
    # Test HOS system login
    test_login "HOS" "$ADMIN_USERNAME" "$ADMIN_PASSWORD"
    
    # Test BOS system login (may fail if admin doesn't have station roles)
    print_warning "BOS login may fail if admin user doesn't have station roles"
    test_login "BOS" "$ADMIN_USERNAME" "$ADMIN_PASSWORD" || true
    
    # Test EDC system login (may fail if admin doesn't have station roles)
    print_warning "EDC login may fail if admin user doesn't have station roles"
    test_login "EDC" "$ADMIN_USERNAME" "$ADMIN_PASSWORD" || true
    
    # Test invalid login scenarios
    test_invalid_login
    
    echo "=========================================="
    echo "  Test Summary"
    echo "=========================================="
    print_success "Unified authentication system tests completed!"
    print_status "Note: BOS and EDC tests may fail if the admin user doesn't have station role assignments."
    print_status "This is expected behavior as these systems require station-level permissions."
    echo ""
}

# Run tests
main "$@"
