-- Create Test Users for Unified Authentication System
-- 为统一身份认证系统创建测试用户
-- Date: 2025-01-20

\echo 'Creating test users for unified authentication system...'

-- Set search path
SET search_path TO core_schema, public;

-- =====================================================
-- 1. Create Test Users
-- =====================================================

-- 清理可能存在的测试用户
DELETE FROM core_schema.users WHERE username IN (
    'hos_admin', 'hos_manager', 'bos_manager', 'bos_supervisor', 
    'edc_supervisor', 'edc_operator', 'multi_user'
);

-- 1.1 HOS系统用户
-- HOS总部管理员
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'hos_admin',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'HOS System Administrator',
    '+62-21-1234-5001',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- HOS区域经理
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'hos_manager',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'HOS Regional Manager',
    '+62-21-1234-5002',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 1.2 BOS系统用户
-- BOS站长
INSERT INTO core_schema.users (
    id, username, email, password_hash, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'bos_manager',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'BOS Station Manager',
    '+62-21-1234-5003',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- BOS副站长
INSERT INTO core_schema.users (
    id, username, email, password_hash, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'bos_supervisor',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'BOS Station Supervisor',
    '+62-21-1234-5004',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 1.3 EDC系统用户
-- EDC主管
INSERT INTO core_schema.users (
    id, username, email, password_hash, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'edc_supervisor',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'EDC System Supervisor',
    '+62-21-1234-5005',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- EDC操作员
INSERT INTO core_schema.users (
    id, username, email, password_hash, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'edc_operator',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'EDC System Operator',
    '+62-21-1234-5006',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 1.4 混合权限用户
-- 同时拥有多个系统访问权限的用户
INSERT INTO core_schema.users (
    id, username, email, password_hash, full_name, phone, status,
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'multi_user',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'Multi-System User',
    '+62-21-1234-5007',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. Verify User Creation
-- =====================================================

-- 验证用户创建结果
DO $$
DECLARE
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count 
    FROM core_schema.users 
    WHERE username IN (
        'hos_admin', 'hos_manager', 'bos_manager', 'bos_supervisor', 
        'edc_supervisor', 'edc_operator', 'multi_user'
    );
    
    IF user_count = 7 THEN
        RAISE NOTICE 'Successfully created % test users', user_count;
    ELSE
        RAISE WARNING 'Expected 7 users, but created %', user_count;
    END IF;
END $$;

-- 显示创建的用户列表
\echo 'Test users created:'
SELECT 
    username,
    full_name,
    email,
    status,
    created_at
FROM core_schema.users 
WHERE username IN (
    'hos_admin', 'hos_manager', 'bos_manager', 'bos_supervisor', 
    'edc_supervisor', 'edc_operator', 'multi_user'
)
ORDER BY username;

\echo 'Test users creation completed!'
