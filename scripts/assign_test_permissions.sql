-- Assign Test Permissions for Unified Authentication System
-- 为统一身份认证系统分配测试权限
-- Date: 2025-01-20

\echo 'Assigning test permissions for unified authentication system...'

-- Set search path
SET search_path TO core_schema, public;

-- =====================================================
-- 1. Assign HOS System Permissions
-- =====================================================

\echo 'Assigning HOS system permissions...'

-- 清理可能存在的测试权限
DELETE FROM core_schema.user_system_access 
WHERE user_id IN (
    SELECT id FROM core_schema.users 
    WHERE username IN ('hos_admin', 'hos_manager', 'multi_user')
);

-- 1.1 HOS总部管理员 - 全局管理权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status, granted_at
)
SELECT 
    u.id,
    'HOS',
    'admin',
    'global',
    NULL,
    'active',
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'hos_admin';

-- 1.2 HOS区域经理 - 全局经理权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status, granted_at
)
SELECT 
    u.id,
    'HOS',
    'manager',
    'global',
    NULL,
    'active',
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'hos_manager';

-- 1.3 混合权限用户 - HOS管理权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status, granted_at
)
SELECT 
    u.id,
    'HOS',
    'manager',
    'global',
    NULL,
    'active',
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'multi_user';

-- =====================================================
-- 2. Assign BOS/EDC Station Roles
-- =====================================================

\echo 'Assigning BOS/EDC station roles...'

-- 清理可能存在的测试站点角色
DELETE FROM core_schema.user_station_roles 
WHERE user_id IN (
    SELECT id FROM core_schema.users 
    WHERE username IN ('bos_manager', 'bos_supervisor', 'edc_supervisor', 'edc_operator', 'multi_user')
);

-- 2.1 BOS站长 - 雅加达中央站管理权限
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, assigned_at, created_at, updated_at
)
SELECT 
    gen_random_uuid(),
    u.id,
    1001, -- Jakarta Central Test Station
    'manager',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'bos_manager';

-- 2.2 BOS副站长 - 万隆北站监督权限
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, assigned_at, created_at, updated_at
)
SELECT 
    gen_random_uuid(),
    u.id,
    1002, -- Bandung North Test Station
    'supervisor',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'bos_supervisor';

-- 2.3 EDC主管 - 雅加达中央站监督权限
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, assigned_at, created_at, updated_at
)
SELECT 
    gen_random_uuid(),
    u.id,
    1001, -- Jakarta Central Test Station
    'supervisor',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'edc_supervisor';

-- 2.4 EDC操作员 - 万隆北站操作权限
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, assigned_at, created_at, updated_at
)
SELECT 
    gen_random_uuid(),
    u.id,
    1002, -- Bandung North Test Station
    'operator',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'edc_operator';

-- 2.5 混合权限用户 - 两个站点的管理权限
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, assigned_at, created_at, updated_at
)
SELECT 
    gen_random_uuid(),
    u.id,
    s.id,
    'manager',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM core_schema.users u 
CROSS JOIN core_schema.stations s
WHERE u.username = 'multi_user' 
AND s.site_code IN ('TEST001', 'TEST002');

-- =====================================================
-- 3. Verify Permission Assignment
-- =====================================================

-- 验证权限分配结果
DO $$
DECLARE
    hos_permissions INTEGER;
    station_roles INTEGER;
BEGIN
    -- 检查HOS系统权限数量
    SELECT COUNT(*) INTO hos_permissions 
    FROM core_schema.user_system_access usa
    JOIN core_schema.users u ON usa.user_id = u.id
    WHERE u.username IN ('hos_admin', 'hos_manager', 'multi_user')
    AND usa.system_code = 'HOS';
    
    -- 检查站点角色数量
    SELECT COUNT(*) INTO station_roles 
    FROM core_schema.user_station_roles usr
    JOIN core_schema.users u ON usr.user_id = u.id
    WHERE u.username IN ('bos_manager', 'bos_supervisor', 'edc_supervisor', 'edc_operator', 'multi_user');
    
    RAISE NOTICE 'Assigned % HOS system permissions', hos_permissions;
    RAISE NOTICE 'Assigned % station roles', station_roles;
    
    IF hos_permissions = 3 AND station_roles = 6 THEN
        RAISE NOTICE 'All test permissions assigned successfully!';
    ELSE
        RAISE WARNING 'Some test permissions may be missing. Expected: 3 HOS permissions, 6 station roles';
    END IF;
END $$;

-- 显示HOS系统权限分配
\echo 'HOS System Permissions:'
SELECT 
    u.username,
    u.full_name,
    usa.system_code,
    usa.access_level,
    usa.scope_type,
    usa.status
FROM core_schema.user_system_access usa
JOIN core_schema.users u ON usa.user_id = u.id
WHERE usa.system_code = 'HOS'
ORDER BY u.username;

-- 显示站点角色分配
\echo 'Station Role Assignments:'
SELECT 
    u.username,
    u.full_name,
    s.site_code,
    s.site_name,
    usr.role_type,
    usr.status
FROM core_schema.user_station_roles usr
JOIN core_schema.users u ON usr.user_id = u.id
JOIN core_schema.stations s ON usr.station_id = s.id
WHERE s.site_code IN ('TEST001', 'TEST002')
ORDER BY u.username, s.site_code;

\echo 'Test permissions assignment completed!'
