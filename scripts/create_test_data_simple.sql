-- Simple Test Data Creation for Unified Authentication System
-- 简化的统一身份认证系统测试数据创建
-- Date: 2025-01-20

\echo 'Creating test data for unified authentication system...'

-- Set search path
SET search_path TO core_schema, public;

-- =====================================================
-- 1. Clean up existing test data
-- =====================================================

\echo 'Cleaning up existing test data...'

-- 清理测试用户
DELETE FROM core_schema.users WHERE username IN (
    'hos_admin', 'hos_manager', 'bos_manager', 'bos_supervisor', 
    'edc_supervisor', 'edc_operator', 'multi_user'
);

-- 清理测试站点
DELETE FROM core_schema.stations WHERE site_code IN ('TEST001', 'TEST002');

-- =====================================================
-- 2. Create Test Users
-- =====================================================

\echo 'Creating test users...'

-- HOS总部管理员
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'hos_admin',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'HOS System Administrator',
    '+62-21-1234-5001',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- HOS区域经理
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'hos_manager',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'HOS Regional Manager',
    '+62-21-1234-5002',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- BOS站长
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'bos_manager',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'BOS Station Manager',
    '+62-21-1234-5003',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- BOS副站长
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'bos_supervisor',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'BOS Station Supervisor',
    '+62-21-1234-5004',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- EDC主管
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'edc_supervisor',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'EDC System Supervisor',
    '+62-21-1234-5005',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- EDC操作员
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'edc_operator',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'EDC System Operator',
    '+62-21-1234-5006',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 混合权限用户
INSERT INTO core_schema.users (
    id, username, email, password_hash, salt, full_name, phone, status, 
    created_at, updated_at
) VALUES (
    gen_random_uuid(),
    'multi_user',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'test_salt',
    'Multi-System User',
    '+62-21-1234-5007',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. Create Test Stations
-- =====================================================

\echo 'Creating test stations...'

-- 测试站点1
INSERT INTO core_schema.stations (
    id, site_code, site_name, address, business_status, created_at, updated_at
) VALUES (
    1001,
    'TEST001',
    'Jakarta Central Test Station',
    '{"street": "Jl. Sudirman No. 123", "city": "Jakarta", "country": "Indonesia"}',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 测试站点2
INSERT INTO core_schema.stations (
    id, site_code, site_name, address, business_status, created_at, updated_at
) VALUES (
    1002,
    'TEST002',
    'Bandung North Test Station',
    '{"street": "Jl. Asia Afrika No. 456", "city": "Bandung", "country": "Indonesia"}',
    'active',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. Assign HOS System Permissions
-- =====================================================

\echo 'Assigning HOS system permissions...'

-- HOS总部管理员权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status, granted_at
)
SELECT 
    u.id, 'HOS', 'admin', 'global', NULL, 'active', CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'hos_admin';

-- HOS区域经理权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status, granted_at
)
SELECT 
    u.id, 'HOS', 'manager', 'global', NULL, 'active', CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'hos_manager';

-- 混合权限用户HOS权限
INSERT INTO core_schema.user_system_access (
    user_id, system_code, access_level, scope_type, scope_ids, status, granted_at
)
SELECT 
    u.id, 'HOS', 'manager', 'global', NULL, 'active', CURRENT_TIMESTAMP
FROM core_schema.users u 
WHERE u.username = 'multi_user';

-- =====================================================
-- 5. Assign BOS/EDC Station Roles
-- =====================================================

\echo 'Assigning BOS/EDC station roles...'

-- BOS站长角色
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, granted_at, created_at, updated_at
)
SELECT
    gen_random_uuid(), u.id, 1001, 'manager', 'active',
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM core_schema.users u
WHERE u.username = 'bos_manager';

-- BOS副站长角色
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, granted_at, created_at, updated_at
)
SELECT
    gen_random_uuid(), u.id, 1002, 'supervisor', 'active',
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM core_schema.users u
WHERE u.username = 'bos_supervisor';

-- EDC主管角色
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, granted_at, created_at, updated_at
)
SELECT
    gen_random_uuid(), u.id, 1001, 'supervisor', 'active',
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM core_schema.users u
WHERE u.username = 'edc_supervisor';

-- EDC操作员角色
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, granted_at, created_at, updated_at
)
SELECT
    gen_random_uuid(), u.id, 1002, 'operator', 'active',
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM core_schema.users u
WHERE u.username = 'edc_operator';

-- 混合权限用户站点角色
INSERT INTO core_schema.user_station_roles (
    id, user_id, station_id, role_type, status, granted_at, created_at, updated_at
)
SELECT
    gen_random_uuid(), u.id, s.id, 'manager', 'active',
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
FROM core_schema.users u
CROSS JOIN core_schema.stations s
WHERE u.username = 'multi_user'
AND s.site_code IN ('TEST001', 'TEST002');

\echo 'Test data creation completed successfully!'
