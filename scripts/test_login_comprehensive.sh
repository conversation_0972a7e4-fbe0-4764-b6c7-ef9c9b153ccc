#!/bin/bash

# Comprehensive Login Test for Unified Authentication System
# 统一身份认证系统全面登录测试
# Date: 2025-01-20

set -e

BASE_URL="http://localhost:8080"
API_URL="${BASE_URL}/api/v1/auth/login"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

echo "=========================================="
echo "  Unified Authentication Login Tests"
echo "=========================================="
echo ""

# Function to test login
test_login() {
    local username=$1
    local password=$2
    local system=$3
    local expected_result=$4
    local test_description=$5
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}Test ${TOTAL_TESTS}: ${test_description}${NC}"
    echo "  Username: ${username}"
    echo "  System: ${system}"
    echo "  Expected: ${expected_result}"
    
    # Make login request
    response=$(curl -s -w "\n%{http_code}" -X POST "${API_URL}" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"${username}\", \"password\": \"${password}\", \"system\": \"${system}\"}")
    
    # Extract HTTP status code and response body
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    # Check result
    if [ "$expected_result" = "SUCCESS" ]; then
        if [ "$http_code" = "200" ]; then
            # Check if response contains error code
            error_code=$(echo "$response_body" | grep -o '"code":[0-9]*' | cut -d':' -f2)
            if [ "$error_code" = "0" ] || [ -z "$error_code" ]; then
                # Extract key information from response
                access_token=$(echo "$response_body" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
                access_level=$(echo "$response_body" | grep -o '"accessLevel":"[^"]*"' | cut -d'"' -f4)
                scope_type=$(echo "$response_body" | grep -o '"scopeType":"[^"]*"' | cut -d'"' -f4)
                station_count=$(echo "$response_body" | grep -o '"stationCount":[0-9]*' | cut -d':' -f2)

                echo -e "  ${GREEN}✓ PASSED${NC}"
                echo "    HTTP Code: ${http_code}"
                echo "    Access Level: ${access_level}"
                echo "    Scope Type: ${scope_type}"
                echo "    Station Count: ${station_count}"
                echo "    Token Length: ${#access_token}"
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "  ${RED}✗ FAILED${NC}"
                echo "    Expected: Success, Got: Error Code ${error_code}"
                echo "    Response: ${response_body}"
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            echo -e "  ${RED}✗ FAILED${NC}"
            echo "    Expected: HTTP 200, Got: ${http_code}"
            echo "    Response: ${response_body}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        if [ "$http_code" = "200" ]; then
            # Check if response contains error code (expected for failures)
            error_code=$(echo "$response_body" | grep -o '"code":[0-9]*' | cut -d':' -f2)
            if [ "$error_code" != "0" ] && [ -n "$error_code" ]; then
                echo -e "  ${GREEN}✓ PASSED${NC}"
                echo "    HTTP Code: ${http_code}, Error Code: ${error_code} (Expected failure)"
                echo "    Response: ${response_body}"
                PASSED_TESTS=$((PASSED_TESTS + 1))
            else
                echo -e "  ${RED}✗ FAILED${NC}"
                echo "    Expected: Failure, Got: Success"
                echo "    Response: ${response_body}"
                FAILED_TESTS=$((FAILED_TESTS + 1))
            fi
        else
            echo -e "  ${GREEN}✓ PASSED${NC}"
            echo "    HTTP Code: ${http_code} (Expected failure)"
            echo "    Response: ${response_body}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        fi
    fi
    
    echo ""
}

# Function to test unauthorized access
test_unauthorized_access() {
    local username=$1
    local system=$2
    local test_description=$3
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "${BLUE}Test ${TOTAL_TESTS}: ${test_description}${NC}"
    echo "  Username: ${username}"
    echo "  System: ${system}"
    echo "  Expected: FAILURE (Unauthorized)"
    
    # Make login request
    response=$(curl -s -w "\n%{http_code}" -X POST "${API_URL}" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"${username}\", \"password\": \"password\", \"system\": \"${system}\"}")
    
    # Extract HTTP status code and response body
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    # Check result (should fail)
    if [ "$http_code" = "200" ]; then
        # Check if response contains error code (expected for failures)
        error_code=$(echo "$response_body" | grep -o '"code":[0-9]*' | cut -d':' -f2)
        if [ "$error_code" != "0" ] && [ -n "$error_code" ]; then
            echo -e "  ${GREEN}✓ PASSED${NC}"
            echo "    HTTP Code: ${http_code}, Error Code: ${error_code} (Expected failure)"
            echo "    Response: ${response_body}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "  ${RED}✗ FAILED${NC}"
            echo "    Expected: Failure, Got: Success"
            echo "    Response: ${response_body}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "  ${GREEN}✓ PASSED${NC}"
        echo "    HTTP Code: ${http_code} (Expected failure)"
        echo "    Response: ${response_body}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
    
    echo ""
}

# Check if API is available
echo "Checking API availability..."
if ! curl -s "${BASE_URL}/api/v1/auth/health" > /dev/null; then
    echo -e "${RED}ERROR: API is not available at ${BASE_URL}${NC}"
    exit 1
fi
echo -e "${GREEN}✓ API is available${NC}"
echo ""

# =====================================================
# HOS System Tests
# =====================================================

echo -e "${YELLOW}=== HOS System Tests ===${NC}"
echo ""

test_login "hos_admin" "password" "HOS" "SUCCESS" "HOS Admin Login"
test_login "hos_manager" "password" "HOS" "SUCCESS" "HOS Manager Login"
test_login "multi_user" "password" "HOS" "SUCCESS" "Multi-User HOS Access"

# Test unauthorized HOS access
test_unauthorized_access "bos_manager" "HOS" "BOS Manager trying HOS access (should fail)"
test_unauthorized_access "edc_operator" "HOS" "EDC Operator trying HOS access (should fail)"

# =====================================================
# BOS System Tests
# =====================================================

echo -e "${YELLOW}=== BOS System Tests ===${NC}"
echo ""

test_login "bos_manager" "password" "BOS" "SUCCESS" "BOS Manager Login"
test_login "bos_supervisor" "password" "BOS" "SUCCESS" "BOS Supervisor Login"
test_login "multi_user" "password" "BOS" "SUCCESS" "Multi-User BOS Access"

# Test unauthorized BOS access
test_unauthorized_access "edc_operator" "BOS" "EDC Operator trying BOS access (should fail)"

# =====================================================
# EDC System Tests
# =====================================================

echo -e "${YELLOW}=== EDC System Tests ===${NC}"
echo ""

test_login "edc_supervisor" "password" "EDC" "SUCCESS" "EDC Supervisor Login"
test_login "edc_operator" "password" "EDC" "SUCCESS" "EDC Operator Login"
test_login "multi_user" "password" "EDC" "SUCCESS" "Multi-User EDC Access"

# =====================================================
# Invalid Credentials Tests
# =====================================================

echo -e "${YELLOW}=== Invalid Credentials Tests ===${NC}"
echo ""

test_login "hos_admin" "wrongpassword" "HOS" "FAILURE" "HOS Admin with wrong password"
test_login "nonexistent" "password" "BOS" "FAILURE" "Non-existent user"
test_login "bos_manager" "password" "INVALID" "FAILURE" "Valid user with invalid system"

# =====================================================
# Test Results Summary
# =====================================================

echo "=========================================="
echo "  Test Results Summary"
echo "=========================================="
echo ""
echo "Total Tests: ${TOTAL_TESTS}"
echo -e "Passed: ${GREEN}${PASSED_TESTS}${NC}"
echo -e "Failed: ${RED}${FAILED_TESTS}${NC}"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED!${NC}"
    echo "The unified authentication system is working correctly."
    exit 0
else
    echo -e "${RED}❌ SOME TESTS FAILED!${NC}"
    echo "Please check the failed tests above."
    exit 1
fi
