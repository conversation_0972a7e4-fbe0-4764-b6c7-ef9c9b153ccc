#!/bin/bash

# 数据库迁移脚本：将 ID 字段从 BIGINT 改为 UUID
# 创建时间: 2025-01-20

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认数据库配置
DEFAULT_HOST="*************"
DEFAULT_PORT="5432"
DEFAULT_USER="postgres"
DEFAULT_DB="bos_db"

# 从环境变量或使用默认值
DB_HOST=${DB_HOST:-$DEFAULT_HOST}
DB_PORT=${DB_PORT:-$DEFAULT_PORT}
DB_USER=${DB_USER:-$DEFAULT_USER}
DB_NAME=${DB_NAME:-$DEFAULT_DB}

# 构建 psql 命令
PSQL_CMD="psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"

# 显示帮助信息
show_help() {
    echo "数据库 UUID 迁移脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --dry-run     预览模式，不执行实际迁移"
    echo "  --yes         跳过确认，直接执行迁移"
    echo "  --rollback    执行回滚操作"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST       数据库主机 (默认: $DEFAULT_HOST)"
    echo "  DB_PORT       数据库端口 (默认: $DEFAULT_PORT)"
    echo "  DB_USER       数据库用户 (默认: $DEFAULT_USER)"
    echo "  DB_NAME       数据库名称 (默认: $DEFAULT_DB)"
    echo "  PGPASSWORD    数据库密码"
    echo ""
    echo "示例:"
    echo "  $0 --dry-run                    # 预览迁移"
    echo "  PGPASSWORD=password $0 --yes    # 直接执行迁移"
    echo "  $0 --rollback                   # 回滚迁移"
}

# 检查数据库连接
check_database_connection() {
    print_info "检查数据库连接..."
    
    if ! $PSQL_CMD -c "SELECT 1;" > /dev/null 2>&1; then
        print_error "无法连接到数据库"
        print_error "请检查数据库配置和网络连接"
        print_error "当前配置: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
        exit 1
    fi
    
    print_success "数据库连接正常"
}

# 检查 UUID 扩展
check_uuid_extension() {
    print_info "检查 UUID 扩展..."
    
    local has_extension=$($PSQL_CMD -t -c "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp');" | tr -d ' ')
    
    if [ "$has_extension" = "f" ]; then
        print_warning "UUID 扩展未安装，迁移脚本将自动安装"
    else
        print_success "UUID 扩展已安装"
    fi
}

# 备份数据库
backup_database() {
    print_info "创建数据库备份..."
    
    local backup_file="backup_before_uuid_migration_$(date +%Y%m%d_%H%M%S).sql"
    
    if pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema=order_schema > "$backup_file"; then
        print_success "备份已创建: $backup_file"
    else
        print_error "备份创建失败"
        exit 1
    fi
}

# 检查表是否存在
check_tables() {
    print_info "检查必要的表是否存在..."
    
    local tables=("orders" "order_items" "order_payments" "order_promotions" "fuel_transactions" 
                  "fuel_transaction_order_links" "shifts" "shift_fuel_details" "shift_merchandise_details" 
                  "shift_payment_details" "staff_cards" "shift_templates" "employees" "employee_shifts" "migration_log")
    
    for table in "${tables[@]}"; do
        local exists=$($PSQL_CMD -t -c "SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = 'order_schema' AND table_name = '$table');" | tr -d ' ')
        
        if [ "$exists" = "t" ]; then
            print_success "✓ 表 $table 存在"
        else
            print_error "✗ 表 $table 不存在"
            print_error "请先执行基础迁移脚本创建必要的表"
            exit 1
        fi
    done
}

# 检查数据量
check_data_volume() {
    print_info "检查数据量..."
    
    local total_records=0
    local tables=("orders" "order_items" "order_payments" "order_promotions" "fuel_transactions" 
                  "fuel_transaction_order_links" "shifts" "shift_fuel_details" "shift_merchandise_details" 
                  "shift_payment_details" "staff_cards" "shift_templates" "employees" "employee_shifts" "migration_log")
    
    for table in "${tables[@]}"; do
        local count=$($PSQL_CMD -t -c "SELECT COUNT(*) FROM order_schema.$table;" | tr -d ' ')
        total_records=$((total_records + count))
        echo "  $table: $count 条记录"
    done
    
    print_info "总记录数: $total_records"
    
    if [ $total_records -gt 100000 ]; then
        print_warning "数据量较大，迁移可能需要较长时间"
    fi
}

# 执行迁移
execute_migration() {
    local migration_file="migrations/004_convert_ids_to_uuid.sql"
    
    if [ ! -f "$migration_file" ]; then
        print_error "迁移文件不存在: $migration_file"
        exit 1
    fi
    
    print_info "执行 UUID 迁移..."
    
    if $PSQL_CMD -f "$migration_file"; then
        print_success "UUID 迁移执行成功"
    else
        print_error "UUID 迁移执行失败"
        exit 1
    fi
}

# 执行回滚
execute_rollback() {
    local rollback_file="migrations/004_convert_ids_to_uuid_rollback.sql"
    
    if [ ! -f "$rollback_file" ]; then
        print_error "回滚文件不存在: $rollback_file"
        exit 1
    fi
    
    print_warning "执行回滚操作..."
    print_warning "注意：回滚将丢失现有的 UUID 数据"
    
    if $PSQL_CMD -f "$rollback_file"; then
        print_success "回滚执行成功"
    else
        print_error "回滚执行失败"
        exit 1
    fi
}

# 验证迁移结果
verify_migration() {
    print_info "验证迁移结果..."
    
    local tables=("orders" "order_items" "order_payments" "order_promotions" "fuel_transactions" 
                  "fuel_transaction_order_links" "shifts" "shift_fuel_details" "shift_merchandise_details" 
                  "shift_payment_details" "staff_cards" "shift_templates" "employees" "employee_shifts" "migration_log")
    
    local success_count=0
    
    for table in "${tables[@]}"; do
        local data_type=$($PSQL_CMD -t -c "SELECT data_type FROM information_schema.columns WHERE table_schema = 'order_schema' AND table_name = '$table' AND column_name = 'id';" | tr -d ' ')
        
        if [ "$data_type" = "uuid" ]; then
            print_success "✓ $table.id 字段类型为 UUID"
            ((success_count++))
        else
            print_error "✗ $table.id 字段类型为 $data_type，应为 UUID"
        fi
    done
    
    if [ $success_count -eq ${#tables[@]} ]; then
        print_success "所有表的 ID 字段已成功转换为 UUID 类型"
    else
        print_error "部分表的 ID 字段转换失败"
        exit 1
    fi
}

# 主函数
main() {
    local dry_run=false
    local skip_confirmation=false
    local rollback=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run=true
                shift
                ;;
            --yes)
                skip_confirmation=true
                shift
                ;;
            --rollback)
                rollback=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "开始 UUID 迁移流程..."
    print_info "数据库配置: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    
    # 基础检查
    check_database_connection
    check_uuid_extension
    check_tables
    check_data_volume
    
    if [ "$dry_run" = true ]; then
        print_info "预览模式：以上检查通过，迁移准备就绪"
        print_info "使用 --yes 参数执行实际迁移"
        exit 0
    fi
    
    if [ "$rollback" = true ]; then
        if [ "$skip_confirmation" = false ]; then
            echo ""
            print_warning "您即将执行回滚操作，这将："
            print_warning "1. 将所有 UUID 字段改回 BIGINT 类型"
            print_warning "2. 丢失现有的 UUID 数据"
            print_warning "3. 可能影响应用程序的正常运行"
            echo ""
            read -p "确认执行回滚吗？(yes/no): " confirm
            if [ "$confirm" != "yes" ]; then
                print_info "回滚操作已取消"
                exit 0
            fi
        fi
        
        execute_rollback
        print_success "回滚操作完成"
        exit 0
    fi
    
    # 确认执行
    if [ "$skip_confirmation" = false ]; then
        echo ""
        print_warning "您即将执行 UUID 迁移，这将："
        print_warning "1. 将所有 ID 字段从 BIGINT 改为 UUID 类型"
        print_warning "2. 为现有数据生成新的 UUID 值"
        print_warning "3. 重建所有相关的约束和索引"
        print_warning "4. 可能需要较长时间完成"
        echo ""
        read -p "确认执行迁移吗？(yes/no): " confirm
        if [ "$confirm" != "yes" ]; then
            print_info "迁移操作已取消"
            exit 0
        fi
    fi
    
    # 执行迁移
    backup_database
    execute_migration
    verify_migration
    
    print_success "UUID 迁移完成！"
    print_info "请更新应用程序代码以使用 UUID 类型"
}

# 执行主函数
main "$@"
