package handler

import (
	"net/http"
	"strconv"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/internal/service"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/log"
)

// OrderHandler 处理订单相关HTTP请求
type OrderHandler interface {
	// CreateOrder 创建订单
	CreateOrder(c echo.Context) error
	// GetOrder 获取订单详情
	GetOrder(c echo.Context) error
	// ListOrders 获取订单列表
	ListOrders(c echo.Context) error
	// AddOrderItem 添加订单项
	AddOrderItem(c echo.Context) error
	// RemoveOrderItem 删除订单项
	RemoveOrderItem(c echo.Context) error
	// ApplyPromotion 应用促销
	ApplyPromotion(c echo.Context) error
	// CompleteOrder 完成订单
	CompleteOrder(c echo.Context) error
	// CancelOrder 取消订单
	CancelOrder(c echo.Context) error
}

// orderHandlerImpl OrderHandler接口实现
type orderHandlerImpl struct {
	orderService service.OrderService
	logger       log.Logger
}

// NewOrderHandler 创建新的OrderHandler实例
func NewOrderHandler(orderService service.OrderService, logger log.Logger) OrderHandler {
	return &orderHandlerImpl{
		orderService: orderService,
		logger:       logger,
	}
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	StationID    int64                  `json:"stationId" validate:"required"`
	CustomerID   *int64                 `json:"customerId"`
	CustomerName *string                `json:"customerName"`
	OrderNumber  string                 `json:"orderNumber" validate:"required"`
	Items        []ItemRequest          `json:"items"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ItemRequest 订单项请求
type ItemRequest struct {
	ProductID   int64                  `json:"productId" validate:"required"`
	ProductName string                 `json:"productName" validate:"required"`
	ProductType string                 `json:"productType" validate:"required,oneof=fuel store"`
	Quantity    float64                `json:"quantity" validate:"required,gt=0"`
	UnitPrice   float64                `json:"unitPrice" validate:"required,gte=0"`
	TaxRate     float64                `json:"taxRate" validate:"required,gte=0"`
	FuelGrade   *string                `json:"fuelGrade"`
	PumpID      *string                `json:"pumpId"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// CreateOrder 创建订单
// @Summary 创建新订单
// @Description 创建一个新的订单
// @Tags 订单
// @Accept json
// @Produce json
// @Param order body CreateOrderRequest true "订单信息"
// @Success 201 {object} repository.Order
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders [post]
func (h *orderHandlerImpl) CreateOrder(c echo.Context) error {
	var req CreateOrderRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 转换为仓储层模型
	order := repository.Order{
		OrderNumber:  req.OrderNumber,
		StationID:    repository.IDFromInt64(req.StationID),
		CustomerName: req.CustomerName,
		Status:       repository.OrderStatusNew,
		Metadata:     req.Metadata,
	}

	// 处理CustomerID
	if req.CustomerID != nil {
		customerID := repository.IDFromInt64(*req.CustomerID)
		order.CustomerID = &customerID
	}

	// 创建订单
	createdOrder, err := h.orderService.CreateOrder(c.Request().Context(), order)
	if err != nil {
		h.logger.Error("Failed to create order")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create order")
	}

	// 添加订单项
	for _, itemReq := range req.Items {
		orderItem := repository.OrderItem{
			OrderID:     createdOrder.ID,
			ProductID:   repository.IDFromInt64(itemReq.ProductID),
			ProductName: itemReq.ProductName,
			ProductType: itemReq.ProductType,
			Quantity:    itemReq.Quantity,
			UnitPrice:   itemReq.UnitPrice,
			TaxRate:     itemReq.TaxRate,
			FuelGrade:   itemReq.FuelGrade,
			PumpID:      itemReq.PumpID,
			Metadata:    itemReq.Metadata,
		}

		_, err := h.orderService.AddOrderItem(c.Request().Context(), createdOrder.ID, orderItem)
		if err != nil {
			h.logger.Error("Failed to add order item")
			// 继续添加其他订单项，不中断流程
		}
	}

	// 获取完整的订单（包含所有项）
	completeOrder, err := h.orderService.GetOrder(c.Request().Context(), createdOrder.ID)
	if err != nil {
		h.logger.Error("Failed to get complete order")
		return c.JSON(http.StatusCreated, createdOrder) // 仍返回成功，但只返回基本订单信息
	}

	return c.JSON(http.StatusCreated, completeOrder)
}

// GetOrder 获取订单详情
// @Summary 获取订单详情
// @Description 根据ID获取订单的详细信息
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} repository.Order
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{id} [get]
func (h *orderHandlerImpl) GetOrder(c echo.Context) error {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	order, err := h.orderService.GetOrder(c.Request().Context(), repository.ID(id))
	if err != nil {
		h.logger.Error("Failed to get order")
		return echo.NewHTTPError(http.StatusNotFound, "Order not found")
	}

	return c.JSON(http.StatusOK, order)
}

// OrderFilterRequest 订单过滤请求
type OrderFilterRequest struct {
	StationID   *int64  `query:"stationId"`
	CustomerID  *int64  `query:"customerId"`
	Status      *string `query:"status"`
	OrderNumber *string `query:"orderNumber"`
	DateFrom    *string `query:"dateFrom"`
	DateTo      *string `query:"dateTo"`
	Page        int     `query:"page" validate:"min=1"`
	PageSize    int     `query:"pageSize" validate:"min=1,max=100"`
	SortBy      string  `query:"sortBy"`
	SortOrder   string  `query:"sortOrder" validate:"omitempty,oneof=asc desc"`
}

// ListOrders 获取订单列表
// @Summary 获取订单列表
// @Description 获取符合条件的订单列表
// @Tags 订单
// @Accept json
// @Produce json
// @Param request query OrderFilterRequest false "过滤条件"
// @Success 200 {object} PagedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders [get]
func (h *orderHandlerImpl) ListOrders(c echo.Context) error {
	var req OrderFilterRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	} else if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 创建过滤器
	filter := repository.OrderFilter{
		OrderNumber: req.OrderNumber,
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := repository.IDFromInt64(*req.StationID)
		filter.StationID = &stationID
	}

	// 处理CustomerID
	if req.CustomerID != nil {
		customerID := repository.IDFromInt64(*req.CustomerID)
		filter.CustomerID = &customerID
	}

	// 解析状态
	if req.Status != nil {
		orderStatus := repository.OrderStatus(*req.Status)
		filter.Status = &orderStatus
	}

	// 解析日期范围
	// 省略日期解析代码，实际实现需要添加

	// 创建分页
	pagination := repository.Pagination{
		Page:  req.Page,
		Limit: req.PageSize,
	}

	// 创建排序
	sort := repository.SortOrder{
		Field:     req.SortBy,
		Direction: req.SortOrder,
	}

	// 如果排序字段未指定，默认按创建时间降序
	if sort.Field == "" {
		sort.Field = "created_at"
		sort.Direction = "desc"
	}

	// 获取订单列表
	orders, total, err := h.orderService.ListOrders(c.Request().Context(), filter, pagination, sort)
	if err != nil {
		h.logger.Error("Failed to list orders")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch orders")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  orders,
		"page":  req.Page,
		"size":  req.PageSize,
		"total": total,
	})
}

// AddOrderItemRequest 添加订单项请求
type AddOrderItemRequest struct {
	ProductID   int64                  `json:"productId" validate:"required"`
	ProductName string                 `json:"productName" validate:"required"`
	ProductType string                 `json:"productType" validate:"required,oneof=fuel store"`
	Quantity    float64                `json:"quantity" validate:"required,gt=0"`
	UnitPrice   float64                `json:"unitPrice" validate:"required,gte=0"`
	TaxRate     float64                `json:"taxRate" validate:"required,gte=0"`
	FuelGrade   *string                `json:"fuelGrade"`
	PumpID      *string                `json:"pumpId"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AddOrderItem 添加订单项
// @Summary 添加订单项
// @Description 向指定订单添加新的订单项
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param item body AddOrderItemRequest true "订单项信息"
// @Success 201 {object} repository.OrderItem
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{id}/items [post]
func (h *orderHandlerImpl) AddOrderItem(c echo.Context) error {
	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 解析请求体
	var req AddOrderItemRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建订单项
	item := repository.OrderItem{
		OrderID:     repository.ID(orderID),
		ProductID:   repository.IDFromInt64(req.ProductID),
		ProductName: req.ProductName,
		ProductType: req.ProductType,
		Quantity:    req.Quantity,
		UnitPrice:   req.UnitPrice,
		TaxRate:     req.TaxRate,
		FuelGrade:   req.FuelGrade,
		PumpID:      req.PumpID,
		Metadata:    req.Metadata,
	}

	// 添加订单项
	createdItem, err := h.orderService.AddOrderItem(c.Request().Context(), repository.ID(orderID), item)
	if err != nil {
		h.logger.Error("Failed to add order item")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to add order item")
	}

	return c.JSON(http.StatusCreated, createdItem)
}

// RemoveOrderItem 删除订单项
// @Summary 删除订单项
// @Description 从指定订单中删除订单项
// @Tags 订单
// @Accept json
// @Produce json
// @Param orderId path int true "订单ID"
// @Param itemId path int true "订单项ID"
// @Success 204 "No Content"
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{orderId}/items/{itemId} [delete]
func (h *orderHandlerImpl) RemoveOrderItem(c echo.Context) error {
	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 解析订单项ID
	itemIDStr := c.Param("itemId")
	itemID, err := uuid.Parse(itemIDStr)
	if err != nil {
		h.logger.Error("Invalid item ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid item ID")
	}

	// 删除订单项
	err = h.orderService.RemoveOrderItem(c.Request().Context(), repository.ID(orderID), repository.ID(itemID))
	if err != nil {
		h.logger.Error("Failed to remove order item")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to remove order item")
	}

	return c.NoContent(http.StatusNoContent)
}

// ApplyPromotionRequest 应用促销请求
type ApplyPromotionRequest struct {
	PromotionID        string                 `json:"promotionId" validate:"required"`  // 改为string类型以支持UUID
	PromotionName      string                 `json:"promotionName" validate:"required"`
	PromotionType      string                 `json:"promotionType" validate:"required,oneof=discount gift"`
	DiscountAmount     float64                `json:"discountAmount" validate:"required_if=PromotionType discount,gte=0"`
	FreeItemID         *int64                 `json:"freeItemId" validate:"required_if=PromotionType gift"`
	FreeItemName       *string                `json:"freeItemName" validate:"required_if=PromotionType gift"`
	FreeItemQuantity   *float64               `json:"freeItemQuantity" validate:"required_if=PromotionType gift,gt=0"`
	MinimumOrderAmount *float64               `json:"minimumOrderAmount"`
	Metadata           map[string]interface{} `json:"metadata"`
}

// ApplyPromotion 应用促销
// @Summary 应用促销
// @Description 向指定订单应用促销
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param promotion body ApplyPromotionRequest true "促销信息"
// @Success 201 {object} repository.OrderPromotion
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{id}/promotions [post]
func (h *orderHandlerImpl) ApplyPromotion(c echo.Context) error {
	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 解析请求体
	var req ApplyPromotionRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 创建促销对象
	promotion := repository.OrderPromotion{
		OrderID:            repository.ID(orderID),
		PromotionID:        req.PromotionID, // 直接使用string，不再转换为repository.ID
		PromotionName:      req.PromotionName,
		PromotionType:      req.PromotionType,
		DiscountAmount:     req.DiscountAmount,
		MinimumOrderAmount: req.MinimumOrderAmount,
		Metadata:           req.Metadata,
	}

	// 处理FreeItemID
	if req.FreeItemID != nil {
		freeItemID := repository.IDFromInt64(*req.FreeItemID)
		promotion.FreeItemID = &freeItemID
	}

	// 处理FreeItemName和FreeItemQuantity
	promotion.FreeItemName = req.FreeItemName
	promotion.FreeItemQuantity = req.FreeItemQuantity

	// 应用促销
	appliedPromotion, err := h.orderService.ApplyPromotion(c.Request().Context(), repository.ID(orderID), promotion)
	if err != nil {
		h.logger.Error("Failed to apply promotion")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to apply promotion")
	}

	return c.JSON(http.StatusCreated, appliedPromotion)
}

// CompleteOrder 完成订单
// @Summary 完成订单
// @Description 完成指定订单，处理支付
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} repository.Order
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse "订单状态不允许完成"
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{id}/complete [post]
func (h *orderHandlerImpl) CompleteOrder(c echo.Context) error {
	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 完成订单
	completedOrder, err := h.orderService.CompleteOrder(c.Request().Context(), repository.ID(orderID))
	if err != nil {
		h.logger.Error("Failed to complete order")

		// 处理特定错误类型
		if err.Error() == "order cannot be completed in current state" {
			return echo.NewHTTPError(http.StatusConflict, "Order cannot be completed in its current state")
		}

		if err.Error() == "total payment amount does not match order final amount" {
			return echo.NewHTTPError(http.StatusBadRequest, "Payment amount does not match order amount")
		}

		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to complete order")
	}

	return c.JSON(http.StatusOK, completedOrder)
}

// CancelOrder 取消订单
// @Summary 取消订单
// @Description 取消指定订单
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Success 200 {object} repository.Order
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse "订单状态不允许取消"
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{id}/cancel [post]
func (h *orderHandlerImpl) CancelOrder(c echo.Context) error {
	// 解析订单ID
	orderID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 取消订单
	canceledOrder, err := h.orderService.CancelOrder(c.Request().Context(), repository.IDFromInt64(orderID))
	if err != nil {
		h.logger.Error("Failed to cancel order")

		// 处理特定错误
		if err.Error() == "order cannot be cancelled in current state" {
			return echo.NewHTTPError(http.StatusConflict, "Order cannot be cancelled in its current state")
		}

		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to cancel order")
	}

	return c.JSON(http.StatusOK, canceledOrder)
}

// ErrorResponse 标准错误响应
type ErrorResponse struct {
	Message string `json:"message"`
}

// PagedResponse 分页响应
type PagedResponse struct {
	Data  interface{} `json:"data"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
	Total int         `json:"total"`
}
