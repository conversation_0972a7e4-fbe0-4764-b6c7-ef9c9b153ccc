package handler

import (
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/internal/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/internal/service"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/log"
)

// FuelTransactionHandler 处理燃油交易相关HTTP请求
type FuelTransactionHandler interface {
	// CreateFuelTransaction 创建燃油交易
	CreateFuelTransaction(c echo.Context) error
	// GetFuelTransaction 获取燃油交易详情
	GetFuelTransaction(c echo.Context) error
	// ListFuelTransactions 获取燃油交易列表
	ListFuelTransactions(c echo.Context) error
	// LinkFuelTransactionToOrder 将燃油交易关联到订单
	LinkFuelTransactionToOrder(c echo.Context) error
	// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
	UnlinkFuelTransactionFromOrder(c echo.Context) error
	// GetOrderFuelTransactions 获取订单关联的燃油交易
	GetOrderFuelTransactions(c echo.Context) error
	// GetFuelTransactionOrders 获取燃油交易关联的订单
	GetFuelTransactionOrders(c echo.Context) error
	// UpdateLinkAllocatedAmount 更新关联分配金额
	UpdateLinkAllocatedAmount(c echo.Context) error
}

// fuelTransactionHandlerImpl FuelTransactionHandler接口实现
type fuelTransactionHandlerImpl struct {
	fuelTransactionService service.FuelTransactionService
	logger                 log.Logger
}

// NewFuelTransactionHandler 创建新的FuelTransactionHandler实例
func NewFuelTransactionHandler(fuelTransactionService service.FuelTransactionService, logger log.Logger) FuelTransactionHandler {
	return &fuelTransactionHandlerImpl{
		fuelTransactionService: fuelTransactionService,
		logger:                 logger,
	}
}

// CreateFuelTransactionRequest 创建燃油交易请求
type CreateFuelTransactionRequest struct {
	TransactionNumber string                 `json:"transactionNumber" validate:"required"`
	StationID         int64                  `json:"stationId" validate:"required"`
	PumpID            string                 `json:"pumpID" validate:"required"`
	NozzleID          string                 `json:"nozzleID" validate:"required"`
	FuelType          string                 `json:"fuelType"`
	FuelGrade         string                 `json:"fuelGrade" validate:"required"`
	UnitPrice         float64                `json:"unitPrice" validate:"required,gte=0"`
	Volume            float64                `json:"volume" validate:"required,gte=0"`
	Amount            float64                `json:"amount" validate:"required,gte=0"`
	TransactionDate   time.Time              `json:"transactionDate" validate:"required"`
	MemberCardID      *string                `json:"memberCardID"`
	EmployeeID        *string                `json:"employeeID"`
	Status            string                 `json:"status" validate:"required,oneof=pending processed cancelled"`
	Metadata          map[string]interface{} `json:"metadata"`
	StartTotalizer    *float64               `json:"startTotalizer"`
	EndTotalizer      *float64               `json:"endTotalizer"`
	NozzleStartTime   *time.Time             `json:"nozzleStartTime"`
	NozzleEndTime     *time.Time             `json:"nozzleEndTime"`
}

// CreateFuelTransaction 创建燃油交易
// @Summary 创建新燃油交易
// @Description 创建一个新的燃油交易记录
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transaction body CreateFuelTransactionRequest true "燃油交易信息"
// @Success 201 {object} repository.FuelTransaction
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions [post]
func (h *fuelTransactionHandlerImpl) CreateFuelTransaction(c echo.Context) error {
	var req CreateFuelTransactionRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 转换为仓储层模型
	transaction := repository.FuelTransaction{
		TransactionNumber: req.TransactionNumber,
		StationID:         repository.IDFromInt64(req.StationID),
		PumpID:            req.PumpID,
		NozzleID:          req.NozzleID,
		FuelType:          req.FuelType,
		FuelGrade:         req.FuelGrade,
		UnitPrice:         req.UnitPrice,
		Volume:            req.Volume,
		Amount:            req.Amount,
		MemberCardID:      req.MemberCardID,
		Status:            repository.FuelTransactionStatus(req.Status),
		Metadata:          req.Metadata,
		StartTotalizer:    req.StartTotalizer,
		EndTotalizer:      req.EndTotalizer,
		NozzleStartTime:   req.NozzleStartTime,
		NozzleEndTime:     req.NozzleEndTime,
	}

	// 如果提供了员工ID
	if req.EmployeeID != nil {
		// 这里假设EmployeeID在repository模型中是指向ID类型的指针
		// 实际实现可能需要调整
		empID := repository.ID(uuid.Nil) // 这里应该转换为适当的ID类型
		transaction.EmployeeID = &empID
	}

	// 创建燃油交易
	createdTransaction, err := h.fuelTransactionService.CreateFuelTransaction(c.Request().Context(), transaction)
	if err != nil {
		h.logger.Error("Failed to create fuel transaction")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create fuel transaction")
	}

	return c.JSON(http.StatusCreated, createdTransaction)
}

// GetFuelTransaction 获取燃油交易详情
// @Summary 获取燃油交易详情
// @Description 根据ID获取燃油交易的详细信息
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Success 200 {object} repository.FuelTransaction
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{id} [get]
func (h *fuelTransactionHandlerImpl) GetFuelTransaction(c echo.Context) error {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		h.logger.Error("Invalid fuel transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid fuel transaction ID")
	}

	transaction, err := h.fuelTransactionService.GetFuelTransaction(c.Request().Context(), repository.ID(id))
	if err != nil {
		h.logger.Error("Failed to get fuel transaction")
		return echo.NewHTTPError(http.StatusNotFound, "Fuel transaction not found")
	}

	return c.JSON(http.StatusOK, transaction)
}

// FuelTransactionFilterRequest 燃油交易过滤请求
type FuelTransactionFilterRequest struct {
	StationID         *int64  `query:"stationId"`
	PumpID            *string `query:"pumpId"`
	TransactionNumber *string `query:"transactionNumber"`
	FuelType          *string `query:"fuelType"`
	FuelGrade         *string `query:"fuelGrade"`
	MemberID          *int64  `query:"memberId"`
	Status            *string `query:"status"`
	DateFrom          *string `query:"dateFrom"`
	DateTo            *string `query:"dateTo"`
	Page              int     `query:"page" validate:"min=1"`
	PageSize          int     `query:"pageSize" validate:"min=1,max=100"`
	SortBy            string  `query:"sortBy"`
	SortOrder         string  `query:"sortOrder" validate:"omitempty,oneof=asc desc"`
}

// ListFuelTransactions 获取燃油交易列表
// @Summary 获取燃油交易列表
// @Description 获取符合条件的燃油交易列表
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param request query FuelTransactionFilterRequest false "过滤条件"
// @Success 200 {object} PagedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions [get]
func (h *fuelTransactionHandlerImpl) ListFuelTransactions(c echo.Context) error {
	var req FuelTransactionFilterRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	} else if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 创建过滤器
	filter := repository.FuelTransactionFilter{
		PumpID:            req.PumpID,
		TransactionNumber: req.TransactionNumber,
		FuelType:          req.FuelType,
		FuelGrade:         req.FuelGrade,
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := repository.IDFromInt64(*req.StationID)
		filter.StationID = &stationID
	}

	// 解析状态
	if req.Status != nil {
		status := repository.FuelTransactionStatus(*req.Status)
		filter.Status = &status
	}

	// 解析日期范围
	if req.DateFrom != nil {
		dateFrom, err := time.Parse("2006-01-02", *req.DateFrom)
		if err == nil {
			filter.DateFrom = &dateFrom
		}
	}

	if req.DateTo != nil {
		dateTo, err := time.Parse("2006-01-02", *req.DateTo)
		if err == nil {
			// 设置为当天结束时间
			dateTo = dateTo.Add(24*time.Hour - time.Second)
			filter.DateTo = &dateTo
		}
	}

	// 处理MemberID
	if req.MemberID != nil {
		memberID := repository.IDFromInt64(*req.MemberID)
		filter.MemberID = &memberID
	}

	// 创建分页
	pagination := repository.Pagination{
		Page:  req.Page,
		Limit: req.PageSize,
	}

	// 创建排序
	sort := repository.SortOrder{
		Field:     req.SortBy,
		Direction: req.SortOrder,
	}

	// 如果排序字段未指定，默认按创建时间降序
	if sort.Field == "" {
		sort.Field = "created_at"
		sort.Direction = "desc"
	}

	// 获取燃油交易列表
	transactions, total, err := h.fuelTransactionService.ListFuelTransactions(c.Request().Context(), filter, pagination, sort)
	if err != nil {
		h.logger.Error("Failed to list fuel transactions: " + err.Error())
		// 返回一个空的结果集而不是错误
		return c.JSON(http.StatusOK, map[string]interface{}{
			"data":  []interface{}{},
			"page":  req.Page,
			"size":  req.PageSize,
			"total": 0,
		})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  transactions,
		"page":  req.Page,
		"size":  req.PageSize,
		"total": total,
	})
}

// LinkTransactionRequest 关联燃油交易到订单请求
type LinkTransactionRequest struct {
	AllocatedAmount float64 `json:"allocatedAmount" validate:"required,gte=0"`
}

// LinkFuelTransactionToOrder 将燃油交易关联到订单
// @Summary 关联燃油交易到订单
// @Description 将指定的燃油交易关联到指定的订单
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Param request body LinkTransactionRequest true "关联信息"
// @Success 201 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId} [post]
func (h *fuelTransactionHandlerImpl) LinkFuelTransactionToOrder(c echo.Context) error {
	// 解析燃油交易ID
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 解析请求体
	var req LinkTransactionRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 关联燃油交易到订单
	link, err := h.fuelTransactionService.LinkFuelTransactionToOrder(
		c.Request().Context(),
		repository.ID(transactionID),
		repository.ID(orderID),
		req.AllocatedAmount,
	)
	if err != nil {
		h.logger.Error("Failed to link fuel transaction to order")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to link fuel transaction to order")
	}

	return c.JSON(http.StatusCreated, link)
}

// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
// @Summary 解除燃油交易与订单的关联
// @Description 解除指定的燃油交易与指定订单的关联
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Success 204 "No Content"
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId} [delete]
func (h *fuelTransactionHandlerImpl) UnlinkFuelTransactionFromOrder(c echo.Context) error {
	// 解析燃油交易ID
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 解除关联
	err = h.fuelTransactionService.UnlinkFuelTransactionFromOrder(
		c.Request().Context(),
		repository.ID(transactionID),
		repository.ID(orderID),
	)
	if err != nil {
		h.logger.Error("Failed to unlink fuel transaction from order")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to unlink fuel transaction from order")
	}

	return c.NoContent(http.StatusNoContent)
}

// ConfirmFuelTransactionLink 确认燃油交易链接
// @Summary 确认燃油交易链接
// @Description 将预留状态的燃油交易链接确认为活跃状态
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Success 200 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId}/confirm [post]
func (h *fuelTransactionHandlerImpl) ConfirmFuelTransactionLink(c echo.Context) error {
	// 解析燃油交易ID
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 确认燃油交易链接
	link, err := h.fuelTransactionService.ConfirmFuelTransactionLink(
		c.Request().Context(),
		repository.ID(transactionID),
		repository.ID(orderID),
	)
	if err != nil {
		h.logger.Error("Failed to confirm fuel transaction link: " + err.Error())
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to confirm fuel transaction link")
	}

	return c.JSON(http.StatusOK, link)
}

// CancelFuelTransactionLink 取消燃油交易链接
// @Summary 取消燃油交易链接
// @Description 将预留状态的燃油交易链接取消
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Success 204 "No Content"
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId}/cancel [post]
func (h *fuelTransactionHandlerImpl) CancelFuelTransactionLink(c echo.Context) error {
	// 解析燃油交易ID
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 取消燃油交易链接
	err = h.fuelTransactionService.CancelFuelTransactionLink(
		c.Request().Context(),
		repository.ID(transactionID),
		repository.ID(orderID),
	)
	if err != nil {
		h.logger.Error("Failed to cancel fuel transaction link: " + err.Error())
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to cancel fuel transaction link")
	}

	return c.JSON(http.StatusNoContent, nil)
}

// GetOrderFuelTransactions 获取订单关联的燃油交易
// @Summary 获取订单关联的燃油交易
// @Description 获取指定订单关联的所有燃油交易
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param orderId path int true "订单ID"
// @Success 200 {array} repository.FuelTransaction
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{orderId}/fuel-transactions [get]
func (h *fuelTransactionHandlerImpl) GetOrderFuelTransactions(c echo.Context) error {
	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 获取订单关联的燃油交易
	transactions, err := h.fuelTransactionService.GetOrderFuelTransactions(
		c.Request().Context(),
		repository.ID(orderID),
	)
	if err != nil {
		h.logger.Error("Failed to get order fuel transactions")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get order fuel transactions")
	}

	return c.JSON(http.StatusOK, transactions)
}

// GetFuelTransactionOrders 获取燃油交易关联的订单
// @Summary 获取燃油交易关联的订单
// @Description 获取指定燃油交易关联的所有订单
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Success 200 {array} repository.Order
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders [get]
func (h *fuelTransactionHandlerImpl) GetFuelTransactionOrders(c echo.Context) error {
	// 解析燃油交易ID
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	// 获取燃油交易关联的订单
	orders, err := h.fuelTransactionService.GetFuelTransactionOrders(
		c.Request().Context(),
		repository.ID(transactionID),
	)
	if err != nil {
		h.logger.Error("Failed to get fuel transaction orders")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get fuel transaction orders")
	}

	return c.JSON(http.StatusOK, orders)
}

// UpdateLinkAmountRequest 更新关联分配金额请求
type UpdateLinkAmountRequest struct {
	AllocatedAmount float64 `json:"allocatedAmount" validate:"required,gte=0"`
}

// UpdateLinkAllocatedAmount 更新关联分配金额
// @Summary 更新关联分配金额
// @Description 更新燃油交易与订单关联的分配金额
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Param request body UpdateLinkAmountRequest true "更新信息"
// @Success 200 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId}/amount [put]
func (h *fuelTransactionHandlerImpl) UpdateLinkAllocatedAmount(c echo.Context) error {
	// 解析燃油交易ID
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	// 解析订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 解析请求体
	var req UpdateLinkAmountRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 更新关联分配金额
	link, err := h.fuelTransactionService.UpdateLinkAllocatedAmount(
		c.Request().Context(),
		repository.ID(transactionID),
		repository.ID(orderID),
		req.AllocatedAmount,
	)
	if err != nil {
		h.logger.Error("Failed to update link allocated amount")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update link allocated amount")
	}

	return c.JSON(http.StatusOK, link)
}
