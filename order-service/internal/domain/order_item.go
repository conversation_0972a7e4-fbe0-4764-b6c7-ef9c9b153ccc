package domain

import (
	"errors"
	"time"
	"github.com/google/uuid"
)

// ProductType 商品类型
type ProductType string

const (
	// ProductTypeFuel 燃油商品
	ProductTypeFuel ProductType = "fuel"
	// ProductTypeStore 便利店商品
	ProductTypeStore ProductType = "store"
)

// OrderItem 订单项领域模型
type OrderItem struct {
	ID             uuid.UUID              `json:"id"`
	OrderID        uuid.UUID              `json:"orderId"`
	ProductID      string                 `json:"productId"`
	ProductName    string                 `json:"productName"`
	ProductType    string                 `json:"productType"`
	Quantity       float64                `json:"quantity"`
	UnitPrice      float64                `json:"unitPrice"`
	TotalPrice     float64                `json:"totalPrice"`
	DiscountAmount float64                `json:"discountAmount"`
	FinalPrice     float64                `json:"finalPrice"`
	TaxAmount      float64                `json:"taxAmount"`
	TaxRate        float64                `json:"taxRate"`
	FuelGrade      *string                `json:"fuelGrade,omitempty"`
	PumpID         *string                `json:"pumpId,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
}

// NewOrderItem 创建订单项
func NewOrderItem(
	orderID uuid.UUID,
	productID string,
	productName string,
	productType string,
	quantity float64,
	unitPrice float64,
	taxRate float64,
) (*OrderItem, error) {
	if quantity <= 0 {
		return nil, errors.New("数量必须大于0")
	}
	if unitPrice < 0 {
		return nil, errors.New("单价不能为负数")
	}
	if taxRate < 0 {
		return nil, errors.New("税率不能为负数")
	}

	totalPrice := quantity * unitPrice
	taxAmount := totalPrice * taxRate
	now := time.Now()

	return &OrderItem{
		OrderID:        orderID,
		ProductID:      productID,
		ProductName:    productName,
		ProductType:    productType,
		Quantity:       quantity,
		UnitPrice:      unitPrice,
		TotalPrice:     totalPrice,
		DiscountAmount: 0,
		FinalPrice:     totalPrice,
		TaxAmount:      taxAmount,
		TaxRate:        taxRate,
		Metadata:       make(map[string]interface{}),
		CreatedAt:      now,
		UpdatedAt:      now,
	}, nil
}

// NewFuelOrderItem 创建燃油订单项
func NewFuelOrderItem(
	orderID uuid.UUID,
	productID string,
	productName string,
	quantity float64,
	unitPrice float64,
	taxRate float64,
	fuelGrade string,
	pumpID string,
) (*OrderItem, error) {
	item, err := NewOrderItem(orderID, productID, productName, "FUEL", quantity, unitPrice, taxRate)
	if err != nil {
		return nil, err
	}

	item.FuelGrade = &fuelGrade
	item.PumpID = &pumpID

	return item, nil
}

// ApplyDiscount 应用折扣
func (i *OrderItem) ApplyDiscount(discountAmount float64) error {
	if discountAmount < 0 {
		return errors.New("折扣金额不能为负数")
	}
	if discountAmount > i.TotalPrice {
		return errors.New("折扣金额不能大于总价")
	}

	i.DiscountAmount = discountAmount
	i.FinalPrice = i.TotalPrice - discountAmount
	i.UpdatedAt = time.Now()
	return nil
}

// UpdateQuantity 更新数量
func (i *OrderItem) UpdateQuantity(quantity float64) error {
	if quantity <= 0 {
		return errors.New("数量必须大于0")
	}

	i.Quantity = quantity
	i.TotalPrice = quantity * i.UnitPrice
	i.TaxAmount = i.TotalPrice * i.TaxRate
	i.FinalPrice = i.TotalPrice - i.DiscountAmount
	i.UpdatedAt = time.Now()
	return nil
}

// SetFuelDetails 设置燃料详情
func (i *OrderItem) SetFuelDetails(fuelGrade string, pumpID string) {
	i.FuelGrade = &fuelGrade
	i.PumpID = &pumpID
}

// Clone 克隆订单项
func (i *OrderItem) Clone() *OrderItem {
	clone := *i
	if i.FuelGrade != nil {
		fuelGrade := *i.FuelGrade
		clone.FuelGrade = &fuelGrade
	}
	if i.PumpID != nil {
		pumpID := *i.PumpID
		clone.PumpID = &pumpID
	}
	return &clone
}
