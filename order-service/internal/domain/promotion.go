package domain

import (
	"errors"
	"time"
	"github.com/google/uuid"
)

// PromotionType 促销类型
type PromotionType string

const (
	// PromotionTypeDiscount 折扣
	PromotionTypeDiscount PromotionType = "discount"
	// PromotionTypeVoucher 代金券
	PromotionTypeVoucher PromotionType = "voucher"
	// PromotionTypeFreeItem 赠品
	PromotionTypeFreeItem PromotionType = "free_item"
)

// PromotionStatus 促销状态
type PromotionStatus string

const (
	// PromotionStatusActive 活跃
	PromotionStatusActive PromotionStatus = "active"
	// PromotionStatusInactive 非活跃
	PromotionStatusInactive PromotionStatus = "inactive"
	// PromotionStatusExpired 已过期
	PromotionStatusExpired PromotionStatus = "expired"
)

// DiscountType 折扣类型
type DiscountType string

const (
	// DiscountTypePercentage 百分比
	DiscountTypePercentage DiscountType = "percentage"
	// DiscountTypeFixed 固定金额
	DiscountTypeFixed DiscountType = "fixed"
)

// Promotion 促销领域模型
type Promotion struct {
	ID                uuid.UUID              `json:"id"`
	Code              string                 `json:"code"`
	Name              string                 `json:"name"`
	Description       string                 `json:"description"`
	Type              PromotionType          `json:"type"`
	Status            PromotionStatus        `json:"status"`
	DiscountType      DiscountType           `json:"discountType"`
	DiscountValue     float64                `json:"discountValue"`
	MinOrderAmount    float64                `json:"minOrderAmount"`
	MaxDiscountAmount *float64               `json:"maxDiscountAmount,omitempty"`
	StartDate         time.Time              `json:"startDate"`
	EndDate           time.Time              `json:"endDate"`
	UsageLimit        *int                   `json:"usageLimit,omitempty"`
	CurrentUsage      int                    `json:"currentUsage"`
	ApplicableItems   []uuid.UUID            `json:"applicableItems,omitempty"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt         time.Time              `json:"createdAt"`
	UpdatedAt         time.Time              `json:"updatedAt"`
}

// NewPromotion 创建新的促销
func NewPromotion(
	code string,
	name string,
	description string,
	promotionType PromotionType,
	discountType DiscountType,
	discountValue float64,
	minOrderAmount float64,
	maxDiscountAmount *float64,
	startDate time.Time,
	endDate time.Time,
	usageLimit *int,
	applicableItems []uuid.UUID,
) (*Promotion, error) {
	if code == "" {
		return nil, errors.New("促销代码不能为空")
	}

	if name == "" {
		return nil, errors.New("促销名称不能为空")
	}

	if discountValue <= 0 {
		return nil, errors.New("折扣值必须大于0")
	}

	if discountType == DiscountTypePercentage && discountValue > 100 {
		return nil, errors.New("百分比折扣不能超过100%")
	}

	if minOrderAmount < 0 {
		return nil, errors.New("最小订单金额不能为负数")
	}

	if maxDiscountAmount != nil && *maxDiscountAmount <= 0 {
		return nil, errors.New("最大折扣金额必须大于0")
	}

	if endDate.Before(startDate) {
		return nil, errors.New("结束日期不能早于开始日期")
	}

	now := time.Now()
	status := PromotionStatusActive

	if now.After(endDate) {
		status = PromotionStatusExpired
	} else if now.Before(startDate) {
		status = PromotionStatusInactive
	}

	return &Promotion{
		Code:              code,
		Name:              name,
		Description:       description,
		Type:              promotionType,
		Status:            status,
		DiscountType:      discountType,
		DiscountValue:     discountValue,
		MinOrderAmount:    minOrderAmount,
		MaxDiscountAmount: maxDiscountAmount,
		StartDate:         startDate,
		EndDate:           endDate,
		UsageLimit:        usageLimit,
		CurrentUsage:      0,
		ApplicableItems:   applicableItems,
		Metadata:          make(map[string]interface{}),
		CreatedAt:         now,
		UpdatedAt:         now,
	}, nil
}

// Activate 激活促销
func (p *Promotion) Activate() error {
	if p.Status == PromotionStatusExpired {
		return errors.New("已过期的促销无法激活")
	}

	now := time.Now()
	if now.After(p.EndDate) {
		p.Status = PromotionStatusExpired
		p.UpdatedAt = now
		return errors.New("促销已过期，无法激活")
	}

	if now.Before(p.StartDate) {
		return errors.New("促销尚未开始，无法激活")
	}

	p.Status = PromotionStatusActive
	p.UpdatedAt = now
	return nil
}

// Deactivate 停用促销
func (p *Promotion) Deactivate() error {
	if p.Status == PromotionStatusExpired {
		return errors.New("已过期的促销无法停用")
	}

	p.Status = PromotionStatusInactive
	p.UpdatedAt = time.Now()
	return nil
}

// IncrementUsage 增加使用次数
func (p *Promotion) IncrementUsage() error {
	if p.Status != PromotionStatusActive {
		return errors.New("只有活跃状态的促销可以被使用")
	}

	if p.UsageLimit != nil && p.CurrentUsage >= *p.UsageLimit {
		return errors.New("促销已达到使用上限")
	}

	p.CurrentUsage++
	p.UpdatedAt = time.Now()

	if p.UsageLimit != nil && p.CurrentUsage >= *p.UsageLimit {
		p.Status = PromotionStatusInactive
	}

	return nil
}

// CalculateDiscount 计算折扣金额
func (p *Promotion) CalculateDiscount(orderAmount float64, itemIDs []uuid.UUID) (float64, error) {
	if p.Status != PromotionStatusActive {
		return 0, errors.New("只有活跃状态的促销可以计算折扣")
	}

	now := time.Now()
	if now.Before(p.StartDate) || now.After(p.EndDate) {
		return 0, errors.New("促销不在有效期内")
	}

	if orderAmount < p.MinOrderAmount {
		return 0, errors.New("订单金额未达到最低要求")
	}

	var discountAmount float64

	// 检查是否有适用商品限制
	if len(p.ApplicableItems) > 0 {
		// 检查所有商品是否都适用
		applicable := false
		for _, itemID := range itemIDs {
			for _, applicableItemID := range p.ApplicableItems {
				if itemID == applicableItemID {
					applicable = true
					break
				}
			}
			if applicable {
				break
			}
		}

		if !applicable {
			return 0, errors.New("没有适用的商品")
		}
	}

	// 计算折扣
	if p.DiscountType == DiscountTypePercentage {
		discountAmount = orderAmount * (p.DiscountValue / 100)
	} else {
		discountAmount = p.DiscountValue
	}

	// 检查最大折扣限制
	if p.MaxDiscountAmount != nil && discountAmount > *p.MaxDiscountAmount {
		discountAmount = *p.MaxDiscountAmount
	}

	return discountAmount, nil
}

// CheckExpiry 检查是否过期
func (p *Promotion) CheckExpiry() bool {
	now := time.Now()
	if now.After(p.EndDate) && p.Status != PromotionStatusExpired {
		p.Status = PromotionStatusExpired
		p.UpdatedAt = now
		return true
	}
	return p.Status == PromotionStatusExpired
}

// AddMetadata 添加元数据
func (p *Promotion) AddMetadata(key string, value interface{}) {
	if p.Metadata == nil {
		p.Metadata = make(map[string]interface{})
	}
	p.Metadata[key] = value
	p.UpdatedAt = time.Now()
}

// IsActive 是否活跃
func (p *Promotion) IsActive() bool {
	p.CheckExpiry()
	return p.Status == PromotionStatusActive
}

// IsInactive 是否非活跃
func (p *Promotion) IsInactive() bool {
	return p.Status == PromotionStatusInactive
}

// IsExpired 是否已过期
func (p *Promotion) IsExpired() bool {
	p.CheckExpiry()
	return p.Status == PromotionStatusExpired
}

// HasReachedUsageLimit 是否已达到使用上限
func (p *Promotion) HasReachedUsageLimit() bool {
	return p.UsageLimit != nil && p.CurrentUsage >= *p.UsageLimit
}
