package domain

import (
	"errors"
	"math"
	"time"
	"github.com/google/uuid"
)

// OrderStatus 订单状态
type OrderStatus string

const (
	// OrderStatusNew 新建订单
	OrderStatusNew OrderStatus = "new"
	// OrderStatusProcessing 处理中订单
	OrderStatusProcessing OrderStatus = "processing"
	// OrderStatusCompleted 已完成订单
	OrderStatusCompleted OrderStatus = "completed"
	// OrderStatusCanceled 已取消订单
	OrderStatusCanceled OrderStatus = "canceled"
)

// Order 订单领域模型
type Order struct {
	ID             uuid.UUID              `json:"id"`
	OrderNumber    string                 `json:"orderNumber"`
	Status         OrderStatus            `json:"status"`
	StationID      uuid.UUID              `json:"stationId"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
	CompletedAt    *time.Time             `json:"completedAt,omitempty"`
	CanceledAt     *time.Time             `json:"canceledAt,omitempty"`
	CustomerID     *uuid.UUID             `json:"customerId,omitempty"`
	CustomerName   *string                `json:"customerName,omitempty"`
	Items          []OrderItem            `json:"items"`
	Promotions     []Promotion            `json:"promotions"`
	Payments       []Payment              `json:"payments"`
	TotalAmount    float64                `json:"totalAmount"`
	DiscountAmount float64                `json:"discountAmount"`
	TaxAmount      float64                `json:"taxAmount"`
	FinalAmount    float64                `json:"finalAmount"`
	PaidAmount     float64                `json:"paidAmount"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// NewOrder 创建新订单
func NewOrder(stationID uuid.UUID, customerID *uuid.UUID, customerName *string, orderNumber string) *Order {
	now := time.Now()
	return &Order{
		OrderNumber:    orderNumber,
		Status:         OrderStatusNew,
		StationID:      stationID,
		CreatedAt:      now,
		UpdatedAt:      now,
		CustomerID:     customerID,
		CustomerName:   customerName,
		Items:          []OrderItem{},
		Promotions:     []Promotion{},
		Payments:       []Payment{},
		TotalAmount:    0,
		DiscountAmount: 0,
		TaxAmount:      0,
		FinalAmount:    0,
		PaidAmount:     0,
		Metadata:       make(map[string]interface{}),
	}
}

// AddItem 添加订单项
func (o *Order) AddItem(item OrderItem) {
	o.Items = append(o.Items, item)
	o.calculateAmounts()
	o.UpdatedAt = time.Now()
}

// RemoveItem 移除订单项
func (o *Order) RemoveItem(itemID uuid.UUID) bool {
	for i, item := range o.Items {
		if item.ID == itemID {
			o.Items = append(o.Items[:i], o.Items[i+1:]...)
			o.calculateAmounts()
			o.UpdatedAt = time.Now()
			return true
		}
	}
	return false
}

// GetItem 获取订单项
func (o *Order) GetItem(itemID uuid.UUID) *OrderItem {
	for i, item := range o.Items {
		if item.ID == itemID {
			return &o.Items[i]
		}
	}
	return nil
}

// UpdateItemQuantity 更新订单项数量
func (o *Order) UpdateItemQuantity(itemID uuid.UUID, quantity float64) error {
	if quantity <= 0 {
		return errors.New("数量必须大于0")
	}

	// 直接在items列表中找到对应的项并更新
	var item *OrderItem
	for i := range o.Items {
		if o.Items[i].ID == itemID {
			// 获取指向该项的指针
			item = &o.Items[i]
			break
		}
	}

	if item == nil {
		return errors.New("订单项不存在")
	}

	// 更新数量和金额
	item.Quantity = quantity
	item.TotalPrice = quantity * item.UnitPrice
	item.TaxAmount = item.TotalPrice * item.TaxRate
	item.FinalPrice = item.TotalPrice - item.DiscountAmount

	// 重新计算订单金额
	o.calculateAmounts()
	o.UpdatedAt = time.Now()

	return nil
}

// ApplyPromotion 应用促销
func (o *Order) ApplyPromotion(promotion Promotion) {
	o.Promotions = append(o.Promotions, promotion)
	o.calculateAmounts()
	o.UpdatedAt = time.Now()
}

// RemovePromotion 移除促销
func (o *Order) RemovePromotion(promotionID uuid.UUID) bool {
	for i, promotion := range o.Promotions {
		if promotion.ID == promotionID {
			o.Promotions = append(o.Promotions[:i], o.Promotions[i+1:]...)
			o.calculateAmounts()
			o.UpdatedAt = time.Now()
			return true
		}
	}
	return false
}

// AddPayment 添加支付
func (o *Order) AddPayment(payment Payment) {
	o.Payments = append(o.Payments, payment)
	o.calculatePaidAmount()
	o.UpdatedAt = time.Now()
}

// Complete 完成订单
func (o *Order) Complete() error {
	if o.Status != OrderStatusNew && o.Status != OrderStatusProcessing {
		return errors.New("订单状态不允许完成操作")
	}

	// 检查支付金额是否足够
	if math.Abs(o.PaidAmount-o.FinalAmount) > 0.01 {
		return errors.New("订单未完全支付")
	}

	now := time.Now()
	o.Status = OrderStatusCompleted
	o.CompletedAt = &now
	o.UpdatedAt = now

	return nil
}

// Cancel 取消订单
func (o *Order) Cancel() error {
	if o.Status == OrderStatusCompleted {
		return errors.New("已完成的订单无法取消")
	}

	if o.Status == OrderStatusCanceled {
		return errors.New("订单已经被取消")
	}

	now := time.Now()
	o.Status = OrderStatusCanceled
	o.CanceledAt = &now
	o.UpdatedAt = now

	return nil
}

// Process 处理订单
func (o *Order) Process() error {
	if o.Status != OrderStatusNew {
		return errors.New("只有新建状态的订单可以设置为处理中")
	}

	o.Status = OrderStatusProcessing
	o.UpdatedAt = time.Now()
	return nil
}

// IsEmpty 判断订单是否为空
func (o *Order) IsEmpty() bool {
	return len(o.Items) == 0
}

// calculateAmounts 计算订单金额
func (o *Order) calculateAmounts() {
	// 计算商品总金额和税额
	o.TotalAmount = 0
	o.TaxAmount = 0

	for _, item := range o.Items {
		o.TotalAmount += item.TotalPrice
		o.TaxAmount += item.TaxAmount
	}

	// 应用促销折扣
	o.DiscountAmount = 0
	// 收集所有订单项的ID，用于促销计算
	itemIDs := make([]uuid.UUID, 0, len(o.Items))
	for _, item := range o.Items {
		itemIDs = append(itemIDs, item.ID)
	}

	// 计算每个促销的折扣金额
	for _, promotion := range o.Promotions {
		discountAmount, err := promotion.CalculateDiscount(o.TotalAmount, itemIDs)
		if err == nil {
			o.DiscountAmount += discountAmount
		}
	}

	o.FinalAmount = o.TotalAmount - o.DiscountAmount + o.TaxAmount
	if o.FinalAmount < 0 {
		o.FinalAmount = 0
	}
}

// calculatePaidAmount 计算已支付金额
func (o *Order) calculatePaidAmount() {
	o.PaidAmount = 0
	for _, payment := range o.Payments {
		if payment.Status == PaymentStatusCompleted {
			o.PaidAmount += payment.Amount
		}
	}
}
