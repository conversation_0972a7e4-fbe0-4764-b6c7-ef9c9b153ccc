package domain

import (
	"errors"
	"time"
	"github.com/google/uuid"
)

// PaymentStatus 支付状态
type PaymentStatus string

const (
	// PaymentStatusPending 待处理
	PaymentStatusPending PaymentStatus = "pending"
	// PaymentStatusCompleted 已完成
	PaymentStatusCompleted PaymentStatus = "completed"
	// PaymentStatusFailed 已失败
	PaymentStatusFailed PaymentStatus = "failed"
	// PaymentStatusCanceled 已取消
	PaymentStatusCanceled PaymentStatus = "canceled"
	// PaymentStatusRefunded 已退款
	PaymentStatusRefunded PaymentStatus = "refunded"
)

// PaymentMethod 支付方式
type PaymentMethod string

const (
	// PaymentMethodCash 现金
	PaymentMethodCash PaymentMethod = "cash"
	// PaymentMethodCard 卡
	PaymentMethodCard PaymentMethod = "card"
	// PaymentMethodMobile 移动支付
	PaymentMethodMobile PaymentMethod = "mobile"
	// PaymentMethodVoucher 代金券
	PaymentMethodVoucher PaymentMethod = "voucher"
)

// Payment 支付领域模型
type Payment struct {
	ID                 uuid.UUID              `json:"id"`
	OrderID            uuid.UUID              `json:"orderId"`
	Amount             float64                `json:"amount"`
	Method             PaymentMethod          `json:"method"`
	Status             PaymentStatus          `json:"status"`
	ReferenceNumber    string                 `json:"referenceNumber"`
	TransactionID      *string                `json:"transactionId,omitempty"`
	Metadata           map[string]interface{} `json:"metadata,omitempty"`
	EmployeeID         *uuid.UUID             `json:"employeeId,omitempty"`
	MemberID           *uuid.UUID             `json:"memberId,omitempty"`
	MemberCardID       *string                `json:"memberCardId,omitempty"`
	CreatedAt          time.Time              `json:"createdAt"`
	UpdatedAt          time.Time              `json:"updatedAt"`
	CompletedAt        *time.Time             `json:"completedAt,omitempty"`
	FailedAt           *time.Time             `json:"failedAt,omitempty"`
	CanceledAt         *time.Time             `json:"canceledAt,omitempty"`
	RefundedAt         *time.Time             `json:"refundedAt,omitempty"`
	FailureReason      *string                `json:"failureReason,omitempty"`
	CancellationReason *string                `json:"cancellationReason,omitempty"`
	RefundReason       *string                `json:"refundReason,omitempty"`
}

// NewPayment 创建新的支付
func NewPayment(
	orderID uuid.UUID,
	amount float64,
	method PaymentMethod,
	referenceNumber string,
) (*Payment, error) {
	if amount <= 0 {
		return nil, errors.New("金额必须大于0")
	}

	now := time.Now()

	return &Payment{
		OrderID:         orderID,
		Amount:          amount,
		Method:          method,
		Status:          PaymentStatusPending,
		ReferenceNumber: referenceNumber,
		Metadata:        make(map[string]interface{}),
		CreatedAt:       now,
		UpdatedAt:       now,
	}, nil
}

// Complete 完成支付
func (p *Payment) Complete(transactionID string) error {
	if p.Status != PaymentStatusPending {
		return errors.New("只有待处理状态的支付可以被完成")
	}

	now := time.Now()
	p.Status = PaymentStatusCompleted
	p.TransactionID = &transactionID
	p.CompletedAt = &now
	p.UpdatedAt = now

	return nil
}

// Fail 支付失败
func (p *Payment) Fail(reason string) error {
	if p.Status != PaymentStatusPending {
		return errors.New("只有待处理状态的支付可以被标记为失败")
	}

	now := time.Now()
	p.Status = PaymentStatusFailed
	p.FailureReason = &reason
	p.FailedAt = &now
	p.UpdatedAt = now

	return nil
}

// Cancel 取消支付
func (p *Payment) Cancel(reason string) error {
	if p.Status != PaymentStatusPending {
		return errors.New("只有待处理状态的支付可以被取消")
	}

	now := time.Now()
	p.Status = PaymentStatusCanceled
	p.CancellationReason = &reason
	p.CanceledAt = &now
	p.UpdatedAt = now

	return nil
}

// Refund 退款
func (p *Payment) Refund(reason string) error {
	if p.Status != PaymentStatusCompleted {
		return errors.New("只有已完成状态的支付可以被退款")
	}

	now := time.Now()
	p.Status = PaymentStatusRefunded
	p.RefundReason = &reason
	p.RefundedAt = &now
	p.UpdatedAt = now

	return nil
}

// AddMemberInfo 添加会员信息
func (p *Payment) AddMemberInfo(memberCardID string, memberID uuid.UUID) {
	p.MemberCardID = &memberCardID
	p.MemberID = &memberID
	p.UpdatedAt = time.Now()
}

// AddEmployeeInfo 添加员工信息
func (p *Payment) AddEmployeeInfo(employeeID uuid.UUID) {
	p.EmployeeID = &employeeID
	p.UpdatedAt = time.Now()
}

// AddMetadata 添加元数据
func (p *Payment) AddMetadata(key string, value interface{}) {
	if p.Metadata == nil {
		p.Metadata = make(map[string]interface{})
	}
	p.Metadata[key] = value
	p.UpdatedAt = time.Now()
}

// IsPending 是否待处理
func (p *Payment) IsPending() bool {
	return p.Status == PaymentStatusPending
}

// IsCompleted 是否已完成
func (p *Payment) IsCompleted() bool {
	return p.Status == PaymentStatusCompleted
}

// IsFailed 是否已失败
func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

// IsCanceled 是否已取消
func (p *Payment) IsCanceled() bool {
	return p.Status == PaymentStatusCanceled
}

// IsRefunded 是否已退款
func (p *Payment) IsRefunded() bool {
	return p.Status == PaymentStatusRefunded
}
