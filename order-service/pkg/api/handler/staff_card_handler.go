package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/log"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// StaffCardHandler 处理员工卡相关HTTP请求
type StaffCardHandler interface {
	// CreateStaffCard 创建员工卡
	CreateStaffCard(c echo.Context) error
	// GetStaffCard 获取员工卡详情
	GetStaffCard(c echo.Context) error
	// GetStaffCardByNumber 根据卡号获取员工卡
	GetStaffCardByNumber(c echo.Context) error
	// ListStaffCards 获取员工卡列表
	ListStaffCards(c echo.Context) error
	// UpdateStaffCard 更新员工卡
	UpdateStaffCard(c echo.Context) error
	// DeleteStaffCard 删除员工卡
	DeleteStaffCard(c echo.Context) error
	// ActivateCard 激活员工卡
	ActivateCard(c echo.Context) error
	// SuspendCard 暂停员工卡
	SuspendCard(c echo.Context) error
	// ValidateCard 验证员工卡
	ValidateCard(c echo.Context) error
	// GetUserStaffCards 获取用户的员工卡列表
	GetUserStaffCards(c echo.Context) error
	// CreateStaffCardForUser 为用户创建员工卡
	CreateStaffCardForUser(c echo.Context) error
	// ExtendCardValidity 延长员工卡有效期
	ExtendCardValidity(c echo.Context) error
	// GetActiveCardsByStation 获取指定站点的活跃员工卡
	GetActiveCardsByStation(c echo.Context) error
	// BulkCreateStaffCards 批量创建员工卡
	BulkCreateStaffCards(c echo.Context) error
}

// staffCardHandlerImpl StaffCardHandler接口实现
type staffCardHandlerImpl struct {
	staffCardService service.StaffCardService
	logger           log.Logger
}

// NewStaffCardHandler 创建新的StaffCardHandler实例
func NewStaffCardHandler(staffCardService service.StaffCardService, logger log.Logger) StaffCardHandler {
	return &staffCardHandlerImpl{
		staffCardService: staffCardService,
		logger:           logger,
	}
}

// CreateStaffCardRequest 创建员工卡请求
type CreateStaffCardRequest struct {
	CardNumber  string                 `json:"cardNumber,omitempty"`
	UserID      string                 `json:"userId" validate:"required"`
	StationID   *int64                 `json:"stationId,omitempty"`
	CardType    string                 `json:"cardType" validate:"required,oneof=employee manager admin"`
	Status      string                 `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended expired"`
	ValidFrom   *time.Time             `json:"validFrom,omitempty"`
	ValidUntil  *time.Time             `json:"validUntil,omitempty"`
	Permissions map[string]interface{} `json:"permissions,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// CreateStaffCard 创建员工卡
// @Summary 创建员工卡
// @Description 创建一个新的员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param staffCard body CreateStaffCardRequest true "员工卡信息"
// @Success 201 {object} repository.StaffCard
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards [post]
func (h *staffCardHandlerImpl) CreateStaffCard(c echo.Context) error {
	var req CreateStaffCardRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 转换为仓储层模型
	staffCard := repository.StaffCard{
		CardNumber: req.CardNumber,
		UserID:     req.UserID,
		CardType:   repository.StaffCardType(req.CardType),
		Status:     repository.StaffCardStatus(req.Status),
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := repository.IDFromInt64(*req.StationID)
		staffCard.StationID = &stationID
	}

	// 处理时间字段
	if req.ValidFrom != nil {
		staffCard.ValidFrom = *req.ValidFrom
	}
	staffCard.ValidUntil = req.ValidUntil

	// 处理权限和元数据
	if req.Permissions != nil {
		staffCard.Permissions = req.Permissions
	} else {
		staffCard.Permissions = make(map[string]interface{})
	}

	if req.Metadata != nil {
		staffCard.Metadata = req.Metadata
	} else {
		staffCard.Metadata = make(map[string]interface{})
	}

	// 创建员工卡
	createdCard, err := h.staffCardService.CreateStaffCard(c.Request().Context(), staffCard)
	if err != nil {
		h.logger.Error("Failed to create staff card")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create staff card")
	}

	return c.JSON(http.StatusCreated, createdCard)
}

// GetStaffCard 获取员工卡详情
// @Summary 获取员工卡详情
// @Description 根据ID获取员工卡的详细信息
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param id path int true "员工卡ID"
// @Param includeUser query bool false "是否包含用户信息"
// @Success 200 {object} repository.StaffCard
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{id} [get]
func (h *staffCardHandlerImpl) GetStaffCard(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid staff card ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid staff card ID")
	}

	includeUser := c.QueryParam("includeUser") == "true"

	var staffCard repository.StaffCard
	if includeUser {
		staffCard, err = h.staffCardService.GetStaffCardWithUser(c.Request().Context(), repository.IDFromInt64(id))
	} else {
		staffCard, err = h.staffCardService.GetStaffCard(c.Request().Context(), repository.IDFromInt64(id))
	}

	if err != nil {
		h.logger.Error("Failed to get staff card")
		return echo.NewHTTPError(http.StatusNotFound, "Staff card not found")
	}

	return c.JSON(http.StatusOK, staffCard)
}

// GetStaffCardByNumber 根据卡号获取员工卡
// @Summary 根据卡号获取员工卡
// @Description 根据卡号获取员工卡的详细信息
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param cardNumber path string true "员工卡号"
// @Success 200 {object} repository.StaffCard
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/by-number/{cardNumber} [get]
func (h *staffCardHandlerImpl) GetStaffCardByNumber(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	staffCard, err := h.staffCardService.GetStaffCardByNumber(c.Request().Context(), cardNumber)
	if err != nil {
		h.logger.Error("Failed to get staff card by number")
		return echo.NewHTTPError(http.StatusNotFound, "Staff card not found")
	}

	return c.JSON(http.StatusOK, staffCard)
}

// StaffCardFilterRequest 员工卡过滤请求
type StaffCardFilterRequest struct {
	UserID      *string `query:"userId"`
	StationID   *int64  `query:"stationId"`
	CardType    *string `query:"cardType" validate:"omitempty,oneof=employee manager admin"`
	Status      *string `query:"status" validate:"omitempty,oneof=active inactive suspended expired"`
	CardNumber  *string `query:"cardNumber"`
	DateFrom    *string `query:"dateFrom"`
	DateTo      *string `query:"dateTo"`
	ValidOnly   *bool   `query:"validOnly"`
	IncludeUser *bool   `query:"includeUser"`
	Page        int     `query:"page" validate:"min=1"`
	PageSize    int     `query:"pageSize" validate:"min=1,max=100"`
	SortBy      string  `query:"sortBy"`
	SortOrder   string  `query:"sortOrder" validate:"omitempty,oneof=asc desc"`
}

// ListStaffCards 获取员工卡列表
// @Summary 获取员工卡列表
// @Description 获取符合条件的员工卡列表
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param request query StaffCardFilterRequest false "过滤条件"
// @Success 200 {object} PagedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards [get]
func (h *staffCardHandlerImpl) ListStaffCards(c echo.Context) error {
	var req StaffCardFilterRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	} else if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 创建过滤器
	filter := repository.StaffCardFilter{
		UserID:     req.UserID,
		CardNumber: req.CardNumber,
		ValidOnly:  req.ValidOnly,
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := repository.IDFromInt64(*req.StationID)
		filter.StationID = &stationID
	}

	// 处理CardType
	if req.CardType != nil {
		cardType := repository.StaffCardType(*req.CardType)
		filter.CardType = &cardType
	}

	// 处理Status
	if req.Status != nil {
		status := repository.StaffCardStatus(*req.Status)
		filter.Status = &status
	}

	// 处理日期范围
	if req.DateFrom != nil {
		if dateFrom, err := time.Parse("2006-01-02", *req.DateFrom); err == nil {
			filter.DateFrom = &dateFrom
		}
	}
	if req.DateTo != nil {
		if dateTo, err := time.Parse("2006-01-02", *req.DateTo); err == nil {
			filter.DateTo = &dateTo
		}
	}

	// 创建分页
	pagination := repository.Pagination{
		Page:  req.Page,
		Limit: req.PageSize,
	}

	// 创建排序
	sort := repository.SortOrder{
		Field:     req.SortBy,
		Direction: req.SortOrder,
	}

	// 如果排序字段未指定，默认按创建时间降序
	if sort.Field == "" {
		sort.Field = "created_at"
		sort.Direction = "desc"
	}

	// 获取员工卡列表
	var staffCards []repository.StaffCard
	var total int
	var err error

	includeUser := req.IncludeUser != nil && *req.IncludeUser
	if includeUser {
		staffCards, total, err = h.staffCardService.ListStaffCardsWithUser(c.Request().Context(), filter, pagination, sort)
	} else {
		staffCards, total, err = h.staffCardService.ListStaffCards(c.Request().Context(), filter, pagination, sort)
	}

	if err != nil {
		h.logger.Error("Failed to list staff cards")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch staff cards")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  staffCards,
		"page":  req.Page,
		"size":  req.PageSize,
		"total": total,
	})
}

// UpdateStaffCardRequest 更新员工卡请求
type UpdateStaffCardRequest struct {
	StationID   *int64                 `json:"stationId,omitempty"`
	CardType    *string                `json:"cardType,omitempty" validate:"omitempty,oneof=employee manager admin"`
	Status      *string                `json:"status,omitempty" validate:"omitempty,oneof=active inactive suspended expired"`
	ValidFrom   *time.Time             `json:"validFrom,omitempty"`
	ValidUntil  *time.Time             `json:"validUntil,omitempty"`
	Permissions map[string]interface{} `json:"permissions,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateStaffCard 更新员工卡
// @Summary 更新员工卡
// @Description 更新员工卡信息
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param id path int true "员工卡ID"
// @Param staffCard body UpdateStaffCardRequest true "员工卡更新信息"
// @Success 200 {object} repository.StaffCard
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{id} [put]
func (h *staffCardHandlerImpl) UpdateStaffCard(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid staff card ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid staff card ID")
	}

	var req UpdateStaffCardRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 获取现有员工卡
	existingCard, err := h.staffCardService.GetStaffCard(c.Request().Context(), repository.IDFromInt64(id))
	if err != nil {
		h.logger.Error("Failed to get existing staff card")
		return echo.NewHTTPError(http.StatusNotFound, "Staff card not found")
	}

	// 更新字段
	if req.StationID != nil {
		stationID := repository.IDFromInt64(*req.StationID)
		existingCard.StationID = &stationID
	}

	if req.CardType != nil {
		existingCard.CardType = repository.StaffCardType(*req.CardType)
	}

	if req.Status != nil {
		existingCard.Status = repository.StaffCardStatus(*req.Status)
	}

	if req.ValidFrom != nil {
		existingCard.ValidFrom = *req.ValidFrom
	}

	if req.ValidUntil != nil {
		existingCard.ValidUntil = req.ValidUntil
	}

	if req.Permissions != nil {
		existingCard.Permissions = req.Permissions
	}

	if req.Metadata != nil {
		existingCard.Metadata = req.Metadata
	}

	// 更新员工卡
	updatedCard, err := h.staffCardService.UpdateStaffCard(c.Request().Context(), existingCard)
	if err != nil {
		h.logger.Error("Failed to update staff card")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update staff card")
	}

	return c.JSON(http.StatusOK, updatedCard)
}

// DeleteStaffCard 删除员工卡
// @Summary 删除员工卡
// @Description 软删除员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param id path int true "员工卡ID"
// @Success 204 "No Content"
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{id} [delete]
func (h *staffCardHandlerImpl) DeleteStaffCard(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid staff card ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid staff card ID")
	}

	err = h.staffCardService.DeleteStaffCard(c.Request().Context(), repository.IDFromInt64(id))
	if err != nil {
		h.logger.Error("Failed to delete staff card")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete staff card")
	}

	return c.NoContent(http.StatusNoContent)
}

// ActivateCard 激活员工卡
// @Summary 激活员工卡
// @Description 激活指定的员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param cardNumber path string true "员工卡号"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{cardNumber}/activate [post]
func (h *staffCardHandlerImpl) ActivateCard(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	err := h.staffCardService.ActivateCard(c.Request().Context(), cardNumber)
	if err != nil {
		h.logger.Error("Failed to activate card")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to activate card")
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Card activated successfully",
	})
}

// SuspendCardRequest 暂停员工卡请求
type SuspendCardRequest struct {
	Reason string `json:"reason,omitempty"`
}

// SuspendCard 暂停员工卡
// @Summary 暂停员工卡
// @Description 暂停指定的员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param cardNumber path string true "员工卡号"
// @Param request body SuspendCardRequest false "暂停原因"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{cardNumber}/suspend [post]
func (h *staffCardHandlerImpl) SuspendCard(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	var req SuspendCardRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	reason := req.Reason
	if reason == "" {
		reason = "Manual suspension"
	}

	err := h.staffCardService.SuspendCard(c.Request().Context(), cardNumber, reason)
	if err != nil {
		h.logger.Error("Failed to suspend card")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to suspend card")
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Card suspended successfully",
	})
}

// ValidateCardRequest 验证员工卡请求
type ValidateCardRequest struct {
	StationID int64 `json:"stationId" validate:"required"`
}

// ValidateCard 验证员工卡
// @Summary 验证员工卡
// @Description 验证员工卡是否可用于交易
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param cardNumber path string true "员工卡号"
// @Param request body ValidateCardRequest true "验证请求"
// @Success 200 {object} repository.StaffCardValidationResult
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{cardNumber}/validate [post]
func (h *staffCardHandlerImpl) ValidateCard(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	var req ValidateCardRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	result, err := h.staffCardService.ValidateCardForTransaction(c.Request().Context(), cardNumber, repository.IDFromInt64(req.StationID))
	if err != nil {
		h.logger.Error("Failed to validate card")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to validate card")
	}

	return c.JSON(http.StatusOK, result)
}

// GetUserStaffCards 获取用户的员工卡列表
// @Summary 获取用户的员工卡列表
// @Description 获取指定用户的所有员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param userId path string true "用户ID"
// @Success 200 {array} repository.StaffCard
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users/{userId}/staff-cards [get]
func (h *staffCardHandlerImpl) GetUserStaffCards(c echo.Context) error {
	userID := c.Param("userId")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	staffCards, err := h.staffCardService.GetUserStaffCards(c.Request().Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get user staff cards")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get user staff cards")
	}

	return c.JSON(http.StatusOK, staffCards)
}

// CreateStaffCardForUserRequest 为用户创建员工卡请求
type CreateStaffCardForUserRequest struct {
	CardNumber string `json:"cardNumber" validate:"required,min=3,max=50"`
	CardType   string `json:"cardType" validate:"required,oneof=employee manager admin"`
	StationID  *int64 `json:"stationId,omitempty"`
}

// CreateStaffCardForUser 为用户创建员工卡
// @Summary 为用户创建员工卡
// @Description 为指定用户创建员工卡（自动生成卡号）
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param userId path string true "用户ID"
// @Param request body CreateStaffCardForUserRequest true "创建请求"
// @Success 201 {object} repository.StaffCard
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users/{userId}/staff-cards [post]
func (h *staffCardHandlerImpl) CreateStaffCardForUser(c echo.Context) error {
	userID := c.Param("userId")
	if userID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "User ID is required")
	}

	var req CreateStaffCardForUserRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	var stationID *repository.ID
	if req.StationID != nil {
		id := repository.IDFromInt64(*req.StationID)
		stationID = &id
	}

	staffCard, err := h.staffCardService.CreateStaffCardForUserWithCardNumber(c.Request().Context(), userID, req.CardNumber, repository.StaffCardType(req.CardType), stationID)
	if err != nil {
		h.logger.Error("Failed to create staff card for user")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create staff card")
	}

	return c.JSON(http.StatusCreated, staffCard)
}

// ExtendCardValidityRequest 延长员工卡有效期请求
type ExtendCardValidityRequest struct {
	ValidUntil *time.Time `json:"validUntil,omitempty"`
}

// ExtendCardValidity 延长员工卡有效期
// @Summary 延长员工卡有效期
// @Description 延长指定员工卡的有效期
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param cardNumber path string true "员工卡号"
// @Param request body ExtendCardValidityRequest true "延期请求"
// @Success 200 {object} map[string]string
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/{cardNumber}/extend [post]
func (h *staffCardHandlerImpl) ExtendCardValidity(c echo.Context) error {
	cardNumber := c.Param("cardNumber")
	if cardNumber == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Card number is required")
	}

	var req ExtendCardValidityRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	err := h.staffCardService.ExtendCardValidity(c.Request().Context(), cardNumber, req.ValidUntil)
	if err != nil {
		h.logger.Error("Failed to extend card validity")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to extend card validity")
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Card validity extended successfully",
	})
}

// GetActiveCardsByStation 获取指定站点的活跃员工卡
// @Summary 获取指定站点的活跃员工卡
// @Description 获取指定站点的所有活跃员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param stationId path int true "站点ID"
// @Success 200 {array} repository.StaffCard
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/stations/{stationId}/staff-cards [get]
func (h *staffCardHandlerImpl) GetActiveCardsByStation(c echo.Context) error {
	stationID, err := strconv.ParseInt(c.Param("stationId"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid station ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid station ID")
	}

	staffCards, err := h.staffCardService.GetActiveCardsByStation(c.Request().Context(), repository.IDFromInt64(stationID))
	if err != nil {
		h.logger.Error("Failed to get active cards by station")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get active cards")
	}

	return c.JSON(http.StatusOK, staffCards)
}

// BulkCreateStaffCardsRequest 批量创建员工卡请求
type BulkCreateStaffCardsRequest struct {
	UserIDs   []string `json:"userIds" validate:"required,min=1,max=50"`
	CardType  string   `json:"cardType" validate:"required,oneof=employee manager admin"`
	StationID *int64   `json:"stationId,omitempty"`
}

// BulkCreateStaffCards 批量创建员工卡
// @Summary 批量创建员工卡
// @Description 为多个用户批量创建员工卡
// @Tags 员工卡
// @Accept json
// @Produce json
// @Param request body BulkCreateStaffCardsRequest true "批量创建请求"
// @Success 201 {array} repository.StaffCard
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/staff-cards/bulk [post]
func (h *staffCardHandlerImpl) BulkCreateStaffCards(c echo.Context) error {
	var req BulkCreateStaffCardsRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	var stationID *repository.ID
	if req.StationID != nil {
		id := repository.IDFromInt64(*req.StationID)
		stationID = &id
	}

	staffCards, err := h.staffCardService.BulkCreateStaffCards(c.Request().Context(), req.UserIDs, repository.StaffCardType(req.CardType), stationID)
	if err != nil {
		h.logger.Error("Failed to bulk create staff cards")
		// 如果是部分失败，仍然返回成功创建的卡片
		if len(staffCards) > 0 {
			return c.JSON(http.StatusCreated, map[string]interface{}{
				"data":    staffCards,
				"message": err.Error(),
			})
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create staff cards")
	}

	return c.JSON(http.StatusCreated, staffCards)
} 