package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/log"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// ReportHandler 处理报表相关的请求
type ReportHandler struct {
	reportService service.ReportService
	logger        log.Logger
}

// NewReportHandler 创建一个新的ReportHandler实例
func NewReportHandler(reportService service.ReportService, logger log.Logger) *ReportHandler {
	return &ReportHandler{
		reportService: reportService,
		logger:        logger,
	}
}

// GetPaymentMethodSummary 获取按支付方式的销售汇总
func (h *ReportHandler) GetPaymentMethodSummary(c echo.Context) error {
	// 获取查询参数
	stationIDStr := c.QueryParam("station_id")
	dateFromStr := c.QueryParam("date_from")
	dateToStr := c.QueryParam("date_to")

	// 构建筛选条件
	filter := repository.ReportFilter{}

	// 处理站点ID
	if stationIDStr != "" {
		var stationIDInt64 int64
		if err := echo.QueryParamsBinder(c).Int64("station_id", &stationIDInt64).BindError(); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的站点ID")
		}
		stationID := repository.IDFromInt64(stationIDInt64)
		filter.StationID = &stationID
	}

	// 处理日期范围
	if dateFromStr != "" {
		dateFrom, err := time.Parse("2006-01-02", dateFromStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的开始日期格式，应为YYYY-MM-DD")
		}
		filter.DateFrom = &dateFrom
	}

	if dateToStr != "" {
		dateTo, err := time.Parse("2006-01-02", dateToStr)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的结束日期格式，应为YYYY-MM-DD")
		}
		// 设置为当天的结束时间
		dateTo = time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), 23, 59, 59, 999999999, dateTo.Location())
		filter.DateTo = &dateTo
	}

	// 调用服务层获取数据
	summaries, err := h.reportService.GetPaymentMethodSummary(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取支付方式汇总失败", log.NewField("error", err.Error()))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取支付方式汇总失败")
	}

	return c.JSON(http.StatusOK, summaries)
}

// GetAggregatedRevenue 获取聚合收入报表
func (h *ReportHandler) GetAggregatedRevenue(c echo.Context) error {
	// 获取查询参数
	startDate := c.QueryParam("start_date")
	endDate := c.QueryParam("end_date")

	// 验证必填参数
	if startDate == "" || endDate == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "开始日期和结束日期是必填参数")
	}

	// 获取站点ID列表（可选）
	var siteIDs []int
	siteIDsParam := c.QueryParam("site_ids")
	if siteIDsParam != "" {
		if err := echo.QueryParamsBinder(c).Ints("site_ids", &siteIDs).BindError(); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的站点ID列表格式")
		}
	}

	// 构建筛选条件
	filter := repository.AggregationFilter{
		StartDate: startDate,
		EndDate:   endDate,
		SiteIDs:   siteIDs,
	}

	// 调用服务层获取数据
	revenues, err := h.reportService.GetAggregatedRevenue(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取聚合收入报表失败", log.NewField("error", err.Error()))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取聚合收入报表失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, revenues)
}

// GetAggregatedReceivable 获取油品应收汇总
func (h *ReportHandler) GetAggregatedReceivable(c echo.Context) error {
	// 获取查询参数
	startDate := c.QueryParam("start_date")
	endDate := c.QueryParam("end_date")

	// 验证必填参数
	if startDate == "" || endDate == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "开始日期和结束日期是必填参数")
	}

	// 获取站点ID列表（可选）
	var siteIDs []int
	siteIDsParam := c.QueryParam("site_ids")
	if siteIDsParam != "" {
		if err := echo.QueryParamsBinder(c).Ints("site_ids", &siteIDs).BindError(); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的站点ID列表格式")
		}
	}

	// 构建筛选条件
	filter := repository.AggregationFilter{
		StartDate: startDate,
		EndDate:   endDate,
		SiteIDs:   siteIDs,
	}

	// 调用服务层获取数据
	receivables, err := h.reportService.GetAggregatedReceivable(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取油品应收汇总失败", log.NewField("error", err.Error()))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取油品应收汇总失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, receivables)
}

// GetNozzleSalesSummary 获取油枪销售汇总
func (h *ReportHandler) GetNozzleSalesSummary(c echo.Context) error {
	// 获取查询参数
	startDate := c.QueryParam("start_date")
	endDate := c.QueryParam("end_date")

	// 验证必填参数
	if startDate == "" || endDate == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "开始日期和结束日期是必填参数")
	}

	// 获取站点ID列表（可选）
	var siteIDs []int
	siteIDsParam := c.QueryParam("site_ids")
	if siteIDsParam != "" {
		if err := echo.QueryParamsBinder(c).Ints("site_ids", &siteIDs).BindError(); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的站点ID列表格式")
		}
	}

	// 构建筛选条件
	filter := repository.AggregationFilter{
		StartDate: startDate,
		EndDate:   endDate,
		SiteIDs:   siteIDs,
	}

	// 调用服务层获取数据
	summaries, err := h.reportService.GetNozzleSalesSummary(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取油枪销售汇总失败", log.NewField("error", err.Error()))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取油枪销售汇总失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, summaries)
}

// GetTransactionsForReport 获取交易明细数据(用于报表)
func (h *ReportHandler) GetTransactionsForReport(c echo.Context) error {
	// 获取查询参数
	startDate := c.QueryParam("start_date")
	endDate := c.QueryParam("end_date")

	// 验证必填参数
	if startDate == "" || endDate == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "开始日期和结束日期是必填参数")
	}

	// 获取站点ID列表（可选）
	var siteIDs []int
	siteIDsParam := c.QueryParam("site_ids")
	if siteIDsParam != "" {
		if err := echo.QueryParamsBinder(c).Ints("site_ids", &siteIDs).BindError(); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的站点ID列表格式")
		}
	}

	// 构建筛选条件
	filter := repository.TransactionFilter{
		StartDate: startDate,
		EndDate:   endDate,
		SiteIDs:   siteIDs,
	}

	// 调用服务层获取数据
	transactions, err := h.reportService.GetTransactionsForReport(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取交易明细数据失败", log.NewField("error", err.Error()))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取交易明细数据失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, transactions)
}

// GetAggregatedSalesByProductCategory 获取按商品分类的销售汇总
func (h *ReportHandler) GetAggregatedSalesByProductCategory(c echo.Context) error {
	// 获取查询参数
	startDate := c.QueryParam("start_date")
	endDate := c.QueryParam("end_date")

	// 验证必填参数
	if startDate == "" || endDate == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "开始日期和结束日期是必填参数")
	}

	// 获取站点ID列表（可选）
	var siteIDs []int
	siteIDsParam := c.QueryParam("site_ids")
	if siteIDsParam != "" {
		if err := echo.QueryParamsBinder(c).Ints("site_ids", &siteIDs).BindError(); err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "无效的站点ID列表格式")
		}
	}

	// 构建筛选条件
	filter := repository.AggregationFilter{
		StartDate: startDate,
		EndDate:   endDate,
		SiteIDs:   siteIDs,
	}

	// 调用服务层获取数据
	sales, err := h.reportService.GetAggregatedSalesByProductCategory(c.Request().Context(), filter)
	if err != nil {
		h.logger.Error("获取按商品分类的销售汇总失败", log.NewField("error", err.Error()))
		return echo.NewHTTPError(http.StatusInternalServerError, "获取按商品分类的销售汇总失败: "+err.Error())
	}

	return c.JSON(http.StatusOK, sales)
}

// GetShiftEODReport 获取班次日终报表
func (h *ReportHandler) GetShiftEODReport(c echo.Context) error {
	// 获取查询参数
	stationIDStr := c.QueryParam("station_id")
	date := c.QueryParam("date")

	// 验证必填参数
	if stationIDStr == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Station ID is required")
	}
	if date == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Date is required")
	}

	// 解析站点ID
	stationID, err := strconv.ParseInt(stationIDStr, 10, 64)
	if err != nil || stationID <= 0 {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid station ID")
	}

	// 调用服务层获取数据
	report, err := h.reportService.GetShiftEODReport(c.Request().Context(), repository.IDFromInt64(stationID), date)
	if err != nil {
		h.logger.Error("获取班次日终报表失败", log.NewField("error", err.Error()), log.NewField("station_id", stationID), log.NewField("date", date))
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get shift EOD report: "+err.Error())
	}

	// 返回成功响应
	response := map[string]interface{}{
		"code":    200,
		"message": "Success",
		"data":    report,
	}

	return c.JSON(http.StatusOK, response)
}
