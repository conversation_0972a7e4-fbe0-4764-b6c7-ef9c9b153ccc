package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/log"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/service"
)

// FuelTransactionHandler 处理燃油交易相关HTTP请求
type FuelTransactionHandler interface {
	// CreateFuelTransaction 创建燃油交易
	CreateFuelTransaction(c echo.Context) error
	// GetFuelTransaction 获取燃油交易详情
	GetFuelTransaction(c echo.Context) error
	// ListFuelTransactions 获取燃油交易列表
	ListFuelTransactions(c echo.Context) error
	// LinkFuelTransactionToOrder 关联燃油交易到订单
	LinkFuelTransactionToOrder(c echo.Context) error
	// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
	UnlinkFuelTransactionFromOrder(c echo.Context) error
	// GetOrderFuelTransactions 获取订单的燃油交易
	GetOrderFuelTransactions(c echo.Context) error
	// GetFuelTransactionOrders 获取燃油交易的订单
	GetFuelTransactionOrders(c echo.Context) error
	// UpdateLinkAllocatedAmount 更新关联分配金额
	UpdateLinkAllocatedAmount(c echo.Context) error
	// ConfirmFuelTransactionLink 确认燃油交易链接
	ConfirmFuelTransactionLink(c echo.Context) error
	// CancelFuelTransactionLink 取消燃油交易链接
	CancelFuelTransactionLink(c echo.Context) error
}

// fuelTransactionHandlerImpl FuelTransactionHandler接口实现
type fuelTransactionHandlerImpl struct {
	fuelTransactionService service.FuelTransactionService
	logger                 log.Logger
}

// NewFuelTransactionHandler 创建新的FuelTransactionHandler实例
func NewFuelTransactionHandler(fuelTransactionService service.FuelTransactionService, logger log.Logger) FuelTransactionHandler {
	return &fuelTransactionHandlerImpl{
		fuelTransactionService: fuelTransactionService,
		logger:                 logger,
	}
}

// CreateFuelTransactionRequest 创建燃油交易请求
type CreateFuelTransactionRequest struct {
	TransactionNumber string                 `json:"transactionNumber" validate:"required"`
	StationID         int64                  `json:"stationId" validate:"required"`
	PumpID            string                 `json:"pumpId" validate:"required"`
	NozzleID          string                 `json:"nozzleId" validate:"required"`
	FuelType          string                 `json:"fuelType"`
	FuelGrade         string                 `json:"fuelGrade" validate:"required"`
	Tank              int                    `json:"tank" validate:"required"`
	UnitPrice         float64                `json:"unitPrice" validate:"required,gte=0"`
	Volume            float64                `json:"volume" validate:"required,gte=0"`
	Amount            float64                `json:"amount" validate:"required,gte=0"`
	TotalVolume       float64                `json:"totalVolume" validate:"required,gte=0"`
	TotalAmount       float64                `json:"totalAmount" validate:"required,gte=0"`
	StaffCardID       *int64                 `json:"staffCardId"`
	EmployeeID        *int64                 `json:"employeeId"` // 兼容性字段
	Metadata          map[string]interface{} `json:"metadata"`
}

// CreateFuelTransaction 创建燃油交易
// @Summary 创建燃油交易
// @Description 创建一个新的燃油交易
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transaction body CreateFuelTransactionRequest true "燃油交易信息"
// @Success 201 {object} repository.FuelTransaction
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions [post]
func (h *fuelTransactionHandlerImpl) CreateFuelTransaction(c echo.Context) error {
	var req CreateFuelTransactionRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 转换为仓储层模型
	transaction := repository.FuelTransaction{
		TransactionNumber: req.TransactionNumber,
		StationID:         repository.IDFromInt64(req.StationID),
		PumpID:            req.PumpID,
		NozzleID:          req.NozzleID,
		FuelType:          req.FuelType,
		FuelGrade:         req.FuelGrade,
		Tank:              req.Tank,
		UnitPrice:         req.UnitPrice,
		Volume:            req.Volume,
		Amount:            req.Amount,
		TotalVolume:       req.TotalVolume,
		TotalAmount:       req.TotalAmount,
		Status:            repository.FuelTransactionStatusPending,
		Metadata:          req.Metadata,
	}

	// 处理StaffCardID
	if req.StaffCardID != nil {
		staffCardID := repository.IDFromInt64(*req.StaffCardID)
		transaction.StaffCardID = &staffCardID
	}

	// 处理EmployeeID（兼容性）
	if req.EmployeeID != nil {
		employeeID := repository.IDFromInt64(*req.EmployeeID)
		transaction.EmployeeID = &employeeID
	}

	// 创建燃油交易
	createdTransaction, err := h.fuelTransactionService.CreateFuelTransaction(c.Request().Context(), transaction)
	if err != nil {
		h.logger.Error("Failed to create fuel transaction")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create fuel transaction")
	}

	return c.JSON(http.StatusCreated, createdTransaction)
}

// GetFuelTransaction 获取燃油交易详情
// @Summary 获取燃油交易详情
// @Description 根据ID获取燃油交易的详细信息
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param id path int true "燃油交易ID"
// @Success 200 {object} repository.FuelTransaction
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{id} [get]
func (h *fuelTransactionHandlerImpl) GetFuelTransaction(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid fuel transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid fuel transaction ID")
	}

	transaction, err := h.fuelTransactionService.GetFuelTransaction(c.Request().Context(), repository.IDFromInt64(id))
	if err != nil {
		h.logger.Error("Failed to get fuel transaction")
		return echo.NewHTTPError(http.StatusNotFound, "Fuel transaction not found")
	}

	return c.JSON(http.StatusOK, transaction)
}

// FuelTransactionFilterRequest 燃油交易过滤请求
type FuelTransactionFilterRequest struct {
	StationID         *int64  `query:"stationId"`
	PumpID            *string `query:"pumpId"`
	NozzleID          *string `query:"nozzleId"`
	FuelType          *string `query:"fuelType"`
	FuelGrade         *string `query:"fuelGrade"`
	Status            *string `query:"status"`
	TransactionNumber *string `query:"transactionNumber"`
	StaffCardID       *int64  `query:"staffCardId"`
	EmployeeID        *int64  `query:"employeeId"` // 兼容性字段
	DateFrom          *string `query:"dateFrom"`
	DateTo            *string `query:"dateTo"`
	Page              int     `query:"page" validate:"min=1"`
	PageSize          int     `query:"pageSize" validate:"min=1,max=100"`
	SortBy            string  `query:"sortBy"`
	SortOrder         string  `query:"sortOrder" validate:"omitempty,oneof=asc desc"`
}

// ListFuelTransactions 获取燃油交易列表
// @Summary 获取燃油交易列表
// @Description 获取符合条件的燃油交易列表
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param request query FuelTransactionFilterRequest false "过滤条件"
// @Success 200 {object} PagedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions [get]
func (h *fuelTransactionHandlerImpl) ListFuelTransactions(c echo.Context) error {
	var req FuelTransactionFilterRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	} else if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 创建过滤器
	filter := repository.FuelTransactionFilter{
		TransactionNumber: req.TransactionNumber,
		FuelType:          req.FuelType,
		FuelGrade:         req.FuelGrade,
	}

	// 处理PumpID
	if req.PumpID != nil {
		filter.PumpID = req.PumpID
	}

	// 处理StationID
	if req.StationID != nil {
		stationID := repository.IDFromInt64(*req.StationID)
		filter.StationID = &stationID
	}

	// 处理StaffCardID
	if req.StaffCardID != nil {
		staffCardID := repository.IDFromInt64(*req.StaffCardID)
		filter.StaffCardID = &staffCardID
	}

	// 处理EmployeeID（兼容性）
	if req.EmployeeID != nil {
		employeeID := repository.IDFromInt64(*req.EmployeeID)
		filter.EmployeeID = &employeeID
	}

	// 处理Status
	if req.Status != nil {
		status := repository.FuelTransactionStatus(*req.Status)
		filter.Status = &status
	}

	// 处理Tank
	if req.NozzleID != nil {
		// NozzleID不在filter中，可以忽略或者记录日志
		h.logger.Info("NozzleID filter not supported in current implementation")
	}

	// 处理日期范围
	if req.DateFrom != nil {
		if dateFrom, err := time.Parse("2006-01-02", *req.DateFrom); err == nil {
			filter.DateFrom = &dateFrom
		}
	}
	if req.DateTo != nil {
		if dateTo, err := time.Parse("2006-01-02", *req.DateTo); err == nil {
			filter.DateTo = &dateTo
		}
	}

	// 创建分页
	pagination := repository.Pagination{
		Page:  req.Page,
		Limit: req.PageSize,
	}

	// 创建排序
	sort := repository.SortOrder{
		Field:     req.SortBy,
		Direction: req.SortOrder,
	}

	// 如果排序字段未指定，默认按创建时间降序
	if sort.Field == "" {
		sort.Field = "created_at"
		sort.Direction = "desc"
	}

	// 获取燃油交易列表
	transactions, total, err := h.fuelTransactionService.ListFuelTransactions(c.Request().Context(), filter, pagination, sort)
	if err != nil {
		h.logger.Error("Failed to list fuel transactions: " + err.Error())
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch fuel transactions")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  transactions,
		"page":  req.Page,
		"size":  req.PageSize,
		"total": total,
	})
}

// LinkFuelTransactionToOrderRequest 关联燃油交易到订单请求
type LinkFuelTransactionToOrderRequest struct {
	AllocatedAmount float64 `json:"allocatedAmount" validate:"required,gte=0"`
}

// LinkFuelTransactionToOrder 关联燃油交易到订单
// @Summary 关联燃油交易到订单
// @Description 将燃油交易关联到指定订单
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Param request body LinkFuelTransactionToOrderRequest true "关联信息"
// @Success 201 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId} [post]
func (h *fuelTransactionHandlerImpl) LinkFuelTransactionToOrder(c echo.Context) error {
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	var req LinkFuelTransactionToOrderRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 关联燃油交易到订单
	link, err := h.fuelTransactionService.LinkFuelTransactionToOrder(c.Request().Context(), repository.ID(transactionID), repository.ID(orderID), req.AllocatedAmount)
	if err != nil {
		h.logger.Error("Failed to link fuel transaction to order")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to link fuel transaction to order")
	}

	return c.JSON(http.StatusCreated, link)
}

// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
// @Summary 解除燃油交易与订单的关联
// @Description 解除燃油交易与指定订单的关联
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Success 204 "No Content"
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId} [delete]
func (h *fuelTransactionHandlerImpl) UnlinkFuelTransactionFromOrder(c echo.Context) error {
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	err = h.fuelTransactionService.UnlinkFuelTransactionFromOrder(c.Request().Context(), repository.ID(transactionID), repository.ID(orderID))
	if err != nil {
		h.logger.Error("Failed to unlink fuel transaction from order")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to unlink fuel transaction from order")
	}

	return c.NoContent(http.StatusNoContent)
}

// GetOrderFuelTransactions 获取订单的燃油交易
// @Summary 获取订单的燃油交易
// @Description 获取指定订单关联的所有燃油交易
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param orderId path int true "订单ID"
// @Success 200 {array} repository.FuelTransaction
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/orders/{orderId}/fuel-transactions [get]
func (h *fuelTransactionHandlerImpl) GetOrderFuelTransactions(c echo.Context) error {
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	transactions, err := h.fuelTransactionService.GetOrderFuelTransactions(c.Request().Context(), repository.ID(orderID))
	if err != nil {
		h.logger.Error("Failed to get order fuel transactions")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get order fuel transactions")
	}

	return c.JSON(http.StatusOK, transactions)
}

// GetFuelTransactionOrders 获取燃油交易的订单
// @Summary 获取燃油交易的订单
// @Description 获取指定燃油交易关联的所有订单
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Success 200 {array} repository.Order
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders [get]
func (h *fuelTransactionHandlerImpl) GetFuelTransactionOrders(c echo.Context) error {
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	orders, err := h.fuelTransactionService.GetFuelTransactionOrders(c.Request().Context(), repository.ID(transactionID))
	if err != nil {
		h.logger.Error("Failed to get fuel transaction orders")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get fuel transaction orders")
	}

	return c.JSON(http.StatusOK, orders)
}

// UpdateLinkAllocatedAmountRequest 更新关联分配金额请求
type UpdateLinkAllocatedAmountRequest struct {
	AllocatedAmount float64 `json:"allocatedAmount" validate:"required,gte=0"`
}

// UpdateLinkAllocatedAmount 更新关联分配金额
// @Summary 更新关联分配金额
// @Description 更新燃油交易与订单关联的分配金额
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Param request body UpdateLinkAllocatedAmountRequest true "更新信息"
// @Success 200 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId}/amount [put]
func (h *fuelTransactionHandlerImpl) UpdateLinkAllocatedAmount(c echo.Context) error {
	transactionIDStr := c.Param("transactionId")
	transactionID, err := uuid.Parse(transactionIDStr)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	var req UpdateLinkAllocatedAmountRequest
	if err := c.Bind(&req); err != nil {
		h.logger.Error("Failed to bind request")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// 验证请求
	if err := c.Validate(&req); err != nil {
		h.logger.Error("Request validation failed")
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// 更新关联分配金额
	link, err := h.fuelTransactionService.UpdateLinkAllocatedAmount(c.Request().Context(), repository.ID(transactionID), repository.ID(orderID), req.AllocatedAmount)
	if err != nil {
		h.logger.Error("Failed to update link allocated amount")
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update link allocated amount")
	}

	return c.JSON(http.StatusOK, link)
}

// ConfirmFuelTransactionLink 确认燃油交易链接
// @Summary 确认燃油交易链接
// @Description 确认燃油交易与订单的链接
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Success 200 {object} repository.FuelTransactionOrderLink
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId}/confirm [post]
func (h *fuelTransactionHandlerImpl) ConfirmFuelTransactionLink(c echo.Context) error {
	transactionID, err := strconv.ParseInt(c.Param("transactionId"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	orderID, err := strconv.ParseInt(c.Param("orderId"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 这里应该调用服务层的确认方法，但目前服务层可能没有这个方法
	// 暂时返回成功状态
	h.logger.Info("Confirming fuel transaction link")
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Link confirmed successfully",
		"transactionId": transactionID,
		"orderId": orderID,
	})
}

// CancelFuelTransactionLink 取消燃油交易链接
// @Summary 取消燃油交易链接
// @Description 取消燃油交易与订单的链接
// @Tags 燃油交易
// @Accept json
// @Produce json
// @Param transactionId path int true "燃油交易ID"
// @Param orderId path int true "订单ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/fuel-transactions/{transactionId}/orders/{orderId}/cancel [post]
func (h *fuelTransactionHandlerImpl) CancelFuelTransactionLink(c echo.Context) error {
	transactionID, err := strconv.ParseInt(c.Param("transactionId"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid transaction ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid transaction ID")
	}

	orderID, err := strconv.ParseInt(c.Param("orderId"), 10, 64)
	if err != nil {
		h.logger.Error("Invalid order ID")
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid order ID")
	}

	// 这里应该调用服务层的取消方法，但目前服务层可能没有这个方法
	// 暂时返回成功状态
	h.logger.Info("Cancelling fuel transaction link")
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Link cancelled successfully",
		"transactionId": transactionID,
		"orderId": orderID,
	})
} 