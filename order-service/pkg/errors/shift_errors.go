package errors

import (
	"fmt"
)

// 班次相关的错误常量
const (
	// ErrMsgShiftAlreadyActive 当前站点已有活跃班次
	ErrMsgShiftAlreadyActive = "当前站点已有活跃班次"
	// ErrMsgNoActiveShift 当前站点没有活跃班次
	ErrMsgNoActiveShift = "当前站点没有活跃班次"
	// ErrMsgShiftNotFound 找不到指定班次
	ErrMsgShiftNotFound = "找不到指定班次"
	// ErrMsgCannotEndShiftWithPendingTransactions 班次中有未处理的交易，无法结束
	ErrMsgCannotEndShiftWithPendingTransactions = "班次中有未处理的交易，无法结束"
	// ErrMsgCannotDeleteActiveShift 无法删除活跃班次
	ErrMsgCannotDeleteActiveShift = "无法删除活跃班次"
	// ErrMsgInvalidShiftEndTime 班次结束时间无效
	ErrMsgInvalidShiftEndTime = "班次结束时间无效"
	// ErrMsgShiftTemplateNotFound 找不到指定班次模板
	ErrMsgShiftTemplateNotFound = "找不到指定班次模板"
	// ErrMsgEmployeeShiftNotFound 找不到指定员工班次
	ErrMsgEmployeeShiftNotFound = "找不到指定员工班次"
)

// ShiftAlreadyActiveError 当前站点已有活跃班次错误
func ShiftAlreadyActiveError(stationID string, err error) *AppError {
	return Conflict(ErrMsgShiftAlreadyActive, err).
		AddField("station_id", stationID)
}

// NoActiveShiftError 当前站点没有活跃班次错误
func NoActiveShiftError(stationID string, err error) *AppError {
	return NotFound(ErrMsgNoActiveShift, err).
		AddField("station_id", stationID)
}

// ShiftNotFoundError 找不到指定班次错误
func ShiftNotFoundError(shiftID interface{}, err error) *AppError {
	return NotFound(ErrMsgShiftNotFound, err).
		AddField("shift_id", fmt.Sprintf("%v", shiftID))
}

// CannotEndShiftWithPendingTransactionsError 班次中有未处理的交易，无法结束错误
func CannotEndShiftWithPendingTransactionsError(stationID string, err error) *AppError {
	return Conflict(ErrMsgCannotEndShiftWithPendingTransactions, err).
		AddField("station_id", stationID)
}

// CannotDeleteActiveShiftError 无法删除活跃班次错误
func CannotDeleteActiveShiftError(shiftID string, err error) *AppError {
	return Conflict(ErrMsgCannotDeleteActiveShift, err).
		AddField("shift_id", shiftID)
}

// InvalidShiftEndTimeError 班次结束时间无效错误
func InvalidShiftEndTimeError(shiftID int64, err error) *AppError {
	return BadRequest(ErrMsgInvalidShiftEndTime, err).
		AddField("shift_id", fmt.Sprintf("%d", shiftID))
}

// ShiftTemplateNotFoundError 班次模板未找到错误
func ShiftTemplateNotFoundError(templateID interface{}, err error) *AppError {
	return NotFound(ErrMsgShiftTemplateNotFound, err).
		AddField("template_id", fmt.Sprintf("%v", templateID))
}

// EmployeeShiftNotFoundError 员工班次未找到错误
func EmployeeShiftNotFoundError(shiftID interface{}, err error) *AppError {
	return NotFound(ErrMsgEmployeeShiftNotFound, err).
		AddField("shift_id", fmt.Sprintf("%v", shiftID))
}
