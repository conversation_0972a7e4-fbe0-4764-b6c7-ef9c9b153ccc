package repository

import (
	"time"
)

// PaymentInfo 支付信息
type PaymentInfo struct {
	ID              int64      `json:"id"` // Payment ID 是 int64 类型
	OrderID         string     `json:"order_id"`
	PaymentNumber   string     `json:"payment_number"`
	Amount          float64    `json:"amount"`
	Currency        string     `json:"currency"`
	PaymentType     string     `json:"payment_type"`
	PaymentMethod   *int64     `json:"payment_method"` // Payment Method ID 是 int64 类型
	Status          string     `json:"status"`
	GatewayType     *string    `json:"gateway_type"`
	GatewayOrderNo  *string    `json:"gateway_order_no"`
	GatewayResponse *string    `json:"gateway_response"`
	CustomerID      *string    `json:"customer_id"`
	CustomerName    *string    `json:"customer_name"`
	StationID       int64      `json:"station_id"` // Station ID 是 int64 类型
	TerminalID      *string    `json:"terminal_id"`
	OperatorID      *string    `json:"operator_id"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	PaidAt          *time.Time `json:"paid_at"`
	ExpiresAt       *time.Time `json:"expires_at"`
	Metadata        *string    `json:"metadata"`
}

// PaymentMethod 支付方式配置
type PaymentMethod struct {
	ID              int64      `json:"id"` // Payment Method ID 是 int64 类型
	Type            string     `json:"type"`
	Name            string     `json:"name"`
	DisplayName     string     `json:"display_name"`
	Description     *string    `json:"description"`
	Icon            *string    `json:"icon"`
	GatewayType     string     `json:"gateway_type"`
	GatewayConfig   *string    `json:"gateway_config"`
	Enabled         bool       `json:"enabled"`
	MinAmount       *float64   `json:"min_amount"`
	MaxAmount       *float64   `json:"max_amount"`
	DailyLimit      *float64   `json:"daily_limit"`
	FeeType         *string    `json:"fee_type"`
	FeeValue        *float64   `json:"fee_value"`
	AvailableTime   *string    `json:"available_time"`
	AllowedStations *string    `json:"allowed_stations"`
	SortOrder       *int       `json:"sort_order"`
	GroupName       *string    `json:"group_name"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
	DeletedAt       *time.Time `json:"deleted_at"`
}
