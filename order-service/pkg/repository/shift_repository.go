package repository

import (
	"context"
	"time"
)

// ShiftRepository 定义班次存储库接口
type ShiftRepository interface {
	// CreateShift 创建新的班次
	CreateShift(ctx context.Context, shift *Shift) (*Shift, error)

	// UpdateShift 更新班次信息
	UpdateShift(ctx context.Context, shift *Shift) (*Shift, error)

	// UpdateShiftEndTime 更新班次结束时间
	UpdateShiftEndTime(ctx context.Context, shiftID ID, endTime time.Time) (*Shift, error)

	// GetShiftByID 根据ID获取班次
	GetShiftByID(ctx context.Context, id ID) (*Shift, error)

	// GetShiftByNumber 根据班次编号获取班次
	GetShiftByNumber(ctx context.Context, shiftNumber string) (*Shift, error)

	// FindActiveShiftByStationID 查找指定加油站的活跃班次
	FindActiveShiftByStationID(ctx context.Context, stationID int64) (*Shift, error)

	// FindActiveShiftByStationIDWithLock 查找并锁定指定加油站的活跃班次（用于事务）
	FindActiveShiftByStationIDWithLock(ctx context.Context, stationID int64) (*Shift, error)

	// FindLastCompletedShiftByStationID 查找指定加油站最后一个完成的班次
	FindLastCompletedShiftByStationID(ctx context.Context, stationID int64) (*Shift, error)

	// ListShifts 根据筛选条件列出班次
	ListShifts(ctx context.Context, filter *ShiftFilter, pagination *Pagination, sort *SortOrder) ([]*Shift, int, error)

	// GetDailyShiftCount 获取指定站点在指定日期的班次数量
	// 用于生成当天的班次序号
	GetDailyShiftCount(ctx context.Context, stationID int64, date time.Time) (int, error)

	// SoftDeleteShift 软删除班次
	SoftDeleteShift(ctx context.Context, shiftID ID) error

	// RestoreShift 恢复已删除的班次
	RestoreShift(ctx context.Context, shiftID ID) error
}

// ShiftTemplateRepository 班次模板存储接口
type ShiftTemplateRepository interface {
	// CreateShiftTemplate 创建班次模板
	CreateShiftTemplate(ctx context.Context, template *ShiftTemplate) error

	// UpdateShiftTemplate 更新班次模板
	UpdateShiftTemplate(ctx context.Context, template *ShiftTemplate) error

	// DeleteShiftTemplate 删除班次模板（软删除）
	DeleteShiftTemplate(ctx context.Context, id ID) error

	// GetShiftTemplateByID 根据ID获取班次模板
	GetShiftTemplateByID(ctx context.Context, id ID) (*ShiftTemplate, error)

	// ListShiftTemplates 列出班次模板
	ListShiftTemplates(ctx context.Context, filter *ShiftTemplateFilter, pagination *Pagination,
		sortOrder *SortOrder) ([]*ShiftTemplate, int, error)

	// GetActiveShiftTemplatesForStation 获取指定加油站的所有活跃班次模板
	GetActiveShiftTemplatesForStation(ctx context.Context, stationID int64) ([]*ShiftTemplate, error)
}

// EmployeeShiftRepository 员工班次存储接口
type EmployeeShiftRepository interface {
	// CreateEmployeeShift 创建员工班次关联
	CreateEmployeeShift(ctx context.Context, employeeShift *EmployeeShift) error

	// BatchCreateEmployeeShifts 批量创建员工班次关联
	BatchCreateEmployeeShifts(ctx context.Context, employeeShifts []*EmployeeShift) error

	// UpdateEmployeeShift 更新员工班次关联
	UpdateEmployeeShift(ctx context.Context, employeeShift *EmployeeShift) error

	// GetEmployeeShiftByID 根据ID获取员工班次关联
	GetEmployeeShiftByID(ctx context.Context, id ID) (*EmployeeShift, error)

	// GetEmployeeShiftByEmployeeAndDate 获取指定员工在特定日期的班次关联
	GetEmployeeShiftByEmployeeAndDate(ctx context.Context, employeeID ID, date string) (*EmployeeShift, error)

	// ListEmployeeShifts 列出员工班次关联
	ListEmployeeShifts(ctx context.Context, filter *EmployeeShiftFilter, pagination *Pagination,
		sortOrder *SortOrder) ([]*EmployeeShift, int, error)

	// CheckInEmployeeShift 员工签到
	CheckInEmployeeShift(ctx context.Context, id ID, checkInTime string) error

	// CheckOutEmployeeShift 员工签退
	CheckOutEmployeeShift(ctx context.Context, id ID, checkOutTime string) error

	// MarkEmployeeShiftAbsent 标记员工缺勤
	MarkEmployeeShiftAbsent(ctx context.Context, id ID, notes string) error
}
