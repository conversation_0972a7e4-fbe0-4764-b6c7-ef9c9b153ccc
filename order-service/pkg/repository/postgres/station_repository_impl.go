package postgres

import (
	"context"
	"database/sql"
	"fmt"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// stationRepositoryImpl 站点存储库PostgreSQL实现
type stationRepositoryImpl struct {
	db *database.Database
}

// NewStationRepository 创建站点存储库实例
func NewStationRepository(db *database.Database) repository.StationRepository {
	return &stationRepositoryImpl{
		db: db,
	}
}

// GetStationByID 根据ID获取站点信息
func (r *stationRepositoryImpl) GetStationByID(ctx context.Context, stationID int64) (*repository.Station, error) {
	query := `
		SELECT
			id, site_code, site_name, address, latitude, longitude,
			administrative_division, business_hours, business_status, contact_info,
			manager_id, management_level, parent_organization,
			created_at, updated_at, created_by, updated_by, version
		FROM core_schema.stations
		WHERE id = $1 AND deleted_at IS NULL
	`

	var station repository.Station
	err := r.db.GetPool().QueryRow(ctx, query, stationID).Scan(
		&station.ID,
		&station.SiteCode,
		&station.SiteName,
		&station.Address,
		&station.Latitude,
		&station.Longitude,
		&station.AdministrativeDivision,
		&station.BusinessHours,
		&station.BusinessStatus,
		&station.ContactInfo,
		&station.ManagerID,
		&station.ManagementLevel,
		&station.ParentOrganization,
		&station.CreatedAt,
		&station.UpdatedAt,
		&station.CreatedBy,
		&station.UpdatedBy,
		&station.Version,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("station not found: %d", stationID)
		}
		return nil, fmt.Errorf("failed to get station: %w", err)
	}

	return &station, nil
}

// GetStationName 根据ID获取站点名称
func (r *stationRepositoryImpl) GetStationName(ctx context.Context, stationID int64) (string, error) {
	query := `SELECT site_name FROM core_schema.stations WHERE id = $1 AND deleted_at IS NULL`

	var stationName string
	err := r.db.GetPool().QueryRow(ctx, query, stationID).Scan(&stationName)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("station not found: %d", stationID)
		}
		return "", fmt.Errorf("failed to get station name: %w", err)
	}

	return stationName, nil
}

// GetStationsBatch 批量获取站点信息
func (r *stationRepositoryImpl) GetStationsBatch(ctx context.Context, stationIDs []int64) (map[int64]*repository.Station, error) {
	if len(stationIDs) == 0 {
		return make(map[int64]*repository.Station), nil
	}

	query := `
		SELECT
			id, site_code, site_name, address, latitude, longitude,
			administrative_division, business_hours, business_status, contact_info,
			manager_id, management_level, parent_organization,
			created_at, updated_at, created_by, updated_by, version
		FROM core_schema.stations
		WHERE id = ANY($1) AND deleted_at IS NULL
	`

	// 直接使用 []int64
	rows, err := r.db.GetPool().Query(ctx, query, stationIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to query stations: %w", err)
	}
	defer rows.Close()

	stations := make(map[int64]*repository.Station)
	for rows.Next() {
		var station repository.Station
		err := rows.Scan(
			&station.ID,
			&station.SiteCode,
			&station.SiteName,
			&station.Address,
			&station.Latitude,
			&station.Longitude,
			&station.AdministrativeDivision,
			&station.BusinessHours,
			&station.BusinessStatus,
			&station.ContactInfo,
			&station.ManagerID,
			&station.ManagementLevel,
			&station.ParentOrganization,
			&station.CreatedAt,
			&station.UpdatedAt,
			&station.CreatedBy,
			&station.UpdatedBy,
			&station.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan station: %w", err)
		}
		stations[station.ID] = &station
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("rows iteration error: %w", err)
	}

	return stations, nil
}
