package postgres

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ShiftTemplateRepository PostgreSQL实现的班次模板存储库
type ShiftTemplateRepository struct {
	db *sqlx.DB
}

// NewShiftTemplateRepository 创建一个新的班次模板存储库实例
func NewShiftTemplateRepository(db *sqlx.DB) *ShiftTemplateRepository {
	return &ShiftTemplateRepository{
		db: db,
	}
}

// CreateShiftTemplate 创建班次模板
func (r *ShiftTemplateRepository) CreateShiftTemplate(ctx context.Context, template *repository.ShiftTemplate) error {
	now := time.Now()
	template.CreatedAt = now
	template.UpdatedAt = now

	query := `
		INSERT INTO shift_templates (
			name, start_time, end_time, station_id, status, metadata, created_at, updated_at
		) VALUES (
			:name, :start_time, :end_time, :station_id, :status, :metadata, :created_at, :updated_at
		) RETURNING id
	`

	rows, err := r.db.NamedQueryContext(ctx, query, template)
	if err != nil {
		return errors.Wrap(err, "failed to create shift template")
	}
	defer rows.Close()

	if rows.Next() {
		if err := rows.Scan(&template.ID); err != nil {
			return errors.Wrap(err, "failed to scan inserted shift template ID")
		}
	}

	return nil
}

// UpdateShiftTemplate 更新班次模板
func (r *ShiftTemplateRepository) UpdateShiftTemplate(ctx context.Context, template *repository.ShiftTemplate) error {
	template.UpdatedAt = time.Now()

	query := `
		UPDATE shift_templates SET
			name = :name,
			start_time = :start_time,
			end_time = :end_time,
			station_id = :station_id,
			status = :status,
			metadata = :metadata,
			updated_at = :updated_at
		WHERE id = :id AND deleted_at IS NULL
	`

	result, err := r.db.NamedExecContext(ctx, query, template)
	if err != nil {
		return errors.Wrap(err, "failed to update shift template")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrap(err, "failed to get rows affected")
	}

	if rowsAffected == 0 {
		return repository.ErrNotFound
	}

	return nil
}

// DeleteShiftTemplate 删除班次模板（软删除）
func (r *ShiftTemplateRepository) DeleteShiftTemplate(ctx context.Context, id repository.ID) error {
	now := time.Now()

	query := `
		UPDATE shift_templates SET
			deleted_at = $1,
			updated_at = $1
		WHERE id = $2 AND deleted_at IS NULL
	`

	result, err := r.db.ExecContext(ctx, query, now, id)
	if err != nil {
		return errors.Wrap(err, "failed to delete shift template")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return errors.Wrap(err, "failed to get rows affected")
	}

	if rowsAffected == 0 {
		return repository.ErrNotFound
	}

	return nil
}

// GetShiftTemplateByID 根据ID获取班次模板
func (r *ShiftTemplateRepository) GetShiftTemplateByID(ctx context.Context, id repository.ID) (*repository.ShiftTemplate, error) {
	query := `
		SELECT
			id, name, start_time, end_time, station_id, status, metadata, created_at, updated_at, deleted_at
		FROM shift_templates
		WHERE id = $1 AND deleted_at IS NULL
	`

	var template repository.ShiftTemplate
	if err := r.db.GetContext(ctx, &template, query, id); err != nil {
		if err == sql.ErrNoRows {
			return nil, repository.ErrNotFound
		}
		return nil, errors.Wrap(err, "failed to get shift template by ID")
	}

	return &template, nil
}

// ListShiftTemplates 列出班次模板
func (r *ShiftTemplateRepository) ListShiftTemplates(ctx context.Context, filter *repository.ShiftTemplateFilter, pagination *repository.Pagination, sortOrder *repository.SortOrder) ([]*repository.ShiftTemplate, int, error) {
	// 构建查询条件
	baseQuery := `FROM shift_templates WHERE 1=1`
	countQuery := `SELECT COUNT(*) ` + baseQuery
	selectQuery := `
		SELECT
			id, name, start_time, end_time, station_id, status, metadata, created_at, updated_at, deleted_at
	` + baseQuery

	// 添加筛选条件
	args := []interface{}{}
	argIndex := 1

	// 默认只显示未删除的记录，除非明确包含已删除
	if filter.IncludeDeleted == nil || !*filter.IncludeDeleted {
		baseQuery += ` AND deleted_at IS NULL`
	}

	if filter.StationID != nil {
		baseQuery += fmt.Sprintf(" AND station_id = $%d", argIndex)
		args = append(args, *filter.StationID)
		argIndex++
	}

	if filter.Status != nil {
		baseQuery += fmt.Sprintf(" AND status = $%d", argIndex)
		args = append(args, *filter.Status)
		argIndex++
	}

	if filter.Name != nil {
		baseQuery += fmt.Sprintf(" AND name ILIKE $%d", argIndex)
		args = append(args, "%"+*filter.Name+"%")
		argIndex++
	}

	// 计算总记录数
	var total int
	if err := r.db.GetContext(ctx, &total, countQuery, args...); err != nil {
		return nil, 0, errors.Wrap(err, "failed to count shift templates")
	}

	// 添加排序和分页
	if sortOrder != nil && sortOrder.Field != "" {
		direction := "ASC"
		if sortOrder.Direction == "desc" {
			direction = "DESC"
		}
		baseQuery += ` ORDER BY ` + sortOrder.Field + ` ` + direction
	} else {
		// 默认按创建时间降序排列
		baseQuery += ` ORDER BY created_at DESC`
	}

	// 添加分页
	if pagination != nil {
		baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		args = append(args, pagination.Limit, (pagination.Page-1)*pagination.Limit)
	}

	// 执行查询
	selectQuery += baseQuery
	rows, err := r.db.QueryxContext(ctx, selectQuery, args...)
	if err != nil {
		return nil, 0, errors.Wrap(err, "failed to list shift templates")
	}
	defer rows.Close()

	// 提取结果
	templates := []*repository.ShiftTemplate{}
	for rows.Next() {
		var template repository.ShiftTemplate
		if err := rows.StructScan(&template); err != nil {
			return nil, 0, errors.Wrap(err, "failed to scan shift template")
		}
		templates = append(templates, &template)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, errors.Wrap(err, "error iterating shift template rows")
	}

	return templates, total, nil
}

// GetActiveShiftTemplatesForStation 获取指定加油站的所有活跃班次模板
func (r *ShiftTemplateRepository) GetActiveShiftTemplatesForStation(ctx context.Context, stationID int64) ([]*repository.ShiftTemplate, error) {
	query := `
		SELECT
			id, name, start_time, end_time, station_id, description, status, metadata, created_at, updated_at, deleted_at
		FROM shift_templates
		WHERE station_id = $1 AND status = $2 AND deleted_at IS NULL
		ORDER BY start_time ASC
	`

	rows, err := r.db.QueryxContext(ctx, query, stationID, repository.ShiftTemplateStatusActive)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get active shift templates for station")
	}
	defer rows.Close()

	// 提取结果
	templates := []*repository.ShiftTemplate{}
	for rows.Next() {
		var template repository.ShiftTemplate
		if err := rows.StructScan(&template); err != nil {
			return nil, errors.Wrap(err, "failed to scan shift template")
		}
		templates = append(templates, &template)
	}

	if err := rows.Err(); err != nil {
		return nil, errors.Wrap(err, "error iterating shift template rows")
	}

	return templates, nil
}
