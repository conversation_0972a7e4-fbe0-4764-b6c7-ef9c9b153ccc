package postgres

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/infrastructure/database"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/utils"
)

// 使用共享的时区工具

// ShiftRepositoryImpl 实现了ShiftRepository接口
type ShiftRepositoryImpl struct {
	db *database.Database
}

// NewShiftRepository 创建一个新的ShiftRepository实例
func NewShiftRepository(db *database.Database) repository.ShiftRepository {
	return &ShiftRepositoryImpl{
		db: db,
	}
}

// CreateShift 创建新的班次
func (r *ShiftRepositoryImpl) CreateShift(ctx context.Context, shift *repository.Shift) (*repository.Shift, error) {
	var newShift repository.Shift

	err := r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
		// 设置创建时间和更新时间
		now := time.Now()
		shift.CreatedAt = now
		shift.UpdatedAt = now

		// 序列化元数据
		var metadataJSON []byte
		var err error
		if shift.Metadata != nil {
			metadataJSON, err = json.Marshal(shift.Metadata)
			if err != nil {
				return fmt.Errorf("序列化元数据失败: %w", err)
			}
		}

		// 插入班次
		query := `
			INSERT INTO shifts (
				shift_number, station_id, start_time, end_time,
				metadata, created_at, updated_at
			) VALUES (
				$1, $2, $3, $4,
				$5, $6, $7
			) RETURNING
				id, shift_number, station_id, start_time, end_time,
				metadata, created_at, updated_at, deleted_at
		`

		var metadataJSONDB []byte
		err = tx.QueryRow(
			ctx,
			query,
			shift.ShiftNumber,
			shift.StationID,
			shift.StartTime,
			shift.EndTime,
			metadataJSON,
			shift.CreatedAt,
			shift.UpdatedAt,
		).Scan(
			&newShift.ID,
			&newShift.ShiftNumber,
			&newShift.StationID,
			&newShift.StartTime,
			&newShift.EndTime,
			&metadataJSONDB,
			&newShift.CreatedAt,
			&newShift.UpdatedAt,
			&newShift.DeletedAt,
		)

		if err != nil {
			return fmt.Errorf("创建班次失败: %w", err)
		}

		// 反序列化元数据
		if len(metadataJSONDB) > 0 {
			if err := json.Unmarshal(metadataJSONDB, &newShift.Metadata); err != nil {
				return fmt.Errorf("解析元数据失败: %w", err)
			}
		} else {
			newShift.Metadata = make(map[string]interface{})
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &newShift, nil
}

// UpdateShift 更新班次信息
func (r *ShiftRepositoryImpl) UpdateShift(ctx context.Context, shift *repository.Shift) (*repository.Shift, error) {
	var updatedShift repository.Shift

	err := r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
		// 设置更新时间
		now := time.Now()
		shift.UpdatedAt = now

		// 序列化元数据
		var metadataJSON []byte
		var err error
		if shift.Metadata != nil {
			metadataJSON, err = json.Marshal(shift.Metadata)
			if err != nil {
				return fmt.Errorf("序列化元数据失败: %w", err)
			}
		}

		// 更新班次
		query := `
			UPDATE shifts
			SET
				shift_number = $1,
				station_id = $2,
				start_time = $3,
				end_time = $4,
				metadata = $5,
				updated_at = $6
			WHERE
				id = $7 AND deleted_at IS NULL
			RETURNING
				id, shift_number, station_id, start_time, end_time,
				metadata, created_at, updated_at, deleted_at
		`

		var metadataJSONDB []byte
		err = tx.QueryRow(
			ctx,
			query,
			shift.ShiftNumber,
			shift.StationID,
			shift.StartTime,
			shift.EndTime,
			metadataJSON,
			shift.UpdatedAt,
			shift.ID,
		).Scan(
			&updatedShift.ID,
			&updatedShift.ShiftNumber,
			&updatedShift.StationID,
			&updatedShift.StartTime,
			&updatedShift.EndTime,
			&metadataJSONDB,
			&updatedShift.CreatedAt,
			&updatedShift.UpdatedAt,
			&updatedShift.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return fmt.Errorf("未找到班次或班次已删除: %d", shift.ID)
			}
			return fmt.Errorf("更新班次失败: %w", err)
		}

		// 反序列化元数据
		if len(metadataJSONDB) > 0 {
			if err := json.Unmarshal(metadataJSONDB, &updatedShift.Metadata); err != nil {
				return fmt.Errorf("解析元数据失败: %w", err)
			}
		} else {
			updatedShift.Metadata = make(map[string]interface{})
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &updatedShift, nil
}

// UpdateShiftEndTime 更新班次结束时间
func (r *ShiftRepositoryImpl) UpdateShiftEndTime(ctx context.Context, shiftID repository.ID, endTime time.Time) (*repository.Shift, error) {
	var updatedShift repository.Shift

	query := `
		UPDATE shifts
		SET
			end_time = $1,
			updated_at = $2
		WHERE
			id = $3 AND deleted_at IS NULL
		RETURNING
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
	`

	now := time.Now()
	var metadataJSON []byte
	err := r.db.GetPool().QueryRow(
		ctx,
		query,
		endTime,
		now,
		shiftID,
	).Scan(
		&updatedShift.ID,
		&updatedShift.ShiftNumber,
		&updatedShift.StationID,
		&updatedShift.StartTime,
		&updatedShift.EndTime,
		&metadataJSON,
		&updatedShift.CreatedAt,
		&updatedShift.UpdatedAt,
		&updatedShift.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("未找到班次或班次已删除: %d", shiftID)
		}
		return nil, fmt.Errorf("更新班次结束时间失败: %w", err)
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &updatedShift.Metadata); err != nil {
			return nil, fmt.Errorf("解析元数据失败: %w", err)
		}
	} else {
		updatedShift.Metadata = make(map[string]interface{})
	}

	return &updatedShift, nil
}

// GetShiftByID 根据ID获取班次
func (r *ShiftRepositoryImpl) GetShiftByID(ctx context.Context, id repository.ID) (*repository.Shift, error) {
	var shift repository.Shift

	query := `
		SELECT
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
		FROM
			shifts
		WHERE
			id = $1 AND deleted_at IS NULL
	`

	var metadataJSON []byte
	err := r.db.GetPool().QueryRow(
		ctx,
		query,
		id,
	).Scan(
		&shift.ID,
		&shift.ShiftNumber,
		&shift.StationID,
		&shift.StartTime,
		&shift.EndTime,
		&metadataJSON,
		&shift.CreatedAt,
		&shift.UpdatedAt,
		&shift.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, repository.ErrNotFound
		}
		return nil, fmt.Errorf("获取班次失败: %w", err)
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &shift.Metadata); err != nil {
			return nil, fmt.Errorf("解析元数据失败: %w", err)
		}
	} else {
		shift.Metadata = make(map[string]interface{})
	}

	return &shift, nil
}

// GetShiftByNumber 根据班次编号获取班次
func (r *ShiftRepositoryImpl) GetShiftByNumber(ctx context.Context, shiftNumber string) (*repository.Shift, error) {
	var shift repository.Shift

	query := `
		SELECT
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
		FROM
			shifts
		WHERE
			shift_number = $1 AND deleted_at IS NULL
	`

	var metadataJSON []byte
	err := r.db.GetPool().QueryRow(
		ctx,
		query,
		shiftNumber,
	).Scan(
		&shift.ID,
		&shift.ShiftNumber,
		&shift.StationID,
		&shift.StartTime,
		&shift.EndTime,
		&metadataJSON,
		&shift.CreatedAt,
		&shift.UpdatedAt,
		&shift.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, repository.ErrNotFound
		}
		return nil, fmt.Errorf("获取班次失败: %w", err)
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &shift.Metadata); err != nil {
			return nil, fmt.Errorf("解析元数据失败: %w", err)
		}
	} else {
		shift.Metadata = make(map[string]interface{})
	}

	return &shift, nil
}

// FindActiveShiftByStationID 查找指定加油站的活跃班次
func (r *ShiftRepositoryImpl) FindActiveShiftByStationID(ctx context.Context, stationID int64) (*repository.Shift, error) {
	var shift repository.Shift

	query := `
		SELECT
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
		FROM
			shifts
		WHERE
			station_id = $1 AND end_time IS NULL AND deleted_at IS NULL
		ORDER BY
			start_time DESC
		LIMIT 1
	`

	var metadataJSON []byte
	err := r.db.GetPool().QueryRow(
		ctx,
		query,
		stationID,
	).Scan(
		&shift.ID,
		&shift.ShiftNumber,
		&shift.StationID,
		&shift.StartTime,
		&shift.EndTime,
		&metadataJSON,
		&shift.CreatedAt,
		&shift.UpdatedAt,
		&shift.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, repository.ErrNotFound
		}
		return nil, fmt.Errorf("查找活跃班次失败: %w", err)
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &shift.Metadata); err != nil {
			return nil, fmt.Errorf("解析元数据失败: %w", err)
		}
	} else {
		shift.Metadata = make(map[string]interface{})
	}

	return &shift, nil
}

// FindActiveShiftByStationIDWithLock 查找并锁定指定加油站的活跃班次（用于事务）
func (r *ShiftRepositoryImpl) FindActiveShiftByStationIDWithLock(ctx context.Context, stationID int64) (*repository.Shift, error) {
	var shift repository.Shift

	err := r.db.RunInTransaction(ctx, func(tx pgx.Tx) error {
		query := `
			SELECT
				id, shift_number, station_id, start_time, end_time,
				metadata, created_at, updated_at, deleted_at
			FROM
				shifts
			WHERE
				station_id = $1 AND end_time IS NULL AND deleted_at IS NULL
			ORDER BY
				start_time DESC
			LIMIT 1
			FOR UPDATE
		`

		var metadataJSON []byte
		err := tx.QueryRow(
			ctx,
			query,
			stationID,
		).Scan(
			&shift.ID,
			&shift.ShiftNumber,
			&shift.StationID,
			&shift.StartTime,
			&shift.EndTime,
			&metadataJSON,
			&shift.CreatedAt,
			&shift.UpdatedAt,
			&shift.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return repository.ErrNotFound
			}
			return fmt.Errorf("查找活跃班次失败: %w", err)
		}

		// 反序列化元数据
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &shift.Metadata); err != nil {
				return fmt.Errorf("解析元数据失败: %w", err)
			}
		} else {
			shift.Metadata = make(map[string]interface{})
		}

		return nil
	})

	if err != nil {
		if err == repository.ErrNotFound {
			return nil, err
		}
		return nil, fmt.Errorf("查找并锁定活跃班次失败: %w", err)
	}

	return &shift, nil
}

// FindLastCompletedShiftByStationID 查找指定加油站最后一个完成的班次
func (r *ShiftRepositoryImpl) FindLastCompletedShiftByStationID(ctx context.Context, stationID int64) (*repository.Shift, error) {
	var shift repository.Shift

	query := `
		SELECT
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
		FROM
			shifts
		WHERE
			station_id = $1 AND end_time IS NOT NULL AND deleted_at IS NULL
		ORDER BY
			end_time DESC
		LIMIT 1
	`

	var metadataJSON []byte
	err := r.db.GetPool().QueryRow(
		ctx,
		query,
		stationID,
	).Scan(
		&shift.ID,
		&shift.ShiftNumber,
		&shift.StationID,
		&shift.StartTime,
		&shift.EndTime,
		&metadataJSON,
		&shift.CreatedAt,
		&shift.UpdatedAt,
		&shift.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, repository.ErrNotFound
		}
		return nil, fmt.Errorf("查找最后完成班次失败: %w", err)
	}

	// 反序列化元数据
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &shift.Metadata); err != nil {
			return nil, fmt.Errorf("解析元数据失败: %w", err)
		}
	} else {
		shift.Metadata = make(map[string]interface{})
	}

	return &shift, nil
}

// ListShifts 根据筛选条件列出班次
func (r *ShiftRepositoryImpl) ListShifts(ctx context.Context, filter *repository.ShiftFilter, pagination *repository.Pagination, sort *repository.SortOrder) ([]*repository.Shift, int, error) {
	shifts := []*repository.Shift{}

	// 构建查询
	baseQuery := "FROM shifts WHERE deleted_at IS NULL"
	selectQuery := `
		SELECT
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
	` + baseQuery

	// 添加筛选条件
	args := []interface{}{}
	argIndex := 1

	if filter.StationID != nil {
		baseQuery += fmt.Sprintf(" AND station_id = $%d", argIndex)
		args = append(args, *filter.StationID)
		argIndex++
	}

	if filter.ShiftNumber != nil {
		baseQuery += fmt.Sprintf(" AND shift_number = $%d", argIndex)
		args = append(args, *filter.ShiftNumber)
		argIndex++
	}

	if filter.DateFrom != nil {
		// 使用雅加达时区进行日期比较，确保班次按照雅加达时间的日期进行过滤
		baseQuery += fmt.Sprintf(" AND DATE(start_time AT TIME ZONE 'Asia/Jakarta') >= DATE($%d AT TIME ZONE 'Asia/Jakarta')", argIndex)
		args = append(args, *filter.DateFrom)
		argIndex++
	}

	if filter.DateTo != nil {
		// 使用雅加达时区进行日期比较，确保班次按照雅加达时间的日期进行过滤
		baseQuery += fmt.Sprintf(" AND DATE(start_time AT TIME ZONE 'Asia/Jakarta') <= DATE($%d AT TIME ZONE 'Asia/Jakarta')", argIndex)
		args = append(args, *filter.DateTo)
		argIndex++
	}

	// 处理状态过滤条件
	if filter.Status != nil {
		switch *filter.Status {
		case repository.ShiftStatusActive:
			// 活跃状态：end_time 为 NULL
			baseQuery += " AND end_time IS NULL"
		case repository.ShiftStatusClosed:
			// 已关闭状态：end_time 不为 NULL
			baseQuery += " AND end_time IS NOT NULL"
		}
	}

	if filter.IncludeDeleted != nil && *filter.IncludeDeleted {
		// 替换原有的删除条件为空
		baseQuery = strings.Replace(baseQuery, "WHERE deleted_at IS NULL", "WHERE 1=1", 1)
	}

	// 在添加完筛选条件后构建计数查询
	countQuery := "SELECT COUNT(*) " + baseQuery

	// 计算总记录数
	var total int
	err := r.db.GetPool().QueryRow(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("计算总记录数失败: %w", err)
	}

	// 排序
	if sort != nil && sort.Field != "" {
		direction := "ASC"
		if sort.Direction == "desc" {
			direction = "DESC"
		}
		baseQuery += fmt.Sprintf(" ORDER BY %s %s", sort.Field, direction)
	} else {
		// 默认按创建时间倒序
		baseQuery += " ORDER BY created_at DESC"
	}

	// 分页
	if pagination != nil {
		baseQuery += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		args = append(args, pagination.Limit, (pagination.Page-1)*pagination.Limit)
		argIndex += 2
	}

	// 执行查询 - 修复重复包含baseQuery的问题
	// 更新selectQuery以包含最新的baseQuery（包含筛选条件、排序和分页）
	selectQuery = `
		SELECT
			id, shift_number, station_id, start_time, end_time,
			metadata, created_at, updated_at, deleted_at
	` + baseQuery

	rows, err := r.db.GetPool().Query(ctx, selectQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询班次列表失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	for rows.Next() {
		var shift repository.Shift
		var metadataJSON []byte

		err := rows.Scan(
			&shift.ID,
			&shift.ShiftNumber,
			&shift.StationID,
			&shift.StartTime,
			&shift.EndTime,
			&metadataJSON,
			&shift.CreatedAt,
			&shift.UpdatedAt,
			&shift.DeletedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描班次数据失败: %w", err)
		}

		// 反序列化元数据
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &shift.Metadata); err != nil {
				return nil, 0, fmt.Errorf("解析元数据失败: %w", err)
			}
		} else {
			shift.Metadata = make(map[string]interface{})
		}

		shifts = append(shifts, &shift)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("遍历班次数据失败: %w", err)
	}

	return shifts, total, nil
}

// SoftDeleteShift 软删除班次
func (r *ShiftRepositoryImpl) SoftDeleteShift(ctx context.Context, shiftID repository.ID) error {
	query := `
		UPDATE shifts
		SET deleted_at = $1, updated_at = $1
		WHERE id = $2 AND deleted_at IS NULL
	`

	now := time.Now()
	result, err := r.db.GetPool().Exec(ctx, query, now, shiftID)
	if err != nil {
		return fmt.Errorf("软删除班次失败: %w", err)
	}

	if result.RowsAffected() == 0 {
		return repository.ErrNotFound
	}

	return nil
}

// RestoreShift 恢复已删除的班次
func (r *ShiftRepositoryImpl) RestoreShift(ctx context.Context, shiftID repository.ID) error {
	query := `
		UPDATE shifts
		SET deleted_at = NULL, updated_at = $1
		WHERE id = $2 AND deleted_at IS NOT NULL
	`

	now := time.Now()
	result, err := r.db.GetPool().Exec(ctx, query, now, shiftID)
	if err != nil {
		return fmt.Errorf("恢复班次失败: %w", err)
	}

	if result.RowsAffected() == 0 {
		return repository.ErrNotFound
	}

	return nil
}

// GetDailyShiftCount 获取指定站点在指定日期的班次数量
func (r *ShiftRepositoryImpl) GetDailyShiftCount(ctx context.Context, stationID int64, date time.Time) (int, error) {
	// 确保date使用雅加达时区
	dateInJakarta := date.In(utils.JakartaLocation)
	
	// 计算当天的开始和结束时间（基于雅加达时区）
	startOfDay := time.Date(dateInJakarta.Year(), dateInJakarta.Month(), dateInJakarta.Day(), 0, 0, 0, 0, utils.JakartaLocation)
	endOfDay := startOfDay.AddDate(0, 0, 1)

	query := `
		SELECT COUNT(*)
		FROM shifts
		WHERE station_id = $1 
			AND start_time >= $2 
			AND start_time < $3
			AND deleted_at IS NULL
	`

	var count int
	err := r.db.GetPool().QueryRow(ctx, query, stationID, startOfDay, endOfDay).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("获取当日班次数量失败: %w", err)
	}

	return count, nil
}
