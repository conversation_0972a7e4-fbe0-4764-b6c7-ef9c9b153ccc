package repository

import "time"

// ShiftReportDTO 班次报表数据传输对象
type ShiftReportDTO struct {
	ShiftInfo          ShiftInfoDTO          `json:"shift_info"`
	PaymentSummary     PaymentSummaryDTO     `json:"payment_summary"`
	FuelSummary        FuelSummaryDTO        `json:"fuel_summary"`
	MerchandiseSummary MerchandiseSummaryDTO `json:"merchandise_summary"`
	TeraSummary        TeraSummaryDTO        `json:"tera_summary"`
	ReceiptInfo        ReceiptInfoDTO        `json:"receipt_info"`
}

// ShiftInfoDTO 班次基本信息
type ShiftInfoDTO struct {
	ID            ID         `json:"id"`          // Shift ID 是 UUID 类型
	ShiftNumber   string     `json:"shift_number"`
	StationID     int64      `json:"station_id"`  // Station ID 是 int64 类型
	StationName   string     `json:"station_name"`
	StaffID       *ID        `json:"staff_id,omitempty"`
	StaffName     *string    `json:"staff_name,omitempty"`
	StartTime     time.Time  `json:"start_time"`
	EndTime       *time.Time `json:"end_time,omitempty"`
	DurationHours *float64   `json:"duration_hours,omitempty"`
	Status        string     `json:"status"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// PaymentSummaryDTO 支付方式汇总
type PaymentSummaryDTO struct {
	TotalSales        float64               `json:"total_sales"`
	TotalTransactions int                   `json:"total_transactions"`
	PaymentMethods    []PaymentMethodDetail `json:"payment_methods"`
}

// PaymentMethodDetail 支付方式明细
type PaymentMethodDetail struct {
	Method           string  `json:"method"`
	MethodName       string  `json:"method_name"`
	Amount           float64 `json:"amount"`
	TransactionCount int     `json:"transaction_count"`
	Percentage       float64 `json:"percentage"`
}

// FuelSummaryDTO 油品汇总
type FuelSummaryDTO struct {
	TotalVolume       float64           `json:"total_volume"`
	TotalGrossSales   float64           `json:"total_gross_sales"`
	TotalDiscount     float64           `json:"total_discount"`
	TotalNetSales     float64           `json:"total_net_sales"`
	TotalTransactions int               `json:"total_transactions"`
	FuelGrades        []FuelGradeDetail `json:"fuel_grades"`
}

// FuelGradeDetail 油品等级明细
type FuelGradeDetail struct {
	Grade            string  `json:"grade"`
	Type             string  `json:"type"`
	Name             string  `json:"name"`
	Volume           float64 `json:"volume"`
	GrossAmount      float64 `json:"gross_amount"`
	DiscountAmount   float64 `json:"discount_amount"`
	NetAmount        float64 `json:"net_amount"`
	AveragePrice     float64 `json:"average_price"`
	TransactionCount int     `json:"transaction_count"`
	VolumePercentage float64 `json:"volume_percentage"`
}

// MerchandiseSummaryDTO 非油品汇总
type MerchandiseSummaryDTO struct {
	TotalQuantity     int                  `json:"total_quantity"`
	TotalGrossSales   float64              `json:"total_gross_sales"`
	TotalDiscount     float64              `json:"total_discount"`
	TotalNetSales     float64              `json:"total_net_sales"`
	TotalTransactions int                  `json:"total_transactions"`
	TopProducts       []MerchandiseProduct `json:"top_products"`
}

// MerchandiseProduct 非油品商品明细
type MerchandiseProduct struct {
	ProductID        ID      `json:"product_id"`
	ProductName      string  `json:"product_name"`
	ProductType      string  `json:"product_type"`
	Category         string  `json:"category"`
	Quantity         int     `json:"quantity"`
	UnitPrice        float64 `json:"unit_price"`
	GrossAmount      float64 `json:"gross_amount"`
	DiscountAmount   float64 `json:"discount_amount"`
	NetAmount        float64 `json:"net_amount"`
	TransactionCount int     `json:"transaction_count"`
}

// TeraSummaryDTO TERA分类汇总
type TeraSummaryDTO struct {
	Fuel        TeraCategoryDTO `json:"fuel"`
	Merchandise TeraCategoryDTO `json:"merchandise"`
	Total       TeraTotalDTO    `json:"total"`
}

// TeraCategoryDTO TERA分类明细
type TeraCategoryDTO struct {
	GrossSales    float64 `json:"gross_sales"`
	TotalDiscount float64 `json:"total_discount"`
	NetSales      float64 `json:"net_sales"`
	Percentage    float64 `json:"percentage"`
}

// TeraTotalDTO TERA总计
type TeraTotalDTO struct {
	GrossSales    float64 `json:"gross_sales"`
	TotalDiscount float64 `json:"total_discount"`
	NetSales      float64 `json:"net_sales"`
}

// ReceiptInfoDTO 小票信息
type ReceiptInfoDTO struct {
	PrintTime     time.Time `json:"print_time"`
	ReceiptNumber string    `json:"receipt_number"`
	Currency      string    `json:"currency"`
	Timezone      string    `json:"timezone"`
}

// ShiftReceiptDTO 班次小票格式数据
type ShiftReceiptDTO struct {
	Header       ReceiptHeaderDTO `json:"header"`
	ShiftInfo    ReceiptShiftDTO  `json:"shift_info"`
	SalesSummary []string         `json:"sales_summary"`
	Footer       ReceiptFooterDTO `json:"footer"`
}

// ReceiptHeaderDTO 小票头部信息
type ReceiptHeaderDTO struct {
	StationName string `json:"station_name"`
	Address     string `json:"address"`
	Phone       string `json:"phone"`
}

// ReceiptShiftDTO 小票班次信息
type ReceiptShiftDTO struct {
	ShiftNumber string `json:"shift_number"`
	StaffName   string `json:"staff_name"`
	StartTime   string `json:"start_time"`
	EndTime     string `json:"end_time"`
}

// ReceiptFooterDTO 小票底部信息
type ReceiptFooterDTO struct {
	PrintTime       string `json:"print_time"`
	ReceiptNumber   string `json:"receipt_number"`
	ThankYouMessage string `json:"thank_you_message"`
}

// ShiftReportOptions 班次报表查询选项
type ShiftReportOptions struct {
	Format         string `json:"format"`          // json, receipt
	Currency       string `json:"currency"`        // IDR, USD
	Timezone       string `json:"timezone"`        // Asia/Jakarta
	IncludeDetails bool   `json:"include_details"` // 是否包含明细数据
}

// ShiftReportResponse API响应结构
type ShiftReportResponse struct {
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Data    interface{}        `json:"data"` // ShiftReportDTO 或 ShiftReceiptDTO
	Meta    ShiftReportMetaDTO `json:"meta"`
}

// ShiftReportJSONResponse 班次报表JSON格式响应结构（用于Swagger文档）
// @Description 班次报表JSON格式响应结构，当API参数format=json时使用此结构
// @Description data字段包含完整的班次报表数据，包括班次信息、支付汇总、油品汇总、非油品汇总、TERA汇总和小票信息
type ShiftReportJSONResponse struct {
	Success bool               `json:"success" example:"true"`
	Message string             `json:"message" example:"班次报表数据获取成功"`
	Data    ShiftReportDTO     `json:"data"`
	Meta    ShiftReportMetaDTO `json:"meta"`
}

// ShiftReportReceiptResponse 班次报表小票格式响应结构（用于Swagger文档）
// @Description 班次报表小票格式响应结构，当API参数format=receipt时使用此结构
// @Description data字段包含格式化的小票数据，适用于打印或显示
type ShiftReportReceiptResponse struct {
	Success bool               `json:"success" example:"true"`
	Message string             `json:"message" example:"班次小票数据获取成功"`
	Data    ShiftReceiptDTO    `json:"data"`
	Meta    ShiftReportMetaDTO `json:"meta"`
}

// ShiftReportMetaDTO 响应元数据
type ShiftReportMetaDTO struct {
	GeneratedAt      time.Time `json:"generated_at"`
	ProcessingTimeMs int64     `json:"processing_time_ms"`
	DataSource       string    `json:"data_source"`
	Version          string    `json:"version"`
}

// ShiftSummary 班次汇总数据结构（暂时定义，需要根据实际汇总表结构调整）
type ShiftSummary struct {
	ID                 ID                     `json:"id"`
	ShiftID            ID                     `json:"shift_id"`
	TotalSales         float64                `json:"total_sales"`
	TotalTransactions  int                    `json:"total_transactions"`
	PaymentDetails     map[string]interface{} `json:"payment_details"`
	FuelDetails        map[string]interface{} `json:"fuel_details"`
	MerchandiseDetails map[string]interface{} `json:"merchandise_details"`
	CreatedAt          time.Time              `json:"created_at"`
	UpdatedAt          time.Time              `json:"updated_at"`
}
