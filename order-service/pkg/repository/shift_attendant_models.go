package repository

import "time"

// ShiftAttendantRequest 班次员工查询请求参数
type ShiftAttendantRequest struct {
	ShiftID       ID      `json:"shift_id"`
	AttendantName *string `json:"attendant_name,omitempty"`
	FuelGrade     *string `json:"fuel_grade,omitempty"`
	PaymentMethod *string `json:"payment_method,omitempty"`
}

// ShiftAttendantResponse 班次员工响应数据
type ShiftAttendantResponse struct {
	Success bool                `json:"success"`
	Message string              `json:"message"`
	Data    ShiftAttendantData  `json:"data"`
	Meta    ShiftAttendantMeta  `json:"meta"`
}

// ShiftAttendantData 班次员工数据
type ShiftAttendantData struct {
	ShiftInfo    ShiftAttendantShiftInfo `json:"shift_info"`
	Attendants   []ShiftAttendantInfo    `json:"attendants"`
	ShiftSummary ShiftAttendantSummary   `json:"shift_summary"`
}

// ShiftAttendantShiftInfo 班次基本信息
type ShiftAttendantShiftInfo struct {
	ID          ID         `json:"id"`          // Shift ID 是 UUID 类型
	ShiftNumber string     `json:"shift_number"`
	StationID   int64      `json:"station_id"`  // Station ID 是 int64 类型
	StationName string     `json:"station_name"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time,omitempty"`
	Status      string     `json:"status"`
}

// ShiftAttendantInfo 员工详细信息
type ShiftAttendantInfo struct {
	AttendantInfo    AttendantBasicInfo       `json:"attendant_info"`
	TransactionCount int                      `json:"transaction_count"`
	SalesVolumeLtr   float64                  `json:"sales_volume_ltr"`
	SalesAmountIDR   float64                  `json:"sales_amount_idr"`
	FuelSales        ShiftAttendantFuelSales  `json:"fuel_sales"`
	PaymentSummary   ShiftAttendantPayments   `json:"payment_summary"`
	DryIncome        float64                  `json:"dry_income"`
	GrandTotal       float64                  `json:"grand_total"`
}

// AttendantBasicInfo 员工基本信息
type AttendantBasicInfo struct {
	AttendantName string `json:"attendant_name"`
	StaffCardID   ID     `json:"staff_card_id"`
}

// ShiftAttendantFuelSales 油品销售数据
type ShiftAttendantFuelSales struct {
	ByGrade []ShiftAttendantFuelGrade `json:"by_grade"`
	Total   ShiftAttendantFuelTotal   `json:"total"`
}

// ShiftAttendantFuelGrade 按油品等级的销售数据
type ShiftAttendantFuelGrade struct {
	FuelGrade        string  `json:"fuel_grade"`
	FuelName         string  `json:"fuel_name"`
	FuelType         string  `json:"fuel_type"`
	SalesVolume      float64 `json:"sales_volume"`
	GrossAmount      float64 `json:"gross_amount"`
	DiscountAmount   float64 `json:"discount_amount"`
	NetAmount        float64 `json:"net_amount"`
	UnitPrice        float64 `json:"unit_price"`
	TransactionCount int     `json:"transaction_count"`
}

// ShiftAttendantFuelTotal 油品销售汇总
type ShiftAttendantFuelTotal struct {
	TotalVolume         float64 `json:"total_volume"`
	TotalGrossAmount    float64 `json:"total_gross_amount"`
	TotalDiscountAmount float64 `json:"total_discount_amount"`
	TotalNetAmount      float64 `json:"total_net_amount"`
	TotalTransactions   int     `json:"total_transactions"`
}

// ShiftAttendantPayments 支付方式汇总
type ShiftAttendantPayments struct {
	Cash         float64                         `json:"cash"`
	NonCashTotal float64                         `json:"non_cash_total"`
	PVC          float64                         `json:"pvc"`
	CIMB         float64                         `json:"cimb"`
	BCA          float64                         `json:"bca"`
	Mandiri      float64                         `json:"mandiri"`
	BRI          float64                         `json:"bri"`
	BNI          float64                         `json:"bni"`
	Voucher      float64                         `json:"voucher"`
	B2B          float64                         `json:"b2b"`
	Tera         float64                         `json:"tera"`
	ByMethod     []ShiftAttendantPaymentMethod   `json:"by_method"`
}

// ShiftAttendantPaymentMethod 支付方式明细
type ShiftAttendantPaymentMethod struct {
	PaymentMethod     string  `json:"payment_method"`
	PaymentMethodName string  `json:"payment_method_name"`
	TotalAmount       float64 `json:"total_amount"`
	TransactionCount  int     `json:"transaction_count"`
	Percentage        float64 `json:"percentage"`
}

// ShiftAttendantSummary 班次汇总
type ShiftAttendantSummary struct {
	TotalAttendants    int     `json:"total_attendants"`
	TotalTransactions  int     `json:"total_transactions"`
	TotalSalesVolume   float64 `json:"total_sales_volume"`
	TotalSalesAmount   float64 `json:"total_sales_amount"`
	TotalCash          float64 `json:"total_cash"`
	TotalNonCash       float64 `json:"total_non_cash"`
	TotalPVC           float64 `json:"total_pvc"`
	TotalCIMB          float64 `json:"total_cimb"`
	TotalBCA           float64 `json:"total_bca"`
	TotalMandiri       float64 `json:"total_mandiri"`
	TotalBRI           float64 `json:"total_bri"`
	TotalBNI           float64 `json:"total_bni"`
	TotalVoucher       float64 `json:"total_voucher"`
	TotalB2B           float64 `json:"total_b2b"`
	TotalTera          float64 `json:"total_tera"`
	GrandTotal         float64 `json:"grand_total"`
}

// ShiftAttendantMeta 响应元数据
type ShiftAttendantMeta struct {
	GeneratedAt      time.Time `json:"generated_at"`
	ProcessingTimeMs int64     `json:"processing_time_ms"`
	DataSource       string    `json:"data_source"`
	Version          string    `json:"version"`
} 