package service

import (
	"context"
	"fmt"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/errors"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/utils"
)

// 使用shift_service_impl.go中定义的jakartaLocation，避免重复声明

// EmployeeShiftServiceImpl 实现EmployeeShiftService接口
type EmployeeShiftServiceImpl struct {
	employeeShiftRepo repository.EmployeeShiftRepository
	shiftRepo         repository.ShiftRepository
	templateRepo      repository.ShiftTemplateRepository
	employeeRepo      repository.EmployeeRepository
	stationService    StationService
}

// NewEmployeeShiftService 创建一个新的EmployeeShiftService实例
func NewEmployeeShiftService(
	employeeShiftRepo repository.EmployeeShiftRepository,
	shiftRepo repository.ShiftRepository,
	templateRepo repository.ShiftTemplateRepository,
	employeeRepo repository.EmployeeRepository,
	stationService StationService,
) EmployeeShiftService {
	return &EmployeeShiftServiceImpl{
		employeeShiftRepo: employeeShiftRepo,
		shiftRepo:         shiftRepo,
		templateRepo:      templateRepo,
		employeeRepo:      employeeRepo,
		stationService:    stationService,
	}
}

// AssignShift 为员工分配班次
func (s *EmployeeShiftServiceImpl) AssignShift(ctx context.Context, employeeShift repository.EmployeeShift) (repository.EmployeeShift, error) {
	// 验证员工存在
	_, err := s.employeeRepo.Get(ctx, employeeShift.EmployeeID)
	if err != nil {
		return repository.EmployeeShift{}, fmt.Errorf("员工不存在: %w", err)
	}

	// 验证班次存在
	_, err = s.shiftRepo.GetShiftByID(ctx, employeeShift.ShiftID)
	if err != nil {
		return repository.EmployeeShift{}, fmt.Errorf("班次不存在: %w", err)
	}

	// 检查该员工在同一天是否已有排班
	dateStr := employeeShift.Date.Format("2006-01-02")
	_, err = s.employeeShiftRepo.GetEmployeeShiftByEmployeeAndDate(ctx, employeeShift.EmployeeID, dateStr)
	if err == nil {
		return repository.EmployeeShift{}, fmt.Errorf("员工在该日期已有排班安排")
	} else if err != repository.ErrNotFound {
		return repository.EmployeeShift{}, fmt.Errorf("检查员工排班时发生错误: %w", err)
	}

	// 设置默认值
	now := time.Now()
	employeeShift.CreatedAt = now
	employeeShift.UpdatedAt = now

	// 如果没有设置状态，默认为已排班状态
	if employeeShift.Status == "" {
		employeeShift.Status = string(repository.EmployeeShiftStatusScheduled)
	}

	// 如果没有设置元数据，初始化为空map
	if employeeShift.Metadata == nil {
		employeeShift.Metadata = make(map[string]interface{})
	}

	// 创建排班记录
	err = s.employeeShiftRepo.CreateEmployeeShift(ctx, &employeeShift)
	if err != nil {
		return repository.EmployeeShift{}, fmt.Errorf("创建员工排班失败: %w", err)
	}

	return employeeShift, nil
}

// BatchAssignShifts 批量为员工分配班次
func (s *EmployeeShiftServiceImpl) BatchAssignShifts(ctx context.Context, employeeShifts []repository.EmployeeShift) ([]repository.EmployeeShift, error) {
	if len(employeeShifts) == 0 {
		return []repository.EmployeeShift{}, nil
	}

	// 准备批量创建的数据
	now := time.Now()
	esPointers := make([]*repository.EmployeeShift, len(employeeShifts))

	for i := range employeeShifts {
		// 设置默认值
		employeeShifts[i].CreatedAt = now
		employeeShifts[i].UpdatedAt = now

		// 如果没有设置状态，默认为已排班状态
		if employeeShifts[i].Status == "" {
			employeeShifts[i].Status = string(repository.EmployeeShiftStatusScheduled)
		}

		// 如果没有设置元数据，初始化为空map
		if employeeShifts[i].Metadata == nil {
			employeeShifts[i].Metadata = make(map[string]interface{})
		}

		esPointers[i] = &employeeShifts[i]
	}

	// 批量创建排班记录
	err := s.employeeShiftRepo.BatchCreateEmployeeShifts(ctx, esPointers)
	if err != nil {
		return nil, fmt.Errorf("批量创建员工排班失败: %w", err)
	}

	return employeeShifts, nil
}

// UpdateShiftAssignment 更新员工班次分配
func (s *EmployeeShiftServiceImpl) UpdateShiftAssignment(ctx context.Context, employeeShift repository.EmployeeShift) (repository.EmployeeShift, error) {
	// 先获取现有记录
	existingShift, err := s.employeeShiftRepo.GetEmployeeShiftByID(ctx, employeeShift.ID)
	if err != nil {
		if err == repository.ErrNotFound {
			return repository.EmployeeShift{}, errors.EmployeeShiftNotFoundError(employeeShift.ID.String(), err)
		}
		return repository.EmployeeShift{}, fmt.Errorf("获取员工排班信息失败: %w", err)
	}

	// 如果员工ID或班次日期发生变化，需要检查是否与现有排班冲突
	if employeeShift.EmployeeID != existingShift.EmployeeID || !sameDate(employeeShift.Date, existingShift.Date) {
		dateStr := employeeShift.Date.Format("2006-01-02")
		conflictShift, err := s.employeeShiftRepo.GetEmployeeShiftByEmployeeAndDate(ctx, employeeShift.EmployeeID, dateStr)
		if err == nil && conflictShift.ID != employeeShift.ID {
			return repository.EmployeeShift{}, fmt.Errorf("员工在该日期已有其他排班安排")
		} else if err != nil && err != repository.ErrNotFound {
			return repository.EmployeeShift{}, fmt.Errorf("检查排班冲突时发生错误: %w", err)
		}
	}

	// 更新时间
	employeeShift.UpdatedAt = time.Now()

	// 保留原始创建时间
	employeeShift.CreatedAt = existingShift.CreatedAt

	// 更新排班记录
	err = s.employeeShiftRepo.UpdateEmployeeShift(ctx, &employeeShift)
	if err != nil {
		return repository.EmployeeShift{}, fmt.Errorf("更新员工排班失败: %w", err)
	}

	return employeeShift, nil
}

// GetEmployeeShiftByID 根据ID获取员工班次
func (s *EmployeeShiftServiceImpl) GetEmployeeShiftByID(ctx context.Context, employeeShiftID repository.ID) (repository.EmployeeShift, error) {
	employeeShift, err := s.employeeShiftRepo.GetEmployeeShiftByID(ctx, employeeShiftID)
	if err != nil {
		if err == repository.ErrNotFound {
			return repository.EmployeeShift{}, errors.EmployeeShiftNotFoundError(employeeShiftID.String(), err)
		}
		return repository.EmployeeShift{}, fmt.Errorf("获取员工排班信息失败: %w", err)
	}

	return *employeeShift, nil
}

// ListEmployeeShifts 列出员工班次
func (s *EmployeeShiftServiceImpl) ListEmployeeShifts(ctx context.Context, filter repository.EmployeeShiftFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.EmployeeShift, int, error) {
	shifts, total, err := s.employeeShiftRepo.ListEmployeeShifts(ctx, &filter, &pagination, &sort)
	if err != nil {
		return nil, 0, fmt.Errorf("获取员工排班列表失败: %w", err)
	}

	// 转换为非指针类型的切片
	result := make([]repository.EmployeeShift, len(shifts))
	for i, s := range shifts {
		result[i] = *s
	}

	return result, total, nil
}

// CheckIn 员工签到
func (s *EmployeeShiftServiceImpl) CheckIn(ctx context.Context, employeeShiftID repository.ID, checkInTime time.Time) error {
	// 先获取排班记录
	employeeShift, err := s.employeeShiftRepo.GetEmployeeShiftByID(ctx, employeeShiftID)
	if err != nil {
		if err == repository.ErrNotFound {
			return errors.EmployeeShiftNotFoundError(employeeShiftID.String(), err)
		}
		return fmt.Errorf("获取员工排班信息失败: %w", err)
	}

	// 检查状态是否允许签到
	if !employeeShift.CanCheckIn() {
		return fmt.Errorf("当前状态不允许签到")
	}

	// 执行签到
	checkInTimeStr := checkInTime.Format(time.RFC3339)
	err = s.employeeShiftRepo.CheckInEmployeeShift(ctx, employeeShiftID, checkInTimeStr)
	if err != nil {
		return fmt.Errorf("员工签到失败: %w", err)
	}

	return nil
}

// CheckOut 员工签退
func (s *EmployeeShiftServiceImpl) CheckOut(ctx context.Context, employeeShiftID repository.ID, checkOutTime time.Time) error {
	// 先获取排班记录
	employeeShift, err := s.employeeShiftRepo.GetEmployeeShiftByID(ctx, employeeShiftID)
	if err != nil {
		if err == repository.ErrNotFound {
			return errors.EmployeeShiftNotFoundError(employeeShiftID.String(), err)
		}
		return fmt.Errorf("获取员工排班信息失败: %w", err)
	}

	// 检查状态是否允许签退
	if !employeeShift.CanCheckOut() {
		return fmt.Errorf("当前状态不允许签退")
	}

	// 执行签退
	checkOutTimeStr := checkOutTime.Format(time.RFC3339)
	err = s.employeeShiftRepo.CheckOutEmployeeShift(ctx, employeeShiftID, checkOutTimeStr)
	if err != nil {
		return fmt.Errorf("员工签退失败: %w", err)
	}

	return nil
}

// MarkAbsent 标记员工缺勤
func (s *EmployeeShiftServiceImpl) MarkAbsent(ctx context.Context, employeeShiftID repository.ID, notes string) error {
	// 先获取排班记录
	employeeShift, err := s.employeeShiftRepo.GetEmployeeShiftByID(ctx, employeeShiftID)
	if err != nil {
		if err == repository.ErrNotFound {
			return errors.EmployeeShiftNotFoundError(employeeShiftID.String(), err)
		}
		return fmt.Errorf("获取员工排班信息失败: %w", err)
	}

	// 只有已排班状态的记录可以标记为缺勤
	if employeeShift.Status != string(repository.EmployeeShiftStatusScheduled) {
		return fmt.Errorf("只有未签到的排班记录可以标记为缺勤")
	}

	// 标记缺勤
	err = s.employeeShiftRepo.MarkEmployeeShiftAbsent(ctx, employeeShiftID, notes)
	if err != nil {
		return fmt.Errorf("标记员工缺勤失败: %w", err)
	}

	return nil
}

// GetEmployeeShiftByEmployeeAndDate 获取指定员工在特定日期的班次安排
func (s *EmployeeShiftServiceImpl) GetEmployeeShiftByEmployeeAndDate(ctx context.Context, employeeID repository.ID, date time.Time) (repository.EmployeeShift, error) {
	// 格式化日期为字符串
	dateStr := date.Format("2006-01-02")

	// 查询员工在该日期的排班
	employeeShift, err := s.employeeShiftRepo.GetEmployeeShiftByEmployeeAndDate(ctx, employeeID, dateStr)
	if err != nil {
		if err == repository.ErrNotFound {
			return repository.EmployeeShift{}, errors.EmployeeShiftNotFoundError(employeeID.String(), err)
		}
		return repository.EmployeeShift{}, fmt.Errorf("获取员工排班信息失败: %w", err)
	}

	return *employeeShift, nil
}

// GenerateShiftsFromTemplate 根据班次模板生成员工排班
func (s *EmployeeShiftServiceImpl) GenerateShiftsFromTemplate(
	ctx context.Context,
	templateID repository.ID,
	employeeIDs []repository.ID,
	startDate time.Time,
	endDate time.Time,
) ([]repository.EmployeeShift, error) {
	// 获取班次模板
	template, err := s.templateRepo.GetShiftTemplateByID(ctx, templateID)
	if err != nil {
		if err == repository.ErrNotFound {
			return nil, errors.ShiftTemplateNotFoundError(templateID.String(), err)
		}
		return nil, fmt.Errorf("获取班次模板失败: %w", err)
	}

	// 检查模板是否活跃
	if !template.IsActive() {
		return nil, fmt.Errorf("班次模板未激活")
	}

	// 计算日期范围
	days := daysBetween(startDate, endDate)
	if days <= 0 {
		return nil, fmt.Errorf("无效的日期范围")
	}

	// 准备结果
	result := []repository.EmployeeShift{}

	// 为每个员工在日期范围内创建排班
	currentDate := startDate
	for day := 0; day <= days; day++ {
		// 创建当天的班次
		shift, err := s.createShiftFromTemplate(ctx, template, currentDate)
		if err != nil {
			return nil, fmt.Errorf("根据模板创建班次失败: %w", err)
		}

		// 为每个员工创建排班
		for _, employeeID := range employeeIDs {
			employeeShift := repository.EmployeeShift{
				EmployeeID: employeeID,
				ShiftID:    shift.ID,
				Date:       currentDate,
				Status:     string(repository.EmployeeShiftStatusScheduled),
				Metadata: map[string]interface{}{
					"generated_from_template": true,
					"template_id":             templateID,
				},
			}

			result = append(result, employeeShift)
		}

		// 增加一天
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	// 批量创建排班记录
	createdShifts, err := s.BatchAssignShifts(ctx, result)
	if err != nil {
		return nil, fmt.Errorf("批量创建排班记录失败: %w", err)
	}

	return createdShifts, nil
}

// 辅助方法

// createShiftFromTemplate 根据模板创建班次
func (s *EmployeeShiftServiceImpl) createShiftFromTemplate(ctx context.Context, template *repository.ShiftTemplate, date time.Time) (*repository.Shift, error) {
	// 解析模板中的时间
	startTimeStr := fmt.Sprintf("%s %s", date.Format("2006-01-02"), template.StartTime)
	startTime, err := time.Parse("2006-01-02 15:04", startTimeStr)
	if err != nil {
		return nil, fmt.Errorf("解析开始时间失败: %w", err)
	}

	endTimeStr := fmt.Sprintf("%s %s", date.Format("2006-01-02"), template.EndTime)
	endTime, err := time.Parse("2006-01-02 15:04", endTimeStr)
	if err != nil {
		return nil, fmt.Errorf("解析结束时间失败: %w", err)
	}

	// 如果结束时间早于开始时间，说明跨天，增加一天
	if endTime.Before(startTime) {
		endTime = endTime.AddDate(0, 0, 1)
	}

	// 生成班次编号，使用新的生成规则
	shiftNumber, err := s.generateShiftNumberFromTemplate(ctx, template, date)
	if err != nil {
		return nil, fmt.Errorf("生成班次编号失败: %w", err)
	}

	// 创建班次
	shift := repository.Shift{
		ShiftNumber: shiftNumber,
		StationID:   template.StationID, // 现在都是 int64 类型
		StartTime:   startTime,
		EndTime:     &endTime,
		Metadata: map[string]interface{}{
			"created_from_template": true,
			"template_id":           template.ID,
			"template_name":         template.Name,
		},
	}

	createdShift, err := s.shiftRepo.CreateShift(ctx, &shift)
	if err != nil {
		return nil, fmt.Errorf("创建班次失败: %w", err)
	}

	return createdShift, nil
}

// generateShiftNumberFromTemplate 根据模板生成班次编号
// 使用新的格式: SHIFT-{SiteCode}-{YYMMDD}-{number_of_shifts}
func (s *EmployeeShiftServiceImpl) generateShiftNumberFromTemplate(ctx context.Context, template *repository.ShiftTemplate, date time.Time) (string, error) {
	// 获取站点的site code
	station, err := s.stationService.GetStationByID(ctx, template.StationID)
	if err != nil {
		return "", fmt.Errorf("获取站点信息失败: %w", err)
	}

	// 确保date使用雅加达时区
	dateInJakarta := date.In(utils.JakartaLocation)
	dateStr := dateInJakarta.Format("060102") // YYMMDD格式

	// 获取当天已有的班次数量
	count, err := s.shiftRepo.GetDailyShiftCount(ctx, template.StationID, dateInJakarta)
	if err != nil {
		return "", fmt.Errorf("获取当日班次数量失败: %w", err)
	}

	// 新班次序号是当前数量+1，格式化为两位数字
	nextShiftNumber := count + 1
	shiftSequence := fmt.Sprintf("%02d", nextShiftNumber)

	return fmt.Sprintf("SHIFT-%s-%s-%s", station.SiteCode, dateStr, shiftSequence), nil
}

// sameDate 判断两个时间是否是同一天
func sameDate(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// daysBetween 计算两个日期之间的天数
func daysBetween(startDate, endDate time.Time) int {
	// 标准化日期，去除时分秒
	start := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, startDate.Location())
	end := time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 0, 0, 0, 0, endDate.Location())

	// 计算相差的小时数，然后转换为天数
	hours := end.Sub(start).Hours()
	return int(hours / 24)
}
