package service

import (
	"context"
	"fmt"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// StationServiceImpl 站点服务实现
type StationServiceImpl struct {
	stationRepo repository.StationRepository
}

// NewStationService 创建站点服务实例
func NewStationService(stationRepo repository.StationRepository) StationService {
	return &StationServiceImpl{
		stationRepo: stationRepo,
	}
}

// GetStationByID 根据ID获取站点信息
func (s *StationServiceImpl) GetStationByID(ctx context.Context, stationID int64) (*repository.Station, error) {
	// 直接使用 int64 类型
	station, err := s.stationRepo.GetStationByID(ctx, stationID)
	if err != nil {
		return nil, fmt.Errorf("获取站点信息失败: %w", err)
	}
	return station, nil
}

// GetStationName 根据ID获取站点名称
func (s *StationServiceImpl) GetStationName(ctx context.Context, stationID int64) (string, error) {
	// 直接使用 int64 类型
	stationName, err := s.stationRepo.GetStationName(ctx, stationID)
	if err != nil {
		return "", fmt.Errorf("获取站点名称失败: %w", err)
	}
	return stationName, nil
}

// GetStationsBatch 批量获取站点信息
func (s *StationServiceImpl) GetStationsBatch(ctx context.Context, stationIDs []int64) (map[int64]*repository.Station, error) {
	// 直接使用 []int64 类型
	stations, err := s.stationRepo.GetStationsBatch(ctx, stationIDs)
	if err != nil {
		return nil, fmt.Errorf("批量获取站点信息失败: %w", err)
	}

	return stations, nil
}