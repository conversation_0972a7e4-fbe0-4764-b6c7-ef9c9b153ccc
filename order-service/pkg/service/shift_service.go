package service

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ShiftService 班次服务接口
type ShiftService interface {
	// StartShift 开始一个新的班次
	// 如果当前站点已有活跃班次，则返回错误
	StartShift(ctx context.Context, stationID int64, metadata map[string]interface{}) (repository.Shift, error)

	// EndShift 结束当前活跃班次
	// 如果当前站点没有活跃班次，则返回错误
	// 如果有未处理的交易，则返回错误
	EndShift(ctx context.Context, stationID int64) (repository.Shift, error)

	// GetCurrentShift 获取当前站点的活跃班次
	// 如果当前站点没有活跃班次，则返回错误
	GetCurrentShift(ctx context.Context, stationID int64) (repository.Shift, error)

	// EnsureShiftStarted 确保当前站点有活跃班次
	// 如果没有活跃班次，则自动创建一个新的班次
	EnsureShiftStarted(ctx context.Context, stationID int64) (repository.Shift, bool, error)

	// ListShifts 列出符合筛选条件的班次
	ListShifts(ctx context.Context, filter repository.ShiftFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.Shift, int, error)

	// GetShift 根据ID获取班次
	GetShift(ctx context.Context, shiftID repository.ID) (repository.Shift, error)

	// GetShiftByNumber 根据班次编号获取班次
	GetShiftByNumber(ctx context.Context, shiftNumber string) (repository.Shift, error)

	// SoftDeleteShift 软删除班次
	// 如果班次处于活跃状态，则返回错误
	SoftDeleteShift(ctx context.Context, shiftID repository.ID) error

	// RestoreShift 恢复已删除的班次
	RestoreShift(ctx context.Context, shiftID repository.ID) error

	// GetShiftReport 获取班次报表数据
	// 根据班次ID生成完整的班次报表，包含支付汇总、油品汇总、非油品汇总等
	GetShiftReport(ctx context.Context, shiftID repository.ID, options repository.ShiftReportOptions) (repository.ShiftReportDTO, error)

	// GetShiftReceipt 获取班次小票格式数据
	// 根据班次ID生成小票格式的数据，用于打印或显示
	GetShiftReceipt(ctx context.Context, shiftID repository.ID, options repository.ShiftReportOptions) (repository.ShiftReceiptDTO, error)
}
