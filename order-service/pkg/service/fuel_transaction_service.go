package service

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// FuelTransactionService 定义燃油交易服务接口
type FuelTransactionService interface {
	// CreateFuelTransaction 创建燃油交易
	CreateFuelTransaction(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error)

	// GetFuelTransaction 获取燃油交易详情
	GetFuelTransaction(ctx context.Context, id repository.ID) (repository.FuelTransaction, error)

	// ListFuelTransactions 列出燃油交易列表
	ListFuelTransactions(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransaction, int, error)

	// GetFuelTransactionsFullWithJoins 获取完整燃油交易信息（使用连表查询优化）
	GetFuelTransactionsFullWithJoins(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransactionFull, int, error)

	// LinkFuelTransactionToOrder 将燃油交易关联到订单
	LinkFuelTransactionToOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID, allocatedAmount float64) (repository.FuelTransactionOrderLink, error)

	// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
	UnlinkFuelTransactionFromOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error

	// GetOrderFuelTransactions 获取订单关联的燃油交易
	GetOrderFuelTransactions(ctx context.Context, orderID repository.ID) ([]repository.FuelTransaction, error)

	// GetFuelTransactionOrders 获取燃油交易关联的订单
	GetFuelTransactionOrders(ctx context.Context, fuelTransactionID repository.ID) ([]repository.Order, error)

	// UpdateLinkAllocatedAmount 更新关联分配金额
	UpdateLinkAllocatedAmount(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID, newAllocatedAmount float64) (repository.FuelTransactionOrderLink, error)

	// GetActiveFuelTransactionLinks 获取燃油交易关联的所有活跃订单链接
	GetActiveFuelTransactionLinks(ctx context.Context, fuelTransactionID repository.ID) ([]repository.FuelTransactionOrderLink, error)

	// ConfirmFuelTransactionLink 确认燃油交易链接
	ConfirmFuelTransactionLink(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error

	// CancelFuelTransactionLink 取消燃油交易链接
	CancelFuelTransactionLink(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error

	// CleanupFuelTransactionLinks 清理燃油交易链接
	CleanupFuelTransactionLinks(ctx context.Context, fuelTransactionID repository.ID) error

	// ProcessFuelTransactionStatus 处理燃油交易状态
	ProcessFuelTransactionStatus(ctx context.Context, fuelTransactionID repository.ID, force bool) error

	// UpdatePumpReadings 更新燃油交易的泵码数
	UpdatePumpReadings(ctx context.Context, fuelTransactionID repository.ID, startTotalizer *float64, endTotalizer *float64) (repository.FuelTransaction, error)
}
