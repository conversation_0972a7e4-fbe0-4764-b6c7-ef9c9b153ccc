package service

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ShiftTemplateService 班次模板服务接口
type ShiftTemplateService interface {
	// CreateTemplate 创建班次模板
	CreateTemplate(ctx context.Context, template repository.ShiftTemplate) (repository.ShiftTemplate, error)

	// UpdateTemplate 更新班次模板
	UpdateTemplate(ctx context.Context, template repository.ShiftTemplate) (repository.ShiftTemplate, error)

	// DeleteTemplate 删除班次模板（软删除）
	DeleteTemplate(ctx context.Context, templateID repository.ID) error

	// GetTemplateByID 根据ID获取班次模板
	GetTemplateByID(ctx context.Context, templateID repository.ID) (repository.ShiftTemplate, error)

	// ListTemplates 列出班次模板
	ListTemplates(ctx context.Context, filter repository.ShiftTemplateFilter, pagination repository.Pagination,
		sort repository.SortOrder) ([]repository.ShiftTemplate, int, error)

	// GetActiveTemplatesForStation 获取指定加油站的所有活跃班次模板
	GetActiveTemplatesForStation(ctx context.Context, stationID int64) ([]repository.ShiftTemplate, error)

	// ActivateTemplate 激活班次模板
	ActivateTemplate(ctx context.Context, templateID repository.ID) error

	// DeactivateTemplate 停用班次模板
	DeactivateTemplate(ctx context.Context, templateID repository.ID) error
}
