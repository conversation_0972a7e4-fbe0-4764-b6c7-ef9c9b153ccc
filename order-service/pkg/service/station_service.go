package service

import (
	"context"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// StationService 站点服务接口
type StationService interface {
	// GetStationByID 根据ID获取站点信息
	GetStationByID(ctx context.Context, stationID int64) (*repository.Station, error)

	// GetStationName 根据ID获取站点名称
	GetStationName(ctx context.Context, stationID int64) (string, error)

	// GetStationsBatch 批量获取站点信息
	GetStationsBatch(ctx context.Context, stationIDs []int64) (map[int64]*repository.Station, error)
}