package service

import (
	"context"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// PaymentService 支付服务接口
type PaymentService interface {
	// GetPaymentsByOrderIDs 根据订单ID列表获取支付信息
	GetPaymentsByOrderIDs(ctx context.Context, orderIDs []string) ([]repository.PaymentInfo, error)
	
	// GetPaymentsByStationAndTimeRange 根据站点和时间范围获取支付信息
	GetPaymentsByStationAndTimeRange(ctx context.Context, stationID int64, startTime, endTime time.Time) ([]repository.PaymentInfo, error)
	
	// GetPaymentMethods 获取支付方式配置
	GetPaymentMethods(ctx context.Context) ([]repository.PaymentMethod, error)
} 