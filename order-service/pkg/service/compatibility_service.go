package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// CompatibilityService 兼容性服务，支持新旧接口并存
type CompatibilityService struct {
	orderService      OrderService
	fuelTxService     FuelTransactionService
	staffCardService  StaffCardService
	employeeService   EmployeeService
}

// NewCompatibilityService 创建兼容性服务实例
func NewCompatibilityService(
	orderService OrderService,
	fuelTxService FuelTransactionService,
	staffCardService StaffCardService,
	employeeService EmployeeService,
) *CompatibilityService {
	return &CompatibilityService{
		orderService:     orderService,
		fuelTxService:    fuelTxService,
		staffCardService: staffCardService,
		employeeService:  employeeService,
	}
}

// OrderServiceCompat 兼容的订单服务方法
type OrderServiceCompat interface {
	// CreateOrderCompat 创建订单（兼容版本）
	CreateOrderCompat(ctx context.Context, order repository.Order) (repository.Order, error)
	
	// UpdateOrderCompat 更新订单（兼容版本）
	UpdateOrderCompat(ctx context.Context, order repository.Order) (repository.Order, error)
	
	// GetOrderCompat 获取订单（兼容版本）
	GetOrderCompat(ctx context.Context, id repository.ID) (repository.Order, error)
	
	// ListOrdersCompat 列出订单（兼容版本）
	ListOrdersCompat(ctx context.Context, filter repository.OrderFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.Order, int, error)
}

// FuelTransactionServiceCompat 兼容的燃油交易服务方法
type FuelTransactionServiceCompat interface {
	// CreateFuelTransactionCompat 创建燃油交易（兼容版本）
	CreateFuelTransactionCompat(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error)
	
	// UpdateFuelTransactionCompat 更新燃油交易（兼容版本）
	UpdateFuelTransactionCompat(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error)
	
	// GetFuelTransactionCompat 获取燃油交易（兼容版本）
	GetFuelTransactionCompat(ctx context.Context, id repository.ID) (repository.FuelTransaction, error)
	
	// ListFuelTransactionsCompat 列出燃油交易（兼容版本）
	ListFuelTransactionsCompat(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransaction, int, error)
}

// EmployeeToStaffCardMigration 员工到员工卡迁移服务
type EmployeeToStaffCardMigration interface {
	// MigrateEmployeeToStaffCard 将employee转换为staff_card
	MigrateEmployeeToStaffCard(ctx context.Context, employeeID repository.ID) (repository.StaffCard, error)
	
	// MigrateAllEmployees 批量迁移所有员工
	MigrateAllEmployees(ctx context.Context) ([]repository.StaffCard, error)
	
	// FindStaffCardByEmployeeNo 根据员工编号查找对应的员工卡
	FindStaffCardByEmployeeNo(ctx context.Context, employeeNo string) (repository.StaffCard, error)
	
	// FindEmployeeByStaffCardID 根据员工卡ID查找对应的原员工记录
	FindEmployeeByStaffCardID(ctx context.Context, staffCardID repository.ID) (repository.Employee, error)
}

// CreateOrderCompat 创建订单（兼容版本）
func (c *CompatibilityService) CreateOrderCompat(ctx context.Context, order repository.Order) (repository.Order, error) {
	// 优先使用 staff_card_id
	if order.StaffCardID != nil {
		return c.orderService.CreateOrder(ctx, order)
	}
	
	// 兼容旧的 employee_no 方式
	if order.EmployeeNo != nil && *order.EmployeeNo != "" {
		// 查找对应的 staff_card
		staffCard, err := c.FindStaffCardByEmployeeNo(ctx, *order.EmployeeNo)
		if err == nil {
			// 找到对应的员工卡，使用员工卡ID
			order.StaffCardID = &staffCard.ID
		}
		// 即使没找到员工卡，也继续创建订单（保持向后兼容）
	}
	
	return c.orderService.CreateOrder(ctx, order)
}

// UpdateOrderCompat 更新订单（兼容版本）
func (c *CompatibilityService) UpdateOrderCompat(ctx context.Context, order repository.Order) (repository.Order, error) {
	// 如果提供了employee_no但没有staff_card_id，尝试转换
	if order.EmployeeNo != nil && *order.EmployeeNo != "" && order.StaffCardID == nil {
		staffCard, err := c.FindStaffCardByEmployeeNo(ctx, *order.EmployeeNo)
		if err == nil {
			order.StaffCardID = &staffCard.ID
		}
	}
	
	// 注意：OrderService没有UpdateOrder方法，这里需要通过Repository直接更新
	// 或者返回错误提示该功能暂未实现
	return order, fmt.Errorf("UpdateOrder method not implemented in OrderService")
}

// GetOrderCompat 获取订单（兼容版本）
func (c *CompatibilityService) GetOrderCompat(ctx context.Context, id repository.ID) (repository.Order, error) {
	order, err := c.orderService.GetOrder(ctx, id)
	if err != nil {
		return repository.Order{}, err
	}
	
	// 如果有staff_card_id但没有employee_no，尝试填充employee_no（向后兼容）
	if order.StaffCardID != nil && (order.EmployeeNo == nil || *order.EmployeeNo == "") {
		employee, err := c.FindEmployeeByStaffCardID(ctx, *order.StaffCardID)
		if err == nil {
			order.EmployeeNo = &employee.EmployeeNo
		}
	}
	
	return order, nil
}

// ListOrdersCompat 列出订单（兼容版本）
func (c *CompatibilityService) ListOrdersCompat(ctx context.Context, filter repository.OrderFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.Order, int, error) {
	// 如果筛选条件中有employee_no但没有staff_card_id，尝试转换
	if filter.EmployeeNo != nil && *filter.EmployeeNo != "" && filter.StaffCardID == nil {
		staffCard, err := c.FindStaffCardByEmployeeNo(ctx, *filter.EmployeeNo)
		if err == nil {
			filter.StaffCardID = &staffCard.ID
		}
	}
	
	orders, total, err := c.orderService.ListOrders(ctx, filter, pagination, sort)
	if err != nil {
		return nil, 0, err
	}
	
	// 为每个订单填充兼容信息
	for i := range orders {
		if orders[i].StaffCardID != nil && (orders[i].EmployeeNo == nil || *orders[i].EmployeeNo == "") {
			employee, err := c.FindEmployeeByStaffCardID(ctx, *orders[i].StaffCardID)
			if err == nil {
				orders[i].EmployeeNo = &employee.EmployeeNo
			}
		}
	}
	
	return orders, total, nil
}

// CreateFuelTransactionCompat 创建燃油交易（兼容版本）
func (c *CompatibilityService) CreateFuelTransactionCompat(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error) {
	// 优先使用 staff_card_id
	if transaction.StaffCardID != nil {
		return c.fuelTxService.CreateFuelTransaction(ctx, transaction)
	}
	
	// 兼容旧的 employee_id 方式
	if transaction.EmployeeID != nil {
		// 尝试根据employee_id找到对应的员工，然后找到对应的staff_card
		employee, err := c.employeeService.GetEmployee(ctx, *transaction.EmployeeID)
		if err == nil {
			staffCard, err := c.FindStaffCardByEmployeeNo(ctx, employee.EmployeeNo)
			if err == nil {
				transaction.StaffCardID = &staffCard.ID
			}
		}
	}
	
	return c.fuelTxService.CreateFuelTransaction(ctx, transaction)
}

// UpdateFuelTransactionCompat 更新燃油交易（兼容版本）
func (c *CompatibilityService) UpdateFuelTransactionCompat(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error) {
	// 如果提供了employee_id但没有staff_card_id，尝试转换
	if transaction.EmployeeID != nil && transaction.StaffCardID == nil {
		employee, err := c.employeeService.GetEmployee(ctx, *transaction.EmployeeID)
		if err == nil {
			staffCard, err := c.FindStaffCardByEmployeeNo(ctx, employee.EmployeeNo)
			if err == nil {
				transaction.StaffCardID = &staffCard.ID
			}
		}
	}
	
	// 注意：FuelTransactionService没有UpdateFuelTransaction方法，这里需要通过Repository直接更新
	// 或者返回错误提示该功能暂未实现
	return transaction, fmt.Errorf("UpdateFuelTransaction method not implemented in FuelTransactionService")
}

// GetFuelTransactionCompat 获取燃油交易（兼容版本）
func (c *CompatibilityService) GetFuelTransactionCompat(ctx context.Context, id repository.ID) (repository.FuelTransaction, error) {
	transaction, err := c.fuelTxService.GetFuelTransaction(ctx, id)
	if err != nil {
		return repository.FuelTransaction{}, err
	}
	
	// 如果有staff_card_id但没有employee_id，尝试填充employee_id（向后兼容）
	if transaction.StaffCardID != nil && transaction.EmployeeID == nil {
		employee, err := c.FindEmployeeByStaffCardID(ctx, *transaction.StaffCardID)
		if err == nil {
			transaction.EmployeeID = &employee.ID
		}
	}
	
	return transaction, nil
}

// ListFuelTransactionsCompat 列出燃油交易（兼容版本）
func (c *CompatibilityService) ListFuelTransactionsCompat(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransaction, int, error) {
	// 如果筛选条件中有employee_id但没有staff_card_id，尝试转换
	if filter.EmployeeID != nil && filter.StaffCardID == nil {
		employee, err := c.employeeService.GetEmployee(ctx, *filter.EmployeeID)
		if err == nil {
			staffCard, err := c.FindStaffCardByEmployeeNo(ctx, employee.EmployeeNo)
			if err == nil {
				filter.StaffCardID = &staffCard.ID
			}
		}
	}
	
	transactions, total, err := c.fuelTxService.ListFuelTransactions(ctx, filter, pagination, sort)
	if err != nil {
		return nil, 0, err
	}
	
	// 为每个交易填充兼容信息
	for i := range transactions {
		if transactions[i].StaffCardID != nil && transactions[i].EmployeeID == nil {
			employee, err := c.FindEmployeeByStaffCardID(ctx, *transactions[i].StaffCardID)
			if err == nil {
				transactions[i].EmployeeID = &employee.ID
			}
		}
	}
	
	return transactions, total, nil
}

// MigrateEmployeeToStaffCard 将employee转换为staff_card
func (c *CompatibilityService) MigrateEmployeeToStaffCard(ctx context.Context, employeeID repository.ID) (repository.StaffCard, error) {
	// 获取员工信息
	employee, err := c.employeeService.GetEmployee(ctx, employeeID)
	if err != nil {
		return repository.StaffCard{}, fmt.Errorf("获取员工信息失败: %w", err)
	}
	
	// 检查是否已经存在对应的员工卡
	existingCard, err := c.FindStaffCardByEmployeeNo(ctx, employee.EmployeeNo)
	if err == nil {
		// 已存在，返回现有的员工卡
		return existingCard, nil
	}
	
	// 需要为员工创建用户账号（这里需要根据实际业务需求实现）
	// 暂时使用员工编号作为用户ID的占位符
	userID := c.generateUserIDFromEmployee(employee)
	
	// 创建员工卡
	staffCard := repository.StaffCard{
		UserID:      userID,
		CardType:    repository.StaffCardTypeEmployee,
		Status:      repository.StaffCardStatusActive,
		ValidFrom:   time.Now(),
		Permissions: make(map[string]interface{}),
		Metadata: map[string]interface{}{
			"migrated_from_employee_id": employeeID,
			"original_employee_no":      employee.EmployeeNo,
			"migration_date":            time.Now(),
		},
	}
	
	return c.staffCardService.CreateStaffCard(ctx, staffCard)
}

// MigrateAllEmployees 批量迁移所有员工
func (c *CompatibilityService) MigrateAllEmployees(ctx context.Context) ([]repository.StaffCard, error) {
	// 获取所有员工
	employees, _, err := c.employeeService.ListEmployees(ctx, repository.EmployeeFilter{}, repository.Pagination{Limit: 1000}, repository.SortOrder{})
	if err != nil {
		return nil, fmt.Errorf("获取员工列表失败: %w", err)
	}
	
	var migratedCards []repository.StaffCard
	var failedEmployees []repository.ID
	
	for _, employee := range employees {
		card, err := c.MigrateEmployeeToStaffCard(ctx, employee.ID)
		if err != nil {
			failedEmployees = append(failedEmployees, employee.ID)
			continue
		}
		migratedCards = append(migratedCards, card)
	}
	
	if len(failedEmployees) > 0 {
		return migratedCards, fmt.Errorf("部分员工迁移失败，失败的员工ID: %v", failedEmployees)
	}
	
	return migratedCards, nil
}

// FindStaffCardByEmployeeNo 根据员工编号查找对应的员工卡
func (c *CompatibilityService) FindStaffCardByEmployeeNo(ctx context.Context, employeeNo string) (repository.StaffCard, error) {
	// 通过metadata查找原employee_no对应的staff_card
	filter := repository.StaffCardFilter{
		// 这里需要实现支持JSONB查询的功能
		// 暂时使用简单的列表查询然后过滤
	}
	
	staffCards, _, err := c.staffCardService.ListStaffCards(ctx, filter, repository.Pagination{Limit: 1000}, repository.SortOrder{})
	if err != nil {
		return repository.StaffCard{}, err
	}
	
	// 在内存中过滤查找
	for _, card := range staffCards {
		if originalEmployeeNo, exists := card.Metadata["original_employee_no"]; exists {
			if originalEmployeeNo.(string) == employeeNo {
				return card, nil
			}
		}
	}
	
	return repository.StaffCard{}, fmt.Errorf("未找到员工编号 %s 对应的员工卡", employeeNo)
}

// FindEmployeeByStaffCardID 根据员工卡ID查找对应的原员工记录
func (c *CompatibilityService) FindEmployeeByStaffCardID(ctx context.Context, staffCardID repository.ID) (repository.Employee, error) {
	// 获取员工卡信息
	staffCard, err := c.staffCardService.GetStaffCard(ctx, staffCardID)
	if err != nil {
		return repository.Employee{}, err
	}
	
	// 从metadata中获取原始员工ID
	if employeeIDStr, exists := staffCard.Metadata["migrated_from_employee_id"]; exists {
		var employeeID repository.ID
		switch v := employeeIDStr.(type) {
		case string:
			// 尝试解析为 UUID
			id, err := uuid.Parse(v)
			if err != nil {
				return repository.Employee{}, fmt.Errorf("解析员工ID失败: %w", err)
			}
			employeeID = repository.ID(id)
		default:
			return repository.Employee{}, fmt.Errorf("无效的员工ID格式，必须是UUID字符串")
		}
		
		return c.employeeService.GetEmployee(ctx, employeeID)
	}
	
	// 如果没有找到原始员工ID，尝试通过员工编号查找
	if originalEmployeeNo, exists := staffCard.Metadata["original_employee_no"]; exists {
		if employeeNo, ok := originalEmployeeNo.(string); ok {
			return c.employeeService.GetEmployeeByNo(ctx, employeeNo)
		}
	}
	
	return repository.Employee{}, fmt.Errorf("未找到员工卡 %d 对应的原员工记录", staffCardID)
}

// 私有辅助方法

// generateUserIDFromEmployee 从员工信息生成用户ID
// 这是一个占位符实现，实际项目中需要根据业务需求实现
func (c *CompatibilityService) generateUserIDFromEmployee(employee repository.Employee) string {
	// 这里应该调用auth_db中的用户创建接口
	// 暂时返回一个基于员工编号的UUID格式字符串
	return fmt.Sprintf("user-%s-%d", strings.ToLower(employee.EmployeeNo), time.Now().Unix())
} 