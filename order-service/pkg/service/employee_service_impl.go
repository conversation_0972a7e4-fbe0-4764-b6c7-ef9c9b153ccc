package service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// EmployeeServiceImpl 实现EmployeeService接口
type EmployeeServiceImpl struct {
	employeeRepo repository.EmployeeRepository
}

// NewEmployeeService 创建一个新的EmployeeService实例
func NewEmployeeService(employeeRepo repository.EmployeeRepository) EmployeeService {
	return &EmployeeServiceImpl{
		employeeRepo: employeeRepo,
	}
}

// CreateEmployee 创建新员工
func (s *EmployeeServiceImpl) CreateEmployee(ctx context.Context, employee repository.Employee) (repository.Employee, error) {
	// 验证员工信息
	if employee.EmployeeNo == "" {
		return repository.Employee{}, errors.New("员工编号不能为空")
	}
	if employee.Name == "" {
		return repository.Employee{}, errors.New("员工姓名不能为空")
	}
	if employee.Password == "" {
		return repository.Employee{}, errors.New("员工密码不能为空")
	}

	// 通过存储库创建员工
	return s.employeeRepo.Create(ctx, employee)
}

// UpdateEmployee 更新员工信息
func (s *EmployeeServiceImpl) UpdateEmployee(ctx context.Context, employee repository.Employee) (repository.Employee, error) {
	// 检查员工ID是否有效
	if uuid.UUID(employee.ID) == uuid.Nil {
		return repository.Employee{}, errors.New("无效的员工ID")
	}

	// 获取现有员工信息
	existingEmployee, err := s.employeeRepo.Get(ctx, employee.ID)
	if err != nil {
		return repository.Employee{}, fmt.Errorf("获取员工信息失败: %w", err)
	}

	// 合并员工信息
	if employee.Name == "" {
		employee.Name = existingEmployee.Name
	}

	// 保留员工编号，不允许修改
	employee.EmployeeNo = existingEmployee.EmployeeNo

	// 通过存储库更新员工
	return s.employeeRepo.Update(ctx, employee)
}

// GetEmployee 获取员工详情
func (s *EmployeeServiceImpl) GetEmployee(ctx context.Context, id repository.ID) (repository.Employee, error) {
	if uuid.UUID(id) == uuid.Nil {
		return repository.Employee{}, errors.New("无效的员工ID")
	}
	return s.employeeRepo.Get(ctx, id)
}

// GetEmployeeByNo 根据员工编号获取员工
func (s *EmployeeServiceImpl) GetEmployeeByNo(ctx context.Context, employeeNo string) (repository.Employee, error) {
	if employeeNo == "" {
		return repository.Employee{}, errors.New("员工编号不能为空")
	}
	return s.employeeRepo.GetByEmployeeNo(ctx, employeeNo)
}

// ListEmployees 获取员工列表
func (s *EmployeeServiceImpl) ListEmployees(ctx context.Context, filter repository.EmployeeFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.Employee, int, error) {
	// 处理分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.Limit <= 0 {
		pagination.Limit = 20 // 默认每页20条记录
	}

	// 处理排序参数
	if sort.Field == "" {
		sort.Field = "id"
		sort.Direction = "asc"
	}
	if strings.ToLower(sort.Direction) != "asc" && strings.ToLower(sort.Direction) != "desc" {
		sort.Direction = "asc" // 默认升序
	}

	// 通过存储库获取员工列表
	return s.employeeRepo.List(ctx, filter, pagination, sort)
}

// DeleteEmployee 删除员工
func (s *EmployeeServiceImpl) DeleteEmployee(ctx context.Context, id repository.ID) error {
	if uuid.UUID(id) == uuid.Nil {
		return errors.New("无效的员工ID")
	}

	// 检查员工是否存在
	_, err := s.employeeRepo.Get(ctx, id)
	if err != nil {
		return fmt.Errorf("获取员工信息失败: %w", err)
	}

	// 通过存储库删除员工
	return s.employeeRepo.Delete(ctx, id)
}

// AuthenticateEmployee 员工登录验证
func (s *EmployeeServiceImpl) AuthenticateEmployee(ctx context.Context, employeeNo string, password string) (repository.Employee, error) {
	if employeeNo == "" {
		return repository.Employee{}, errors.New("员工编号不能为空")
	}
	if password == "" {
		return repository.Employee{}, errors.New("密码不能为空")
	}

	// 通过存储库验证员工身份
	return s.employeeRepo.Authenticate(ctx, employeeNo, password)
}
