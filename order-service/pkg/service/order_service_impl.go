package service

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// OrderServiceImpl 实现OrderService接口
type OrderServiceImpl struct {
	orderRepo               repository.OrderRepository
	fuelTransactionRepo     repository.FuelTransactionRepository
	fuelTransactionLinkRepo repository.FuelTransactionOrderLinkRepository
	shiftRepo               repository.ShiftRepository
}

// NewOrderService 创建一个新的OrderService实例
func NewOrderService(
	orderRepo repository.OrderRepository,
	fuelTransactionRepo repository.FuelTransactionRepository,
	fuelTransactionLinkRepo repository.FuelTransactionOrderLinkRepository,
	shiftRepo repository.ShiftRepository,
) OrderService {
	return &OrderServiceImpl{
		orderRepo:               orderRepo,
		fuelTransactionRepo:     fuelTransactionRepo,
		fuelTransactionLinkRepo: fuelTransactionLinkRepo,
		shiftRepo:               shiftRepo,
	}
}

// CreateOrder 创建新订单
func (s *OrderServiceImpl) CreateOrder(ctx context.Context, order repository.Order) (repository.Order, error) {
	// 设置初始状态
	order.Status = repository.OrderStatusNew

	// 确保元数据不为空
	if order.Metadata == nil {
		order.Metadata = make(map[string]interface{})
	}

	// 调试日志：检查order-service接收到的字段
	customerName := ""
	if order.CustomerName != nil {
		customerName = *order.CustomerName
	}
	customerPhone := ""
	if order.CustomerPhone != nil {
		customerPhone = *order.CustomerPhone
	}
	vehicleType := ""
	if order.VehicleType != nil {
		vehicleType = *order.VehicleType
	}
	licensePlate := ""
	if order.LicensePlate != nil {
		licensePlate = *order.LicensePlate
	}
	log.Printf("🔍 [ORDER-SERVICE] 接收到的字段 - CustomerName: '%s', CustomerPhone: '%s', VehicleType: '%s', LicensePlate: '%s'",
		customerName, customerPhone, vehicleType, licensePlate)

	// 通过存储库创建订单
	return s.orderRepo.Create(ctx, order)
}

// GetOrder 获取订单详情
func (s *OrderServiceImpl) GetOrder(ctx context.Context, id repository.ID) (repository.Order, error) {
	return s.orderRepo.Get(ctx, id)
}

// GetOrderByNumber 根据订单号获取订单
func (s *OrderServiceImpl) GetOrderByNumber(ctx context.Context, orderNumber string) (repository.Order, error) {
	return s.orderRepo.GetByOrderNumber(ctx, orderNumber)
}

// ListOrders 列出订单列表
func (s *OrderServiceImpl) ListOrders(ctx context.Context, filter repository.OrderFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.Order, int, error) {
	return s.orderRepo.List(ctx, filter, pagination, sort)
}

// AddOrderItem 添加订单项
func (s *OrderServiceImpl) AddOrderItem(ctx context.Context, orderID repository.ID, item repository.OrderItem) (repository.OrderItem, error) {
	// 检查订单是否存在
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return repository.OrderItem{}, fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单状态是否允许添加商品
	if order.Status != repository.OrderStatusNew && order.Status != repository.OrderStatusProcessing {
		return repository.OrderItem{}, errors.New("只有新建或处理中的订单可以添加商品")
	}

	// 添加订单项
	return s.orderRepo.AddOrderItem(ctx, orderID, item)
}

// UpdateOrderItem 更新订单项
func (s *OrderServiceImpl) UpdateOrderItem(ctx context.Context, item repository.OrderItem) (repository.OrderItem, error) {
	// 查询订单项以获取订单ID
	items, err := s.orderRepo.ListOrderItems(ctx, item.OrderID)
	if err != nil {
		return repository.OrderItem{}, fmt.Errorf("查询订单项失败: %w", err)
	}

	var existingItem *repository.OrderItem
	for _, i := range items {
		if i.ID == item.ID {
			existingItem = &i
			break
		}
	}

	if existingItem == nil {
		return repository.OrderItem{}, fmt.Errorf("未找到订单项: %d", item.ID)
	}

	// 检查订单状态是否允许更新商品
	order, err := s.orderRepo.Get(ctx, item.OrderID)
	if err != nil {
		return repository.OrderItem{}, fmt.Errorf("获取订单失败: %w", err)
	}

	if order.Status != repository.OrderStatusNew && order.Status != repository.OrderStatusProcessing {
		return repository.OrderItem{}, errors.New("只有新建或处理中的订单可以更新商品")
	}

	// 更新订单项
	return s.orderRepo.UpdateOrderItem(ctx, item)
}

// ListOrderItems 列出订单项
func (s *OrderServiceImpl) ListOrderItems(ctx context.Context, orderID repository.ID) ([]repository.OrderItem, error) {
	// 检查订单是否存在
	_, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	return s.orderRepo.ListOrderItems(ctx, orderID)
}

// RemoveOrderItem 移除订单项
func (s *OrderServiceImpl) RemoveOrderItem(ctx context.Context, orderID repository.ID, itemID repository.ID) error {
	// 检查订单是否存在
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单状态是否允许移除商品
	if order.Status != repository.OrderStatusNew && order.Status != repository.OrderStatusProcessing {
		return errors.New("只有新建或处理中的订单可以移除商品")
	}

	// 删除订单项
	return s.orderRepo.DeleteOrderItem(ctx, orderID, itemID)
}

// ApplyPromotion 应用促销
func (s *OrderServiceImpl) ApplyPromotion(ctx context.Context, orderID repository.ID, promotion repository.OrderPromotion) (repository.OrderPromotion, error) {
	// 检查订单是否存在
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return repository.OrderPromotion{}, fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单状态是否允许应用促销
	if order.Status != repository.OrderStatusNew && order.Status != repository.OrderStatusProcessing {
		return repository.OrderPromotion{}, errors.New("只有新建或处理中的订单可以应用促销")
	}

	// 创建促销记录
	return s.orderRepo.AddPromotion(ctx, orderID, promotion)
}

// RemovePromotion 移除促销
func (s *OrderServiceImpl) RemovePromotion(ctx context.Context, orderID repository.ID, promotionID string) error {
	// 检查订单是否存在
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单状态是否允许移除促销
	if order.Status != repository.OrderStatusNew && order.Status != repository.OrderStatusProcessing {
		return errors.New("只有新建或处理中的订单可以移除促销")
	}

	// 移除促销
	return s.orderRepo.RemovePromotion(ctx, orderID, promotionID)
}

// ListPromotions 列出促销
func (s *OrderServiceImpl) ListPromotions(ctx context.Context, orderID repository.ID) ([]repository.OrderPromotion, error) {
	// 检查订单是否存在
	_, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	return s.orderRepo.ListPromotions(ctx, orderID)
}

// AddPayment 添加支付
func (s *OrderServiceImpl) AddPayment(ctx context.Context, orderID repository.ID, payment repository.OrderPayment) (repository.OrderPayment, error) {
	// 检查订单是否存在
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return repository.OrderPayment{}, fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单状态是否允许添加支付
	if order.Status == repository.OrderStatusCancelled {
		return repository.OrderPayment{}, errors.New("已取消的订单不能添加支付")
	}

	// 添加支付记录
	return s.orderRepo.AddPayment(ctx, orderID, payment)
}

// UpdatePayment 更新支付
func (s *OrderServiceImpl) UpdatePayment(ctx context.Context, payment repository.OrderPayment) (repository.OrderPayment, error) {
	// 检查支付记录是否存在
	payments, err := s.orderRepo.ListPayments(ctx, payment.OrderID)
	if err != nil {
		return repository.OrderPayment{}, fmt.Errorf("获取支付记录失败: %w", err)
	}

	var existingPayment *repository.OrderPayment
	for _, p := range payments {
		if p.ID == payment.ID {
			existingPayment = &p
			break
		}
	}

	if existingPayment == nil {
		return repository.OrderPayment{}, fmt.Errorf("未找到支付记录: %d", payment.ID)
	}

	// 检查订单状态
	order, err := s.orderRepo.Get(ctx, payment.OrderID)
	if err != nil {
		return repository.OrderPayment{}, fmt.Errorf("获取订单失败: %w", err)
	}

	if order.Status == repository.OrderStatusCancelled {
		return repository.OrderPayment{}, errors.New("已取消的订单不能更新支付")
	}

	// 更新支付记录
	return s.orderRepo.UpdatePayment(ctx, payment)
}

// ListPayments 列出支付记录
func (s *OrderServiceImpl) ListPayments(ctx context.Context, orderID repository.ID) ([]repository.OrderPayment, error) {
	// 检查订单是否存在
	_, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	return s.orderRepo.ListPayments(ctx, orderID)
}

// CompleteOrder 完成订单
func (s *OrderServiceImpl) CompleteOrder(ctx context.Context, orderID repository.ID) (repository.Order, error) {
	// 获取订单信息，确认订单存在且状态有效
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return repository.Order{}, fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单是否可以完成
	if order.Status != repository.OrderStatusProcessing {
		return repository.Order{}, errors.New("只有处理中状态的订单可以被完成")
	}

	// 检查是否已经全额支付
	if order.PaidAmount < order.FinalAmount {
		return repository.Order{}, fmt.Errorf("订单未全额支付，已支付金额 %.2f，应付金额 %.2f", order.PaidAmount, order.FinalAmount)
	}

	// 更新订单状态为已完成
	now := time.Now()
	order.Status = repository.OrderStatusCompleted
	order.CompletedAt = &now
	order.UpdatedAt = now

	// 更新订单
	updatedOrder, err := s.orderRepo.Update(ctx, order)
	if err != nil {
		return repository.Order{}, fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 订单完成后，更新关联燃油交易的班次信息
	log.Printf("🔄 [ORDER-SERVICE] 开始更新燃油交易班次信息 - OrderID: %d", orderID)
	err = s.updateFuelTransactionShifts(ctx, orderID, now)
	if err != nil {
		// 记录错误但不中断流程，因为订单已经完成
		log.Printf("🚨 [ORDER-SERVICE] 更新燃油交易班次信息失败: %v", err)
	} else {
		log.Printf("✅ [ORDER-SERVICE] 燃油交易班次信息更新成功 - OrderID: %d", orderID)
	}

	return updatedOrder, nil
}

// CancelOrder 取消订单
func (s *OrderServiceImpl) CancelOrder(ctx context.Context, orderID repository.ID) (repository.Order, error) {
	// 获取订单信息，确认订单存在且状态有效
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return repository.Order{}, fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查订单是否可以取消
	if order.Status == repository.OrderStatusCompleted || order.Status == repository.OrderStatusCancelled {
		return repository.Order{}, errors.New("已完成或已取消的订单不能被取消")
	}

	// 检查是否有与燃油交易的关联
	links, err := s.fuelTransactionLinkRepo.ListByOrder(ctx, orderID)
	if err != nil {
		return repository.Order{}, fmt.Errorf("获取燃油交易关联失败: %w", err)
	}

	// 解除所有关联
	for _, link := range links {
		err = s.UnlinkFuelTransactionFromOrder(ctx, link.FuelTransactionID, orderID)
		if err != nil {
			return repository.Order{}, fmt.Errorf("解除燃油交易关联失败: %w", err)
		}
	}

	// 更新订单状态为已取消
	now := time.Now()
	order.Status = repository.OrderStatusCancelled
	order.CancelledAt = &now
	order.UpdatedAt = now

	// 更新订单
	updatedOrder, err := s.orderRepo.Update(ctx, order)
	if err != nil {
		return repository.Order{}, fmt.Errorf("更新订单状态失败: %w", err)
	}

	return updatedOrder, nil
}

// updateFuelTransactionShifts 更新关联燃油交易的班次信息
func (s *OrderServiceImpl) updateFuelTransactionShifts(ctx context.Context, orderID repository.ID, paymentTime time.Time) error {
	log.Printf("🔍 [ORDER-SERVICE] updateFuelTransactionShifts - OrderID: %d, PaymentTime: %v", orderID, paymentTime)
	// 获取订单信息
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return fmt.Errorf("获取订单失败: %w", err)
	}

	// 获取订单关联的燃油交易
	links, err := s.fuelTransactionLinkRepo.ListByOrder(ctx, orderID)
	if err != nil {
		return fmt.Errorf("获取订单关联失败: %w", err)
	}
	log.Printf("🔗 [ORDER-SERVICE] 找到 %d 个燃油交易关联", len(links))

	// 查找当前活跃的班次
	activeShift, err := s.findActiveShift(ctx, order.StationID, paymentTime)
	if err != nil {
		return fmt.Errorf("查找活跃班次失败: %w", err)
	}

	if activeShift == nil {
		return fmt.Errorf("未找到支付时间 %v 对应的活跃班次", paymentTime)
	}
	log.Printf("📋 [ORDER-SERVICE] 找到活跃班次 - ShiftID: %d, StationID: %d", activeShift.ID, activeShift.StationID)

	// 更新每个关联燃油交易的班次信息
	for _, link := range links {
		if link.Status == repository.LinkStatusActive {
			log.Printf("🔄 [ORDER-SERVICE] 更新燃油交易班次 - FuelTransactionID: %d, ShiftID: %d", link.FuelTransactionID, activeShift.ID)
			err = s.updateFuelTransactionShift(ctx, link.FuelTransactionID, activeShift.ID)
			if err != nil {
				log.Printf("🚨 [ORDER-SERVICE] 更新燃油交易 %d 的班次信息失败: %v", link.FuelTransactionID, err)
				// 继续处理其他交易，不中断流程
			} else {
				log.Printf("✅ [ORDER-SERVICE] 燃油交易班次更新成功 - FuelTransactionID: %d", link.FuelTransactionID)
			}
		} else {
			log.Printf("⏭️ [ORDER-SERVICE] 跳过非活跃关联 - FuelTransactionID: %d, Status: %s", link.FuelTransactionID, link.Status)
		}
	}

	return nil
}

// findActiveShift 查找指定时间的活跃班次
func (s *OrderServiceImpl) findActiveShift(ctx context.Context, stationID int64, targetTime time.Time) (*repository.Shift, error) {
	// 查询条件：同一站点，时间在班次范围内
	filter := repository.ShiftFilter{
		StationID: &stationID,
	}

	// 获取所有班次
	shifts, _, err := s.shiftRepo.ListShifts(ctx, &filter, &repository.Pagination{Page: 1, Limit: 100}, &repository.SortOrder{Field: "start_time", Direction: "desc"})
	if err != nil {
		return nil, fmt.Errorf("查询班次失败: %w", err)
	}

	// 查找匹配的班次
	for _, shift := range shifts {
		// 检查时间是否在班次范围内
		if targetTime.After(shift.StartTime) || targetTime.Equal(shift.StartTime) {
			// 如果班次还未结束（end_time为空）或者时间在结束时间之前
			if shift.EndTime == nil || targetTime.Before(*shift.EndTime) || targetTime.Equal(*shift.EndTime) {
				return shift, nil
			}
		}
	}

	return nil, nil // 未找到匹配的班次
}

// updateFuelTransactionShift 更新单个燃油交易的班次信息
func (s *OrderServiceImpl) updateFuelTransactionShift(ctx context.Context, fuelTransactionID repository.ID, shiftID repository.ID) error {
	log.Printf("🔍 [ORDER-SERVICE] updateFuelTransactionShift - FuelTransactionID: %d, ShiftID: %d", fuelTransactionID, shiftID)

	// 获取燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return fmt.Errorf("获取燃油交易失败: %w", err)
	}

	log.Printf("🔍 [ORDER-SERVICE] 更新前燃油交易 - ID: %d, 当前ShiftID: %v", transaction.ID, transaction.ShiftID)

	// 更新班次ID
	transaction.ShiftID = &shiftID
	transaction.UpdatedAt = time.Now()

	log.Printf("🔍 [ORDER-SERVICE] 更新后燃油交易 - ID: %d, 新ShiftID: %v", transaction.ID, transaction.ShiftID)

	// 保存更新
	updatedTransaction, err := s.fuelTransactionRepo.Update(ctx, transaction)
	if err != nil {
		log.Printf("🚨 [ORDER-SERVICE] 更新燃油交易失败: %v", err)
		return fmt.Errorf("更新燃油交易失败: %w", err)
	}

	log.Printf("✅ [ORDER-SERVICE] 燃油交易更新成功 - ID: %d, 返回的ShiftID: %v", updatedTransaction.ID, updatedTransaction.ShiftID)

	return nil
}

// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
func (s *OrderServiceImpl) UnlinkFuelTransactionFromOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	// 获取关联信息
	link, err := s.fuelTransactionLinkRepo.GetByFuelTransactionAndOrder(ctx, fuelTransactionID, orderID)
	if err != nil {
		return fmt.Errorf("获取燃油交易与订单关联失败: %w", err)
	}

	// 设置关联状态为非活动
	now := time.Now()
	link.Status = repository.LinkStatusInactive
	link.DeactivatedAt = &now
	link.UpdatedAt = now

	// 更新关联
	_, err = s.fuelTransactionLinkRepo.Update(ctx, link)
	if err != nil {
		return fmt.Errorf("更新燃油交易与订单关联失败: %w", err)
	}

	return nil
}
