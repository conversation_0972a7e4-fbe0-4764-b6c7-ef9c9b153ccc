package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// FuelTransactionServiceImpl 实现FuelTransactionService接口
type FuelTransactionServiceImpl struct {
	fuelTransactionRepo     repository.FuelTransactionRepository
	orderRepo               repository.OrderRepository
	fuelTransactionLinkRepo repository.FuelTransactionOrderLinkRepository
}

// NewFuelTransactionService 创建一个新的FuelTransactionService实例
func NewFuelTransactionService(
	fuelTransactionRepo repository.FuelTransactionRepository,
	orderRepo repository.OrderRepository,
	fuelTransactionLinkRepo repository.FuelTransactionOrderLinkRepository,
) FuelTransactionService {
	return &FuelTransactionServiceImpl{
		fuelTransactionRepo:     fuelTransactionRepo,
		orderRepo:               orderRepo,
		fuelTransactionLinkRepo: fuelTransactionLinkRepo,
	}
}

// CreateFuelTransaction 创建燃油交易
func (s *FuelTransactionServiceImpl) CreateFuelTransaction(ctx context.Context, transaction repository.FuelTransaction) (repository.FuelTransaction, error) {
	// 设置初始状态
	transaction.Status = repository.FuelTransactionStatusPending

	// 确保TotalVolume和TotalAmount字段有初始值
	// 如果未提供，则默认使用Volume和Amount值
	if transaction.TotalVolume == 0 {
		transaction.TotalVolume = transaction.Volume
	}
	if transaction.TotalAmount == 0 {
		transaction.TotalAmount = transaction.Amount
	}

	// 确保元数据不为空
	if transaction.Metadata == nil {
		transaction.Metadata = make(map[string]interface{})
	}

	// 🔧 简化：如果提供了employee_id但没有staff_card_id，直接将employee_id赋值给staff_card_id
	if transaction.EmployeeID != nil && transaction.StaffCardID == nil {
		transaction.StaffCardID = transaction.EmployeeID
	}

	// 通过存储库创建燃油交易
	return s.fuelTransactionRepo.Create(ctx, transaction)
}



// GetFuelTransaction 获取燃油交易详情
func (s *FuelTransactionServiceImpl) GetFuelTransaction(ctx context.Context, id repository.ID) (repository.FuelTransaction, error) {
	return s.fuelTransactionRepo.Get(ctx, id)
}

// ListFuelTransactions 列出燃油交易列表
func (s *FuelTransactionServiceImpl) ListFuelTransactions(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransaction, int, error) {
	return s.fuelTransactionRepo.List(ctx, filter, pagination, sort)
}

// GetFuelTransactionsFullWithJoins 获取完整燃油交易信息（使用连表查询优化）
func (s *FuelTransactionServiceImpl) GetFuelTransactionsFullWithJoins(ctx context.Context, filter repository.FuelTransactionFilter, pagination repository.Pagination, sort repository.SortOrder) ([]repository.FuelTransactionFull, int, error) {
	// 调用仓储层方法获取完整燃油交易列表
	transactions, total, err := s.fuelTransactionRepo.GetFuelTransactionsFullWithJoins(ctx, filter, pagination, sort)
	if err != nil {
		return nil, 0, fmt.Errorf("获取完整燃油交易列表失败: %w", err)
	}

	// 返回空数组而不是nil，以避免JSON序列化问题
	if transactions == nil {
		transactions = []repository.FuelTransactionFull{}
	}

	return transactions, total, nil
}

// LinkFuelTransactionToOrder 将燃油交易关联到订单
func (s *FuelTransactionServiceImpl) LinkFuelTransactionToOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID, allocatedAmount float64) (repository.FuelTransactionOrderLink, error) {
	// 获取燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 获取订单
	order, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("获取订单失败: %w", err)
	}

	// 检查燃油交易状态
	if transaction.Status != repository.FuelTransactionStatusPending {
		return repository.FuelTransactionOrderLink{}, errors.New("只能关联待处理状态的燃油交易")
	}

	// 检查订单状态
	if order.Status != repository.OrderStatusNew && order.Status != repository.OrderStatusProcessing {
		return repository.FuelTransactionOrderLink{}, errors.New("只能关联到新建或处理中状态的订单")
	}

	// 检查分配金额是否合法
	if allocatedAmount <= 0 {
		return repository.FuelTransactionOrderLink{}, errors.New("分配金额必须大于零")
	}

	// 检查分配金额是否超过燃油交易总金额
	if allocatedAmount > transaction.Amount {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("分配金额 %.2f 超过燃油交易总金额 %.2f", allocatedAmount, transaction.Amount)
	}

	// 🔧 新增：检查燃油交易的总分配状态，防止重复分配
	activeLinks, err := s.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("获取燃油交易活跃链接失败: %w", err)
	}
	
	// 获取预留状态的链接
	reservedLinks, err := s.GetReservedFuelTransactionLinks(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("获取燃油交易预留链接失败: %w", err)
	}
	
	// 计算当前已分配的总金额（只计算ACTIVE状态）
	var currentAllocatedTotal float64 = 0
	for _, activeLink := range activeLinks {
		currentAllocatedTotal += activeLink.AllocatedAmount
	}
	
	// 计算当前预留的总金额（只计算RESERVED状态）
	var currentReservedTotal float64 = 0
	for _, reservedLink := range reservedLinks {
		currentReservedTotal += reservedLink.AllocatedAmount
	}
	
	// 检查加上本次分配后是否会超过燃油交易总金额（包括已分配和预留的）
	const epsilon = 0.01 // 浮点数比较的精度阈值
	if currentAllocatedTotal + currentReservedTotal + allocatedAmount > transaction.Amount + epsilon {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf(
			"分配金额超限：总金额 %.2f，已分配 %.2f，已预留 %.2f，本次分配 %.2f，超出 %.2f", 
			transaction.Amount, currentAllocatedTotal, currentReservedTotal, allocatedAmount, 
			(currentAllocatedTotal + currentReservedTotal + allocatedAmount - transaction.Amount))
	}
	
	// 特别检查：如果已分配+预留金额已经等于或接近总金额，拒绝再次分配
	if math.Abs(currentAllocatedTotal + currentReservedTotal - transaction.Amount) <= epsilon {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf(
			"燃油交易已被完全分配：总金额 %.2f，已分配 %.2f，已预留 %.2f，活跃链接数 %d，预留链接数 %d", 
			transaction.Amount, currentAllocatedTotal, currentReservedTotal, len(activeLinks), len(reservedLinks))
	}

	// 检查是否已存在关联
	existingLink, err := s.fuelTransactionLinkRepo.GetByFuelTransactionAndOrder(ctx, fuelTransactionID, orderID)
	if err == nil {
		// 关联已存在，检查是否为活跃状态
		if existingLink.Status == repository.LinkStatusActive {
			return repository.FuelTransactionOrderLink{}, errors.New("燃油交易与该订单已存在活跃关联")
		}

		// 重新激活关联
		existingLink.Status = repository.LinkStatusActive
		existingLink.AllocatedAmount = allocatedAmount
		existingLink.UpdatedAt = time.Now()
		existingLink.DeactivatedAt = nil

		return s.fuelTransactionLinkRepo.Update(ctx, existingLink)
	}

	// 创建新的关联
	link := repository.FuelTransactionOrderLink{
		FuelTransactionID: fuelTransactionID,
		OrderID:           orderID,
		AllocatedAmount:   allocatedAmount,
		Status:            repository.LinkStatusReserved, // 修改：创建时使用预留状态
		Metadata:          make(map[string]interface{}),
	}

	// 保存关联
	newLink, err := s.fuelTransactionLinkRepo.Create(ctx, link)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("创建关联失败: %w", err)
	}

	// 如果订单状态为新建，更新为处理中
	if order.Status == repository.OrderStatusNew {
		order.Status = repository.OrderStatusProcessing
		order.UpdatedAt = time.Now()

		_, err = s.orderRepo.Update(ctx, order)
		if err != nil {
			return repository.FuelTransactionOrderLink{}, fmt.Errorf("更新订单状态失败: %w", err)
		}
	}

	return newLink, nil
}

// UnlinkFuelTransactionFromOrder 解除燃油交易与订单的关联
func (s *FuelTransactionServiceImpl) UnlinkFuelTransactionFromOrder(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	// 获取关联
	link, err := s.fuelTransactionLinkRepo.GetByFuelTransactionAndOrder(ctx, fuelTransactionID, orderID)
	if err != nil {
		return fmt.Errorf("获取关联失败: %w", err)
	}

	// 检查关联状态
	if link.Status != repository.LinkStatusActive {
		return errors.New("只能解除活跃状态的关联")
	}

	// 更新关联状态为非活跃
	now := time.Now()
	link.Status = repository.LinkStatusInactive
	link.UpdatedAt = now
	link.DeactivatedAt = &now

	_, err = s.fuelTransactionLinkRepo.Update(ctx, link)
	if err != nil {
		return fmt.Errorf("更新关联状态失败: %w", err)
	}

	return nil
}

// GetOrderFuelTransactions 获取订单关联的燃油交易
func (s *FuelTransactionServiceImpl) GetOrderFuelTransactions(ctx context.Context, orderID repository.ID) ([]repository.FuelTransaction, error) {
	// 获取订单
	_, err := s.orderRepo.Get(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	// 获取订单的所有关联
	links, err := s.fuelTransactionLinkRepo.ListByOrder(ctx, orderID)
	if err != nil {
		return nil, fmt.Errorf("获取订单关联失败: %w", err)
	}

	// 如果没有关联，返回空列表
	if len(links) == 0 {
		return []repository.FuelTransaction{}, nil
	}

	// 获取每个关联的燃油交易
	var transactions []repository.FuelTransaction
	for _, link := range links {
		if link.Status == repository.LinkStatusActive {
			transaction, err := s.fuelTransactionRepo.Get(ctx, link.FuelTransactionID)
			if err != nil {
				// 记录错误但继续处理其他关联
				fmt.Printf("获取燃油交易 %d 失败: %v", link.FuelTransactionID, err)
				continue
			}
			transactions = append(transactions, transaction)
		}
	}

	return transactions, nil
}

// GetFuelTransactionOrders 获取燃油交易关联的订单
func (s *FuelTransactionServiceImpl) GetFuelTransactionOrders(ctx context.Context, fuelTransactionID repository.ID) ([]repository.Order, error) {
	// 获取燃油交易
	_, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 获取燃油交易的所有关联
	links, err := s.fuelTransactionLinkRepo.ListByFuelTransaction(ctx, fuelTransactionID)
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易关联失败: %w", err)
	}

	// 如果没有关联，返回空列表
	if len(links) == 0 {
		return []repository.Order{}, nil
	}

	// 获取每个关联的订单
	var orders []repository.Order
	for _, link := range links {
		if link.Status == repository.LinkStatusActive {
			order, err := s.orderRepo.Get(ctx, link.OrderID)
			if err != nil {
				// 记录错误但继续处理其他关联
				fmt.Printf("获取订单 %d 失败: %v", link.OrderID, err)
				continue
			}
			orders = append(orders, order)
		}
	}

	return orders, nil
}

// UpdateLinkAllocatedAmount 更新关联分配金额
func (s *FuelTransactionServiceImpl) UpdateLinkAllocatedAmount(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID, newAllocatedAmount float64) (repository.FuelTransactionOrderLink, error) {
	// 获取燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 获取关联
	link, err := s.fuelTransactionLinkRepo.GetByFuelTransactionAndOrder(ctx, fuelTransactionID, orderID)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("获取关联失败: %w", err)
	}

	// 检查关联状态
	if link.Status != repository.LinkStatusActive {
		return repository.FuelTransactionOrderLink{}, errors.New("只能更新活跃状态的关联")
	}

	// 检查新的分配金额是否合法
	if newAllocatedAmount <= 0 {
		return repository.FuelTransactionOrderLink{}, errors.New("分配金额必须大于零")
	}

	// 检查新的分配金额是否超过燃油交易总金额
	if newAllocatedAmount > transaction.Amount {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("分配金额 %.2f 超过燃油交易总金额 %.2f", newAllocatedAmount, transaction.Amount)
	}

	// 更新分配金额
	link.AllocatedAmount = newAllocatedAmount
	link.UpdatedAt = time.Now()

	// 保存更新
	updatedLink, err := s.fuelTransactionLinkRepo.Update(ctx, link)
	if err != nil {
		return repository.FuelTransactionOrderLink{}, fmt.Errorf("更新关联失败: %w", err)
	}

	return updatedLink, nil
}

// GetActiveFuelTransactionLinks 获取燃油交易关联的所有活跃订单链接
func (s *FuelTransactionServiceImpl) GetActiveFuelTransactionLinks(ctx context.Context, fuelTransactionID repository.ID) ([]repository.FuelTransactionOrderLink, error) {
	// 验证燃油交易存在
	_, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 获取燃油交易的所有关联
	links, err := s.fuelTransactionLinkRepo.ListByFuelTransaction(ctx, fuelTransactionID)
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易关联失败: %w", err)
	}

	// 如果没有关联，返回空列表
	if len(links) == 0 {
		return []repository.FuelTransactionOrderLink{}, nil
	}

	// 筛选出活跃状态的关联
	var activeLinks []repository.FuelTransactionOrderLink
	for _, link := range links {
		if link.Status == repository.LinkStatusActive {
			activeLinks = append(activeLinks, link)
		}
	}

	return activeLinks, nil
}

// GetReservedFuelTransactionLinks 获取燃油交易关联的所有预留订单链接
func (s *FuelTransactionServiceImpl) GetReservedFuelTransactionLinks(ctx context.Context, fuelTransactionID repository.ID) ([]repository.FuelTransactionOrderLink, error) {
	// 验证燃油交易存在
	_, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 获取燃油交易的所有关联
	links, err := s.fuelTransactionLinkRepo.ListByFuelTransaction(ctx, fuelTransactionID)
	if err != nil {
		return nil, fmt.Errorf("获取燃油交易关联失败: %w", err)
	}

	// 如果没有关联，返回空列表
	if len(links) == 0 {
		return []repository.FuelTransactionOrderLink{}, nil
	}

	// 筛选出预留状态的关联
	var reservedLinks []repository.FuelTransactionOrderLink
	for _, link := range links {
		if link.Status == repository.LinkStatusReserved {
			reservedLinks = append(reservedLinks, link)
		}
	}

	return reservedLinks, nil
}

// ConfirmFuelTransactionLink 确认燃油交易链接（将RESERVED状态更新为ACTIVE）
func (s *FuelTransactionServiceImpl) ConfirmFuelTransactionLink(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	// 获取关联
	link, err := s.fuelTransactionLinkRepo.GetByFuelTransactionAndOrder(ctx, fuelTransactionID, orderID)
	if err != nil {
		return fmt.Errorf("获取关联失败: %w", err)
	}

	// 检查关联状态
	if link.Status != repository.LinkStatusReserved {
		return errors.New("只能确认预留状态的关联")
	}

	// 更新关联状态为活跃
	link.Status = repository.LinkStatusActive
	link.UpdatedAt = time.Now()

	_, err = s.fuelTransactionLinkRepo.Update(ctx, link)
	if err != nil {
		return fmt.Errorf("更新关联状态失败: %w", err)
	}

	// 检查并更新燃油交易状态
	err = s.checkAndUpdateFuelTransactionStatus(ctx, fuelTransactionID)
	if err != nil {
		return fmt.Errorf("更新燃油交易状态失败: %w", err)
	}

	return nil
}

// CancelFuelTransactionLink 取消燃油交易链接（将RESERVED状态更新为INACTIVE）
func (s *FuelTransactionServiceImpl) CancelFuelTransactionLink(ctx context.Context, fuelTransactionID repository.ID, orderID repository.ID) error {
	// 获取现有的链接
	link, err := s.fuelTransactionLinkRepo.GetByFuelTransactionAndOrder(ctx, fuelTransactionID, orderID)
	if err != nil {
		return fmt.Errorf("获取燃油交易链接失败: %w", err)
	}

	// 检查链接状态
	if link.Status != repository.LinkStatusReserved {
		return fmt.Errorf("只能取消预留状态的链接，当前状态: %s", link.Status)
	}

	// 更新链接状态为非活跃
	now := time.Now()
	link.Status = repository.LinkStatusInactive
	link.UpdatedAt = now
	link.DeactivatedAt = &now

	_, err = s.fuelTransactionLinkRepo.Update(ctx, link)
	if err != nil {
		return fmt.Errorf("更新链接状态失败: %w", err)
	}

	return nil
}

// checkAndUpdateFuelTransactionStatus 检查并更新燃油交易状态
func (s *FuelTransactionServiceImpl) checkAndUpdateFuelTransactionStatus(ctx context.Context, fuelTransactionID repository.ID) error {
	// 获取燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 如果已经是processed状态，跳过
	if transaction.Status == repository.FuelTransactionStatusProcessed {
		return nil
	}

	// 获取活跃链接并计算已分配金额
	activeLinks, err := s.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
	if err != nil {
		return fmt.Errorf("获取活跃链接失败: %w", err)
	}

	var totalAllocated float64 = 0
	for _, link := range activeLinks {
		totalAllocated += link.AllocatedAmount
	}

	// 检查是否完全分配
	const epsilon = 0.01
	if math.Abs(totalAllocated - transaction.Amount) <= epsilon {
		// 更新燃油交易状态为已处理
		now := time.Now()
		transaction.Status = repository.FuelTransactionStatusProcessed
		transaction.UpdatedAt = now
		transaction.ProcessedAt = &now

		_, err = s.fuelTransactionRepo.Update(ctx, transaction)
		if err != nil {
			return fmt.Errorf("更新燃油交易状态失败: %w", err)
		}
	}

	return nil
}

// ProcessFuelTransaction 将燃油交易状态更新为已处理状态
func (s *FuelTransactionServiceImpl) ProcessFuelTransaction(ctx context.Context, fuelTransactionID repository.ID) (repository.FuelTransaction, error) {
	// 获取燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransaction{}, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 检查燃油交易状态
	if transaction.Status != repository.FuelTransactionStatusPending {
		return repository.FuelTransaction{}, errors.New("只有待处理状态的燃油交易可以被标记为已处理")
	}

	// 更新燃油交易状态
	now := time.Now()
	transaction.Status = repository.FuelTransactionStatusProcessed
	transaction.UpdatedAt = now
	transaction.ProcessedAt = &now

	// 保存更新
	updatedTransaction, err := s.fuelTransactionRepo.Update(ctx, transaction)
	if err != nil {
		return repository.FuelTransaction{}, fmt.Errorf("更新燃油交易状态失败: %w", err)
	}

	return updatedTransaction, nil
}

// CleanupFuelTransactionLinks 清理燃油交易链接
func (s *FuelTransactionServiceImpl) CleanupFuelTransactionLinks(ctx context.Context, fuelTransactionID repository.ID) error {
	// 获取所有非活跃的关联
	inactiveStatus := repository.LinkStatusInactive
	filter := repository.FuelTransactionOrderLinkFilter{
		FuelTransactionID: &fuelTransactionID,
		Status:            &inactiveStatus,
	}
	
	links, _, err := s.fuelTransactionLinkRepo.List(ctx, filter, repository.Pagination{Page: 1, Limit: 1000}, repository.SortOrder{})
	if err != nil {
		return fmt.Errorf("获取非活跃关联失败: %w", err)
	}
	
	// 将非活跃的关联标记为删除状态（软删除）
	for _, link := range links {
		link.Status = repository.LinkStatusInactive
		now := time.Now()
		link.UpdatedAt = now
		link.DeactivatedAt = &now
		
		_, err = s.fuelTransactionLinkRepo.Update(ctx, link)
		if err != nil {
			return fmt.Errorf("更新关联状态失败: %w", err)
		}
	}
	
	return nil
}

// ProcessFuelTransactionStatus 处理燃油交易状态
func (s *FuelTransactionServiceImpl) ProcessFuelTransactionStatus(ctx context.Context, fuelTransactionID repository.ID, force bool) error {
	// 获取燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return fmt.Errorf("获取燃油交易失败: %w", err)
	}
	
	// 如果已经是处理状态，直接返回
	if transaction.Status == repository.FuelTransactionStatusProcessed {
		return nil
	}
	
	// 如果不是强制模式，检查金额是否匹配
	if !force {
		// 获取所有活跃的关联
		links, err := s.GetActiveFuelTransactionLinks(ctx, fuelTransactionID)
		if err != nil {
			return fmt.Errorf("获取活跃关联失败: %w", err)
		}
		
		// 计算总分配金额
		totalAllocated := 0.0
		for _, link := range links {
			totalAllocated += link.AllocatedAmount
		}
		
		// 检查分配金额是否匹配交易金额
		if math.Abs(totalAllocated-transaction.Amount) > 0.01 {
			return fmt.Errorf("分配金额(%.2f)与交易金额(%.2f)不匹配", totalAllocated, transaction.Amount)
		}
	}
	
	// 更新状态为已处理
	transaction.Status = repository.FuelTransactionStatusProcessed
	now := time.Now()
	transaction.ProcessedAt = &now
	transaction.UpdatedAt = now
	
	_, err = s.fuelTransactionRepo.Update(ctx, transaction)
	if err != nil {
		return fmt.Errorf("更新燃油交易状态失败: %w", err)
	}

	return nil
}

// UpdatePumpReadings 更新燃油交易的泵码数
func (s *FuelTransactionServiceImpl) UpdatePumpReadings(ctx context.Context, fuelTransactionID repository.ID, startTotalizer *float64, endTotalizer *float64) (repository.FuelTransaction, error) {
	// 参数验证
	if startTotalizer == nil && endTotalizer == nil {
		return repository.FuelTransaction{}, fmt.Errorf("至少需要提供一个泵码数字段")
	}

	// 验证泵码数的合理性
	if startTotalizer != nil && *startTotalizer < 0 {
		return repository.FuelTransaction{}, fmt.Errorf("起始泵码数不能为负数")
	}
	if endTotalizer != nil && *endTotalizer < 0 {
		return repository.FuelTransaction{}, fmt.Errorf("截止泵码数不能为负数")
	}
	if startTotalizer != nil && endTotalizer != nil && *endTotalizer < *startTotalizer {
		return repository.FuelTransaction{}, fmt.Errorf("截止泵码数不能小于起始泵码数")
	}

	// 获取现有的燃油交易
	transaction, err := s.fuelTransactionRepo.Get(ctx, fuelTransactionID)
	if err != nil {
		return repository.FuelTransaction{}, fmt.Errorf("获取燃油交易失败: %w", err)
	}

	// 检查交易状态 - 只允许特定状态下修改
	if transaction.Status != repository.FuelTransactionStatusPending &&
	   transaction.Status != repository.FuelTransactionStatusProcessed {
		return repository.FuelTransaction{}, fmt.Errorf("只能修改待处理或已处理状态的交易泵码数")
	}

	// 更新泵码数字段
	if startTotalizer != nil {
		transaction.StartTotalizer = startTotalizer
	}
	if endTotalizer != nil {
		transaction.EndTotalizer = endTotalizer
	}

	// 更新时间戳
	transaction.UpdatedAt = time.Now()

	// 保存更新
	updatedTransaction, err := s.fuelTransactionRepo.Update(ctx, transaction)
	if err != nil {
		return repository.FuelTransaction{}, fmt.Errorf("更新燃油交易失败: %w", err)
	}

	return updatedTransaction, nil
}
