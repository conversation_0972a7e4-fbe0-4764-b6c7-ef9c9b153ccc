package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab4.weicheche.cn/indo-bp/order-service/pkg/repository"
)

// ReportServiceImpl 实现ReportService接口
type ReportServiceImpl struct {
	reportRepo   repository.ReportRepository
	shiftEODRepo repository.ShiftEODRepository
}

// NewReportService 创建一个新的ReportService实例
func NewReportService(reportRepo repository.ReportRepository, shiftEODRepo repository.ShiftEODRepository) ReportService {
	return &ReportServiceImpl{
		reportRepo:   reportRepo,
		shiftEODRepo: shiftEODRepo,
	}
}

// GetPaymentMethodSummary 获取按支付方式的销售汇总
func (s *ReportServiceImpl) GetPaymentMethodSummary(ctx context.Context, filter repository.ReportFilter) ([]repository.PaymentMethodSummary, error) {
	// 参数验证
	if filter.DateFrom != nil && filter.DateTo != nil && filter.DateFrom.After(*filter.DateTo) {
		return nil, fmt.Errorf("开始日期不能晚于结束日期")
	}

	// 调用存储库获取汇总数据
	summaries, err := s.reportRepo.GetPaymentMethodSummary(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("获取支付方式汇总数据失败: %w", err)
	}

	return summaries, nil
}

// GetAggregatedRevenue 获取聚合收入报表
func (s *ReportServiceImpl) GetAggregatedRevenue(ctx context.Context, filter repository.AggregationFilter) ([]repository.AggregatedRevenueDTO, error) {
	// 参数验证
	if err := validateDateRange(filter.StartDate, filter.EndDate); err != nil {
		return nil, err
	}

	// 调用存储库获取聚合收入数据
	revenues, err := s.reportRepo.GetAggregatedRevenue(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("获取聚合收入数据失败: %w", err)
	}

	return revenues, nil
}

// GetAggregatedReceivable 获取油品应收汇总
func (s *ReportServiceImpl) GetAggregatedReceivable(ctx context.Context, filter repository.AggregationFilter) ([]repository.AggregatedReceivableDTO, error) {
	// 参数验证
	if err := validateDateRange(filter.StartDate, filter.EndDate); err != nil {
		return nil, err
	}

	// 调用存储库获取油品应收汇总数据
	receivables, err := s.reportRepo.GetAggregatedReceivable(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("获取油品应收汇总数据失败: %w", err)
	}

	return receivables, nil
}

// GetNozzleSalesSummary 获取油枪销售汇总
func (s *ReportServiceImpl) GetNozzleSalesSummary(ctx context.Context, filter repository.AggregationFilter) ([]repository.NozzleSalesSummaryDTO, error) {
	// 参数验证
	if err := validateDateRange(filter.StartDate, filter.EndDate); err != nil {
		return nil, err
	}

	// 调用存储库获取油枪销售汇总数据
	summaries, err := s.reportRepo.GetNozzleSalesSummary(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("获取油枪销售汇总数据失败: %w", err)
	}

	return summaries, nil
}

// GetTransactionsForReport 获取交易明细数据(用于报表)
func (s *ReportServiceImpl) GetTransactionsForReport(ctx context.Context, filter repository.TransactionFilter) ([]repository.TransactionDTO, error) {
	// 参数验证
	if err := validateDateRange(filter.StartDate, filter.EndDate); err != nil {
		return nil, err
	}

	// 调用存储库获取交易明细数据
	transactions, err := s.reportRepo.GetTransactionsForReport(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("获取交易明细数据失败: %w", err)
	}

	return transactions, nil
}

// GetAggregatedSalesByProductCategory 按商品分类聚合销售额
func (s *ReportServiceImpl) GetAggregatedSalesByProductCategory(ctx context.Context, filter repository.AggregationFilter) ([]repository.AggregatedSalesDTO, error) {
	// 参数验证
	if err := validateDateRange(filter.StartDate, filter.EndDate); err != nil {
		return nil, err
	}

	// 调用存储库获取按商品分类的销售汇总
	sales, err := s.reportRepo.GetAggregatedSalesByProductCategory(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("获取按商品分类的销售汇总失败: %w", err)
	}

	return sales, nil
}

// GetShiftEODReport 获取班次日终报表
func (s *ReportServiceImpl) GetShiftEODReport(ctx context.Context, stationID repository.ID, date string) (*repository.ShiftEODReportResponse, error) {
	// 1. 参数验证
	if err := s.validateGetShiftEODReportParams(stationID, date); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 2. 调用repository获取数据
	report, err := s.shiftEODRepo.GetShiftEODReport(ctx, stationID, date)
	if err != nil {
		return nil, fmt.Errorf("获取班次日终报表失败: %w", err)
	}

	// 3. 业务逻辑处理
	s.processReportData(report)

	return report, nil
}

// validateGetShiftEODReportParams 验证获取班次日终报表的参数
func (s *ReportServiceImpl) validateGetShiftEODReportParams(stationID repository.ID, date string) error {
	// 验证站点ID
	if uuid.UUID(stationID) == uuid.Nil {
		return fmt.Errorf("无效的站点ID: %s，不能为空", stationID.String())
	}

	// 验证日期格式
	if date == "" {
		return fmt.Errorf("日期不能为空")
	}

	// 尝试解析日期格式 YYYY-MM-DD
	_, err := time.Parse("2006-01-02", date)
	if err != nil {
		return fmt.Errorf("无效的日期格式: %s，期望格式为YYYY-MM-DD", date)
	}

	// 验证日期不能是未来日期
	parsedDate, _ := time.Parse("2006-01-02", date)
	today := time.Now().Truncate(24 * time.Hour)
	if parsedDate.After(today) {
		return fmt.Errorf("日期不能是未来日期: %s", date)
	}

	return nil
}

// processReportData 处理报表数据（业务逻辑）
func (s *ReportServiceImpl) processReportData(report *repository.ShiftEODReportResponse) {
	// 1. 确保所有金额字段精度为2位小数
	s.roundAmountFields(report)

	// 2. 确保所有体积字段精度为3位小数
	s.roundVolumeFields(report)

	// 3. 添加业务规则验证标记
	s.addValidationFlags(report)
}

// roundAmountFields 四舍五入金额字段到2位小数
func (s *ReportServiceImpl) roundAmountFields(report *repository.ShiftEODReportResponse) {
	for i := range report.Shifts {
		shift := &report.Shifts[i]

		// 处理员工级别的数据
		for j := range shift.Employees {
			employee := &shift.Employees[j]

			// 员工油品销售金额
			for k := range employee.FuelSales.ByGrade {
				grade := &employee.FuelSales.ByGrade[k]
				grade.UnitPrice = roundToDecimal(grade.UnitPrice, 2)
				grade.GrossAmount = roundToDecimal(grade.GrossAmount, 2)
				grade.NetAmount = roundToDecimal(grade.NetAmount, 2)
				grade.DiscountAmount = roundToDecimal(grade.DiscountAmount, 2)
			}

			// 员工油品销售汇总
			employee.FuelSales.TotalFuelSales.TotalGrossAmount = roundToDecimal(employee.FuelSales.TotalFuelSales.TotalGrossAmount, 2)
			employee.FuelSales.TotalFuelSales.TotalNetAmount = roundToDecimal(employee.FuelSales.TotalFuelSales.TotalNetAmount, 2)
			employee.FuelSales.TotalFuelSales.TotalDiscountAmount = roundToDecimal(employee.FuelSales.TotalFuelSales.TotalDiscountAmount, 2)

			// 员工其他收入
			for k := range employee.OtherIncome.Items {
				item := &employee.OtherIncome.Items[k]
				item.UnitPrice = roundToDecimal(item.UnitPrice, 2)
				item.TotalAmount = roundToDecimal(item.TotalAmount, 2)
			}
			employee.OtherIncome.TotalOtherIncome = roundToDecimal(employee.OtherIncome.TotalOtherIncome, 2)

			// 员工支付汇总
			for k := range employee.PaymentSummary.ByMethod {
				method := &employee.PaymentSummary.ByMethod[k]
				method.TotalAmount = roundToDecimal(method.TotalAmount, 2)
				method.Percentage = roundToDecimal(method.Percentage, 2)
			}
			employee.PaymentSummary.TotalPayment = roundToDecimal(employee.PaymentSummary.TotalPayment, 2)

			// 员工汇总
			employee.EmployeeSummary.TotalSales = roundToDecimal(employee.EmployeeSummary.TotalSales, 2)
			employee.EmployeeSummary.TotalOtherIncome = roundToDecimal(employee.EmployeeSummary.TotalOtherIncome, 2)
			employee.EmployeeSummary.GrandTotal = roundToDecimal(employee.EmployeeSummary.GrandTotal, 2)
			employee.EmployeeSummary.OverCash = roundToDecimal(employee.EmployeeSummary.OverCash, 2)
		}

		// 班次级别汇总
		shift.ShiftSummary.TotalFuelSales.TotalGrossAmount = roundToDecimal(shift.ShiftSummary.TotalFuelSales.TotalGrossAmount, 2)
		shift.ShiftSummary.TotalFuelSales.TotalNetAmount = roundToDecimal(shift.ShiftSummary.TotalFuelSales.TotalNetAmount, 2)
		shift.ShiftSummary.TotalFuelSales.TotalDiscountAmount = roundToDecimal(shift.ShiftSummary.TotalFuelSales.TotalDiscountAmount, 2)
		shift.ShiftSummary.TotalOtherIncome = roundToDecimal(shift.ShiftSummary.TotalOtherIncome, 2)
		shift.ShiftSummary.TotalPayment = roundToDecimal(shift.ShiftSummary.TotalPayment, 2)
		shift.ShiftSummary.GrandTotal = roundToDecimal(shift.ShiftSummary.GrandTotal, 2)
		shift.ShiftSummary.OverCash = roundToDecimal(shift.ShiftSummary.OverCash, 2)
	}

	// 日汇总
	report.DailySummary.TotalGrossSales = roundToDecimal(report.DailySummary.TotalGrossSales, 2)
	report.DailySummary.TotalNetSales = roundToDecimal(report.DailySummary.TotalNetSales, 2)
	report.DailySummary.TotalDiscount = roundToDecimal(report.DailySummary.TotalDiscount, 2)
}

// roundVolumeFields 四舍五入体积字段到3位小数
func (s *ReportServiceImpl) roundVolumeFields(report *repository.ShiftEODReportResponse) {
	for i := range report.Shifts {
		shift := &report.Shifts[i]

		// 处理员工级别的数据
		for j := range shift.Employees {
			employee := &shift.Employees[j]

			// 员工油品销售体积
			for k := range employee.FuelSales.ByGrade {
				grade := &employee.FuelSales.ByGrade[k]
				grade.SalesVolume = roundToDecimal(grade.SalesVolume, 3)
				grade.FreeLiters = roundToDecimal(grade.FreeLiters, 3)
				grade.NetSalesVolume = roundToDecimal(grade.NetSalesVolume, 3)
			}

			// 员工油品销售汇总体积
			employee.FuelSales.TotalFuelSales.TotalVolume = roundToDecimal(employee.FuelSales.TotalFuelSales.TotalVolume, 3)

			// 员工其他收入数量
			for k := range employee.OtherIncome.Items {
				item := &employee.OtherIncome.Items[k]
				item.Quantity = roundToDecimal(item.Quantity, 3)
			}
		}

		// 班次级别汇总体积
		shift.ShiftSummary.TotalFuelSales.TotalVolume = roundToDecimal(shift.ShiftSummary.TotalFuelSales.TotalVolume, 3)
	}

	// 日汇总体积
	report.DailySummary.TotalFuelVolume = roundToDecimal(report.DailySummary.TotalFuelVolume, 3)
}

// addValidationFlags 添加验证标记
func (s *ReportServiceImpl) addValidationFlags(report *repository.ShiftEODReportResponse) {
	for i := range report.Shifts {
		shift := &report.Shifts[i]

		// 检查员工级别的数据一致性
		for j := range shift.Employees {
			employee := &shift.Employees[j]

			// 检查员工金额平衡
			expectedTotal := employee.EmployeeSummary.TotalSales + employee.EmployeeSummary.TotalOtherIncome
			actualTotal := employee.PaymentSummary.TotalPayment

			if abs(expectedTotal-actualTotal) > 0.01 { // 允许1分钱的误差
				shift.ShiftSummary.Comments += fmt.Sprintf("Warning: Employee %s payment amount mismatch detected. ", employee.EmployeeInfo.EmployeeName)
			}

			// 检查员工净销售量逻辑
			for _, grade := range employee.FuelSales.ByGrade {
				if grade.NetSalesVolume > grade.SalesVolume {
					shift.ShiftSummary.Comments += fmt.Sprintf("Warning: Employee %s net sales volume exceeds gross sales volume. ", employee.EmployeeInfo.EmployeeName)
					break
				}
			}
		}

		// 检查班次级别金额平衡
		expectedShiftTotal := shift.ShiftSummary.TotalFuelSales.TotalNetAmount + shift.ShiftSummary.TotalOtherIncome
		actualShiftTotal := shift.ShiftSummary.TotalPayment

		if abs(expectedShiftTotal-actualShiftTotal) > 0.01 { // 允许1分钱的误差
			shift.ShiftSummary.Comments += "Warning: Shift payment amount mismatch detected. "
		}

		// 验证员工数据汇总 = 班次汇总
		var employeeTotalSales, employeeTotalOtherIncome, employeeTotalPayment float64
		var employeeTotalTransactions int
		for _, employee := range shift.Employees {
			employeeTotalSales += employee.EmployeeSummary.TotalSales
			employeeTotalOtherIncome += employee.EmployeeSummary.TotalOtherIncome
			employeeTotalPayment += employee.PaymentSummary.TotalPayment
			employeeTotalTransactions += employee.EmployeeSummary.TotalTransactions
		}

		// 检查数据一致性
		if abs(employeeTotalSales-shift.ShiftSummary.TotalFuelSales.TotalNetAmount) > 0.01 {
			shift.ShiftSummary.Comments += "Warning: Employee sales total does not match shift total. "
		}
		if abs(employeeTotalOtherIncome-shift.ShiftSummary.TotalOtherIncome) > 0.01 {
			shift.ShiftSummary.Comments += "Warning: Employee other income total does not match shift total. "
		}
		if abs(employeeTotalPayment-shift.ShiftSummary.TotalPayment) > 0.01 {
			shift.ShiftSummary.Comments += "Warning: Employee payment total does not match shift total. "
		}
		if employeeTotalTransactions != shift.ShiftSummary.TotalTransactions {
			shift.ShiftSummary.Comments += "Warning: Employee transaction count does not match shift total. "
		}
	}
}

// roundToDecimal 四舍五入到指定小数位
func roundToDecimal(value float64, decimals int) float64 {
	multiplier := 1.0
	for i := 0; i < decimals; i++ {
		multiplier *= 10
	}
	return float64(int(value*multiplier+0.5)) / multiplier
}

// abs 返回浮点数的绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// validateDateRange 验证日期范围是否有效
func validateDateRange(startDateStr, endDateStr string) error {
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return fmt.Errorf("无效的开始日期格式: %w", err)
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return fmt.Errorf("无效的结束日期格式: %w", err)
	}

	if startDate.After(endDate) {
		return fmt.Errorf("开始日期不能晚于结束日期")
	}

	return nil
}
