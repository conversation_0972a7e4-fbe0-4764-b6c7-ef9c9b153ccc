-- 数据库迁移脚本：将 order_schema 下所有表的 id 字段从 BIGINT 改为 UUID
-- 创建时间: 2025-01-20
-- 说明: 此脚本将所有主键和外键字段从整数类型改为UUID类型

-- 设置 schema 和启用 UUID 扩展
SET search_path TO order_schema, public, pg_catalog;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 开始事务
BEGIN;

-- ==========================================
-- 第一阶段：添加新的 UUID 字段
-- ==========================================

-- 1. 为所有表添加新的 UUID 主键字段
ALTER TABLE employees ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shifts ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE staff_cards ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_templates ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE orders ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE fuel_transactions ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE order_items ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE order_payments ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE order_promotions ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE fuel_transaction_order_links ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_fuel_details ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_merchandise_details ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_payment_details ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE employee_shifts ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE migration_log ADD COLUMN id_new UUID DEFAULT gen_random_uuid();

-- 2. 为所有外键字段添加新的 UUID 字段
-- employees 相关
ALTER TABLE employee_shifts ADD COLUMN employee_id_new UUID;

-- shifts 相关
ALTER TABLE employee_shifts ADD COLUMN shift_id_new UUID;
ALTER TABLE shift_fuel_details ADD COLUMN shift_id_new UUID;
ALTER TABLE shift_merchandise_details ADD COLUMN shift_id_new UUID;
ALTER TABLE shift_payment_details ADD COLUMN shift_id_new UUID;
ALTER TABLE fuel_transactions ADD COLUMN shift_id_new UUID;

-- staff_cards 相关
ALTER TABLE orders ADD COLUMN staff_card_id_new UUID;
ALTER TABLE fuel_transactions ADD COLUMN staff_card_id_new UUID;
ALTER TABLE shift_fuel_details ADD COLUMN staff_card_id_new UUID;
ALTER TABLE shift_merchandise_details ADD COLUMN staff_card_id_new UUID;
ALTER TABLE shift_payment_details ADD COLUMN staff_card_id_new UUID;

-- orders 相关
ALTER TABLE order_items ADD COLUMN order_id_new UUID;
ALTER TABLE order_payments ADD COLUMN order_id_new UUID;
ALTER TABLE order_promotions ADD COLUMN order_id_new UUID;
ALTER TABLE fuel_transaction_order_links ADD COLUMN order_id_new UUID;

-- fuel_transactions 相关
ALTER TABLE fuel_transaction_order_links ADD COLUMN fuel_transaction_id_new UUID;
ALTER TABLE fuel_transactions_summary ADD COLUMN fuel_transaction_id_new UUID;

-- ==========================================
-- 第二阶段：填充新的 UUID 字段
-- ==========================================

-- 为现有记录生成 UUID 值
UPDATE employees SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shifts SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE staff_cards SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_templates SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE orders SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE fuel_transactions SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE order_items SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE order_payments SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE order_promotions SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE fuel_transaction_order_links SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_fuel_details SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_merchandise_details SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_payment_details SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE employee_shifts SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE migration_log SET id_new = gen_random_uuid() WHERE id_new IS NULL;

-- ==========================================
-- 第三阶段：建立 UUID 外键关系映射
-- ==========================================

-- 更新外键字段的 UUID 值（基于原有的整数关系）
-- employees 相关
UPDATE employee_shifts 
SET employee_id_new = e.id_new 
FROM employees e 
WHERE employee_shifts.employee_id = e.id;

-- shifts 相关
UPDATE employee_shifts 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE employee_shifts.shift_id = s.id;

UPDATE shift_fuel_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_fuel_details.shift_id = s.id;

UPDATE shift_merchandise_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_merchandise_details.shift_id = s.id;

UPDATE shift_payment_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_payment_details.shift_id = s.id;

UPDATE fuel_transactions 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE fuel_transactions.shift_id = s.id;

-- staff_cards 相关
UPDATE orders 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE orders.staff_card_id = sc.id;

UPDATE fuel_transactions 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE fuel_transactions.staff_card_id = sc.id;

UPDATE shift_fuel_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_fuel_details.staff_card_id = sc.id;

UPDATE shift_merchandise_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_merchandise_details.staff_card_id = sc.id;

UPDATE shift_payment_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_payment_details.staff_card_id = sc.id;

-- orders 相关
UPDATE order_items 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_items.order_id = o.id;

UPDATE order_payments 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_payments.order_id = o.id;

UPDATE order_promotions 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_promotions.order_id = o.id;

UPDATE fuel_transaction_order_links 
SET order_id_new = o.id_new 
FROM orders o 
WHERE fuel_transaction_order_links.order_id = o.id;

-- fuel_transactions 相关
UPDATE fuel_transaction_order_links 
SET fuel_transaction_id_new = ft.id_new 
FROM fuel_transactions ft 
WHERE fuel_transaction_order_links.fuel_transaction_id = ft.id;

UPDATE fuel_transactions_summary 
SET fuel_transaction_id_new = ft.id_new 
FROM fuel_transactions ft 
WHERE fuel_transactions_summary.fuel_transaction_id = ft.id;

-- ==========================================
-- 第四阶段：删除旧的外键约束
-- ==========================================

-- 删除现有的外键约束
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_employee_id_fkey;
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_shift_id_fkey;
ALTER TABLE shift_fuel_details DROP CONSTRAINT IF EXISTS shift_fuel_details_shift_id_fkey;
ALTER TABLE shift_merchandise_details DROP CONSTRAINT IF EXISTS shift_merchandise_details_shift_id_fkey;
ALTER TABLE shift_payment_details DROP CONSTRAINT IF EXISTS shift_payment_details_shift_id_fkey;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_shift_id_fkey;
-- 删除 fuel_transactions_summary 表的外键约束
ALTER TABLE fuel_transactions_summary DROP CONSTRAINT IF EXISTS fk_fuel_transactions_summary_transaction;

-- ==========================================
-- 第五阶段：删除旧字段，重命名新字段
-- ==========================================

-- 删除旧的主键约束
ALTER TABLE employees DROP CONSTRAINT IF EXISTS employees_pkey;
ALTER TABLE shifts DROP CONSTRAINT IF EXISTS shifts_pkey;
ALTER TABLE staff_cards DROP CONSTRAINT IF EXISTS staff_cards_pkey;
ALTER TABLE shift_templates DROP CONSTRAINT IF EXISTS shift_templates_pkey;
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_pkey;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_pkey;
ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_pkey;
ALTER TABLE order_payments DROP CONSTRAINT IF EXISTS order_payments_pkey;
ALTER TABLE order_promotions DROP CONSTRAINT IF EXISTS order_promotions_pkey;
ALTER TABLE fuel_transaction_order_links DROP CONSTRAINT IF EXISTS fuel_transaction_order_links_pkey;
ALTER TABLE shift_fuel_details DROP CONSTRAINT IF EXISTS shift_fuel_details_pkey;
ALTER TABLE shift_merchandise_details DROP CONSTRAINT IF EXISTS shift_merchandise_details_pkey;
ALTER TABLE shift_payment_details DROP CONSTRAINT IF EXISTS shift_payment_details_pkey;
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_pkey;
ALTER TABLE migration_log DROP CONSTRAINT IF EXISTS migration_log_pkey;

-- 删除旧的唯一约束
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_order_number_key;
ALTER TABLE shifts DROP CONSTRAINT IF EXISTS shifts_shift_number_key;
ALTER TABLE staff_cards DROP CONSTRAINT IF EXISTS staff_cards_card_number_key;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_transaction_number_key;
ALTER TABLE fuel_transaction_order_links DROP CONSTRAINT IF EXISTS unique_fuel_transaction_order;

-- 删除旧字段（使用 CASCADE 删除依赖的视图）
ALTER TABLE employees DROP COLUMN id CASCADE;
ALTER TABLE shifts DROP COLUMN id CASCADE;
ALTER TABLE staff_cards DROP COLUMN id CASCADE;
ALTER TABLE shift_templates DROP COLUMN id CASCADE;
ALTER TABLE orders DROP COLUMN id CASCADE;
ALTER TABLE fuel_transactions DROP COLUMN id CASCADE;
ALTER TABLE order_items DROP COLUMN id CASCADE;
ALTER TABLE order_payments DROP COLUMN id CASCADE;
ALTER TABLE order_promotions DROP COLUMN id CASCADE;
ALTER TABLE fuel_transaction_order_links DROP COLUMN id CASCADE;
ALTER TABLE shift_fuel_details DROP COLUMN id CASCADE;
ALTER TABLE shift_merchandise_details DROP COLUMN id CASCADE;
ALTER TABLE shift_payment_details DROP COLUMN id CASCADE;
ALTER TABLE employee_shifts DROP COLUMN id CASCADE;
ALTER TABLE migration_log DROP COLUMN id CASCADE;

-- 删除旧的外键字段（使用 CASCADE 删除依赖的视图）
ALTER TABLE employee_shifts DROP COLUMN employee_id CASCADE;
ALTER TABLE employee_shifts DROP COLUMN shift_id CASCADE;
ALTER TABLE shift_fuel_details DROP COLUMN shift_id CASCADE;
ALTER TABLE shift_merchandise_details DROP COLUMN shift_id CASCADE;
ALTER TABLE shift_payment_details DROP COLUMN shift_id CASCADE;
ALTER TABLE fuel_transactions DROP COLUMN shift_id CASCADE;
ALTER TABLE orders DROP COLUMN staff_card_id CASCADE;
ALTER TABLE fuel_transactions DROP COLUMN staff_card_id CASCADE;
ALTER TABLE shift_fuel_details DROP COLUMN staff_card_id CASCADE;
ALTER TABLE shift_merchandise_details DROP COLUMN staff_card_id CASCADE;
ALTER TABLE shift_payment_details DROP COLUMN staff_card_id CASCADE;
ALTER TABLE order_items DROP COLUMN order_id CASCADE;
ALTER TABLE order_payments DROP COLUMN order_id CASCADE;
ALTER TABLE order_promotions DROP COLUMN order_id CASCADE;
ALTER TABLE fuel_transaction_order_links DROP COLUMN order_id CASCADE;
ALTER TABLE fuel_transaction_order_links DROP COLUMN fuel_transaction_id CASCADE;
ALTER TABLE fuel_transactions_summary DROP COLUMN fuel_transaction_id CASCADE;

-- 重命名新字段为原字段名
ALTER TABLE employees RENAME COLUMN id_new TO id;
ALTER TABLE shifts RENAME COLUMN id_new TO id;
ALTER TABLE staff_cards RENAME COLUMN id_new TO id;
ALTER TABLE shift_templates RENAME COLUMN id_new TO id;
ALTER TABLE orders RENAME COLUMN id_new TO id;
ALTER TABLE fuel_transactions RENAME COLUMN id_new TO id;
ALTER TABLE order_items RENAME COLUMN id_new TO id;
ALTER TABLE order_payments RENAME COLUMN id_new TO id;
ALTER TABLE order_promotions RENAME COLUMN id_new TO id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN id_new TO id;
ALTER TABLE shift_fuel_details RENAME COLUMN id_new TO id;
ALTER TABLE shift_merchandise_details RENAME COLUMN id_new TO id;
ALTER TABLE shift_payment_details RENAME COLUMN id_new TO id;
ALTER TABLE employee_shifts RENAME COLUMN id_new TO id;
ALTER TABLE migration_log RENAME COLUMN id_new TO id;

-- 重命名外键字段
ALTER TABLE employee_shifts RENAME COLUMN employee_id_new TO employee_id;
ALTER TABLE employee_shifts RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_fuel_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_merchandise_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_payment_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE fuel_transactions RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE orders RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE fuel_transactions RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_fuel_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_merchandise_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_payment_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE order_items RENAME COLUMN order_id_new TO order_id;
ALTER TABLE order_payments RENAME COLUMN order_id_new TO order_id;
ALTER TABLE order_promotions RENAME COLUMN order_id_new TO order_id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN order_id_new TO order_id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN fuel_transaction_id_new TO fuel_transaction_id;
ALTER TABLE fuel_transactions_summary RENAME COLUMN fuel_transaction_id_new TO fuel_transaction_id;

-- ==========================================
-- 第六阶段：设置新字段为 NOT NULL 并创建新约束
-- ==========================================

-- 设置主键字段为 NOT NULL
ALTER TABLE employees ALTER COLUMN id SET NOT NULL;
ALTER TABLE shifts ALTER COLUMN id SET NOT NULL;
ALTER TABLE staff_cards ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_templates ALTER COLUMN id SET NOT NULL;
ALTER TABLE orders ALTER COLUMN id SET NOT NULL;
ALTER TABLE fuel_transactions ALTER COLUMN id SET NOT NULL;
ALTER TABLE order_items ALTER COLUMN id SET NOT NULL;
ALTER TABLE order_payments ALTER COLUMN id SET NOT NULL;
ALTER TABLE order_promotions ALTER COLUMN id SET NOT NULL;
ALTER TABLE fuel_transaction_order_links ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_fuel_details ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_merchandise_details ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_payment_details ALTER COLUMN id SET NOT NULL;
ALTER TABLE employee_shifts ALTER COLUMN id SET NOT NULL;
ALTER TABLE migration_log ALTER COLUMN id SET NOT NULL;

-- 设置外键字段为 NOT NULL（根据原始约束）
ALTER TABLE employee_shifts ALTER COLUMN employee_id SET NOT NULL;
ALTER TABLE employee_shifts ALTER COLUMN shift_id SET NOT NULL;
ALTER TABLE shift_fuel_details ALTER COLUMN shift_id SET NOT NULL;
ALTER TABLE shift_merchandise_details ALTER COLUMN shift_id SET NOT NULL;
ALTER TABLE shift_payment_details ALTER COLUMN shift_id SET NOT NULL;
ALTER TABLE order_items ALTER COLUMN order_id SET NOT NULL;
ALTER TABLE order_payments ALTER COLUMN order_id SET NOT NULL;
ALTER TABLE order_promotions ALTER COLUMN order_id SET NOT NULL;
ALTER TABLE fuel_transaction_order_links ALTER COLUMN fuel_transaction_id SET NOT NULL;
ALTER TABLE fuel_transaction_order_links ALTER COLUMN order_id SET NOT NULL;
ALTER TABLE fuel_transactions_summary ALTER COLUMN fuel_transaction_id SET NOT NULL;

-- 设置默认值为 gen_random_uuid()
ALTER TABLE employees ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shifts ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE staff_cards ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_templates ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE orders ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE fuel_transactions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE order_items ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE order_payments ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE order_promotions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE fuel_transaction_order_links ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_fuel_details ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_merchandise_details ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_payment_details ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE employee_shifts ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE migration_log ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 创建新的主键约束
ALTER TABLE employees ADD CONSTRAINT employees_pkey PRIMARY KEY (id);
ALTER TABLE shifts ADD CONSTRAINT shifts_pkey PRIMARY KEY (id);
ALTER TABLE staff_cards ADD CONSTRAINT staff_cards_pkey PRIMARY KEY (id);
ALTER TABLE shift_templates ADD CONSTRAINT shift_templates_pkey PRIMARY KEY (id);
ALTER TABLE orders ADD CONSTRAINT orders_pkey PRIMARY KEY (id);
ALTER TABLE fuel_transactions ADD CONSTRAINT fuel_transactions_pkey PRIMARY KEY (id);
ALTER TABLE order_items ADD CONSTRAINT order_items_pkey PRIMARY KEY (id);
ALTER TABLE order_payments ADD CONSTRAINT order_payments_pkey PRIMARY KEY (id);
ALTER TABLE order_promotions ADD CONSTRAINT order_promotions_pkey PRIMARY KEY (id);
ALTER TABLE fuel_transaction_order_links ADD CONSTRAINT fuel_transaction_order_links_pkey PRIMARY KEY (id);
ALTER TABLE shift_fuel_details ADD CONSTRAINT shift_fuel_details_pkey PRIMARY KEY (id);
ALTER TABLE shift_merchandise_details ADD CONSTRAINT shift_merchandise_details_pkey PRIMARY KEY (id);
ALTER TABLE shift_payment_details ADD CONSTRAINT shift_payment_details_pkey PRIMARY KEY (id);
ALTER TABLE employee_shifts ADD CONSTRAINT employee_shifts_pkey PRIMARY KEY (id);
ALTER TABLE migration_log ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);

-- 重新创建唯一约束
ALTER TABLE orders ADD CONSTRAINT orders_order_number_key UNIQUE (order_number);
ALTER TABLE shifts ADD CONSTRAINT shifts_shift_number_key UNIQUE (shift_number);
ALTER TABLE staff_cards ADD CONSTRAINT staff_cards_card_number_key UNIQUE (card_number);
ALTER TABLE fuel_transactions ADD CONSTRAINT fuel_transactions_transaction_number_key UNIQUE (transaction_number);
ALTER TABLE fuel_transaction_order_links ADD CONSTRAINT unique_fuel_transaction_order UNIQUE (fuel_transaction_id, order_id);

-- 重新创建外键约束
ALTER TABLE employee_shifts ADD CONSTRAINT employee_shifts_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES employees(id);
ALTER TABLE employee_shifts ADD CONSTRAINT employee_shifts_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id);
ALTER TABLE shift_fuel_details ADD CONSTRAINT shift_fuel_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE;
ALTER TABLE shift_merchandise_details ADD CONSTRAINT shift_merchandise_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE;
ALTER TABLE shift_payment_details ADD CONSTRAINT shift_payment_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE;
ALTER TABLE fuel_transactions ADD CONSTRAINT fuel_transactions_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id);
-- 重新创建 fuel_transactions_summary 表的外键约束
ALTER TABLE fuel_transactions_summary ADD CONSTRAINT fk_fuel_transactions_summary_transaction FOREIGN KEY (fuel_transaction_id) REFERENCES fuel_transactions(id);

-- 创建索引以提高性能
CREATE INDEX IF NOT EXISTS idx_employee_shifts_employee_id ON employee_shifts(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_shifts_shift_id ON employee_shifts(shift_id);
CREATE INDEX IF NOT EXISTS idx_shift_fuel_details_shift_id ON shift_fuel_details(shift_id);
CREATE INDEX IF NOT EXISTS idx_shift_fuel_details_staff_card_id ON shift_fuel_details(staff_card_id);
CREATE INDEX IF NOT EXISTS idx_shift_merchandise_details_shift_id ON shift_merchandise_details(shift_id);
CREATE INDEX IF NOT EXISTS idx_shift_merchandise_details_staff_card_id ON shift_merchandise_details(staff_card_id);
CREATE INDEX IF NOT EXISTS idx_shift_payment_details_shift_id ON shift_payment_details(shift_id);
CREATE INDEX IF NOT EXISTS idx_shift_payment_details_staff_card_id ON shift_payment_details(staff_card_id);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_payments_order_id ON order_payments(order_id);
CREATE INDEX IF NOT EXISTS idx_order_promotions_order_id ON order_promotions(order_id);
CREATE INDEX IF NOT EXISTS idx_fuel_transaction_order_links_fuel_transaction_id ON fuel_transaction_order_links(fuel_transaction_id);
CREATE INDEX IF NOT EXISTS idx_fuel_transaction_order_links_order_id ON fuel_transaction_order_links(order_id);
CREATE INDEX IF NOT EXISTS idx_fuel_transactions_shift_id ON fuel_transactions(shift_id);
CREATE INDEX IF NOT EXISTS idx_fuel_transactions_staff_card_id ON fuel_transactions(staff_card_id);
CREATE INDEX IF NOT EXISTS idx_orders_staff_card_id ON orders(staff_card_id);
CREATE INDEX IF NOT EXISTS idx_fuel_transactions_summary_fuel_transaction_id ON fuel_transactions_summary(fuel_transaction_id);

-- 删除旧的序列（如果存在）
DROP SEQUENCE IF EXISTS order_schema.employees_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shifts_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.staff_cards_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_templates_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.orders_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.fuel_transactions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.order_items_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.order_payments_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.order_promotions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.fuel_transaction_order_links_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_fuel_details_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_merchandise_details_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_payment_details_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.employee_shifts_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.migration_log_id_seq CASCADE;

-- 提交事务
COMMIT;

-- 验证迁移结果
SELECT 'Migration completed successfully. All ID fields converted to UUID.' AS status;
