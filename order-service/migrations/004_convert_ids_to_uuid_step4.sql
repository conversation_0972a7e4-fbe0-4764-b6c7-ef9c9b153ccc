-- 数据库迁移脚本第四步：重新创建约束和索引
-- 创建时间: 2025-01-20

-- 设置 schema
SET search_path TO order_schema, public, pg_catalog;

-- ==========================================
-- 第六阶段：设置新字段为 NOT NULL 并创建新约束
-- ==========================================

-- 设置主键字段为 NOT NULL
ALTER TABLE employees ALTER COLUMN id SET NOT NULL;
ALTER TABLE shifts ALTER COLUMN id SET NOT NULL;
ALTER TABLE staff_cards ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_templates ALTER COLUMN id SET NOT NULL;
ALTER TABLE orders ALTER COLUMN id SET NOT NULL;
ALTER TABLE fuel_transactions ALTER COLUMN id SET NOT NULL;
ALTER TABLE order_items ALTER COLUMN id SET NOT NULL;
ALTER TABLE order_payments ALTER COLUMN id SET NOT NULL;
ALTER TABLE order_promotions ALTER COLUMN id SET NOT NULL;
ALTER TABLE fuel_transaction_order_links ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_fuel_details ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_merchandise_details ALTER COLUMN id SET NOT NULL;
ALTER TABLE shift_payment_details ALTER COLUMN id SET NOT NULL;
ALTER TABLE employee_shifts ALTER COLUMN id SET NOT NULL;
ALTER TABLE migration_log ALTER COLUMN id SET NOT NULL;

-- 设置外键字段为 NOT NULL（根据原始约束，只设置必需的字段）
-- 注意：只有在数据完整的情况下才设置为 NOT NULL
-- employee_shifts 表
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM employee_shifts WHERE employee_id IS NULL) = 0 THEN
        ALTER TABLE employee_shifts ALTER COLUMN employee_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM employee_shifts WHERE shift_id IS NULL) = 0 THEN
        ALTER TABLE employee_shifts ALTER COLUMN shift_id SET NOT NULL;
    END IF;
END $$;

-- shift 相关表
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM shift_fuel_details WHERE shift_id IS NULL) = 0 THEN
        ALTER TABLE shift_fuel_details ALTER COLUMN shift_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM shift_merchandise_details WHERE shift_id IS NULL) = 0 THEN
        ALTER TABLE shift_merchandise_details ALTER COLUMN shift_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM shift_payment_details WHERE shift_id IS NULL) = 0 THEN
        ALTER TABLE shift_payment_details ALTER COLUMN shift_id SET NOT NULL;
    END IF;
END $$;

-- order 相关表
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM order_items WHERE order_id IS NULL) = 0 THEN
        ALTER TABLE order_items ALTER COLUMN order_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM order_payments WHERE order_id IS NULL) = 0 THEN
        ALTER TABLE order_payments ALTER COLUMN order_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM order_promotions WHERE order_id IS NULL) = 0 THEN
        ALTER TABLE order_promotions ALTER COLUMN order_id SET NOT NULL;
    END IF;
END $$;

-- fuel_transaction 相关表
DO $$
BEGIN
    IF (SELECT COUNT(*) FROM fuel_transaction_order_links WHERE fuel_transaction_id IS NULL) = 0 THEN
        ALTER TABLE fuel_transaction_order_links ALTER COLUMN fuel_transaction_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM fuel_transaction_order_links WHERE order_id IS NULL) = 0 THEN
        ALTER TABLE fuel_transaction_order_links ALTER COLUMN order_id SET NOT NULL;
    END IF;
    IF (SELECT COUNT(*) FROM fuel_transactions_summary WHERE fuel_transaction_id IS NULL) = 0 THEN
        ALTER TABLE fuel_transactions_summary ALTER COLUMN fuel_transaction_id SET NOT NULL;
    END IF;
END $$;

-- 设置默认值为 gen_random_uuid()
ALTER TABLE employees ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shifts ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE staff_cards ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_templates ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE orders ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE fuel_transactions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE order_items ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE order_payments ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE order_promotions ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE fuel_transaction_order_links ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_fuel_details ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_merchandise_details ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE shift_payment_details ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE employee_shifts ALTER COLUMN id SET DEFAULT gen_random_uuid();
ALTER TABLE migration_log ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 创建新的主键约束
ALTER TABLE employees ADD CONSTRAINT employees_pkey PRIMARY KEY (id);
ALTER TABLE shifts ADD CONSTRAINT shifts_pkey PRIMARY KEY (id);
ALTER TABLE staff_cards ADD CONSTRAINT staff_cards_pkey PRIMARY KEY (id);
ALTER TABLE shift_templates ADD CONSTRAINT shift_templates_pkey PRIMARY KEY (id);
ALTER TABLE orders ADD CONSTRAINT orders_pkey PRIMARY KEY (id);
ALTER TABLE fuel_transactions ADD CONSTRAINT fuel_transactions_pkey PRIMARY KEY (id);
ALTER TABLE order_items ADD CONSTRAINT order_items_pkey PRIMARY KEY (id);
ALTER TABLE order_payments ADD CONSTRAINT order_payments_pkey PRIMARY KEY (id);
ALTER TABLE order_promotions ADD CONSTRAINT order_promotions_pkey PRIMARY KEY (id);
ALTER TABLE fuel_transaction_order_links ADD CONSTRAINT fuel_transaction_order_links_pkey PRIMARY KEY (id);
ALTER TABLE shift_fuel_details ADD CONSTRAINT shift_fuel_details_pkey PRIMARY KEY (id);
ALTER TABLE shift_merchandise_details ADD CONSTRAINT shift_merchandise_details_pkey PRIMARY KEY (id);
ALTER TABLE shift_payment_details ADD CONSTRAINT shift_payment_details_pkey PRIMARY KEY (id);
ALTER TABLE employee_shifts ADD CONSTRAINT employee_shifts_pkey PRIMARY KEY (id);
ALTER TABLE migration_log ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);

-- 重新创建唯一约束
ALTER TABLE orders ADD CONSTRAINT orders_order_number_key UNIQUE (order_number);
ALTER TABLE shifts ADD CONSTRAINT shifts_shift_number_key UNIQUE (shift_number);
ALTER TABLE staff_cards ADD CONSTRAINT staff_cards_card_number_key UNIQUE (card_number);
ALTER TABLE fuel_transactions ADD CONSTRAINT fuel_transactions_transaction_number_key UNIQUE (transaction_number);
ALTER TABLE fuel_transaction_order_links ADD CONSTRAINT unique_fuel_transaction_order UNIQUE (fuel_transaction_id, order_id);

-- 重新创建外键约束（只在数据完整的情况下）
DO $$
BEGIN
    -- employee_shifts 外键
    IF (SELECT COUNT(*) FROM employee_shifts WHERE employee_id IS NOT NULL) > 0 THEN
        ALTER TABLE employee_shifts ADD CONSTRAINT employee_shifts_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES employees(id);
    END IF;
    IF (SELECT COUNT(*) FROM employee_shifts WHERE shift_id IS NOT NULL) > 0 THEN
        ALTER TABLE employee_shifts ADD CONSTRAINT employee_shifts_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id);
    END IF;
    
    -- shift 相关外键
    ALTER TABLE shift_fuel_details ADD CONSTRAINT shift_fuel_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE;
    ALTER TABLE shift_merchandise_details ADD CONSTRAINT shift_merchandise_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE;
    ALTER TABLE shift_payment_details ADD CONSTRAINT shift_payment_details_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE CASCADE;
    
    -- fuel_transactions 外键
    IF (SELECT COUNT(*) FROM fuel_transactions WHERE shift_id IS NOT NULL) > 0 THEN
        ALTER TABLE fuel_transactions ADD CONSTRAINT fuel_transactions_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES shifts(id);
    END IF;
    
    -- fuel_transactions_summary 外键
    ALTER TABLE fuel_transactions_summary ADD CONSTRAINT fk_fuel_transactions_summary_transaction FOREIGN KEY (fuel_transaction_id) REFERENCES fuel_transactions(id);
END $$;

-- 删除旧的序列（如果存在）
DROP SEQUENCE IF EXISTS order_schema.employees_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shifts_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.staff_cards_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_templates_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.orders_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.fuel_transactions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.order_items_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.order_payments_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.order_promotions_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.fuel_transaction_order_links_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_fuel_details_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_merchandise_details_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.shift_payment_details_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.employee_shifts_id_seq CASCADE;
DROP SEQUENCE IF EXISTS order_schema.migration_log_id_seq CASCADE;

SELECT 'Step 4 completed: Constraints and indexes recreated' AS status;
