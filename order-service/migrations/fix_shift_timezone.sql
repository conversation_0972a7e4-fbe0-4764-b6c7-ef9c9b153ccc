-- 修复shift表中的时区问题
-- 此脚本用于检查和修复现有shift表中的时间数据

-- 1. 设置schema和时区
SET search_path TO order_schema, pg_catalog;
SET timezone TO 'Asia/Jakarta';

-- 2. 首先检查当前数据库时区设置
SELECT current_setting('timezone') as current_timezone;

-- 3. 检查shift表中的时间数据，查看是否有时区问题
-- 显示前10条记录的时间信息
SELECT 
    id,
    shift_number,
    station_id,
    start_time,
    end_time,
    EXTRACT(timezone_hour FROM start_time) as start_tz_hour,
    EXTRACT(timezone_minute FROM start_time) as start_tz_minute,
    CASE 
        WHEN end_time IS NOT NULL THEN EXTRACT(timezone_hour FROM end_time)
        ELSE NULL 
    END as end_tz_hour,
    CASE 
        WHEN end_time IS NOT NULL THEN EXTRACT(timezone_minute FROM end_time)
        ELSE NULL 
    END as end_tz_minute,
    created_at,
    updated_at
FROM shifts 
WHERE deleted_at IS NULL
ORDER BY created_at DESC 
LIMIT 10;

-- 4. 查找可能有时区问题的记录
-- 查找时区不是+07:00的记录
SELECT 
    id,
    shift_number,
    station_id,
    start_time,
    end_time,
    EXTRACT(timezone_hour FROM start_time) as start_tz_hour,
    CASE 
        WHEN end_time IS NOT NULL THEN EXTRACT(timezone_hour FROM end_time)
        ELSE NULL 
    END as end_tz_hour
FROM shifts 
WHERE deleted_at IS NULL
    AND (
        EXTRACT(timezone_hour FROM start_time) != 7
        OR (end_time IS NOT NULL AND EXTRACT(timezone_hour FROM end_time) != 7)
    )
ORDER BY created_at DESC;

-- 5. 统计有问题的记录数量
SELECT 
    COUNT(*) as total_shifts,
    COUNT(CASE WHEN EXTRACT(timezone_hour FROM start_time) != 7 THEN 1 END) as wrong_start_timezone,
    COUNT(CASE WHEN end_time IS NOT NULL AND EXTRACT(timezone_hour FROM end_time) != 7 THEN 1 END) as wrong_end_timezone
FROM shifts 
WHERE deleted_at IS NULL;

-- 6. 修复时区问题的记录
-- 注意：这个操作会修改数据，请在执行前备份数据库

-- 备份原始数据（可选）
-- CREATE TABLE shifts_backup_before_timezone_fix AS SELECT * FROM shifts;

-- 识别并修复错误的时区记录
-- 这些记录的特征：在凌晨0-6点开始的班次（不合理），很可能是UTC时间被错误标记为Jakarta时间

-- 修复start_time：将这些可疑的时间加上7小时
UPDATE shifts
SET
    start_time = start_time + INTERVAL '7 hours',
    updated_at = NOW()
WHERE deleted_at IS NULL
    AND EXTRACT(hour FROM start_time) BETWEEN 0 AND 6  -- 凌晨0-6点的可疑记录
    AND id NOT IN (63);  -- 排除我们的测试记录

-- 修复end_time：对应的结束时间也需要修复
UPDATE shifts
SET
    end_time = end_time + INTERVAL '7 hours',
    updated_at = NOW()
WHERE deleted_at IS NULL
    AND end_time IS NOT NULL
    AND EXTRACT(hour FROM end_time) BETWEEN 0 AND 6  -- 凌晨0-6点的可疑记录
    AND id NOT IN (63);  -- 排除我们的测试记录

-- 7. 验证修复结果
SELECT 
    COUNT(*) as total_shifts,
    COUNT(CASE WHEN EXTRACT(timezone_hour FROM start_time) != 7 THEN 1 END) as remaining_wrong_start_timezone,
    COUNT(CASE WHEN end_time IS NOT NULL AND EXTRACT(timezone_hour FROM end_time) != 7 THEN 1 END) as remaining_wrong_end_timezone
FROM shifts 
WHERE deleted_at IS NULL;

-- 8. 显示修复后的示例数据
SELECT 
    id,
    shift_number,
    station_id,
    start_time,
    end_time,
    EXTRACT(timezone_hour FROM start_time) as start_tz_hour,
    CASE 
        WHEN end_time IS NOT NULL THEN EXTRACT(timezone_hour FROM end_time)
        ELSE NULL 
    END as end_tz_hour,
    updated_at
FROM shifts 
WHERE deleted_at IS NULL
ORDER BY updated_at DESC 
LIMIT 5;

-- 9. 创建索引以提高查询性能（如果不存在）
CREATE INDEX IF NOT EXISTS idx_shifts_start_time ON shifts(start_time);
CREATE INDEX IF NOT EXISTS idx_shifts_end_time ON shifts(end_time);
CREATE INDEX IF NOT EXISTS idx_shifts_station_deleted ON shifts(station_id, deleted_at);

-- 10. 添加注释说明修复情况
COMMENT ON TABLE shifts IS '班次表 - 时区已修复为Asia/Jakarta (+07:00)';
