-- 数据库迁移脚本第三步：删除约束和旧字段
-- 创建时间: 2025-01-20

-- 设置 schema
SET search_path TO order_schema, public, pg_catalog;

-- ==========================================
-- 第四阶段：删除旧的外键约束
-- ==========================================

-- 删除现有的外键约束
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_employee_id_fkey;
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_shift_id_fkey;
ALTER TABLE shift_fuel_details DROP CONSTRAINT IF EXISTS shift_fuel_details_shift_id_fkey;
ALTER TABLE shift_merchandise_details DROP CONSTRAINT IF EXISTS shift_merchandise_details_shift_id_fkey;
ALTER TABLE shift_payment_details DROP CONSTRAINT IF EXISTS shift_payment_details_shift_id_fkey;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_shift_id_fkey;
-- 删除 fuel_transactions_summary 表的外键约束
ALTER TABLE fuel_transactions_summary DROP CONSTRAINT IF EXISTS fk_fuel_transactions_summary_transaction;

-- ==========================================
-- 第五阶段：删除旧字段，重命名新字段
-- ==========================================

-- 删除旧的主键约束
ALTER TABLE employees DROP CONSTRAINT IF EXISTS employees_pkey;
ALTER TABLE shifts DROP CONSTRAINT IF EXISTS shifts_pkey;
ALTER TABLE staff_cards DROP CONSTRAINT IF EXISTS staff_cards_pkey;
ALTER TABLE shift_templates DROP CONSTRAINT IF EXISTS shift_templates_pkey;
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_pkey;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_pkey;
ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_pkey;
ALTER TABLE order_payments DROP CONSTRAINT IF EXISTS order_payments_pkey;
ALTER TABLE order_promotions DROP CONSTRAINT IF EXISTS order_promotions_pkey;
ALTER TABLE fuel_transaction_order_links DROP CONSTRAINT IF EXISTS fuel_transaction_order_links_pkey;
ALTER TABLE shift_fuel_details DROP CONSTRAINT IF EXISTS shift_fuel_details_pkey;
ALTER TABLE shift_merchandise_details DROP CONSTRAINT IF EXISTS shift_merchandise_details_pkey;
ALTER TABLE shift_payment_details DROP CONSTRAINT IF EXISTS shift_payment_details_pkey;
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_pkey;
ALTER TABLE migration_log DROP CONSTRAINT IF EXISTS migration_log_pkey;

-- 删除旧的唯一约束
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_order_number_key;
ALTER TABLE shifts DROP CONSTRAINT IF EXISTS shifts_shift_number_key;
ALTER TABLE staff_cards DROP CONSTRAINT IF EXISTS staff_cards_card_number_key;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_transaction_number_key;
ALTER TABLE fuel_transaction_order_links DROP CONSTRAINT IF EXISTS unique_fuel_transaction_order;

-- 删除旧字段（使用 CASCADE 删除依赖的视图）
ALTER TABLE employees DROP COLUMN id CASCADE;
ALTER TABLE shifts DROP COLUMN id CASCADE;
ALTER TABLE staff_cards DROP COLUMN id CASCADE;
ALTER TABLE shift_templates DROP COLUMN id CASCADE;
ALTER TABLE orders DROP COLUMN id CASCADE;
ALTER TABLE fuel_transactions DROP COLUMN id CASCADE;
ALTER TABLE order_items DROP COLUMN id CASCADE;
ALTER TABLE order_payments DROP COLUMN id CASCADE;
ALTER TABLE order_promotions DROP COLUMN id CASCADE;
ALTER TABLE fuel_transaction_order_links DROP COLUMN id CASCADE;
ALTER TABLE shift_fuel_details DROP COLUMN id CASCADE;
ALTER TABLE shift_merchandise_details DROP COLUMN id CASCADE;
ALTER TABLE shift_payment_details DROP COLUMN id CASCADE;
ALTER TABLE employee_shifts DROP COLUMN id CASCADE;
ALTER TABLE migration_log DROP COLUMN id CASCADE;

-- 删除旧的外键字段（使用 CASCADE 删除依赖的视图）
ALTER TABLE employee_shifts DROP COLUMN employee_id CASCADE;
ALTER TABLE employee_shifts DROP COLUMN shift_id CASCADE;
ALTER TABLE shift_fuel_details DROP COLUMN shift_id CASCADE;
ALTER TABLE shift_merchandise_details DROP COLUMN shift_id CASCADE;
ALTER TABLE shift_payment_details DROP COLUMN shift_id CASCADE;
ALTER TABLE fuel_transactions DROP COLUMN shift_id CASCADE;
ALTER TABLE orders DROP COLUMN staff_card_id CASCADE;
ALTER TABLE fuel_transactions DROP COLUMN staff_card_id CASCADE;
ALTER TABLE shift_fuel_details DROP COLUMN staff_card_id CASCADE;
ALTER TABLE shift_merchandise_details DROP COLUMN staff_card_id CASCADE;
ALTER TABLE shift_payment_details DROP COLUMN staff_card_id CASCADE;
ALTER TABLE order_items DROP COLUMN order_id CASCADE;
ALTER TABLE order_payments DROP COLUMN order_id CASCADE;
ALTER TABLE order_promotions DROP COLUMN order_id CASCADE;
ALTER TABLE fuel_transaction_order_links DROP COLUMN order_id CASCADE;
ALTER TABLE fuel_transaction_order_links DROP COLUMN fuel_transaction_id CASCADE;
ALTER TABLE fuel_transactions_summary DROP COLUMN fuel_transaction_id CASCADE;

-- 重命名新字段为原字段名
ALTER TABLE employees RENAME COLUMN id_new TO id;
ALTER TABLE shifts RENAME COLUMN id_new TO id;
ALTER TABLE staff_cards RENAME COLUMN id_new TO id;
ALTER TABLE shift_templates RENAME COLUMN id_new TO id;
ALTER TABLE orders RENAME COLUMN id_new TO id;
ALTER TABLE fuel_transactions RENAME COLUMN id_new TO id;
ALTER TABLE order_items RENAME COLUMN id_new TO id;
ALTER TABLE order_payments RENAME COLUMN id_new TO id;
ALTER TABLE order_promotions RENAME COLUMN id_new TO id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN id_new TO id;
ALTER TABLE shift_fuel_details RENAME COLUMN id_new TO id;
ALTER TABLE shift_merchandise_details RENAME COLUMN id_new TO id;
ALTER TABLE shift_payment_details RENAME COLUMN id_new TO id;
ALTER TABLE employee_shifts RENAME COLUMN id_new TO id;
ALTER TABLE migration_log RENAME COLUMN id_new TO id;

-- 重命名外键字段
ALTER TABLE employee_shifts RENAME COLUMN employee_id_new TO employee_id;
ALTER TABLE employee_shifts RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_fuel_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_merchandise_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_payment_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE fuel_transactions RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE orders RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE fuel_transactions RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_fuel_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_merchandise_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_payment_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE order_items RENAME COLUMN order_id_new TO order_id;
ALTER TABLE order_payments RENAME COLUMN order_id_new TO order_id;
ALTER TABLE order_promotions RENAME COLUMN order_id_new TO order_id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN order_id_new TO order_id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN fuel_transaction_id_new TO fuel_transaction_id;
ALTER TABLE fuel_transactions_summary RENAME COLUMN fuel_transaction_id_new TO fuel_transaction_id;

SELECT 'Step 3 completed: Old constraints and fields removed, new fields renamed' AS status;
