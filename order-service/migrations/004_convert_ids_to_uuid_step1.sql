-- 数据库迁移脚本第一步：添加新的 UUID 字段
-- 创建时间: 2025-01-20

-- 设置 schema 和启用 UUID 扩展
SET search_path TO order_schema, public, pg_catalog;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ==========================================
-- 第一阶段：添加新的 UUID 字段
-- ==========================================

-- 1. 为所有表添加新的 UUID 主键字段
ALTER TABLE employees ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shifts ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE staff_cards ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_templates ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE orders ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE fuel_transactions ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE order_items ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE order_payments ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE order_promotions ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE fuel_transaction_order_links ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_fuel_details ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_merchandise_details ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE shift_payment_details ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE employee_shifts ADD COLUMN id_new UUID DEFAULT gen_random_uuid();
ALTER TABLE migration_log ADD COLUMN id_new UUID DEFAULT gen_random_uuid();

-- 2. 为所有外键字段添加新的 UUID 字段
-- employees 相关
ALTER TABLE employee_shifts ADD COLUMN employee_id_new UUID;

-- shifts 相关
ALTER TABLE employee_shifts ADD COLUMN shift_id_new UUID;
ALTER TABLE shift_fuel_details ADD COLUMN shift_id_new UUID;
ALTER TABLE shift_merchandise_details ADD COLUMN shift_id_new UUID;
ALTER TABLE shift_payment_details ADD COLUMN shift_id_new UUID;
ALTER TABLE fuel_transactions ADD COLUMN shift_id_new UUID;

-- staff_cards 相关
ALTER TABLE orders ADD COLUMN staff_card_id_new UUID;
ALTER TABLE fuel_transactions ADD COLUMN staff_card_id_new UUID;
ALTER TABLE shift_fuel_details ADD COLUMN staff_card_id_new UUID;
ALTER TABLE shift_merchandise_details ADD COLUMN staff_card_id_new UUID;
ALTER TABLE shift_payment_details ADD COLUMN staff_card_id_new UUID;

-- orders 相关
ALTER TABLE order_items ADD COLUMN order_id_new UUID;
ALTER TABLE order_payments ADD COLUMN order_id_new UUID;
ALTER TABLE order_promotions ADD COLUMN order_id_new UUID;
ALTER TABLE fuel_transaction_order_links ADD COLUMN order_id_new UUID;

-- fuel_transactions 相关
ALTER TABLE fuel_transaction_order_links ADD COLUMN fuel_transaction_id_new UUID;
ALTER TABLE fuel_transactions_summary ADD COLUMN fuel_transaction_id_new UUID;

-- ==========================================
-- 第二阶段：填充新的 UUID 字段
-- ==========================================

-- 为现有记录生成 UUID 值
UPDATE employees SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shifts SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE staff_cards SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_templates SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE orders SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE fuel_transactions SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE order_items SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE order_payments SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE order_promotions SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE fuel_transaction_order_links SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_fuel_details SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_merchandise_details SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE shift_payment_details SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE employee_shifts SET id_new = gen_random_uuid() WHERE id_new IS NULL;
UPDATE migration_log SET id_new = gen_random_uuid() WHERE id_new IS NULL;

SELECT 'Step 1 completed: UUID fields added and populated' AS status;
