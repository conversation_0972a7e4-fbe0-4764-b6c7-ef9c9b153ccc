-- 回滚脚本：将 UUID 字段改回 BIGINT 类型
-- 创建时间: 2025-01-20
-- 警告: 此脚本会丢失现有的 UUID 数据，仅用于紧急回滚

-- 设置 schema
SET search_path TO order_schema, pg_catalog;

-- 开始事务
BEGIN;

-- ==========================================
-- 第一阶段：添加新的 BIGINT 字段
-- ==========================================

-- 为所有表添加新的 BIGINT 主键字段
ALTER TABLE employees ADD COLUMN id_new BIGSERIAL;
ALTER TABLE shifts ADD COLUMN id_new BIGSERIAL;
ALTER TABLE staff_cards ADD COLUMN id_new BIGSERIAL;
ALTER TABLE shift_templates ADD COLUMN id_new BIGSERIAL;
ALTER TABLE orders ADD COLUMN id_new BIGSERIAL;
ALTER TABLE fuel_transactions ADD COLUMN id_new BIGSERIAL;
ALTER TABLE order_items ADD COLUMN id_new BIGSERIAL;
ALTER TABLE order_payments ADD COLUMN id_new BIGSERIAL;
ALTER TABLE order_promotions ADD COLUMN id_new BIGSERIAL;
ALTER TABLE fuel_transaction_order_links ADD COLUMN id_new BIGSERIAL;
ALTER TABLE shift_fuel_details ADD COLUMN id_new BIGSERIAL;
ALTER TABLE shift_merchandise_details ADD COLUMN id_new BIGSERIAL;
ALTER TABLE shift_payment_details ADD COLUMN id_new BIGSERIAL;
ALTER TABLE employee_shifts ADD COLUMN id_new BIGSERIAL;
ALTER TABLE migration_log ADD COLUMN id_new BIGSERIAL;

-- 为所有外键字段添加新的 BIGINT 字段
ALTER TABLE employee_shifts ADD COLUMN employee_id_new BIGINT;
ALTER TABLE employee_shifts ADD COLUMN shift_id_new BIGINT;
ALTER TABLE shift_fuel_details ADD COLUMN shift_id_new BIGINT;
ALTER TABLE shift_merchandise_details ADD COLUMN shift_id_new BIGINT;
ALTER TABLE shift_payment_details ADD COLUMN shift_id_new BIGINT;
ALTER TABLE fuel_transactions ADD COLUMN shift_id_new BIGINT;
ALTER TABLE orders ADD COLUMN staff_card_id_new BIGINT;
ALTER TABLE fuel_transactions ADD COLUMN staff_card_id_new BIGINT;
ALTER TABLE shift_fuel_details ADD COLUMN staff_card_id_new BIGINT;
ALTER TABLE shift_merchandise_details ADD COLUMN staff_card_id_new BIGINT;
ALTER TABLE shift_payment_details ADD COLUMN staff_card_id_new BIGINT;
ALTER TABLE order_items ADD COLUMN order_id_new BIGINT;
ALTER TABLE order_payments ADD COLUMN order_id_new BIGINT;
ALTER TABLE order_promotions ADD COLUMN order_id_new BIGINT;
ALTER TABLE fuel_transaction_order_links ADD COLUMN order_id_new BIGINT;
ALTER TABLE fuel_transaction_order_links ADD COLUMN fuel_transaction_id_new BIGINT;
ALTER TABLE fuel_transactions_summary ADD COLUMN fuel_transaction_id_new BIGINT;

-- ==========================================
-- 第二阶段：建立 BIGINT 外键关系映射
-- ==========================================

-- 更新外键字段的 BIGINT 值（基于 UUID 关系）
-- employees 相关
UPDATE employee_shifts 
SET employee_id_new = e.id_new 
FROM employees e 
WHERE employee_shifts.employee_id = e.id;

-- shifts 相关
UPDATE employee_shifts 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE employee_shifts.shift_id = s.id;

UPDATE shift_fuel_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_fuel_details.shift_id = s.id;

UPDATE shift_merchandise_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_merchandise_details.shift_id = s.id;

UPDATE shift_payment_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_payment_details.shift_id = s.id;

UPDATE fuel_transactions 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE fuel_transactions.shift_id = s.id;

-- staff_cards 相关
UPDATE orders 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE orders.staff_card_id = sc.id;

UPDATE fuel_transactions 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE fuel_transactions.staff_card_id = sc.id;

UPDATE shift_fuel_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_fuel_details.staff_card_id = sc.id;

UPDATE shift_merchandise_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_merchandise_details.staff_card_id = sc.id;

UPDATE shift_payment_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_payment_details.staff_card_id = sc.id;

-- orders 相关
UPDATE order_items 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_items.order_id = o.id;

UPDATE order_payments 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_payments.order_id = o.id;

UPDATE order_promotions 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_promotions.order_id = o.id;

UPDATE fuel_transaction_order_links 
SET order_id_new = o.id_new 
FROM orders o 
WHERE fuel_transaction_order_links.order_id = o.id;

-- fuel_transactions 相关
UPDATE fuel_transaction_order_links 
SET fuel_transaction_id_new = ft.id_new 
FROM fuel_transactions ft 
WHERE fuel_transaction_order_links.fuel_transaction_id = ft.id;

UPDATE fuel_transactions_summary 
SET fuel_transaction_id_new = ft.id_new 
FROM fuel_transactions ft 
WHERE fuel_transactions_summary.fuel_transaction_id = ft.id;

-- ==========================================
-- 第三阶段：删除约束和旧字段
-- ==========================================

-- 删除外键约束
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_employee_id_fkey;
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_shift_id_fkey;
ALTER TABLE shift_fuel_details DROP CONSTRAINT IF EXISTS shift_fuel_details_shift_id_fkey;
ALTER TABLE shift_merchandise_details DROP CONSTRAINT IF EXISTS shift_merchandise_details_shift_id_fkey;
ALTER TABLE shift_payment_details DROP CONSTRAINT IF EXISTS shift_payment_details_shift_id_fkey;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_shift_id_fkey;

-- 删除主键约束
ALTER TABLE employees DROP CONSTRAINT IF EXISTS employees_pkey;
ALTER TABLE shifts DROP CONSTRAINT IF EXISTS shifts_pkey;
ALTER TABLE staff_cards DROP CONSTRAINT IF EXISTS staff_cards_pkey;
ALTER TABLE shift_templates DROP CONSTRAINT IF EXISTS shift_templates_pkey;
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_pkey;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_pkey;
ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_pkey;
ALTER TABLE order_payments DROP CONSTRAINT IF EXISTS order_payments_pkey;
ALTER TABLE order_promotions DROP CONSTRAINT IF EXISTS order_promotions_pkey;
ALTER TABLE fuel_transaction_order_links DROP CONSTRAINT IF EXISTS fuel_transaction_order_links_pkey;
ALTER TABLE shift_fuel_details DROP CONSTRAINT IF EXISTS shift_fuel_details_pkey;
ALTER TABLE shift_merchandise_details DROP CONSTRAINT IF EXISTS shift_merchandise_details_pkey;
ALTER TABLE shift_payment_details DROP CONSTRAINT IF EXISTS shift_payment_details_pkey;
ALTER TABLE employee_shifts DROP CONSTRAINT IF EXISTS employee_shifts_pkey;
ALTER TABLE migration_log DROP CONSTRAINT IF EXISTS migration_log_pkey;

-- 删除唯一约束
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_order_number_key;
ALTER TABLE shifts DROP CONSTRAINT IF EXISTS shifts_shift_number_key;
ALTER TABLE staff_cards DROP CONSTRAINT IF EXISTS staff_cards_card_number_key;
ALTER TABLE fuel_transactions DROP CONSTRAINT IF EXISTS fuel_transactions_transaction_number_key;
ALTER TABLE fuel_transaction_order_links DROP CONSTRAINT IF EXISTS unique_fuel_transaction_order;

-- 删除 UUID 字段
ALTER TABLE employees DROP COLUMN id;
ALTER TABLE shifts DROP COLUMN id;
ALTER TABLE staff_cards DROP COLUMN id;
ALTER TABLE shift_templates DROP COLUMN id;
ALTER TABLE orders DROP COLUMN id;
ALTER TABLE fuel_transactions DROP COLUMN id;
ALTER TABLE order_items DROP COLUMN id;
ALTER TABLE order_payments DROP COLUMN id;
ALTER TABLE order_promotions DROP COLUMN id;
ALTER TABLE fuel_transaction_order_links DROP COLUMN id;
ALTER TABLE shift_fuel_details DROP COLUMN id;
ALTER TABLE shift_merchandise_details DROP COLUMN id;
ALTER TABLE shift_payment_details DROP COLUMN id;
ALTER TABLE employee_shifts DROP COLUMN id;
ALTER TABLE migration_log DROP COLUMN id;

-- 删除 UUID 外键字段
ALTER TABLE employee_shifts DROP COLUMN employee_id;
ALTER TABLE employee_shifts DROP COLUMN shift_id;
ALTER TABLE shift_fuel_details DROP COLUMN shift_id;
ALTER TABLE shift_merchandise_details DROP COLUMN shift_id;
ALTER TABLE shift_payment_details DROP COLUMN shift_id;
ALTER TABLE fuel_transactions DROP COLUMN shift_id;
ALTER TABLE orders DROP COLUMN staff_card_id;
ALTER TABLE fuel_transactions DROP COLUMN staff_card_id;
ALTER TABLE shift_fuel_details DROP COLUMN staff_card_id;
ALTER TABLE shift_merchandise_details DROP COLUMN staff_card_id;
ALTER TABLE shift_payment_details DROP COLUMN staff_card_id;
ALTER TABLE order_items DROP COLUMN order_id;
ALTER TABLE order_payments DROP COLUMN order_id;
ALTER TABLE order_promotions DROP COLUMN order_id;
ALTER TABLE fuel_transaction_order_links DROP COLUMN order_id;
ALTER TABLE fuel_transaction_order_links DROP COLUMN fuel_transaction_id;
ALTER TABLE fuel_transactions_summary DROP COLUMN fuel_transaction_id;

-- 重命名新字段为原字段名
ALTER TABLE employees RENAME COLUMN id_new TO id;
ALTER TABLE shifts RENAME COLUMN id_new TO id;
ALTER TABLE staff_cards RENAME COLUMN id_new TO id;
ALTER TABLE shift_templates RENAME COLUMN id_new TO id;
ALTER TABLE orders RENAME COLUMN id_new TO id;
ALTER TABLE fuel_transactions RENAME COLUMN id_new TO id;
ALTER TABLE order_items RENAME COLUMN id_new TO id;
ALTER TABLE order_payments RENAME COLUMN id_new TO id;
ALTER TABLE order_promotions RENAME COLUMN id_new TO id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN id_new TO id;
ALTER TABLE shift_fuel_details RENAME COLUMN id_new TO id;
ALTER TABLE shift_merchandise_details RENAME COLUMN id_new TO id;
ALTER TABLE shift_payment_details RENAME COLUMN id_new TO id;
ALTER TABLE employee_shifts RENAME COLUMN id_new TO id;
ALTER TABLE migration_log RENAME COLUMN id_new TO id;

-- 重命名外键字段
ALTER TABLE employee_shifts RENAME COLUMN employee_id_new TO employee_id;
ALTER TABLE employee_shifts RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_fuel_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_merchandise_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE shift_payment_details RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE fuel_transactions RENAME COLUMN shift_id_new TO shift_id;
ALTER TABLE orders RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE fuel_transactions RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_fuel_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_merchandise_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE shift_payment_details RENAME COLUMN staff_card_id_new TO staff_card_id;
ALTER TABLE order_items RENAME COLUMN order_id_new TO order_id;
ALTER TABLE order_payments RENAME COLUMN order_id_new TO order_id;
ALTER TABLE order_promotions RENAME COLUMN order_id_new TO order_id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN order_id_new TO order_id;
ALTER TABLE fuel_transaction_order_links RENAME COLUMN fuel_transaction_id_new TO fuel_transaction_id;
ALTER TABLE fuel_transactions_summary RENAME COLUMN fuel_transaction_id_new TO fuel_transaction_id;

COMMIT;
