-- 数据库迁移脚本第二步：建立 UUID 外键关系映射
-- 创建时间: 2025-01-20

-- 设置 schema
SET search_path TO order_schema, public, pg_catalog;

-- ==========================================
-- 第三阶段：建立 UUID 外键关系映射
-- ==========================================

-- 更新外键字段的 UUID 值（基于原有的整数关系）
-- employees 相关
UPDATE employee_shifts 
SET employee_id_new = e.id_new 
FROM employees e 
WHERE employee_shifts.employee_id = e.id;

-- shifts 相关
UPDATE employee_shifts 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE employee_shifts.shift_id = s.id;

UPDATE shift_fuel_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_fuel_details.shift_id = s.id;

UPDATE shift_merchandise_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_merchandise_details.shift_id = s.id;

UPDATE shift_payment_details 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE shift_payment_details.shift_id = s.id;

UPDATE fuel_transactions 
SET shift_id_new = s.id_new 
FROM shifts s 
WHERE fuel_transactions.shift_id = s.id;

-- staff_cards 相关
UPDATE orders 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE orders.staff_card_id = sc.id;

UPDATE fuel_transactions 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE fuel_transactions.staff_card_id = sc.id;

UPDATE shift_fuel_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_fuel_details.staff_card_id = sc.id;

UPDATE shift_merchandise_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_merchandise_details.staff_card_id = sc.id;

UPDATE shift_payment_details 
SET staff_card_id_new = sc.id_new 
FROM staff_cards sc 
WHERE shift_payment_details.staff_card_id = sc.id;

-- orders 相关
UPDATE order_items 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_items.order_id = o.id;

UPDATE order_payments 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_payments.order_id = o.id;

UPDATE order_promotions 
SET order_id_new = o.id_new 
FROM orders o 
WHERE order_promotions.order_id = o.id;

UPDATE fuel_transaction_order_links 
SET order_id_new = o.id_new 
FROM orders o 
WHERE fuel_transaction_order_links.order_id = o.id;

-- fuel_transactions 相关
UPDATE fuel_transaction_order_links 
SET fuel_transaction_id_new = ft.id_new 
FROM fuel_transactions ft 
WHERE fuel_transaction_order_links.fuel_transaction_id = ft.id;

UPDATE fuel_transactions_summary 
SET fuel_transaction_id_new = ft.id_new 
FROM fuel_transactions ft 
WHERE fuel_transactions_summary.fuel_transaction_id = ft.id;

-- 验证映射结果
SELECT 'Step 2 completed: UUID foreign key mappings established' AS status;

-- 检查是否有未映射的记录
SELECT 
    'employee_shifts' as table_name,
    COUNT(*) as total_records,
    COUNT(employee_id_new) as mapped_employee_id,
    COUNT(shift_id_new) as mapped_shift_id
FROM employee_shifts
UNION ALL
SELECT 
    'order_items' as table_name,
    COUNT(*) as total_records,
    COUNT(order_id_new) as mapped_order_id,
    0 as mapped_shift_id
FROM order_items
UNION ALL
SELECT 
    'fuel_transactions_summary' as table_name,
    COUNT(*) as total_records,
    COUNT(fuel_transaction_id_new) as mapped_fuel_transaction_id,
    0 as mapped_shift_id
FROM fuel_transactions_summary;
